<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\BuyAndSell\Entities\PriceList;
use Modules\Good\Entities\Good;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create($this->table(), function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(PriceList::class)->constrained(PriceList::getTableName())->cascadeOnUpdate();
            $table->foreignIdFor(Good::class)->constrained(Good::getTableName())->cascadeOnUpdate();
            $table->unsignedBigInteger('price');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists($this->table());
    }

    public function table()
    {
        $prefix = config('buyandsell.prefix');
        return ($prefix ? $prefix . '__' : '') . 'price_list_items';
    }
};
