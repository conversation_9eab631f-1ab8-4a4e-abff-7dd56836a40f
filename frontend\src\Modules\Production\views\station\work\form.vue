<template>
    <j-input v-model="form.name" label="نام" error-field="name" dense />
    <div class="grid xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
        <q-checkbox v-for="item, j in additional.attributes" v-model="form.attributes" :label="item.name" :val="item.id"
            :key="j" />
    </div>
</template>
<script>

export default {
    props: {
        form: {
            type: Object,
            default: () => { attributes: [] }
        },
        additional: {
            type: Object,
            default: () => { }
        }
    },
};
</script>