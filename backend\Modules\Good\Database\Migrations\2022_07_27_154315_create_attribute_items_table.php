<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Good\Entities\Attribute;
use Modules\Good\Entities\AttributeItem;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create(AttributeItem::getTableName(), function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Attribute::class)->constrained(Attribute::getTableName());
            $table->string('name');
            $table->string('key')->nullable();
            $table->json('data')->default('{}');
            $table->boolean('is_active')->default(false);
            $table->boolean('is_active_customer')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists(AttributeItem::getTableName());
    }
};
