# Set default behavior to automatically normalize line endings
* text=auto eol=lf

# Backend (Laravel/PHP) files
backend/*.php diff=php linguist-language=PHP
backend/*.blade.php diff=html linguist-language=PHP
backend/composer.json linguist-language=JSON
backend/composer.lock linguist-generated=true

# Frontend (Vue.js/JavaScript) files
frontend/*.js diff=javascript linguist-language=JavaScript
frontend/*.ts diff=typescript linguist-language=TypeScript
frontend/*.vue diff=html linguist-language=Vue
frontend/*.json linguist-language=JSON
frontend/package-lock.json linguist-generated=true
frontend/yarn.lock linguist-generated=true

# Common web files
*.css diff=css linguist-language=CSS
*.scss diff=css linguist-language=SCSS
*.sass diff=css linguist-language=Sass
*.html diff=html linguist-language=HTML
*.md diff=markdown linguist-language=Markdown

# Configuration files
*.json linguist-language=JSON
*.yml linguist-language=YAML
*.yaml linguist-language=YAML

# Binary files
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg text
*.woff binary
*.woff2 binary
*.ttf binary
*.eot binary

# Export ignore (files not included in git archive)
/.github export-ignore
CHANGELOG.md export-ignore
.styleci.yml export-ignore
README.md export-ignore
.gitignore export-ignore
.gitattributes export-ignore
