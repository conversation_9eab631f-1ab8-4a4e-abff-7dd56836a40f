<template>
  <div class="q-gutter-sm">
    <j-table-data :url="url" :columns="columns"
      :can-be-add="checkPermission('production_order_create') || checkRole('admin')"
      :can-be-edit="(selected) => checkPermission('production_order_edit_force') || (selected.status == 'PREORDER' && (checkRole('admin') || checkPermission('production_order_edit')))"
      :can-be-delete="(selected) => (selected.status == 'PREORDER' && checkPermission('production_order_delete')) || checkRole('admin')"
      v-model:selected="selected">
      <template #select_bar>
        <j-btn v-if="(checkPermission('production_order_status') || checkRole('admin')) && selected && selected.id"
          dense icon="task_alt" title="مراحل اداری" color="secondary" @click="showDialogLevel()" />
        <j-btn v-if="selected" dense icon="print" title="پرینت" color="secondary" @click="showDialogPrint()"
          @shortkey="showDialogPrint()" v-shortkey="{ en: ['shift', 'p'], fa: ['shift', 'ح'] }" />
        <j-btn
          v-if="selected && ['PRODUCTION', 'FINISH_JOB'].includes(selected.status) && checkPermission('production_order_tracking')"
          dense icon="query_stats" title="رهگیری تولید" color="secondary"
          :to="{ name: 'order_tracking', params: { id: selected.id } }" />
        <!-- <j-btn v-if="selected && checkPermission('order_id')" dense
          icon="edit" title="ویرایش" color="secondary" :to="{ name: 'order_id', params: { id: selected.id } }" /> -->
      </template>
      <template #dialog="props">
        <table-form v-bind="props" v-model:form="props.form" />
      </template>
      <template #prefix_title>
        سفارش
      </template>
      <!-- <template #bar>
        <j-btn v-if="(checkPermission('production_order_status') || checkRole('admin')) && selected && selected.id"
          label="مراحل اداری" color="secondary" @click="showDialogLevel()" />
        <j-btn v-if="selected && selected.id" label="پرینت" color="secondary" @click="showDialogPrint()"
          @shortkey="showDialogPrint()" v-shortkey="{ en: ['shift', 'p'], fa: ['shift', 'ح'] }" />
      </template> -->
    </j-table-data>
  </div>
  <j-dialog v-model="dialogLevel" @hide="publicStore.reloadViewRouter()">
    <checklist v-bind="{ selected }" />
  </j-dialog>
  <print v-if="dialogPrint" v-model="dialogPrint" v-bind="{ selected }">
    <template #bar>
      <j-btn v-if="(checkPermission('production_order_status') || checkRole('admin')) && selected && selected.id"
        label="مراحل اداری" color="secondary" @click="showDialogLevel()" />
    </template>
  </print>
</template>

<script>
import { ref } from "vue";
import TableForm from "./form.vue";
import Print from "./print.vue";
import Checklist from "./checklist.vue";
import { checkPermission, checkRole } from '@/helpers'
import { usePublicStore } from "@/stores";

export default {
  setup() {

    const columns = [
      {
        name: 'code',
        required: true,
        label: 'کد سفارش',
        field: 'code',
        sortable: true,
        style: 'width: 50px',
        filter: 'FilterInput',
      },
      {
        name: 'status',
        required: true,
        label: 'وضعیت',
        field: 'label_status',
        sortable: true,
        style: 'width: 80px',
        filter: 'FilterSelect',
        filterOption: 'statuses'
      },
      {
        name: 'full_name',
        required: true,
        label: 'نمایندگی',
        field: 'party_name',
        sortable: true,
        filter: {
          type: 'FilterInput',
          relation: 'party',
        },
      },
      {
        name: 'customer_name',
        required: true,
        label: 'مشتری',
        field: 'customer_name',
        sortable: true,
        filter: 'FilterInput',
      },
      {
        name: 'created_at',
        required: true,
        label: 'تاریخ ثبت',
        field: 'created_at',
        sortable: true,
        style: 'width: 50px',
      },
      {
        name: 'submit_date',
        required: true,
        label: 'تاریخ شروع',
        field: 'submit_date',
        sortable: true,
        style: 'width: 50px',
        filter: 'FilterDate'
      },
      {
        name: 'delivery_date',
        required: true,
        label: 'تاریخ سفارش',
        field: 'delivery_date',
        sortable: true,
        style: 'width: 50px',
        filter: 'FilterDate'
      },
      {
        name: 'name',
        label: 'کاربر',
        field: 'user_name',
        sortable: true,
        filter: {
          type: 'FilterInput',
          relation: 'user',
        },
      },
    ]
    const url = ref('/production/production_order')

    const dialogLevel = ref(false)
    const dialogPrint = ref(false)
    const showDialogLevel = () => {
      dialogLevel.value = true;
    }
    const showDialogPrint = () => {
      dialogPrint.value = true;
    }
    const publicStore = usePublicStore();

    return {
      publicStore,
      url,
      columns,
      // changeStatus,
      showDialogPrint,
      showDialogLevel,
      dialogLevel,
      dialogPrint,
      checkPermission,
      checkRole,
      selected: ref({})
    };
  },
  components: { TableForm, Checklist, Print },
};
</script>
