<?php

namespace Modules\Production\Http\Controllers;

use App\Http\Controllers\API\BaseController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Carbon;
use Illuminate\Validation\Rule;
use Modules\Good\Entities\Attribute;
use Modules\Production\Entities\ProductionOrder;

class ProductionOrderController extends BaseController implements HasMiddleware
{

    // protected $userRepository;

    // public function __construct(ProductionOrderRepository $userRepository)
    // {
    //     parent::__construct($userRepository);
    // }

    public static function middleware(): array
    {
        return [
            new Middleware('permission:manage_users|production_order', only: ['index']),
            new Middleware('permission:manage_users.create|production_order.create', only: ['create', 'store']),
            new Middleware('permission:manage_users.edit|production_order.edit', only: ['show', 'update']),
            new Middleware('permission:manage_users.delete|production_order.delete', only: ['delete', 'destroy']),
            new Middleware('permission:production_order.print-order', only: ['print']),
            //new Middleware('subscribed', except: ['store']),
        ];
    }

    public function index()
    {
        return JsonResource::collection(ProductionOrder::query()->filter()->jpaginate(null, 'id', 'true'))->additional([
             'statuses' => ProductionOrder::$statuses
        ]);
    }
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'delivery_date' => 'required',
            'party_id' => 'required',
            'customer_name' => 'required',
            'items' => 'required',
        ], [
            'items.required' => 'اقلام سفارش الزامی است!',
        ]);
        $data = $request->all();
        $data['status'] = ProductionOrder::PREORDER;
        $data['user_id'] = auth()->user()->id;
        $productionOrder = ProductionOrder::create($data);
        $items = collect($data['items'])->map(function ($item) {
            return [...$item, 'good_id' => $item['good']['id'], 'total_price' => $item['count'] * $item['price']];
        })->toArray();
        $productionOrder->items()->sync($items);
        $productionOrder->items = $productionOrder->items()->with('good')->get();

        $productionOrder->checklists()->create([
            'status' => $data['status'],
            'updated_at' => Carbon::now(),
            'username' => auth()->user()->full_name,
        ]);

        return $this->handleResponse($productionOrder, trans('request.done'));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(ProductionOrder $productionOrder)
    {

        $productionOrder->items = $productionOrder->items()->with('good.attributes')->get();
        return $this->handleResponse([
            'form' => $productionOrder,
            'formOptions' => [
                'attributes' => Attribute::query()->with('items')->get(),
            ],
        ]);
    }
    public function create()
    {
        return $this->handleResponse([
            'formOptions' => [
                'attributes' => Attribute::query()->with('items')->get(),
            ],
        ]);
    }
    // متد update با قوانین اعتبارسنجی مشخص
    public function update(Request $request, ProductionOrder $productionOrder): JsonResponse
    {
        $request->validate([
            'delivery_date' => 'required',
            'party_id' => 'required',
        ]);
        $data = $request->all();
        unset($data['status']);
        $data['user_id'] = auth()->user()->id;
        $productionOrder->update($data);
        $items = collect($data['items'])->map(function ($item) {
            return [...$item, 'good_id' => $item['good']['id'], 'total_price' => $item['count'] * $item['price']];
        })->toArray();

        $productionOrder->items()->sync($items);
        $productionOrder->items = $productionOrder->items()->with('good')->get();
        return $this->handleResponse([
            'form' => $productionOrder,
        ]);
        // اگر اعتبارسنجی موفق بود، فراخوانی متد update از کلاس پایه
        //return parent::update($request, $id);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $ids
     * @return \Illuminate\Http\Response
     */
    public function delete(Request $request)
    {
        // اعتبارسنجی ورودی برای اطمینان از اینکه ورودی آرایه‌ای از شناسه‌هاست
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => ['integer', Rule::exists(ProductionOrder::class, 'id')],
        ], [
            'ids.*.exists' => 'کاربری با شناسه :input یافت نشد یا امکان حذف ندارد.',
        ]);

        // دریافت شناسه‌ها از ورودی
        $ids = $validated['ids'];

        // حذف مدل‌ها با شناسه‌های مشخص شده
        try {
            ProductionOrder::destroy($ids);
        } catch (\Exception $e) {
            // در صورت بروز خطا، می‌توانید یک پاسخ مناسب ارسال کنید
            return $this->handleError($e, 'امکان حذف وجود ندارد', 500);
        }

        // ارسال پاسخ موفقیت‌آمیز
        return $this->handleResponse(null, trans('request.done')); // response()->json(['message' => 'Records deleted successfully']);
    }

    public function checklists(ProductionOrder $productionOrder, $message = null)
    {
        $productionOrder->checklists;
        return $this->handleResponse([
            'id' => $productionOrder['id'],
            'status' => $productionOrder['status'],
            'next_status' => $productionOrder['next_status'],
            'previous_status' => $productionOrder['previous_status'],
            //'statuses' => ProductionOrder::$statuses,
            'statuses' => collect(ProductionOrder::$statuses)->map(function ($m) use ($productionOrder) {
                $checklist = $productionOrder->checklists->where('status', $m['value'])->first();
                if ($checklist) {
                    $m['is_done'] = $m['value'] == $productionOrder['status'];
                    $m['updated_at'] = $checklist['updated_at'];
                    $m['username'] = $checklist['username'];
                }

                return $m;
            }),
        ], $message);
    }
    public function changeStatus(ProductionOrder $productionOrder)
    {

        $status = request('status') ?? $productionOrder->status;
        if (!auth()->user()->can('production_order.change-status.' . $status)) {
            return $this->handleError('دسترسی ندارید!', 403);
        }

        if ($current = $productionOrder->checklists()->where('status', $status)->first()) {
            $current->update([
                'status' => $status,
                'updated_at' => Carbon::now(),
                'username' => auth()->user()->full_name,
            ]);
        } else {
            $productionOrder->checklists()->create([
                'status' => $status,
                'updated_at' => Carbon::now(),
                'username' => auth()->user()->full_name,
            ]);
        }

        $productionOrder->update(['status' => $status]);

        return $this->checklists($productionOrder, trans('request.done'));
    }

    public function printLabel(ProductionOrder $productionOrder)
    {
        $temp = $productionOrder->lastPrintLable ?? [];

        $temp[] = ['time' => verta()->formatJalaliDatetime(), 'user' => auth()->user()->name];
        $productionOrder->lastPrintLable = $temp;
        $productionOrder->save();
        return $productionOrder->lastPrintLable;
    }
    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function print(ProductionOrder $productionOrder)
    {
        $attributes = Attribute::query()->with(['items', 'parent.items'])->get()->map(function ($m) {
            if ($m['items'] && $m->parent?->items) {
                $m['items'] = $m['items']->merge($m->parent?->items);
            }
            return array_merge($m->toArray(), [
                "items" => $m['items']->toArray(),
            ]);
        });

        $productionOrder->items = $productionOrder->items()->with('good')->get()->map(function ($m) use ($attributes) {
            $getLabelAttributes = $m->getLabelAttributes($attributes);
            $m['attributes_label'] = collect($getLabelAttributes)->pluck('label', 'attribute_key');
            $m['attributes_label_column'] = $getLabelAttributes;

            return $m;
        });
        $productionOrder['diff_delivery_date'] = !($productionOrder->delivery_date && $productionOrder->submit_date) ? 0 : verta()->parse($productionOrder->submit_date)->diffDays(verta()->parse($productionOrder->delivery_date));
        return $this->handleResponse($productionOrder, null, [
            'statuses' => collect(ProductionOrder::$statuses)->map(function ($m) use ($productionOrder) {
                $checklist = $productionOrder->checklists->where('status', $m['value'])->first();
                if ($checklist) {
                    $m['is_done'] = $m['value'] == $productionOrder['status'];
                    $m['updated_at'] = $checklist['updated_at'];
                }

                return $m;
            }),
            'attributes' => $attributes,
            'station_labels' => '',
            'templates' => [],
        ]);
    }
}
