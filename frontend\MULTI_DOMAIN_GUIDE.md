# راهنمای Multi-Domain Frontend

این راهنما نحوه استفاده از ساختار جدید فرانت‌اند که برای دو دامنه panel.erp.test و crm.erp.test طراحی شده است را توضیح می‌دهد.

## ساختار پروژه

```
frontend/
├── src/
│   ├── views/
│   │   ├── panel/          # صفحات مخصوص پنل
│   │   ├── crm/            # صفحات مخصوص CRM
│   │   └── shared/         # صفحات مشترک
│   ├── stores/
│   │   ├── panel/          # Store های مخصوص پنل
│   │   ├── crm/            # Store های مخصوص CRM
│   │   └── shared/         # Store های مشترک
│   └── config/
│       └── domains.js      # کانفیگ دامنه‌ها
```

## تنظیمات محیط

### فایل .env

```env
# Domain Configuration
VITE_PANEL_DOMAIN=panel.erp.test
VITE_CRM_DOMAIN=crm.erp.test

# Development Configuration
VITE_DEV_PORT=3000
VITE_CRM_DEV_PORT=3001
```

## دستورات اجرا

### توسعه (Development)

```bash
# اجرای پنل روی panel.erp.test:3000
npm run dev:panel

# اجرای CRM روی crm.erp.test:3001
npm run dev:crm

# اجرای پیش‌فرض (پنل)
npm run dev
```

### ساخت (Build)

```bash
# ساخت پنل
npm run build:panel

# ساخت CRM
npm run build:crm

# ساخت هر دو
npm run build:prod
```

## تنظیم DNS محلی

برای توسعه محلی، باید دامنه‌ها را در فایل hosts تنظیم کنید:

### Windows
فایل: `C:\Windows\System32\drivers\etc\hosts`

### Linux/Mac
فایل: `/etc/hosts`

```
127.0.0.1 panel.erp.test
127.0.0.1 crm.erp.test
```

## نحوه کار

1. **تشخیص دامنه**: سیستم بر اساس `window.location.host` تشخیص می‌دهد که کاربر از کدام دامنه وارد شده
2. **بارگذاری مناسب**: بر اساس دامنه، views و stores مناسب بارگذاری می‌شوند
3. **Routing شرطی**: routes مختلف برای هر دامنه تعریف شده‌اند

## اضافه کردن صفحه جدید

### برای پنل:
1. فایل را در `src/views/panel/` ایجاد کنید
2. آن را در `src/views/panel/index.js` export کنید
3. route مربوطه را در `panel_routes` اضافه کنید

### برای CRM:
1. فایل را در `src/views/crm/` ایجاد کنید
2. آن را در `src/views/crm/index.js` export کنید
3. route مربوطه را در `crm_routes` اضافه کنید

## اضافه کردن Store جدید

### Store مشترک:
1. فایل را در `src/stores/` ایجاد کنید
2. آن را در `src/stores/shared/index.js` export کنید

### Store مخصوص پنل:
1. فایل را در `src/stores/` ایجاد کنید
2. آن را در `src/stores/panel/index.js` export کنید

### Store مخصوص CRM:
1. فایل را در `src/stores/` ایجاد کنید
2. آن را در `src/stores/crm/index.js` export کنید

## مزایای این ساختار

1. **کد مشترک**: Components و utilities مشترک قابل استفاده مجدد
2. **جداسازی منطقی**: هر دامنه views و stores مخصوص خود را دارد
3. **نگهداری آسان**: تغییرات مشترک در یک جا اعمال می‌شود
4. **عملکرد بهتر**: فقط کدهای مورد نیاز بارگذاری می‌شوند
5. **انعطاف‌پذیری**: امکان اضافه کردن دامنه‌های جدید

## نکات مهم

- همیشه از `getCurrentDomainType()` برای تشخیص دامنه استفاده کنید
- Views مشترک را در پوشه `shared` قرار دهید
- Store های مشترک را در `stores/shared` قرار دهید
- برای تست محلی، از دامنه‌های تعریف شده استفاده کنید
