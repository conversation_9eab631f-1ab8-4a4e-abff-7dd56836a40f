<template>
  <!-- <Layout /> -->
  <component
    v-if="layout"
    :is="layout"
    class="bg-page dark:bg-page-dark"
    :class="themeClasses"
  >
    <page404View v-if="isNotFound" />
    <page403View v-else-if="isNotAccess" />
    <router-view v-else :key="route.path" />
  </component>
  <q-inner-loading v-else :showing="true" />
</template>

<script setup>
import { watch, ref, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import { usePublicStore, useThemeStore } from "@/stores";
import { page403View, page404View } from '@/views'
const route = useRoute();
// const layout = computed(() => {
//   publicStore.titleWindow = route.meta.title;
//   publicStore.icon = route.meta.icon;
//   return route.name ? route.meta.layout ?? "layout-auth" : null;
// });
const publicStore = usePublicStore();
const themeStore = useThemeStore();
const updateLayout = (to) => {
  //console.error('App')
  publicStore.titleWindow = route.meta.title;
  //console.error('updateLayout')
  publicStore.icon = route.meta.icon;
  layout.value = route.name ? route.meta.layout ?? "layout-auth" : null;

};

const layout = ref()
watch(route, (to) => {
  updateLayout(to);
});

onMounted(() => {
  updateLayout(route.path);
});



const isNotFound = computed(() => publicStore.isNotFound);
const isNotAccess = computed(() => publicStore.isNotAccess);

// Theme classes
const themeClasses = computed(() => themeStore.themeClasses);

</script>