#!/bin/bash

# Multi-Domain Frontend Setup Script
# This script sets up the multi-domain frontend environment

echo "🚀 Setting up Multi-Domain Frontend..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "frontend/package.json" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Step 1: Install dependencies
print_info "Installing frontend dependencies..."
cd frontend
npm install
if [ $? -eq 0 ]; then
    print_status "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Step 2: Check environment file
print_info "Checking environment configuration..."
if [ ! -f ".env" ]; then
    print_warning ".env file not found, copying from .env.example"
    cp .env.example .env
fi

# Step 3: Update hosts file (requires sudo)
print_info "Setting up local DNS entries..."
print_warning "This step requires sudo access to modify /etc/hosts"

# Check if entries already exist
if ! grep -q "panel.erp.test" /etc/hosts; then
    echo "127.0.0.1 panel.erp.test" | sudo tee -a /etc/hosts
    print_status "Added panel.erp.test to hosts file"
else
    print_status "panel.erp.test already exists in hosts file"
fi

if ! grep -q "crm.erp.test" /etc/hosts; then
    echo "127.0.0.1 crm.erp.test" | sudo tee -a /etc/hosts
    print_status "Added crm.erp.test to hosts file"
else
    print_status "crm.erp.test already exists in hosts file"
fi

if ! grep -q "api.erp.test" /etc/hosts; then
    echo "127.0.0.1 api.erp.test" | sudo tee -a /etc/hosts
    print_status "Added api.erp.test to hosts file"
else
    print_status "api.erp.test already exists in hosts file"
fi

# Step 4: Test build
print_info "Testing build configuration..."
node test-build.js

# Step 5: Instructions
echo ""
print_status "Multi-Domain Frontend setup completed!"
echo ""
print_info "Next steps:"
echo "1. Start the backend: cd ../backend && php artisan serve --host=api.erp.test --port=8000"
echo "2. Start Panel frontend: npm run dev:panel"
echo "3. Start CRM frontend: npm run dev:crm"
echo ""
print_info "Access URLs:"
echo "• Panel: http://panel.erp.test:3000"
echo "• CRM: http://crm.erp.test:3001"
echo "• API: http://api.erp.test:8000"
echo ""
print_info "For production deployment:"
echo "• Build: npm run build:prod"
echo "• Use nginx configuration: ../nginx-multi-domain.conf"
echo "• Or use Docker: docker-compose -f ../docker-compose.multi-domain.yml up"

cd ..
print_status "Setup script completed successfully!"
