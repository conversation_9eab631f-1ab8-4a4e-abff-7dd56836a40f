<template>
    <div class="flex" :class="!$q.screen.xs ? 'q-table__card' : 'border-b-2'"
        style="background: none; border-radius: 10px;">
        <div class="w-12 p-2" style="background:#2e3d50; border-radius: 0 10px 10px 0;">
            <div class="sticky top-14 flex gap-2">
                <slot name="select_bar"
                    v-bind="{ callback: getAll, selected: selected[0], selected_id: selected[0] ? selected[0].id : null }" />
            </div>
        </div>
        <j-table :selected="internalSelected" @update:selected="updateSelected" :rows="rows" :columns="columns"
            selection="single" :row-key="rowKey ?? 'id'" separator="cell" :filters="internalFilters"
            @update:filters="updateFilters" @onFilter="onFilter" class="flex-auto w-12" flat
            :dense="dense || true" style="border-radius: 10px 0 0 10px;">

            <template v-for="_, slot in $slots" v-slot:[slot]="props" :key="slot">
                <slot :name="slot" v-bind="props" :key="slot" />
            </template>
        </j-table>
    </div>
</template>

<script>
import { ref, watch } from 'vue';

export default {
    props: {
        dense: {
            type: Boolean,
            default: false
        },
        rows: {
            type: Array,
            default: () => []
        },
        columns: {
            type: Array,
            default: () => []
        },
        pagination: Object,
        filters: Object,
        formOption: Array,
        rowKey: String | Function,
    },
    setup(props, { emit }) {
        // ایجاد refs داخلی برای selected و filters
        const internalSelected = ref(props.selected ?? []);
        const internalFilters = ref(props.filters ?? {});

        // روش‌های به‌روزرسانی
        const updateSelected = (newSelected) => {
            internalSelected.value = newSelected;
            emit('update:selected', newSelected);
        };

        const updateFilters = (newFilters) => {
            internalFilters.value = newFilters;
            emit('update:filters', newFilters);
        };

        return {
            internalSelected,
            internalFilters,
            onFilter(...param) {
                emit('onFilter', ...param)
            },
            updateSelected,
            updateFilters,
        };
    },
    watch: {
        selected(newVal) {
            internalSelected.value = newVal;
        },
        filters(newVal) {
            internalFilters.value = newVal;
        }
    }
}
</script>
