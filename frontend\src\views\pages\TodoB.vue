<template>
  <div>
    <h2>Todo List 2</h2>
    <div v-if="isLoading">Loading...</div>
    <div v-else-if="error">{{ error }}</div>
    <ul v-else>
      <li v-for="todo in todos" :key="todo.id">
        {{ todo.title }}
        <button @click="deleteTodo(todo.id)">Delete</button>
      </li>
    </ul>
  </div>
</template>

<script>
import { useTodoStore } from '@/stores/todo.store';
import { storeToRefs } from 'pinia';

export default {
  setup() {
    const todoStore2 = useTodoStore(); // نمونه دوم
    const { todos, isLoading, error } = storeToRefs(todoStore2);

    todoStore2.fetchTodos();

    return {
      todos,
      isLoading,
      error,
      deleteTodo: todoStore2.deleteTodo,
    };
  },
};
</script>
