<template>
  <q-page padding>
    <!-- دکمه افزودن وام جدید -->
    <q-btn label="افزودن وام جدید" color="primary" @click="addLoanDialog = true" class="q-mb-md" />

    <!-- جدول اقساط -->
    <q-table
      :rows="loans"
      :columns="columns"
      row-key="day"
      flat
      separator="cell"
    >
      <template v-slot:body-cell="props">
        <q-td :props="props" align="center">
          <!-- ستون روز -->
          <div v-if="props.col.name === 'day'">
            <q-input
              v-model="props.row.day"
              type="number"
              min="1"
              max="31"
              dense
              style="width: 60px"
            />
          </div>

          <!-- ستون توضیحات -->
          <div v-else-if="props.col.name === 'description'">
            <q-input v-model="props.row.description" dense />
          </div>

          <!-- ستون پرداخت‌ها -->
          <div v-else-if="props.col.name.startsWith('month')">
            <q-input
              v-model="props.row.payments[props.col.index].amount"
              type="number"
              dense
              @change="updateLoan(props.row)"
            />
            <q-icon
              name="drag_indicator"
              @click="openCopyDialog(props.row, props.col.index)"
            />
            <q-checkbox
              v-model="props.row.payments[props.col.index].paid"
              color="green"
              @change="updateLoan(props.row)"
            />
          </div>

          <!-- ستون‌های محاسباتی -->
          <div v-else>
            {{ props.row[props.col.name] }}
          </div>
        </q-td>
      </template>

      <!-- ردیف جمع کل -->
      <template v-slot:bottom-row>
        <q-tr>
          <q-td :colspan="2">جمع کل:</q-td>
          <q-td>{{ totalInstallments() }}</q-td>
          <q-td>{{ totalAmount() }}</q-td>
          <q-td>{{ remainingInstallments() }}</q-td>
          <q-td>{{ remainingAmount() }}</q-td>
          <q-td v-for="(month, index) in months" :key="index">
            {{ getColumnTotal(index) }}
          </q-td>
        </q-tr>
      </template>
    </q-table>

    <!-- دیالوگ افزودن وام جدید -->
    <q-dialog v-model="addLoanDialog">
      <q-card>
        <q-card-section>
          <div>افزودن وام جدید</div>
        </q-card-section>

        <q-card-section>
          <q-input v-model="newLoan.day" label="روز" type="number" min="1" max="31" />
          <q-input v-model="newLoan.description" label="توضیحات" />
          <q-input v-model="newLoan.amount" label="مبلغ هر قسط" type="number" />
          <q-input v-model="newLoan.installments" label="تعداد اقساط" type="number" />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn label="لغو" flat @click="addLoanDialog = false" />
          <q-btn label="ثبت" color="primary" @click="addNewLoan" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- دیالوگ کپی -->
    <q-dialog v-model="copyDialog">
      <q-card>
        <q-card-section>
          <div>تعداد سلول‌های کپی</div>
        </q-card-section>

        <q-card-section>
          <q-input v-model="copyCount" label="تعداد" type="number" />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn label="لغو" flat @click="copyDialog = false" />
          <q-btn label="ثبت" color="primary" @click="confirmCopy" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script>
import { ref, reactive } from "vue";
import { useQuasar } from "quasar";
import moment from "moment-jalaali";

export default {
  setup() {
    const $q = useQuasar();

    // مدل وام جدید
    const newLoan = reactive({
      day: 1,
      description: "",
      amount: 0,
      installments: 1,
    });

    // وضعیت دیالوگ
    const addLoanDialog = ref(false);
    const copyDialog = ref(false);
    const draggedAmount = ref(0);
    const copyCount = ref(1);
    const currentCopyRow = ref(null);
    const currentCopyIndex = ref(0);

    const loans = reactive([
      {
        day: 1,
        description: "",
        payments: [],  // ستون پرداخت‌ها
      },
    ]);

    const months = reactive([]);

    const columns = reactive([
      { name: "day", label: "روز", field: "day", align: "center" },
      { name: "description", label: "توضیحات", field: "description", align: "center" },
      { name: "totalInstallments", label: "تعداد کل اقساط", field: "totalInstallments", align: "center" },
      { name: "totalAmount", label: "جمع کل اقساط", field: "totalAmount", align: "center" },
      { name: "remainingInstallments", label: "تعداد اقساط مانده", field: "remainingInstallments", align: "center" },
      { name: "remainingAmount", label: "جمع اقساط مانده", field: "remainingAmount", align: "center" },
    ]);

    // افزودن ماه جدید
    const addMonth = () => {
      const nextMonth = months.length === 0
        ? moment().format("jMMMM jYYYY") // شروع با ماه جاری
        : moment(months[months.length - 1], "jMMMM jYYYY").add(1, "jMonth").format("jMMMM jYYYY");
      
      months.push(nextMonth);

      loans.forEach((loan) => loan.payments.push({ amount: 0, paid: false }));
      columns.push({
        name: `month${months.length - 1}`,
        label: nextMonth,
        field: `month${months.length - 1}`,
        index: months.length - 1,
        align: "center",
      });
    };

    // افزودن وام جدید
    const addNewLoan = () => {
      const loan = {
        day: newLoan.day,
        description: newLoan.description,
        payments: Array(months.length).fill().map(() => ({ amount: newLoan.amount, paid: false })),
      };
      loans.push(loan);
      updateLoan(loan);  // محاسبات اقساط
      addLoanDialog.value = false;
    };

    // افزودن ستون ماه جاری به طور پیش‌فرض
    if (months.length === 0) {
      addMonth();  // افزودن ماه جاری در ابتدای شروع
    }

    // محاسبات اقساط
    const updateLoan = (loan) => {
      loan.totalInstallments = loan.payments.filter((payment) => payment.amount > 0).length;
      loan.totalAmount = loan.payments.reduce((sum, payment) => sum + Number(payment.amount), 0);
      loan.remainingInstallments = loan.payments.filter((payment) => payment.amount > 0 && !payment.paid).length;
      loan.remainingAmount = loan.payments.reduce(
        (sum, payment) => sum + (payment.amount > 0 && !payment.paid ? Number(payment.amount) : 0),
        0
      );
    };

    const totalInstallments = () => loans.reduce((sum, loan) => sum + (loan.totalInstallments ?? 0), 0);
    const totalAmount = () => loans.reduce((sum, loan) => sum + (loan.totalAmount ?? 0), 0);
    const remainingInstallments = () => loans.reduce((sum, loan) => sum + (loan.remainingInstallments ?? 0), 0);
    const remainingAmount = () => loans.reduce((sum, loan) => sum + (loan.remainingAmount ?? 0), 0);
    const getColumnTotal = (monthIndex) => loans.reduce((sum, loan) => sum + (loan.payments[monthIndex].amount ?? 0), 0);

    // دیالوگ کپی
    const openCopyDialog = (row, index) => {
      currentCopyRow.value = row;
      currentCopyIndex.value = index;
      copyDialog.value = true;
    };

    const confirmCopy = () => {
      for (let i = currentCopyIndex.value; i < currentCopyIndex.value + copyCount.value; i++) {
        if (i >= months.length) {
          addMonth();  // اضافه کردن ماه جدید در صورت نیاز
        }
        currentCopyRow.value.payments[i].amount = draggedAmount.value;
      }
      copyDialog.value = false;
    };

    return {
      months,
      loans,
      columns,
      addMonth,
      addNewLoan,
      totalInstallments,
      totalAmount,
      remainingInstallments,
      remainingAmount,
      getColumnTotal,
      addLoanDialog,
      openCopyDialog,
      confirmCopy,
      draggedAmount,
      copyCount,
      copyDialog,
      updateLoan,
      newLoan,
    };
  },
};
</script>

