<?php
namespace App\Traits;

use Illuminate\Support\Str;
use Nwidart\Modules\Facades\Module;

trait ModuleTrait
{
    protected function getModuleName()
    {
        // استخراج نام ماژول از فضای نام مدل
        $namespaceParts = explode('\\', get_class($this));
        $currentModule = Module::find($namespaceParts[1] ?? null);
        return $currentModule ? $currentModule->getLowerName() : null; // فرض بر این است که نام ماژول در فضای نام دوم است
    }

    public function getTable()
    {
        
        $tableName = '';
        if ($this->getModuleName() && config($this->getModuleName() . '.prefix')) {
            $tableName = config($this->getModuleName() . '.prefix') . '__';
        }

        return $this->table ?? $tableName . Str::snake(Str::pluralStudly(class_basename($this)));
    }

    public static function getTableName()
    {
        return (new static)->getTable();
    }
}
