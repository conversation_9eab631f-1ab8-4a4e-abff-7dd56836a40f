<template>
    <j-btn v-if="(checkPermission('production_order_status') || checkRole('admin')) && selected && selected.id" dense
        icon="task_alt" title="مراحل اداری" color="secondary" @click="showDialog()" />


    <j-dialog v-model="dialogLevel" @hide="callback">
        <q-card>
            <q-card-section>
                <div class="text-h6">مراحل اداری</div>
            </q-card-section>
            <q-separator />

            <div class="flex flex-col p-2 bg-zinc-100">
                <template v-for="status, index in data.statuses" :key="index">
                    <q-chip square :color="status.is_done ? 'primary' : (status.updated_at ? 'secondary' : '')"
                        :text-color="status.updated_at ? 'white' : ''" class="">
                        <div class="text-center w-1/2">{{ status.label }}</div>
                        <div class="text-center w-1/2">{{ status.updated_at }}</div>
                    </q-chip>
                </template>

            </div>

            <q-separator />

            <div class="gap-2 p-2 grid grid-cols-2">

                <j-btn v-if="data && data.previous_status
                    && (!['FINISH_JOB', 'PRODUCTION'].includes(data.status) || checkRole('admin'))
                    && (data.status != 'FINANCIAL_APPROVAL' || (data.status == 'FINANCIAL_APPROVAL' && checkPermission('production_order_financial_approval')))
                    && (data.status != 'DELIVERED' || (data.status == 'DELIVERED' && checkPermission('production_order_sent')))

                    " color="red" :label="'برگشت به ' + data.previous_status.label"
                    @click="changeStatus(data.id, data.previous_status.value)" class="col-start-1" />
                <j-btn v-if="data && data.next_status
                    && (((data.next_status.value != 'CONFIRM_CUSTOMER' || (data.next_status.value == 'CONFIRM_CUSTOMER' && !data.is_created_by_customer))
                    && (data.next_status.value != 'CONFIRM' || (data.next_status.value == 'CONFIRM' && checkPermission('production_order_management_approval')))
                    && (data.next_status.value != 'FINANCIAL_APPROVAL' || (data.next_status.value == 'FINANCIAL_APPROVAL' && checkPermission('production_order_financial_approval')))
                    && (data.next_status.value != 'DELIVERED' || (data.next_status.value == 'DELIVERED' && checkPermission('production_order_sent')))
                    && (data.next_status.value != 'PRODUCTION' || (data.next_status.value == 'PRODUCTION' && checkPermission('production_order_production')))
                    && (data.next_status.value != 'FINISH_JOB' || (data.next_status.value == 'FINISH_JOB' && checkPermission('production_order_finish_job')))
                    )|| checkPermission('production_order_confirm_cutomer'))" color="secondary" :label="data.next_status.label"
                    @click="changeStatus(data.id, data.next_status.value)" class="col-end-3" />
            </div>
        </q-card>
    </j-dialog>
</template>
<script>
import { ref } from 'vue';
import { api } from '@/boot/axios';
import { checkRole, checkPermission } from '@/helpers'
export default {
    props: {
        selected: Object,
        callback: Function,
    },
    setup(props) {
        const data = ref({})
        const getData = () => {

            if (props.selected && props.selected.id) {
                api.get(`/production/production_order/${props.selected.id}/checklists`).then(res => {
                    data.value = res.result
                })
            }
        }
        const changeStatus = (id, status) => {
            api.post(`production/production_order/${id}/changeStatus`, {
                status
            }).then(res => {
                data.value = res.result
                //if (props.callback) props.callback()
            });
        }
        const dialogLevel = ref(false)

        return {
            data,
            changeStatus,
            checkRole,
            checkPermission,
            dialogLevel,
            showDialog() {
                dialogLevel.value = true
                getData()
            },

        }
    },
}
</script>