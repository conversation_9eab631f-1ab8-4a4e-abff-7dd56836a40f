<template>
  <j-uploader @uploaded="uploaded" :multiple="false" :max-files="1">
    <template v-slot:header="scope">
      <div class="row no-wrap items-center q-pa-sm q-gutter-xs">
        <div>
          <div class="q-uploader__title">{{ label }}</div>
          <div class="q-uploader__subtitle">{{ scope.uploadSizeLabel }} / {{ scope.uploadProgressLabel }}</div>
        </div>

        <q-btn v-if="scope.isUploading" icon="clear" @click="scope.abort" round dense flat>
          <q-tooltip>لغو آپلود</q-tooltip>
        </q-btn>
        <q-btn v-if="scope.queuedFiles.length > 0" icon="clear_all" @click="scope.removeQueuedFiles" round dense flat>
          <q-tooltip>Clear All</q-tooltip>
        </q-btn>
        <q-btn v-if="scope.uploadedFiles.length > 0" icon="done_all" @click="scope.removeUploadedFiles" round dense flat>
          <q-tooltip>Remove Uploaded Files</q-tooltip>
        </q-btn>
        <q-space />
        <q-spinner v-if="scope.isUploading" class="q-uploader__spinner" />
        <q-btn v-if="scope.canAddFiles" label="انتخاب فایل" icon="add_box" @click="scope.pickFiles" dense flat>
          <q-uploader-add-trigger />
        </q-btn>
      </div>
    </template>

    <template v-slot:list="scope">
      <q-list separator>
        <j-image-viewer v-if="localValue" v-model:src="localValue" fullHeight />
        <template v-else>
          <q-item v-for="file in scope.files" :key="file.__key">
            <q-item-section>
              <q-item-label class="full-width ellipsis">
                {{ file.name }}
              </q-item-label>

              <q-item-label caption>
                Status: {{ file.__status }}
              </q-item-label>

              <q-item-label caption>
                {{ file.__sizeLabel }} / {{ file.__progressLabel }}
              </q-item-label>
            </q-item-section>

            <q-item-section v-if="file.__img" thumbnail class="gt-xs">
              <img :src="file.__img.src" />
            </q-item-section>

            <q-item-section top side>
              <q-btn class="gt-xs" size="12px" flat dense round icon="delete" @click="scope.removeFile(file)" />
            </q-item-section>
          </q-item>
        </template>
      </q-list>
    </template>
  </j-uploader>
</template>

<script>
import { ref, watch } from 'vue';

export default {
  props: {
    value: String,
    label: String,
  },
  setup(props, { emit }) {
    const localValue = ref(props.value);

    // Watch for changes in the prop and update localValue accordingly
    watch(() => props.value, (newValue) => {
      localValue.value = newValue;
    });

    // Emit updates to the parent component when localValue changes
    watch(localValue, (newValue) => {
      emit('update:value', newValue);
    });

    const uploaded = (files) => {
      const res = JSON.parse(files.xhr.response);
      localValue.value = res.path; // Update the local value instead of the prop directly
    };

    return {
      localValue,
      uploaded,
    };
  },
};
</script>
