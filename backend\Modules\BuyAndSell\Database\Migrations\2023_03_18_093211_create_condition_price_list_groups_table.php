<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\BuyAndSell\Entities\ConditionPriceList;
use Modules\Good\Entities\Group;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create($this->table(), function (Blueprint $table) {
            $table->foreignIdFor(ConditionPriceList::class)->constrained(ConditionPriceList::getTableName())->cascadeOnUpdate();
            $table->foreignIdFor(Group::class)->constrained(Group::getTableName())->cascadeOnUpdate();
            $table->unique([ConditionPriceList::getForeignKeyName(), Group::getForeignKeyName()], ConditionPriceList::getForeignKeyName().'_'.Group::getForeignKeyName());
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists($this->table());
    }

    public function table()
    {
        $prefix = config('buyandsell.prefix');
        return ($prefix ? $prefix . '__' : '') . 'condition_price_list_groups';
    }
};
