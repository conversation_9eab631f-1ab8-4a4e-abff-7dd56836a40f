<template>
    <j-input v-model="form.name" label="نام" error-field="name" dense />
    <j-fieldset v-if="additional.machines" label="دستگاه / ابزار">
        <j-checkbox v-for="machine, j in additional.machines" v-model="form.machines" :val="machine.id" required
            :label="machine.name" :key="j" />
    </j-fieldset>
</template>
<script>
export default {
    props: {
        form: {
            type: Object,
            default: () => { machines: [] }
        },
        additional: {
            type: Object,
            default: () => { }
        }

    },

};
</script>