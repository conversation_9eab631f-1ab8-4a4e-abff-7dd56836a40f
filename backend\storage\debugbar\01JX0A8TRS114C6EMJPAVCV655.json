{"__meta": {"id": "01JX0A8TRS114C6EMJPAVCV655", "datetime": "2025-06-05 18:40:06", "utime": **********.618233, "method": "GET", "uri": "/api/production/production_order?page=1&sortBy=id&rowsPerPage=10", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.26072, "end": **********.618246, "duration": 0.3575260639190674, "duration_str": "358ms", "measures": [{"label": "Booting", "start": **********.26072, "relative_start": 0, "end": **********.466871, "relative_end": **********.466871, "duration": 0.*****************, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.466881, "relative_start": 0.****************, "end": **********.618248, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.493638, "relative_start": 0.*****************, "end": **********.496294, "relative_end": **********.496294, "duration": 0.0026559829711914062, "duration_str": "2.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.556392, "relative_start": 0.****************, "end": **********.616602, "relative_end": **********.616602, "duration": 0.*****************, "duration_str": "60.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.616632, "relative_start": 0.****************, "end": **********.616647, "relative_end": **********.616647, "duration": 1.5020370483398438e-05, "duration_str": "15μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.0.1", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "api.erp.test:8000", "Timezone": "Asia/Tehran", "Locale": "fa"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 26, "nb_statements": 26, "nb_visible_statements": 26, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01683, "accumulated_duration_str": "16.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '107' limit 1", "type": "query", "params": [], "bindings": ["107"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.512197, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "raasa", "explain": null, "start_percent": 0, "width_percent": 11.052}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.520789, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "raasa", "explain": null, "start_percent": 11.052, "width_percent": 2.08}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-06-05 18:40:06', `personal_access_tokens`.`updated_at` = '2025-06-05 18:40:06' where `id` = 107", "type": "query", "params": [], "bindings": ["2025-06-05 18:40:06", "2025-06-05 18:40:06", 107], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.524076, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "raasa", "explain": null, "start_percent": 13.131, "width_percent": 10.398}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.53175, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "raasa", "explain": null, "start_percent": 23.529, "width_percent": 2.02}, {"sql": "select count(*) as aggregate from `_pro__production_orders` where `_pro__production_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/DataPagination.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Traits\\DataPagination.php", "line": 27}, {"index": 22, "namespace": null, "name": "Modules/Production/Http/Controllers/ProductionOrderController.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Http\\Controllers\\ProductionOrderController.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.550806, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "DataPagination.php:27", "source": {"index": 16, "namespace": null, "name": "app/Traits/DataPagination.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Traits\\DataPagination.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FTraits%2FDataPagination.php&line=27", "ajax": false, "filename": "DataPagination.php", "line": "27"}, "connection": "raasa", "explain": null, "start_percent": 25.55, "width_percent": 2.198}, {"sql": "select * from `_pro__production_orders` where `_pro__production_orders`.`deleted_at` is null limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/DataPagination.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Traits\\DataPagination.php", "line": 27}, {"index": 22, "namespace": null, "name": "Modules/Production/Http/Controllers/ProductionOrderController.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Http\\Controllers\\ProductionOrderController.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.5522041, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "DataPagination.php:27", "source": {"index": 16, "namespace": null, "name": "app/Traits/DataPagination.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Traits\\DataPagination.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FTraits%2FDataPagination.php&line=27", "ajax": false, "filename": "DataPagination.php", "line": "27"}, "connection": "raasa", "explain": null, "start_percent": 27.748, "width_percent": 2.614}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.5686479, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 30.362, "width_percent": 2.555}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 1 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.571111, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 32.917, "width_percent": 2.971}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.575643, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 35.888, "width_percent": 3.327}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 3 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.577353, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 39.216, "width_percent": 2.852}, {"sql": "select * from `users` where `users`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.5798461, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 42.068, "width_percent": 2.793}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 31 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.581203, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 44.86, "width_percent": 2.496}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.583292, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 47.356, "width_percent": 2.496}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 31 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.584587, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 49.851, "width_percent": 2.674}, {"sql": "select * from `users` where `users`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.586673, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 52.525, "width_percent": 2.436}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 2 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.588022, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 54.961, "width_percent": 10.636}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.592848, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 65.597, "width_percent": 2.852}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 5 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.594247, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 68.449, "width_percent": 2.733}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.597131, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 71.182, "width_percent": 2.793}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 1 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.5985, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 73.975, "width_percent": 2.793}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.6008081, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 76.768, "width_percent": 2.614}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 1 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.6021261, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 79.382, "width_percent": 2.614}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.604564, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 81.996, "width_percent": 9.745}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 1 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.6073072, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 91.741, "width_percent": 2.793}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.6098151, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 94.534, "width_percent": 2.674}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 2 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.611347, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 97.207, "width_percent": 2.793}]}, "models": {"data": {"App\\Models\\User": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Modules\\Production\\Entities\\ProductionOrder": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=1", "ajax": false, "filename": "ProductionOrder.php", "line": "?"}}, "Modules\\Production\\Entities\\Party": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FParty.php&line=1", "ajax": false, "filename": "Party.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}}, "count": 30, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage_users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1713842736 data-indent-pad=\"  \"><span class=sf-dump-note>manage_users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage_users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1713842736\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.536387, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://api.erp.test:8000/api/production/production_order?page=1&rowsPerPage=10&sortBy=id", "action_name": "production_order.index", "controller_action": "Modules\\Production\\Http\\Controllers\\ProductionOrderController@index", "uri": "GET api/production/production_order", "controller": "Modules\\Production\\Http\\Controllers\\ProductionOrderController@index<a href=\"phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FHttp%2FControllers%2FProductionOrderController.php&line=38\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Modules\\Production\\Http\\Controllers", "prefix": "api/production", "file": "<a href=\"phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FHttp%2FControllers%2FProductionOrderController.php&line=38\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">Modules/Production/Http/Controllers/ProductionOrderController.php:38-43</a>", "middleware": "api, auth:sanctum", "duration": "360ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1958525974 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>sortBy</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>rowsPerPage</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1958525974\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2122074913 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2122074913\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-761061561 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">api.erp.test:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 107|t******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://panel.erp.test:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://panel.erp.test:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">fa-IR,fa;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-761061561\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-303383516 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-303383516\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-701368441 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 05 Jun 2025 15:10:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701368441\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1169201385 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1169201385\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://api.erp.test:8000/api/production/production_order?page=1&rowsPerPage=10&sortBy=id", "action_name": "production_order.index", "controller_action": "Modules\\Production\\Http\\Controllers\\ProductionOrderController@index"}, "badge": null}}