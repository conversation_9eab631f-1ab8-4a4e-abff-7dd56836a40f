<template>
    <div class="grid gap-2">
        <q-list separator>
            <q-item v-for="meta, index in metas">
                <q-item-section>
                    <q-item-label class="flex gap-2 max-sm:flex-col">
                        <j-input filled v-model="meta.label" label="عنوان" hide-bottom-space class="sm:grow" />
                        <j-input filled v-model="meta.key" label="کلید" hide-bottom-space class="sm:grow" />
                        <j-input filled v-model="meta.value" label="مقدار" hide-bottom-space class="sm:grow" />
                    </q-item-label>
                </q-item-section>
                <q-item-section side>
                    <j-btn color="red" outline icon="clear" dense @click="remove(index)" />

                </q-item-section>
            </q-item>
        </q-list>
    </div>
    <j-btn label="جدید" flat class="min-w-full py-2 bg-amber-100" icon="add" @click="addNew" />
</template>
<script>
import { ref, watch } from 'vue'
export default {
    props: {
        value: {
            type: Array,
            default: () => []
        }
    },
    setup(props, { emit }) {
        const metas = ref(props.value.length > 0 ? props.value : [
            {
                key: '',
                value: '',
            },
        ]);

        watch(() => props.value, newVal => {
            metas.value = newVal.length > 0 ? newVal : [{
                key: '',
                value: '',
            },]
        })

        watch(() => metas.value, newVal => {
            //  console.log(newVal)
            emit('update:value', newVal)
            emit('update:input', newVal)
        }, {
            deep: true
        })
        return {
            metas,
            addNew() {
                metas.value.push({
                    key: '',
                    value: '',
                })
            },
            remove(index) {
                metas.value.splice(index, 1)
            }
        }
    }
}
</script>