<?php

namespace Modules\Production\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\API\BaseController;
use App\Repositories\ModelRepository;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Good\Entities\Attribute;
use Modules\Production\Entities\Instruction;
use Modules\Production\Entities\ProductionOrder;
use Modules\Production\Entities\ProductionOrderItemChecklist;
use Modules\Production\Entities\Station;

class ProductionChecklistController extends BaseController
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index(Station $station)
    {
        $query = $station->instructionItemChecklists()
            ->whereHas('productionOrderItem', fn ($query) => $query->whereHas('productionOrder', fn ($query) => $query->where('status', ProductionOrder::PRODUCTION)))
            ->where('status', ProductionOrderItemChecklist::PROCESSING)->with([
                'instructionItem',
                'productionOrderItem.productionOrder.party',
                'productionOrderItem.good',
                //'instructionItem.parents',
            ]);
        $attribute_columns = $station->attributes()->pluck('id')->toArray();
        // return Instruction::find(11)->items()->with(['childs.conditions', 'conditions'])->whereDoesntHave('parents')->get()->toArray();
        return JsonResource::collection(ModelRepository::handleFilter($query)
            ->get()
            ->groupBy(function ($q) {
                return $q->productionOrderItem->production_order_id;
            })->map(function ($m) use ($attribute_columns) {
                //$productionOrder = $m[0]->productionOrderItem->productionOrder;
                // $productionOrder->items = $productionOrder->items()->with('good')->get();

                return [
                    'order' =>  [
                        'code' => $m[0]->productionOrderItem->productionOrder['code'],
                        'customer_name' => $m[0]->productionOrderItem->productionOrder['customer_name'],
                        'party_name' => $m[0]->productionOrderItem->productionOrder['party_name'],
                        'delivery_date' => $m[0]->productionOrderItem->productionOrder['delivery_date'],
                    ],
                    'items' => $m->groupBy(fn ($mm) => $mm['instructionItem'] && $mm['instructionItem']['stationWork'] ? $mm['instructionItem']['stationWork']['id'] : '')->values()->map(fn ($mm) => [

                        'attribute_columns' => $mm[0]['instructionItem']['stationWork'] ? $mm[0]['instructionItem']['stationWork']['attributes']->pluck('id') : $attribute_columns,
                        'station_work_name' => $mm[0]['instructionItem']['stationWork'] ? $mm[0]['instructionItem']['stationWork']['name'] : $mm[0]['instructionItem']['name'],
                        'production_order_items' => $mm->map(fn ($mmm) => [
                            'id' => $mmm['id'],
                            'good_name' => $mmm['productionOrderItem']['good']['name'],
                            'attributes' => $mmm['productionOrderItem']['attributes'],
                            'description' => $mmm['productionOrderItem']['description'],
                        ])

                    ]),
                    // 'productionOrder' => $productionOrder,

                ];
            })->values()
            ->sortBy(function ($m) {
                return $m['order'] ? $m['order']['delivery_date'] : null;
            })->values())->additional([
            'station' => $station,
        ]);
    }

    public function new_index(Station $station)
    {
        $query = $station->instructionItemChecklists()
            ->whereHas('productionOrderItem', fn ($query) => $query->whereHas('productionOrder', fn ($query) => $query->whereHas('party')->where('status', ProductionOrder::PRODUCTION))
                ->whereHas('good'))
            ->where('status', ProductionOrderItemChecklist::PROCESSING)->with([
                'instructionItem',
                'productionOrderItem.productionOrder.party',
                'productionOrderItem.good',
                //'instructionItem.parents',
            ]);
        $attribute_columns = $station->attributes()->whereNotNull('key')->pluck('key')->toArray();
        // return Instruction::find(11)->items()->with(['childs.conditions', 'conditions'])->whereDoesntHave('parents')->get()->toArray();
        $attributes = Attribute::query()->with(['items', 'parent.items'])->get()->map(function ($m) {
            if ($m['items'] && $m->parent?->items) {
                $m['items'] = $m['items']->merge($m->parent?->items);
            }
            return array_merge($m->toArray(), [
                "items" => $m['items']->toArray(),
            ]);
        })->toArray();
        return JsonResource::collection(
            ModelRepository::handleFilter($query)
                ->get()
                ->groupBy(function ($q) {
                    return $q->instructionItem->station_work_id;
                })
                ->map(function ($m) use ($attribute_columns, $attributes) {
                    //$productionOrder = $m[0]->productionOrderItem->productionOrder;
                    // $productionOrder->items = $productionOrder->items()->with('good')->get();

                    return [

                        'station_work_name' => $m[0]['instructionItem']['stationWork'] ? $m[0]['instructionItem']['stationWork']['name'] : $m[0]['instructionItem']['name'],
                        'station_work' => $m[0]['instructionItem']['stationWork'],
                        'attribute_columns' => $m[0]['instructionItem']['stationWork'] ? $m[0]['instructionItem']['stationWork']['attributes']->pluck('key') : $attribute_columns,
                        'items' => $m
                            //->groupBy(fn ($mm) => $mm['instructionItem'] && $mm['instructionItem']['stationWork'] ? $mm['instructionItem']['stationWork']['id'] : '')
                            //->values()
                            ->map(function ($mm) use ($attributes) {
                                $temp = $mm['productionOrderItem']->getLabelAttributes($attributes);
                                $description = "";
                                if (isset($mm['productionOrderItem']['productionOrder']['description']))
                                $description .= "<b>توضیحات کلی: </b>"."<p>".$mm['productionOrderItem']['productionOrder']['description']."</p>";
                                if ($mm['productionOrderItem']['description'])
                                $description .= "<b>توضیحات کالا: </b>"."<p>".$mm['productionOrderItem']['description']."</p>";
                                return [
                                    'id' => $mm['id'],
                                    'production_order_code' => $mm->productionOrderItem->productionOrder['code'],
                                    'customer_name' => $mm->productionOrderItem->productionOrder['customer_name'],
                                    'party_name' => $mm->productionOrderItem->productionOrder['party_name'],
                                    'delivery_date' => $mm->productionOrderItem->productionOrder['delivery_date'],
                                    'production_order_item_id' => $mm['productionOrderItem']['id'],// . ($mm['productionOrderItem']['code'] ? '.' . $mm['productionOrderItem']['code'] : ''),
                                    'good' => $mm['productionOrderItem']['good'],
                                    'good_id' => $mm['productionOrderItem']['good_id'],
                                    'group_id' => $mm['productionOrderItem']['good']['group_id'] ?? null,
                                    'good_name' => $mm['productionOrderItem']['good']['name'] ?? '',
                                    // 'attributes' => $mm['productionOrderItem']['attributes'],
                                    'attributes' => $mm['productionOrderItem']['attributes'],
                                    'attributes_label' => collect($temp)->pluck('label', 'attribute_key'),
                                    'attributes_label_column' => $temp,
                                    'description' => $description,


                                ];
                            })->sortBy('delivery_date')->values(),
                        // 'productionOrder' => $productionOrder,

                    ];
                })->values()
            // ->sortBy(function ($m) {
            //     return $m['order'] ? $m['order']['delivery_date'] : null;
            // })->values()
        )->additional([
            'station' => $station,
        ]);
    }
    public static function getLabelAttributes()
    {
    }
    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, Station $station)
    {
        $data = $request->input('data');
        $ids = collect($data)->pluck('id');
        $items = ProductionOrderItemChecklist::whereIn('id', $ids)->with(['instructionItem.treeChilds.treeParents.conditions', 'instructionItem.treeChilds.conditions', 'productionOrderItem'])->get();

        foreach ($items as $item) {

            $statuses = ProductionOrderItemChecklist::where('production_order_item_id', $item->production_order_item_id)
                ->where('code', $item->code)
                ->pluck('status', 'instruction_item_id')->toArray();
            $status = collect($data)->where('id', $item['id'])->pluck('status')->first();
            $item['status'] = $status;
            $res = '';
            if ($status ==  ProductionOrderItemChecklist::DONE) {
                // dd($item->instructionItem->toArray());
                $res = $this->proccessInstructionChilds($item->instructionItem, $item, $item->productionOrderItem->attributes, $statuses, $item->instructionItem);
                //dd(4);
                // foreach ($item->instructionItem->childs as $child) {
                //     $this->instruction_child($child, $item, $statuses);
                // }
            }
            // dd($item['status']);

            $item->save();
        }
        return response()->json($res);
        //dd(1);
    }

    public function new_update(Request $request)
    {
        $data = $request->input('data');
        $res = [];
        collect($data)->map(function ($m, $n) use (&$res) {

            foreach (explode(',', $n) as $k)
                $res[$k] = $m;
            return $res;
        });
        $data = $res;
        $ids = collect($data)->keys();
        $items = ProductionOrderItemChecklist::whereIn('id', $ids)->with(['instructionItem.treeChilds.treeParents.conditions', 'instructionItem.treeChilds.conditions', 'productionOrderItem'])->get();
        $res = '';
        foreach ($items as $item) {
            if ($data[$item['id']] ==  ProductionOrderItemChecklist::REPAIRING)
                continue;
            $statuses = ProductionOrderItemChecklist::where('production_order_item_id', $item->production_order_item_id)
                ->where('code', $item->code)
                ->pluck('status', 'instruction_item_id')->toArray();
            $item['status'] = $data[$item['id']];

            if ($data[$item['id']] ==  ProductionOrderItemChecklist::DONE) {
                $res = $this->proccessInstructionChilds($item->instructionItem, $item, $item->productionOrderItem->attributes, $statuses, $item->instructionItem);
            }
            $item->save();
        }
        //dd(3);
        return response()->json($res);
        //dd(1);
    }

    public function proccessInstructionChilds($instructionItem, $item, $attributes, $statuses, $currentItem)
    {
        //dd($instructionItem->treeChilds->toArray());
        $debug = ['name' => $instructionItem->stationWork->name, 'id' => $instructionItem->id];
        //echo '-------------' . ($instructionItem->stationWork->name) . '-----------<br>';
        // echo $instructionItem->stationWork->name . '<br>';
        foreach ($instructionItem->treeChilds as $k => $child) {
            //echo $child->stationWork->name . (self::checkCondition($child, $attributes)? '*':'').$child->id . '<br>';
            $check_have = self::checkCondition($child, $attributes);
            $check_parents = null;
            $check_childs = null;
            if ($check_have) {

                //if($statuses[])
                //var_dump($this->checkConditionParents($child->parents->whereNotIn('id', [$instructionItem->id])->values(),  $attributes, $statuses));
                //echo '============' . $child->stationWork->name . $child->id . '============<br>';
                $check_parents = $this->checkConditionParents($child,  $attributes, $statuses, $currentItem);
                if ($check_parents['ok']) {
                    //echo $child['id'].'@@@@@@@@@@@@@<br>';
                    //dd('$');
                    ProductionOrderItemChecklist::updateOrCreate([
                        'production_order_item_id' => $item->production_order_item_id,
                        'instruction_item_id' => $child['id'],
                        'code' => $item->code
                    ], [
                        'status' => ProductionOrderItemChecklist::PROCESSING,

                    ]);
                }
                //echo '======================<br>';

                // dd($child->stationWork->name);

            } else {
                // var_dump('b', $child->id);
                // echo $child->station_label . '<br>';
                //dd($child->id, $attributes);
                $check_childs = $this->ProccessInstructionChilds($child,  $item, $attributes, $statuses, $currentItem);
            }
            $debug['childs'][] = [
                'name' => $child->stationWork->name,
                'id' => $child->id,
                'have' => $check_have,
                'have_condition' => [$child, $attributes],
                'check_parents' => $check_parents,
                'check_childs' => $check_childs,
            ];
        }
        return $debug;
    }
    public function checkConditionParents($child,  $attributes, $statuses, $currentItem)
    {
        $debug = [];
        $code = 281;
        // if ($child->id == $code)
        //     dd($child->treeParents, $child->toArray());
        //echo '---------------' . $child->stationWork->name . $child->id . '-----------------<br>';
        $ok = true;
        foreach ($child->treeParents as $parent) {
            $ok2 = true;

            if ($currentItem->id == $parent->id)
                continue;
            $check_have = self::checkCondition($parent, $attributes);
            $check_done = false;

            $check_parents = $this->checkConditionParents($parent,  $attributes, $statuses, $currentItem);
            //echo $currentItem->stationWork->name. ' - '. $parent->stationWork->name ."<br>";
            if ($check_have) {
                // if (!(isset($statuses[$parent->id]) && $statuses[$parent->id] == ProductionOrderItemChecklist::DONE)) 
                // dd($statuses,$parent->stationWork->name,!isset($statuses[$parent->id]) || $statuses[$parent->id]!== ProductionOrderItemChecklist::DONE,!(isset($statuses[$parent->id]) && $statuses[$parent->id] == ProductionOrderItemChecklist::DONE));
                $check_done = isset($statuses[$parent->id]) && $statuses[$parent->id] == ProductionOrderItemChecklist::DONE;
                if (!$check_done) {
                    $ok2 = false;
                    $ok = false;
                }
            } elseif (!$check_parents['ok']) {
                $ok2 = false;
                $ok = false;
            }

            $debug[] = [
                'name' => $parent->stationWork->name,
                'id' => $parent->id,
                'ok' => $ok2,
                'check_have' => $check_have,
                'check_parents' => $check_parents,
            ];

            //if ($child->id == $code && !$ok2)  echo ($ok2 ? 'ok ' : 'no') . $parent->stationWork->name . (self::checkCondition($parent, $attributes) ? '*' : '') . (!(isset($statuses[$parent->id]) && $statuses[$parent->id] == ProductionOrderItemChecklist::DONE) ? '#' : '') . (!$this->checkConditionParents($parent,  $attributes, $statuses, $currentItem) ? '@' : '') . '<br>';
        }

        // if (count($child->treeParents) == 0)
        //     $ok = false;
        //if ($child->id == $code) echo ($ok ? '$<br>' : '');
        return ['name' => $child->stationWork->name, 'id' => $child->id, 'ok' => $ok, 'parents' => $debug];
    }
    public static function checkCondition($child, $attributes)
    { 
        $condition_done = true;
        $res = [];
        foreach ($child->conditions as $condition) {
            $has_in_array = false;
            if (isset($attributes->{$condition->attribute_key}) && in_array($attributes->{$condition->attribute_key}, $condition->items))
                $has_in_array = true;

            // if (
            //     isset($attributes->{$condition->attribute_key})
            //     && (
            //         ($condition->condition && !in_array($attributes->{$condition->attribute_key}, $condition->items))
            //         || (!$condition->condition && in_array($attributes->{$condition->attribute_key}, $condition->items))
            //     )
            // ){

            // } else if (!isset($attributes->{$condition->attribute_key}))


            // if (
            //     !isset($attributes->{$condition->attribute_key})
            //     || ($condition->condition && !in_array($attributes->{$condition->attribute_key}, $condition->items))
            //     || (!$condition->condition && in_array($attributes->{$condition->attribute_key}, $condition->items))
            // ) {
            //     $condition_done = false;
            // }
            // if (!($condition->condition xor $has_in_array)) {
            //     $condition_done = false;
            // }
            // $res[] = ['condition' => $condition->condition, 'has_in_array' => $has_in_array];


            if (!($condition->attribute->type == 'SELECT' || $condition->attribute->type == 'SELECT_IMAGE')) {
                if (isset($attributes->{$condition->attribute_key}) && in_array($attributes->{$condition->attribute_key}, $condition->items)) {
                    $has_in_array = true;
                }
                if ($condition->condition xor $has_in_array) {
                    $condition_done = false;
                }
            } elseif (
                !isset($attributes->{$condition->attribute_key})
                || ($condition->condition && !in_array($attributes->{$condition->attribute_key}, $condition->items))
                || (!$condition->condition && in_array($attributes->{$condition->attribute_key}, $condition->items))
            ) {
                $condition_done = false;
            }
        }
        return $condition_done;
    }

    // public static function checkConditionParents($parent, $attributes, $statuses)
    // {

    //     if (self::checkCondition($parent,  $attributes)) {
    //         if (isset($statuses[$parent->id]) && $statuses[$parent->id] == ProductionOrderItemChecklist::DONE)
    //             return true;
    //         return false;
    //     }

    //     if ($parent->parents) {
    //         $not = true;

    //         foreach ($parent->parents as $parent2) {
    //             // dd($statuses, $parent2->id, self::checkConditionParents($parent2, $attributes, $statuses));

    //             if (!self::checkConditionParents($parent2, $attributes, $statuses))
    //                 $not = false;
    //         }
    //         if ($not)
    //             return true;
    //     }

    //     return false;
    // }
    // public static function instruction_child($child, $item, $statuses, $a = 0)
    // {

    //     if (self::checkCondition($child, $item->productionOrderItem->attributes)) {
    //         $parent_done = true;

    //         //dd($child);
    //         $res = [];

    //         foreach ($child->parents as $k => $parent) {
    //             if ($parent->id == $item->instructionItem->id)
    //                 continue;

    //             //if ($k == 1)
    //             //   dd($parent->id, self::checkConditionParents($parent, $item->productionOrderItem->attributes, $statuses));

    //             $res[] = [
    //                 'parent' => $parent->toArray(),
    //                 'instructionItem' => $item->instructionItem->id,
    //                 'statuses' => $statuses,
    //                 'condition' => self::checkConditionParents($parent, $item->productionOrderItem->attributes, $statuses),
    //                 'condition2' => self::checkCondition($parent,  $item->productionOrderItem->attributes),
    //             ];
    //             if (
    //                 !self::checkConditionParents($parent, $item->productionOrderItem->attributes, $statuses)
    //             ) {
    //                 $parent_done = false;
    //                 // dd($child->id,$child->parents->pluck('id'),$parent->id,self::checkConditionParents($parent, $item->productionOrderItem->attributes, $statuses));
    //             }
    //         }
    //         if ($a == 1 && $child->id == 214) dd(3, $child->id, $res);

    //         if ($parent_done) {
    //             // dd($res);
    //             ProductionOrderItemChecklist::updateOrCreate([
    //                 'production_order_item_id' => $item->production_order_item_id,
    //                 'instruction_item_id' => $child['id'],
    //                 'code' => $item->code
    //             ], [
    //                 'status' => ProductionOrderItemChecklist::PROCESSING,

    //             ]);
    //         }
    //     } else {
    //         //var_dump($child);
    //         //dd($child);
    //         $child->childs = $child->childs()->with(['parents.conditions', 'conditions'])->get();
    //         // if ($child->id == 9)
    //         //     dd($child->toArray(), $child->childs->toArray());
    //         //dd($child->childs->toArray());
    //         foreach ($child->childs as $chld) {

    //             self::instruction_child($chld, $item, $statuses, 1);
    //         }
    //     }
    // }
}
