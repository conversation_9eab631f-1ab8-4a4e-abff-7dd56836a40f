
@font-face {
    font-family: DXIcons;
    src: url(dx-font-icon/dxicons.woff2) format('woff2'),url(dx-font-icon/dxicons.woff) format('woff'),url(dx-font-icon/dxicons.ttf) format('truetype');
    font-weight: 400;
    font-style: normal
}
.dx-icon {
    font-family: DXIcons;
    font-style: normal;
    font-size: 1.715em;
}
.dx-icon-filter-operation-equals:before {
    content: "\f044"
}
.dx-icon-filter-operation-default:before {
    content: "\f027"
}
.dx-icon-filter-operation-not-equals:before {
    content: "\f045"
}
.dx-icon-filter-operation-less:before {
    content: "\f046"
}
.dx-icon-filter-operation-less-equal:before {
    content: "\f048"
}
.dx-icon-filter-operation-greater:before {
    content: "\f047"
}
.dx-icon-filter-operation-greater-equal:before {
    content: "\f049"
}
.dx-icon-filter-operation-contains:before {
    content: "\f063"
}
.dx-icon-filter-operation-not-contains:before {
    content: "\f066"
}
.dx-icon-filter-operation-starts-with:before {
    content: "\f064"
}
.dx-icon-filter-operation-ends-with:before {
    content: "\f065"
}
.dx-icon-filter-operation-between:before {
    content: "\f06a"
}