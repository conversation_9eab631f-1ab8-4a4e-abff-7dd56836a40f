export default (item) => {
    // door cnc
    if ([11, 12].includes(item.good.id) && !item.attributes.hasFrame && !item.attributes.hasRokoob)
        return [
            item.good.id == 11 ? 'modelDoorCNC' : 'ModelDoorRapingi',
            'typeMaterialDoor',
            item.good.id == 11 ? 'sheetCNCThickness' : null,
            'cover',
            'customModelDoor',
            'doorHeight',
            'doorWidth',
            'alignDoor',
            'hasEdgeOfDoor',
            'hasRokoob',
            'typeMaterialRokoob',
            'countBranchParvaz',
            'countBranchRokoob',
            'place',
        ].filter(f => f)


    // door cnc and has frame
    else if ([11, 12].includes(item.good.id) && item.attributes.hasFrame)
        return [
            item.good.id == 11 ? 'modelDoorCNC' : 'ModelDoorRapingi',
            'typeMaterialDoor',
            item.good.id == 11 ? 'sheetCNCThickness' : null,
            'cover',
            'customModelDoor',
            'doorHeight',
            'doorWidth',
            'alignDoor',
            'hasEdgeOfDoor',
            'hasRabet',
            'widthRabet',
            //'hasFrame',
            'typeMaterialFrame',
            'frameHeight',
            'frameWidth',
            'hasThreshold',
            'tinknessFrame',
            'place',
        ].filter(f => f)


    // door cnc and has rokoob
    else if ([11, 12].includes(item.good.id) && item.attributes.hasRokoob)
        return [
            item.good.id == 11 ? 'modelDoorCNC' : 'ModelDoorRapingi',
            'typeMaterialDoor',
            item.good.id == 11 ? 'sheetCNCThickness' : null,
            'cover',
            'customModelDoor',
            'doorHeight',
            'doorWidth',
            'alignDoor',
            'hasEdgeOfDoor',
            //'hasRokoob',
            'typeMaterialRokoob',
            'countBranchParvaz',
            'countBranchRokoob',
            'place',
        ].filter(f => f)


    // frame
    else if (item.good.id == 13)
        return [
            'cover',
            'typeMaterialFrame',
            'frameHeight',
            'frameWidth',
            'hasThreshold',
            'hasRabet',
            'widthRabet',
            'place',
        ]


    // rokoob
    else if (item.good.id == 14)
        return [
            'cover',
            'typeMaterialRokoob',
            'countBranchParvaz',
            'countBranchRokoob',
            'place',
        ]
    else return []

}
