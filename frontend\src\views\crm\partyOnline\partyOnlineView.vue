<template>
  <div class="q-gutter-sm">
    <j-table-data url="/memberships" :columns="columns">
      <template #dialog="{ load, formOption, form }">
        <!-- <table-form :form="form" class="p-5" @afterSubmit="load" :formOption="formOption" /> -->
        <membership-request :form="form" :formOption="formOption" />
      </template>
    </j-table-data>
  </div>
</template>

<script>
import TableForm from "./form.vue";
import membershipRequest from '@/views/crm/partyOnline/membershipRequest.vue'
export default {
  components: { membershipRequest },

  setup() {

    const columns = [
      {
        name: 'full_name',
        required: true,
        label: 'نام و نام خانوادگی',
        field: 'full_name',
        filter: 'FilterInput',
      },
      {
        name: 'second_name',
        required: true,
        label: 'نام شرکت / فروشگاه',
        field: 'second_name',
        sortable: true,
        filter: 'FilterInput',
      },
      {
        name: 'display_name',
        required: true,
        label: 'نام نمایشی',
        field: 'display_name',
        sortable: true,
        filter: 'FilterInput',

      },
      {
        name: 'mobile_number',
        required: true,
        label: 'شماره موبایل',
        field: 'mobile_number',
        sortable: true,
        filter: 'FilterInput',

      },
      {
        name: 'status',
        required: true,
        label: 'وضعیت',
        field: 'label_status',
        filter: 'FilterSelect',
        filterOption: 'statuses'
      },
    ]

    return {
      columns,
    };
  },
};
</script>
