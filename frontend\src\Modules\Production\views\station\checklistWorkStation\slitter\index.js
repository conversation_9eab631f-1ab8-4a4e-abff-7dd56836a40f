import { cmFormat } from "@/Modules/Production/computeProductionFormula/index";

export function varagVast(items) {
    let res = {};
    let type = "";
    let type_front = "";
    let type_back = "";


    // درفک
    type = "pvcSlitterDorfak";
    type_front = 195;
    type_back = 195;

    if (items.filter(f => f.attributes.typeFrontPVC == type_front || f.attributes.typeBackPVC == type_back).length > 0) {
        const sizes = items.filter(f => f.attributes.typeFrontPVC == type_front || f.attributes.typeBackPVC == type_back).map(comPvcDorfak);
        res[type] = {label: 'درفک', items: pvcSizes(sizes)};
        // [
        //     {
        //         height: cmFormat(sizes.reduce((s, m) => s + m.height, 0)),
        //         width: cmFormat(125),
        //         id: pvcSizes(sizes),
        //     },
        // ];
    }

    // سیلیکون
    type = "pvcSlitterSilicon";
    type_front = 194;
    type_back = 198;
    if (items.filter(f => f.attributes.typeFrontPVC == type_front || f.attributes.typeBackPVC == type_back).length > 0) {
        const sizes = items.filter(f => f.attributes.typeFrontPVC == type_front || f.attributes.typeBackPVC == type_back).map(comPvcSilicon);
        res[type] = {label: 'سیلیکون', items: pvcSizes(sizes)};
    }

    // راپود
    type = "pvcSlitterRapood";
    type_front = 196;
    type_back = 199;
    if (items.filter(f => f.attributes.typeFrontPVC == type_front || f.attributes.typeBackPVC == type_back).length > 0) {
        const sizes = items.filter(f => f.attributes.typeFrontPVC == type_front || f.attributes.typeBackPVC == type_back).map(comPvcRapood);
        res[type] = {label: 'راپود', items: pvcSizes(sizes)};
    }
    return res;
}

function pvcSizes(sizes) {
    let res = []
    for (
        let index = Math.max(...sizes.map((m) => m.width));
        index >= Math.min(...sizes.map((m) => m.width));
        index -= 5
    ) {
        const filter = sizes.filter(
            (f) => index >= f.width && index - 5 < f.width
        );
        //console.log(filter)
        if (filter.length > 0) {
            const length = filter
                .reduce((s, c) => s + c.height, 0);
            const temp = filter.group("production_order_code");
            res.push({
                height: cmFormat(length),
                width: cmFormat(Math.max(...filter.map((m) => m.width))),
                items: Object.keys(temp).map((m) => ({
                    id: temp[m].map(m => m.id),
                    production_order_code: temp[m][0].production_order_code,
                    production_order_item_id: temp[m][0].production_order_item_id,
                    party_name: temp[m][0].party_name,
                    customer_name: temp[m][0].customer_name,
                    width: cmFormat(Math.max(...temp[m].map((m) => m.width))),
                    height: cmFormat(temp
                    [m].reduce((s, x) => s + x.height, 0)),
                })),
            });
        }

    }
    return res;
}

function comPvcRapood(f) {
    let gablabeh_width = 1.5;
    if (f.attributes.hasEdgeOfDoor) {
        gablabeh_width = 3;
    }

    const door_width =
        f.attributes.doorWidth /
        (() => {
            switch (f.attributes.doorLengeh * 1) {
                case 1: // یک لنگه
                case 1.5: // 1.5 لنگه
                    return 1;
                case 2: // 2لنگه
                    return 2;
                case 3: // 3 لنگه
                    return 3;
                case 4: // 4 لنگه
                    return 4;
            }
        })();



    let width =
        (1 * (f.attributes.doorHeight ?? 0) +
            (f.attributes.hasEdgeOfDoor ? 2 : 1.5) -
            (f.attributes.isTopPasar13 ? 16.5 + 13.5 : 16.5 * 2) +
            2) *
        (f.count ?? 1);

    let height =
        1 * door_width +
        gablabeh_width -
        2 * (f.attributes_label.countBaghalBazoo13 > 0 ? 13.5 : 16.5) +
        2;

    // 1.5 لنگه
    if (f.attributes.doorLengeh == 1.5)
        height =
            (1 * door_width + gablabeh_width - 44 + 2) * (f.count ?? 1);

    switch (f.attributes.doorLengeh * 1) {
        case 2: // 2لنگه
            width *= 2;
            break;
        case 3: // 3 لنگه
            width *= 3;
            break;
        case 4: // 4 لنگه
            width *= 4;
            break;
    }

    return {
        ...f,
        width,
        height,
    };
};

function comPvcSilicon(f) {
    let gablabeh_width = 1.5;
    if (f.attributes.hasEdgeOfDoor) {
        gablabeh_width = 3;
    }
    const door_width =
        f.attributes.doorWidth /
        (() => {
            switch (f.attributes.doorLengeh * 1) {
                case 1: // یک لنگه
                case 1.5: // 1.5 لنگه
                    return 1;
                case 2: // 2لنگه
                    return 2;
                case 3: // 3 لنگه
                    return 3;
                case 4: // 4 لنگه
                    return 4;
            }
        })();
    let height = 0
    let width = 0
    if (f.typeDoor == 'CNC') {
        height =
            (1 * (f.attributes.doorHeight ?? 0) + 12) * (f.count ?? 1);
        width = 1 * door_width + 9;
    } else {
        height =
            (1 * (f.attributes.doorHeight ?? 0) +
                (f.attributes.hasEdgeOfDoor ? 2 : 1.5) -
                (f.attributes.isTopPasar13 ? 16.5 + 13.5 : 16.5 * 2) +
                2) *
            (f.count ?? 1);

        width =
            1 * door_width +
            gablabeh_width -
            2 * (f.attributes_label.countBaghalBazoo13 > 0 ? 13.5 : 16.5) +
            2;
    }




    // 1.5 لنگه
    if (f.attributes.doorLengeh == 1.5)
        width = 1 * door_width + gablabeh_width - 44 + 2;

    width += (() => {
        switch (f.attributes.doorLengeh * 1) {
            case 1: // یک لنگه
                return 0;
            case 1.5: // 1.5 لنگه
            case 2: // 2لنگه
                return 1;
            case 3: // 3 لنگه
                return 2;
            case 4: // 4 لنگه
                return 3;
        }
    })() * (f.attributes.hasEdgeOfDoor ? 2 : 1)

    switch (f.attributes.doorLengeh * 1) {
        case 2: // 2لنگه
            height *= 2;
            break;
        case 3: // 3 لنگه
            height *= 3;
            break;
        case 4: // 4 لنگه
            height *= 4;
            break;
    }

    return {
        ...f,
        width,
        height,
    };
};

function comPvcDorfak(m) {
    return {
        ...m,
        id: m.id,
        width: 125,
        height:
            250 *
            (m.count ?? 1) *
            (() => {
                switch (m.attributes.doorLengeh * 1) {
                    case 1: // یک لنگه
                    case 1.5: // 1.5 لنگه
                        return 1;
                    case 2: // 2لنگه
                        return 2;
                    case 3: // 3 لنگه
                        return 3;
                    case 4: // 4 لنگه
                        return 4;
                }
            })(),
    };

}