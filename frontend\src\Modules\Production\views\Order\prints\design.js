import { cmFormat, getField, getFieldByKey } from ".";

export default function (data, attributes, template) {

    const goods = Object.values(
        data.items
            .filter(
                (f) =>
                    (template.station_id == 15 &&
                        [8, 18, 24].includes(f.good.group_id)) ||
                    (template.station_id == 24 &&
                        [16, 20].includes(f.good.group_id))
            )
            .map((m) => ({
                ...m,
                key: [
                    m.good_id,
                     (m.attributes.centerLayerThickness ?? m.attributes.sheetCNCThickness),
                     m.attributes.template,
                     m.attributes.pvcColor,
                     (m.attributes.hasBarjestegi ? true: false),
                    ].join('_'),

                width: m.attributes.doorWidth * 1,
                height: m.attributes.doorHeight * 1,
                label: m.attributes_label.typeMaterialDoor,
            }))
            .group("key")
    ).map((good) => {
        const type = good
            .map((m) => ({ ...m, key: m.attributes.typeMaterialDoor }))
            .group("key");
        if (type['mdf'])
            type['mdf'] = Object.values(
                type['mdf']
                    .map((m) => ({
                        ...m,
                        key: [
                            m.attributes.doorHeight,
                            m.attributes.doorWidth,
                            m.attributes.hasEdgeOfDoor,
                            m.attributes.alignEdgeOfDoor,
                            m.attributes.doorAlign,
                            m.description,
                        ].join('_')
                    }))
                    .group("key")
            ).map((m) => ({
                ...m[0],
                count: m.reduce((a, b) => a + b.count, 0),
            }));

        if (type['fomizeh'])
            type['fomizeh'] = Object.values(
                type['fomizeh']
                    .map((m) => ({
                        ...m,
                        key: [
                            m.attributes.doorHeight,
                            m.attributes.doorWidth,
                            m.attributes.hasEdgeOfDoor,
                            m.attributes.alignEdgeOfDoor,
                            m.attributes.doorAlign,
                            m.description,
                        ].join('_')
                    }))
                    .group("key")
            ).map((m) => ({
                ...m[0],
                count: m.reduce((a, b) => a + b.count, 0),
            }));

        return { good: good[0], mdf: type['mdf'], fomizeh: type['fomizeh'] };
    });

    const columns = [
        {
            label: "پشت درپشت",
            field: (row) => {
                return `<strong>${row?.attributes_label?.poshtdarposhtSize?.height}</strong><span style="margin: 5px;color: red;">x</span><strong>${row?.attributes_label?.poshtdarposhtSize?.width}</strong>`;

                let width = row.attributes.doorWidth * 1;
                let height = row.attributes.doorHeight * 1;

                if (row.attributes.hasEdgeOfDoor) {
                    switch (row.attributes.alignEdgeOfDoor) {
                        case "threeSide":
                            width += 3;
                            height += 2;
                            break;
                        case "fourSide":
                            width += 3;
                            height += 3;
                            break;
                        case "onlyTopSide":
                            width += 1.5;
                            height += 2;
                            break;
                        case "onlyLolaSide":
                            width += 2;
                            height += 1.5;
                            break;
                        case "onlyLockSide":
                            width += 2;
                            height += 1.5;
                            break;
                        case "onlyTopAndLolaSide":
                            width += 2;
                            height += 2;
                            break;
                        case "onlyTopAndLockSide":
                            width += 2;
                            height += 2;
                            break;
                        case "onlyLockAndLolaSide":
                            width += 3;
                            height += 1.5;
                            break;
                    }

                    if (row.attributes.doorLengeh * 1) {
                        switch (row.attributes.doorLengeh * 1) {
                            case 1.5:
                            case 2:
                                width += 2;
                                break;
                            case 3:
                                width += 4;
                                break;
                            case 4:
                                width += 6;
                                break;
                        }
                    }
                } else {
                    let per = 1.5;
                    if ([16, 20].includes(row.good.group_id)) {
                        height += 1;
                        per = 1;
                    } else height += 1.5;

                    if (row.attributes.doorLengeh * 1) {
                        switch (row.attributes.doorLengeh * 1) {
                            case 1:
                                width += per;
                                break;
                            case 1.5:
                            case 2:
                                width += 2 * per;
                                break;
                            case 3:
                                width += 3 * per;
                                break;
                            case 4:
                                width += 4 * per;
                                break;
                        }
                    }
                }

                return `<strong>${height}</strong><span style="margin: 5px;color: red;">x</span><strong>${width}</strong>`;
            },
            width: "100px",
        },

        {
            label: "توضیحات",
            field: (row) => {
                let res = row.description ?? "";
                const lengeh =
                    row.attributes.doorLengeh == 1
                        ? false
                        : row.attributes_label.doorLengeh;
                if (lengeh) res += lengeh;
                return res;
            },
        },
        {
            label: "جهت",
            field: (row) => {
                const align = row.attributes_label.doorAlign;
                return align ?? "";
            },
            width: "40px",
        },
        {
            label: "تعداد",
            field: (row) => {
                return row.count;
            },
            width: "40px",
        },
        {
            label: "قابلبه",
            field: (row) => {
                const gablabe = row.attributes_label.alignEdgeOfDoor;
                return row.attributes.hasEdgeOfDoor
                    ? row.attributes.alignEdgeOfDoor !== 'threeSide'
                        ? gablabe
                        : "دارد"
                    : "";
            },
            width: "70px",
        },
        {
            label: "اندازه",
            field: (row) => {
                return `<strong>${row.attributes.doorHeight}</strong><span style="margin: 5px;color: red;">x</span><strong>${row.attributes.doorWidth}</strong>`;
            },
            width: "100px",
        },
    ];


    const rows_data = goods
        .map((good) => {
            let res = `
        <table class="j-table text-center odd-highlight">
            <tr>
                <th width="100px">مدل</th>
                <th width="100px">برجستگی</th>
                <th width="50px">ضخامت</th>
                <th width="200px">رنگ</th>
            </tr>
            <tr>
                <td>${good.good.good.name}</td>
                <td>${good.good.attributes_label.hasBarjestegi
                ?? ""
                }</td>
                <td>${good.good.attributes_label.centerLayerThickness ??
                good.good.attributes_label.sheetCNCThickness
                }</td>
                <td>${good.good.attributes_label.pvcColor}</td>
            </tr>
        </table>`;

            if (good.fomizeh) {
                res += `<table class="j-table w-full text-center odd-highlight" style="margin-top:10px">
                    <tr>
                    ${columns
                        .map((column) => {
                            return `<th>${column.label}</th>`;
                        })
                        .join("")}
                    <th rowspan="50" width="130px" style="font-size:14px">${good.fomizeh[0].good.group.name +
                    "<br>" +
                    "<span>" +
                    good.fomizeh[0].label +
                    "</span>"
                    }</th>

                    </tr>
                    
                    ${good.fomizeh
                        .sortAsc("width", "height")
                        .map((row) => {
                            return (
                                `<tr>` +
                                columns
                                    .map((column) => {
                                        return `<td${column.width
                                            ? ` width="${column.width}"`
                                            : ""
                                            }>${column.field(row)}</td>`;
                                    })
                                    .join("") +
                                `</tr>`
                            );
                        })
                        .join("")}
                    
                </table>`;
            }
            if (good.mdf) {
                res += `<table class="j-table w-full text-center odd-highlight" style="margin-top:10px">
                    <tr>
                    ${columns
                        .map((column) => {
                            return `<th>${column.label}</th>`;
                        })
                        .join("")}

                            <th rowspan="50" width="130px" style="font-size:14px">${good.mdf[0].good.group.name +
                    "<br>" +
                    "<span>" +
                    good.mdf[0].label +
                    "</span>"
                    }</th>
                    </tr>
                    
                    ${good.mdf
                        .sortAsc("width", "height")
                        .map((row) => {
                            return (
                                `<tr>` +
                                columns
                                    .map((column) => {
                                        return `<td${column.width
                                            ? ` width="${column.width}"`
                                            : ""
                                            }>${column.field(row)}</td>`;
                                    })
                                    .join("") +
                                `</tr>`
                            );
                        })
                        .join("")}
                    
                </table>`;
            }
            res += `<table class="j-table w-full text-center odd-highlight" style="margin-top:10px">
                    <tr>
                        <th style="width:1cm">قید</th>
                        <th style="width:1cm">پایین</th>
                        <th style="width:1cm">بازو</th>
                        <th style="width:1cm">بالا</th>

                        <th></th>
                        <th style="width:1cm">پهنا</th>
                        <th style="width:1cm">عمق</th>
                        <th style="width:2cm">ابزار</th>
                    </tr>
                    ${`<tr style="height:7mm;">
                        <td></dt>
                        <td></dt>
                        <td></dt>
                        <td></dt>
                        <td></td>
                        <td></dt>
                        <td></td>
                        <td></td>
                    </tr>`.repeat(6)}
                    
                </table>`;

            return `<tr>
            <td colspan="10" class="no-border" style="padding-bottom:10px">${res}</td>
        </tr>`;
        })
        .join("");


    return `
    
    <table class="j-table w-full text-center">
        
       
        <thead>
                    <tr>
                        <td class="no-border p-0">
                            <table class="j-table w-full text-center odd-highlight">
                                <tr class="h-8">

                                    <td class="no-border w-1/3 bg-white">
                                        <div class="text-left text-sm">
                                            <b>نام نمایندگی: </b> ${data.party_name
        }
                                        </div>
                                        <div class="text-left text-sm">
                                            <b>نام مشتری: </b> ${data.customer_name
        }
                                        </div>
                                    </td>
                                    <td class="no-border w-1/3 bg-white">
                                    <img src="/images/logo-factor.png" class="h-10 m-auto" />
                                    <h6 style="text-align: center;font-weight: bolder;">برگه تولید ${template.station.name
        }</h6>
                                    </td>
                                    <td class="no-border w-1/3 bg-white text-right">
                                        <table class="ml-auto mr-0 text-sm">
                                            <tr>
                                                <th class="no-border bg-white">شناسه سفارش:</th>
                                                <td class="no-border bg-white">${data.code
        }</td>

                                            </tr>
                                            <tr>
                                                <th class="no-border bg-white">تاریخ سفارش:</th>

                                                <td class="no-border bg-white">${data.submit_date
        }</td>
                                            </tr>
                                            <tr>
                                                <th class="no-border bg-white">تاریخ سفارش:</th>

                                                <td class="no-border bg-white">${data.delivery_date
        }</td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>


                            </table>
                        </td>
                    </tr>
                   

                </thead>

        <tbody>
        ${data.description
            ? `
        <tr>
            <th colspan="10">توضیحات</th>
            </tr>
        <tr>
            <td colspan="10">${data.description}</td>
        </tr>`
            : ""
        }
        <tr>
            <td colspan="10" class="no-border">
            ${rows_data}
            </td>
        </tr>
        
        
        </tbody>
    </table>
`;
}