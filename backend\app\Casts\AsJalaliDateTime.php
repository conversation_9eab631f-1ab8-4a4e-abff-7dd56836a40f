<?php

namespace App\Casts;

use DateTimeZone;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;

class AsJalaliDateTime implements CastsAttributes
{
    /**
     * Cast the given value.
     *
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @param  string  $key
     * @param  mixed  $value
     * @param  array  $attributes
     * @return mixed
     */
    public function get($model, string $key, $value, array $attributes)
    {
        return !$value ? null : Verta::instance($value)->timezone('Asia/Tehran')->format('Y/m/d H:i:s');
    }

    /**
     * Prepare the given value for storage.
     *
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @param  string  $key
     * @param  mixed  $value
     * @param  array  $attributes
     * @return mixed
     */
    public function set($model, string $key, $value, array $attributes)
    {
        if (in_array($key, ['created_at', 'updated_at', 'delete_at']))
            return $value;
        return Verta::parse($value)->timezone('Asia/Tehran')->formatGregorian('Y-m-d H:i:s');
    }
}
