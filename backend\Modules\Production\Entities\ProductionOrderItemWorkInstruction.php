<?php

namespace Modules\Production\Entities;

interface ProductionOrderItemChecklistStatus
{
    const PROCESSING = 'PROCESSING';
    const DONE = 'DONE';
    const REPAIRING = 'REPAIRING';
}
class ProductionOrderItemWorkInstruction extends BModel implements ProductionOrderItemChecklistStatus
{
    protected $fillable = [
        'production_order_item_id',
        'work_instruction_id',
        'status',
        'code',
    ];

    const type = [
        [
            'value' => self::PROCESSING,
            'label' => 'در حال انجام',
        ], [
            'value' => self::DONE,
            'label' => 'انجام شده',
        ], [
            'value' => self::REPAIRING,
            'label' => 'در حال تعمیر',
        ],
    ];

    public function workInstruction()
    {
        return $this->belongsTo(WorkInstruction::class);
    }
    public function productionOrderItem()
    {
        return $this->belongsTo(ProductionOrderItem::class);
    }
}
