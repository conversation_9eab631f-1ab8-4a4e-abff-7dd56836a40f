<template>
    <div class="space-y-4">
        <!-- <j-table :columns="columns" :rows="data" separator="cell" row-key="key" hide-pagination dense
    :rows-per-page-options="[0]">
    <template #top-left>
        لیست سفارش
    </template>
    <template #top-right>
        <j-btn flat dense icon="add" color="primary" @click="onAdd()" @shortkey="onAdd()"
            v-shortkey="['insert']" />
    </template>
    <template v-if="data && data.length > 0" #bottom-row>
        <q-tr>
            <q-td class="text-bold">
                جمع
            </q-td>
            <q-td />

            <q-td class="text-center">
                {{ data.reduce((a, b) => a * 1 + b.count, 0) }}
            </q-td>
            <q-td colspan="50" />
        </q-tr>
    </template>
    <template #body-cell-58="props">
        <q-td :props="props">
            <j-icon v-if="props.value" name="image" size="md" color="primary" @click="showImage(props.value)" />
        </q-td>
    </template>
</j-table> -->


        <!-- <j-dialog v-model="add" full-width>
    <q-card class="p-5">

        <select-good ref="ref_select_good" @confirm="addToTable" />
    </q-card>
</j-dialog> -->
        <select-good ref="ref_select_good" @confirm="addToTable" />

        <div class="flex q-table__card mt-5">
            <div class="flex flex-col border-r-gray-200 border-r-2 w-10">
                <!-- <j-btn flat dense icon="add" color="primary" @click="onAdd()" @shortkey="onAdd()"
            v-shortkey="['insert']" /> -->
                <template v-if="selected && selected.length > 0">
                    <j-btn flat dense icon="edit" color="green" @click="onEdit()" />
                    <j-btn flat dense icon="file_copy" color="primary" @click="onCopy()" />
                    <j-btn flat dense icon="delete" color="red" @click="onDelete()" />
                </template>
            </div>
            <j-table v-model:selected="selected" flat :columns="columns" :rows="data" separator="cell" row-key="key"
                selection="single" :rows-per-page-options="[0]" dense class="flex-auto w-12">
                <template #body-cell-good="props">
                    <q-td :props="props">
                        <image-by-text
                            v-bind="{ src: props.row.good.image_src ?? props.row.attributes.template, text: props.row.good.name }" />
                        <!-- <div class="flex gap-2 items-center">
                    <j-image-viewer v-if="props.row.good.image_src" :src="props.row.good.image_src" />
                    {{ props.value }}
                </div> -->
                    </q-td>
                </template>
                <!-- <template #body-cell-count="props">
                    <q-td :props="props">
                        {{ props.row.count }}
                        <q-popup-edit v-model="props.row.count" @update:model-value="updateData" buttons v-slot="scope">
                            <j-input v-model="scope.value" dense autofocus @keyup.enter="scope.set" step="0.1"
                                type="number" min="1" hide-bottom-space />
                        </q-popup-edit>
                    </q-td>
                </template> -->
                <template #body-cell-description="props">
                    <q-td :props="props">
                        {{ props.row.description }}
                        <q-popup-edit v-model="props.row.description" @update:model-value="updateData" buttons
                            v-slot="scope">
                            <q-input v-model="scope.value" dense autofocus @keyup.enter="scope.set" hide-bottom-space />
                        </q-popup-edit>
                    </q-td>
                </template>
                <!-- <template #body-cell-58="props">
                    <q-td :props="props">
                        <j-icon v-if="props.value" name="image" size="md" color="primary"
                            @click="showImage(props.value)" />
                    </q-td>
                </template> -->
                <template v-if="data && data.length > 0" #bottom-row>
                    <q-tr>
                        <q-td class="text-bold text-center" colspan="4">
                            جمع
                        </q-td>
                        <!-- <q-td />
                        <q-td /> -->
                        <q-td class="text-center">
                            {{ data.reduce((a, b) => a + b.count * 1, 0) }}
                        </q-td>
                        <q-td colspan="50" />
                    </q-tr>
                </template>
            </j-table>
        </div>
        <!-- </j-dialog-bar> -->
        <!-- <j-dialog v-model="isShowImage" maximized>
            <q-card>
                <q-card-section class="row items-center q-pb-none">
                    <div class="text-h6">نمایش طرح</div>
                    <q-space />
                    <q-btn icon="close" flat round dense v-close-popup />
                </q-card-section>

                <q-card-section>
                    <img :src="urlImage" class="rounded-md h-screen m-auto" />
                </q-card-section>
            </q-card>
        </j-dialog> -->
    </div>
</template>
<script>
import { computed, ref, watch } from 'vue';
import { api } from '@/boot/axios';
import SelectGood from './selectGoodNew.vue';
import { useQuasar } from "quasar";
import ImageByText from '../components/ImageByText.vue';

export default {
    props: {
        id: Number,
        data: {
            type: Array,
            default: () => []
        },
        onSubmit: {
            type: Function,
        }
    },
    setup(props, { emit }) {
        const selected = ref([])
        const ref_select_good = ref(null)
        const add = ref(false)
        const $q = useQuasar();
        const attributes = ref([])



        const columns = computed(() => {
            const attribute_columns = data.value.map(m => Object.keys(m.attributes)).flat().unique();
            return [...[
                {
                    name: "index",
                    label: 'ردیف',
                    field: row => row.key + 1,
                    align: 'center'
                },
                {
                    name: "id",
                    label: 'شناسه',
                    field: row => row.id ?? '-',
                    align: 'center'
                },
                {
                    name: "good",
                    label: "کالا/خدمات",
                    field: row => row.good.name ?? '',
                    align: 'center'
                },
                {
                    name: "count",
                    label: "تعداد",
                    field: "count",
                    align: 'center'
                }
            ],
            ...attributes.value.filter(f => attribute_columns.includes(f.key + '') && f.showing).sort(function (a, b) {
                if (a.sort < b.sort) return -1;
                if (a.sort > b.sort) return 1;
                return 0;
            }).map(m => {


                return {
                    name: m.key,
                    label: m.name,
                    vertical: true,
                    hasImage: m.type == 'SELECT_IMAGE',
                    image: row => {
                        if (m.type == 'SELECT_IMAGE') {
                            const find = attributes.value.findIndex(f => m.id)
                            if (find >= 0) return attributes.value[find].items[attributes.value[find].items.findIndex(f => f.id == row.attributes[m.key])]?.data?.image;
                        }
                        return ''

                    },
                    align: 'center',
                    field: row => {
                        switch (m.type) {
                            case 'SELECT':
                            case 'SELECT_IMAGE':
                                const find = m.items.findIndex(ff => ff.key + '' == row.attributes[m.key])
                                return find >= 0 ? m.items[find].name : '';
                            case 'SWITCH':
                                return row.attributes[m.key] !== undefined ? (row.attributes[m.key] ? 'دارد' : '') : ''
                            case 'FILE':
                                return row.attributes[m.key]
                            case 'NUMBER':
                            case 'INPUT':
                                return row.attributes[m.key] ?? ''
                        }
                    },
                }
            }),
            {
                name: "description",
                label: "توضیحات",
                field: "description",
            }
            ];

        })
        const data = ref(props.data.map((m, i) => ({ ...m, key: i })) ?? [])
        watch(() => props.data, (newVal) => {
            data.value = newVal.map((m, i) => ({ ...m, key: i }))
        })

        api.get('good/attribute').then(res => {
            attributes.value = res.data
        })

        // const conditions = ref([])
        // api.get('good/condition').then(res => {
        //     conditions.value = res.result
        // })
        const updateData = () => {
            emit('update:data', data.value)
            props.onSubmit()
        }

        const addToTable = (value, is_edit) => {
            // const find = data.value.findIndex(f => f.good_id == value.good_id && JSON.stringify(f.attributes) == JSON.stringify(value.attributes))
            // if (find >= 0)
            //     Object.assign(data.value[find], value)
            // else
            //     data.value.push(value)
            if (is_edit) {
                const find = data.value.findIndex(f => f.key == selected.value[0].key);
                if (find >= 0) {
                    Object.assign(data.value[find], value)
                }
            } else
                data.value.push({ ...value, key: data.value.length + 1 })

            emit('update:data', data.value)
            props.onSubmit()
            selected.value = []
            add.value = false;
        }

        const onDelete = () => {
            $q.dialog({
                title: "مطمئن هستید؟",
                cancel: true,
                persistent: true,
            }).onOk(() => {
                const value = selected.value[0];
                //const find = data.value.findIndex(f => f.good_id == value.good_id && JSON.stringify(f.attributes) == JSON.stringify(value.attributes))
                const find = data.value.findIndex(f => f.key == value.key)

                if (find >= 0)
                    data.value.splice(find, 1)
                selected.value = []

                emit('update:data', data.value)
                props.onSubmit()
            });
        }

        const onEdit = () => {
            add.value = true;
            ref_select_good.value.onEdit(selected.value[0])
        }
        const onCopy = () => {
            ref_select_good.value.onCopy(selected.value[0])
        }
        const onAdd = () => {
            add.value = true
        }
        // const attribute_columns = computed(() => {
        //     return data.value.map(m => Object.keys(m.attribute)).flat().unique()
        // })

        const isShowImage = ref(false)
        const urlImage = ref('')
        return {
            columns,
            data,
            add,
            attributes,
            // conditions,
            // attribute_columns,
            addToTable,
            onEdit,
            onAdd,
            onDelete,
            onCopy,
            updateData,
            selected,
            ref_select_good,
            isShowImage,
            urlImage,
            showImage(value) {
                urlImage.value = value
                isShowImage.value = true
            },
        };
    },
    components: { SelectGood, ImageByText }
}
</script>,
ImageByText