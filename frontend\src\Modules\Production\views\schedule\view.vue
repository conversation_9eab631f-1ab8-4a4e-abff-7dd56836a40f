<template>
  <div class="q-gutter-sm">
    <list-order-production-item />
    
    <!-- <j-table-data url="/production/schedule" :columns="columns">
      <template #dialog="props">
        <table-form v-bind="props" v-model:form="props.form" />
      </template>
    </j-table-data> -->
    <list-production-schedule />

  </div>
</template>

<script>
import { ref } from "vue";
import TableForm from "./form.vue";
import ListOrderProductionItem from "./listOrderProductionItem2.vue";
import listProductionSchedule from "./listProductionSchedule.vue";

export default {
  components: {
    TableForm,
    ListOrderProductionItem,
    listProductionSchedule
  },
  setup() {

    const columns = [
      {
        name: 'full_name',
        required: true,
        label: 'نام طرف حساب',
        field: 'full_name',
        sortable: true,
      },
      {
        name: 'display_name',
        required: true,
        label: 'نام نمایشی',
        field: 'display_name',
        sortable: true,
      },
      {
        name: 'mobile_number',
        required: true,
        label: 'شماره تماس',
        field: 'mobile_number',
        sortable: true,
      },
    ]
    const date = ref('')
    return {
      columns,
      date
    };
  },

};
</script>
