<template>
    <j-btn v-if="data.length > 0" icon="cancel" dense flat :size="size" class="mr-1" color="red"
        @click="data = []; save()" />

    <slot />
    <j-btn v-if="(typeof filterOption != 'string')" icon="rule" dense flat :size="size"
        :title="filterOption.filter(f => data && data.length > 0 && data.includes(f.value)).map(m => m.label)" class="ml-1">
        <q-menu anchor="bottom end" self="top end" fit @hide="hide" :class="{ 'custom-scrollbar': size == 'lg' }">
            <!-- <q-item clickable dense>
                <q-item-section>New tab</q-item-section>
            </q-item> -->
            <j-input v-model="searchValue" hide-bottom-space class="m-2" dense />
            <q-option-group v-model="data"
                :options="filterOption.filter(f => (f.label + '').includes(searchValue))" type="checkbox"
                dense class="p-3 bg-white text-lg gap-3 grid grid-cols-1 flex-col" :size="size" />
        </q-menu>
        <!-- <q-popup-proxy ref="popup" cover transition-show="scale" transition-hide="scale" @hide="hide">
            <q-option-group v-model="data" :options="filterOption" type="checkbox" dense class="p-3 bg-white" />
        </q-popup-proxy> -->
    </j-btn>
</template>
<script>
import { ref, watch } from 'vue'

export default {
    props: {
        value: String,
        label: String,
        size: {
            type: String,
            default: 'md'
        },
        filterOption: {
            type: Array,
            default: () => []
        },
    },
    setup(props, context) {
        //const popup = ref(null);
        const data = ref(props.value ?? []);
        const searchValue = ref('')

        const hide = () => {
            // popup.value.hide();
            save();
            searchValue.value = ''
        }
        const save = () => {
            // console.log(options.value.filter(f => {
            //     console.log(33333333333)
            //     return data.value.includes(f.value);
            // }).map(m => m.label))
            context.emit("input", data.value);
            context.emit("update:input", data.value);
            context.emit("update:value", data.value);
        }
        return {
            data,
            //popup,
            searchValue,
            save,
            hide,
        };
    },
}
</script>