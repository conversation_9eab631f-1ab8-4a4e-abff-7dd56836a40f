<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="256" height="256" viewBox="0 0 256 256" xml:space="preserve">

<defs>
</defs>
<g style="stroke: none; stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;" transform="translate(1.4065934065934016 1.4065934065934016) scale(2.81 2.81)" >
	<path d="M 44.764 19.384 c 0 0.033 0 0.065 0 0.098 v 6.496 c 0 10.208 -8.275 18.483 -18.483 18.483 h 0 c -10.208 0 -18.483 -8.275 -18.483 -18.483 v -6.496 c 0 -0.517 0.021 -1.029 0.063 -1.535 C 23.866 12.153 37.334 10.656 44.764 19.384 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(249,203,180); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 52.563 79.98 V 63.816 c 0 -10.689 -8.665 -19.354 -19.354 -19.354 H 19.354 C 8.665 44.461 0 53.126 0 63.816 v 0 C 0 72.743 7.237 79.98 16.165 79.98 H 52.563 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(252,95,97); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 24.645 69.183 h -6.361 c -3.544 0 -6.427 -2.883 -6.427 -6.427 c 0 -0.553 0.448 -1 1 -1 s 1 0.447 1 1 c 0 2.441 1.986 4.427 4.427 4.427 h 6.361 c 0.552 0 1 0.447 1 1 S 25.197 69.183 24.645 69.183 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(239,77,85); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 12.857 63.756 c -0.552 0 -1 -0.447 -1 -1 c 0 -1.672 0.539 -3.254 1.557 -4.575 c 0.336 -0.438 0.965 -0.521 1.402 -0.182 c 0.438 0.337 0.519 0.965 0.182 1.402 c -0.747 0.969 -1.141 2.129 -1.141 3.354 C 13.857 63.309 13.409 63.756 12.857 63.756 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(239,77,85); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 7.876 17.948 c 2.891 1.024 6.171 1.611 9.657 1.611 c 5.974 0 11.35 -1.709 15.165 -4.436 c 3.139 2.244 7.344 3.781 12.061 4.261 C 44.706 9.222 36.456 0.999 26.282 0.999 h 0 C 16.592 0.999 8.658 8.46 7.876 17.948 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(147,93,56); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 85.785 44.461 H 39.501 c -2.89 0 -5.435 1.901 -6.255 4.672 L 24.111 79.98 h 56.797 l 8.916 -30.111 C 90.623 47.17 88.6 44.461 85.785 44.461 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(237,235,237); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<ellipse cx="58.952" cy="61.786" rx="5.692" ry="4.066" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(206,200,198); fill-rule: nonzero; opacity: 1;" transform=" matrix(0.2883 -0.9576 0.9576 0.2883 -17.2073 100.4251) "/>
	<path d="M 86.366 88.001 H 3.634 C 1.627 88.001 0 86.374 0 84.367 l 0 -1.775 c 0 -2.007 1.627 -3.634 3.634 -3.634 h 82.732 c 2.007 0 3.634 1.627 3.634 3.634 v 1.775 C 90 86.374 88.373 88.001 86.366 88.001 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(206,200,198); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
</g>
</svg>