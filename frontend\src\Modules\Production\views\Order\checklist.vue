<template>
    <q-dialog>
        <q-card class="w-full max-w-96">
            <div class="flex flex-col">
                <!-- <q-table class="bordered" :columns="[
                {
                    label: 'وضعیت',
                    name: 'label',
                },
                {
                    label: 'زمان',
                    name: 'updated_at',
                },
                {
                    label: 'کاربر',
                    name: 'username',
                }
                ]" :rows="data.statuses" /> -->
                <!-- <table class="border-collapse border">
                    <tr>
                        <th class="border border-slate-600 text-center padding">وضعیت</th>
                        <th class="border border-slate-600 text-center padding">زمان</th>
                        <th class="border border-slate-600 text-center padding">کاربر</th>
                    </tr>
                    <tr v-for="status, index in data.statuses" :key="index"
                        :class="status.is_done ? 'bg-primary' : (status.updated_at ? 'bg-secondary' : '')">
                        <td class="border border-slate-600 text-center padding">{{ status.label }}</td>
                        <td class="border border-slate-600 text-center padding">{{ status.updated_at }}</td>
                        <td class="border border-slate-600 text-center padding">{{ status.username }}</td>
                    </tr>
                </table> -->
                <q-markup-table dense class="grid">
                    <thead class="bg-primary text-white">
                        <tr>
                            <th class="w-1/3 font-bold">وضعیت</th>
                            <th class="w-1/3 font-bold">زمان</th>
                            <th class="w-1/3 font-bold">کاربر</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="status, index in data.statuses" :key="index"
                            :class="{ 'bg-slate-200 dark:bg-slate-500': status.is_done === false, 'bg-secondary': status.is_done, 'text-white': status.is_done }">
                            <td class="w-1/3 text-center">
                                <j-btn v-if="clickable(status)" dense size="sm" :outline="!$q.dark.isActive" color="secondary"
                                    @click="changeStatus(data.id, status.value)"
                                    :icon="!clickable(status) ? '' : (data.previous_status?.value == status.value ? 'arrow_upward' : (data.next_status?.value == status.value ? 'arrow_downward' : ''))">{{
                                        status.label }}</j-btn>
                                <span class="text-xs" v-else>{{ status.label }}</span>
                            </td>
                            <td class="w-1/3 text-center"><span class="text-xs">{{ status.updated_at }}</span></td>
                            <td class="w-1/3 text-center"><span class="text-xs">{{ status.username }}</span></td>

                        </tr>
                    </tbody>
                </q-markup-table>
                <!-- <q-chip color="blue-grey" text-color="white" style="margin: 0;">
                    <div class="text-left w-1/3 font-bold">وضعیت</div>
                    <div class="text-center w-1/3 font-bold">زمان</div>
                    <div class="text-right w-1/3 font-bold">کاربر</div>
                </q-chip>
                <template v-for="status, index in data.statuses" :key="index">
                    <q-chip square
                        :icon="!clickable(status) ? '' : (data.previous_status?.value == status.value ? 'arrow_upward' : (data.next_status?.value == status.value ? 'arrow_downward' : ''))"
                        @click="changeStatus(data.id, status.value)" :color="status.is_done ? 'primary' : ''"
                        :clickable="clickable(status)" :outline="!status.is_done && !status.updated_at"
                        :text-color="(status.is_done) ? 'white' : ''">
                        <div class="text-left w-1/3 text-xs">{{ status.label }}</div>
                        <div class="text-center w-1/3 text-xs">{{ status.updated_at }}</div>
                        <div class="text-right w-1/3 text-xs">{{ status.username }}</div>
                    </q-chip>
                </template> -->

            </div>

            <!-- <q-separator />

            <div class="gap-2 p-2 grid grid-cols-2">

                <j-btn v-if="data && data.previous_status
                    && (!['FINISH_JOB', 'PRODUCTION'].includes(data.status) || checkRole('admin'))
                    && (data.status != 'FINANCIAL_APPROVAL' || (data.status == 'FINANCIAL_APPROVAL' && checkPermission('production_order_financial_approval')))
                    && (data.status != 'DELIVERED' || (data.status == 'DELIVERED' && checkPermission('production_order_sent')))

                " color="red" :label="'برگشت به ' + data.previous_status.label"
                    @click="changeStatus(data.id, data.previous_status.value)" class="col-start-1" />
                <j-btn v-if="data && data.next_status
                    && (((data.next_status.value != 'CONFIRM_CUSTOMER' || (data.next_status.value == 'CONFIRM_CUSTOMER' && !data.is_created_by_customer))
                        && (data.next_status.value != 'CONFIRM' || (data.next_status.value == 'CONFIRM' && checkPermission('production_order_management_approval')))
                        && (data.next_status.value != 'FINANCIAL_APPROVAL' || (data.next_status.value == 'FINANCIAL_APPROVAL' && checkPermission('production_order_financial_approval')))
                        && (data.next_status.value != 'DELIVERED' || (data.next_status.value == 'DELIVERED' && checkPermission('production_order_sent')))
                        && (data.next_status.value != 'PRODUCTION' || (data.next_status.value == 'PRODUCTION' && checkPermission('production_order_production')))
                        && (data.next_status.value != 'FINISH_JOB' || (data.next_status.value == 'FINISH_JOB' && checkPermission('production_order_finish_job')))
                    ) || checkPermission('production_order_confirm_cutomer'))" color="secondary"
                    :label="data.next_status.label" @click="changeStatus(data.id, data.next_status.value)"
                    class="col-end-3" />
            </div> -->
        </q-card>
    </q-dialog>
</template>
<script>
import { ref } from 'vue';
import { api } from '@/boot/axios';
import { checkRole, checkPermission } from '@/helpers'
import { useQuasar } from 'quasar';

export default {
    props: {
        selected: Object,
        callback: Function,
    },
    setup(props) {
        //console.error('check list')
        const data = ref({})
        const $q = useQuasar()
        const getData = () => {
            //console.error('get Data', props)
            if (props.selected && props.selected.id) {
                api.get(`/production/production_order/${props.selected.id}/checklists`).then(res => {
                    data.value = res.result
                })
            }
        }
        const changeStatus = (id, status) => {
            $q.dialog({
                message: 'عملیات انجام شود؟',
                // ok:{
                //     label: 'بله',
                //     flat:true,
                // },
                cancel: true,
                persistent: true
            }).onOk(() => {
                api.post(`production/production_order/${id}/changeStatus`, {
                    status
                }).then(res => {
                    data.value = res.result
                    //if (props.callback) props.callback()
                });
            })


        }
        const dialogLevel = ref(false)
        getData()
        return {
            data,
            changeStatus,
            checkRole,
            checkPermission,
            dialogLevel,
            clickable(status) {
                return (checkPermission('production_order.change-status.' + status.value) && data.value.next_status?.value == status.value)
                    || (checkRole('admin') && [data.value.previous_status?.value, data.value.next_status?.value].includes(status.value))
            }

        }
    },
}
</script>