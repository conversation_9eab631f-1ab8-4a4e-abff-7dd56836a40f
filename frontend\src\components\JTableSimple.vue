<template>
    {{ list }}
    <div class="flex" :class="!$q.screen.xs ? 'q-table__card' : 'border-b-2'">
        <div class="w-12 p-2 border-r-gray-200 border-r-2">
            <div class="sticky top-14 flex gap-2">
                <j-btn flat dense icon="add" color="blue" @click="onAdd()" />
                <template v-if="selected.length > 0">
                    <j-btn flat dense icon="edit" color="green" @click="onEdit()" />
                    <j-btn flat dense icon="delete" color="red" @click="onDelete()" />
                </template>
            </div>
        </div>
    

        <j-table v-model:selected="selected" :rows="list" :columns="columns" selection="single" :row-key="rowKey"
            separator="cell" v-model:filters="filters" v-model:formOption="formOption" @onFilter="onFilter" class="flex-auto w-12" flat>

            <template v-for="_, slot in $slots" v-slot:[slot]="props" :key="slot">
                <slot :name="slot" v-bind="props" :key="slot" />
            </template>

        </j-table>
    </div>
    <j-dialog-bar v-model="add">
        <template #bar>
            <j-btn label="ذخیره" color="primary" @click="onSubmit" />
        </template>
        <q-form ref="ref_form" @submit="submitForm" class="space-y-2">
            <slot name="dialog" v-bind="{ formOption, form }" />
        </q-form>
    </j-dialog-bar>
</template>
<script>
import { tableApi } from '@/helpers';
import { ref } from 'vue';

export default {
    props: {
        columns: {
            type: Array,
            default: () => []
        },
        rowKey:{
            type: String,
            default: 'id'
        },
    },
    setup(props, context) {
        const add = ref(false);
        const ref_form = ref(null);

        const {
            list,
            columns,
            selected,
            onDelete,
            formOption,
            getAll,
            onFilter,
            filters,
            submitForm,
            form,
        } = tableApi(props.url, props, context)

        columns.value = props.columns;


        const onAdd = () => {
            add.value = true;
            form.value = {}
        }
        const onEdit = async () => {
            form.value = Object.assign({}, selected.value[0]);
            add.value = true;
            selected.value = [];
        }
        const onSubmit = () => {
            ref_form.value.submit()
        }

        return {
            add,
            onAdd,
            onEdit,
            list,
            onDelete,
            selected,
            getAll,
            onFilter,
            formOption,
            form,
            filters,
            form,
            submitForm,
            onSubmit,
            ref_form,
        }
    },
}
</script>