<template>
    <q-list bordered separator dense>
        <template v-for="attribute, i in attributes" :key="i">
            <q-expansion-item group="somegroup" icon="explore" label="First" default-opened header-class="text-primary"
                dense>
                <template #header>
                    <q-item-section avatar>
                        <q-checkbox v-model="selected" :label="attribute.name" :val="attribute.id" />
                    </q-item-section>
                    <q-space />
                    <j-select v-if="attribute.type == 'SELECT'" v-model="default_attribute[attribute.key]" dense
                        :options="attribute.items" option-label="name" option-value="key" search-local hide-bottom-space
                        :label="attribute.name" clearable />
                    <j-select-image v-if="attribute.type == 'SELECT_IMAGE'" v-model="default_attribute[attribute.key]" dense
                        :options="attribute.items" option-label="name" option-value="key" search-local hide-bottom-space
                        :label="attribute.name" clearable />
                    <j-input v-else-if="attribute.type == 'INPUT'" v-model="default_attribute[attribute.key]" dense
                        hide-bottom-space :label="attribute.name" clearable />
                    <j-input v-else-if="attribute.type == 'NUMBER'" v-model="default_attribute[attribute.key]" dense
                        hide-bottom-space :label="attribute.name" type="number" step="0.01" min="0" clearable />
                    <j-toggle v-else-if="attribute.type == 'SWITCH'" v-model="default_attribute[attribute.key]" dense
                        hide-bottom-space :label="attribute.name" clearable />
                </template>
                <template v-if="selected.findIndex(f => f == attribute.id) >= 0">
                    <q-card>
                        <q-card-section>

                            <j-toggle label="اجباری" v-model="options[attribute.id].required" />
                            <j-toggle label="قابل نمایش" v-model="options[attribute.id].showing" />
                            <j-input label="ترتیب" v-model="options[attribute.id].sort" dense />
                        </q-card-section>
                        <template v-if="attribute.items.length">
                            <q-separator />
                            <q-card-section>
                                <div class="grid md:grid-cols-4">
                                    <q-checkbox v-for="item, j in attribute.items" v-model="options[attribute.id].options"
                                        :label="item.name" :val="item.id" :key="j" />
                                </div>
                            </q-card-section>
                        </template>
                    </q-card>
                </template>

            </q-expansion-item>
        </template>
    </q-list>
    <!-- <q-list bordered separator dense>

        <template v-for="attribute, i in attributes" :key="i">


            <q-item>
                <q-item-section>
                    <div class="content-center flex">
                        <q-checkbox v-model="selected" :label="attribute.name" :val="attribute.id" />
                        <q-space />
                        <j-select v-if="attribute.type == 'SELECT'" v-model="default_attribute[attribute.key]" dense
                            :options="attribute.items" option-label="name" option-value="id" search-local
                            hide-bottom-space :label="attribute.name" clearable />
                        <j-input v-else-if="attribute.type == 'INPUT'" v-model="default_attribute[attribute.key]" dense
                            hide-bottom-space :label="attribute.name" clearable />
                        <j-input v-else-if="attribute.type == 'NUMBER'" v-model="default_attribute[attribute.key]" dense
                            hide-bottom-space :label="attribute.name" type="number" step="0.01" min="0" clearable />
                        <j-toggle v-else-if="attribute.type == 'SWITCH'" v-model="default_attribute[attribute.key]" dense
                            hide-bottom-space :label="attribute.name" clearable />
                    </div>
                    <template v-if="selected.findIndex(f => f == attribute.id) >= 0 && attribute.items.length">
                        <q-separator />

                        <div class="grid md:grid-cols-4">
                            <q-checkbox v-for="item, j in attribute.items" v-model="options[attribute.id].options"
                                :label="item.name" :val="item.id" :key="j" />
                        </div>
                    </template>
                </q-item-section>
            </q-item>
        </template>
    </q-list> -->
</template>
<script>
import { ref, watch } from 'vue'
export default {
    props: {
        value: {
            type: Object,
            default: () => { }
        },
        default_attribute: {
            type: Object,
            default: () => { }
        },
        attributes: {
            type: Array,
            default: () => []
        },
    },
    setup(props, { emit }) {
        const selected = ref([])
        const default_attribute = ref(props.default_attribute ?? {})
        const options = ref(props.value ?? {})
        watch(() => props.value, (newVal) => {
            options.value = newVal;
            selected.value = Object.keys(newVal).map(m => m * 1);
        })

        watch(() => props.default_attribute, newVal => {
            default_attribute.value = newVal;
        })

        watch(() => default_attribute.value, newVal => {
            emit('update:default_attribute', newVal)
        }, { deep: true })

        watch(() => options.value, (value) => {
            emit('update:value', value)
        }, { deep: true })
        watch(() => selected.value, () => {
            selected.value.forEach(k => {
                if (!options.value[k] || !options.value[k].options)
                    options.value[k] = { options: [] }
            })
            Object.keys(options.value).forEach(k => {
                if (selected.value.findIndex(f => f == k) < 0)
                    delete options.value[k]
                else {
                    options.value[k].options = options.value[k].options.filter(f => props.attributes.find(f => f.id == k)?.items.map(m => m.id).includes(f))
                }
            })
        })
        return {
            selected,
            default_attribute,
            options,
        }
    },
}
</script>