<template>
  <div id="q-app">
    <q-layout view="lHh Lpr fff">
      <q-page-container>
        <q-page :style="pageStyle" class="row justify-center items-center  q-pa-lg">
          <div class="column shadow-4 bg-white/90 rounded-xl overflow-auto">


            <q-card class="flex md:flex-row flex-col">
              <div class="image-container flex-1 flex items-center justify-center p-8 bg-blue-50">
                <img src="@/assets/logo.svg" class="w-3/4 h-auto">
              </div>
              <div class="form-container flex-1 p-6">
                <h1 class="text-2xl font-bold text-center text-primary">پنل نمایندگان</h1>
                <q-card-section>
                  <j-form class="q-px-sm" @submit="submitForm">
                    <j-input clearable v-model="mobile_number"
                      lazy-rules unmasked-value place-holder="09" mask="09##-###-####" label="شماره موبایل" required :square="false">
                      <template v-slot:prepend>
                        <q-icon name="person" />
                      </template>
                    </j-input>
                    <j-input clearable v-model="password" type="password"
                      lazy-rules label="کلمه عبور" required :square="false">
                      <template v-slot:prepend>
                        <q-icon name="lock" />
                      </template>
                    </j-input>
                    <!-- <j-input v-for="(field, index) in inputFields" :key="index" clearable v-model="field.value"
                      :type="field.type" lazy-rules :placeholder="field.placeholder ?? ''" unmasked-value fill-mask="#" :mask="field.mask ?? ''" :label="field.label" required :square="false">
                      <template v-slot:prepend>
                        <q-icon :name="field.icon" />
                      </template>
                    </j-input> -->

                    <j-btn unelevated :loading="loading" size="md" color="blue" icon="login" type="submit"
                      class="full-width text-white mt-4" label="ورود" />
                  </j-form>

                </q-card-section>
              </div>
            </q-card>
          </div>
        </q-page>
      </q-page-container>
    </q-layout>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useAuthStore } from "@/stores";
import bg from '@/assets/background-login.svg';

const loading = ref(false);
const inputFields = ref([
  { label: "شماره موبایل", type: "text", icon: "person", value: ref(""), placeholder: '09', mask: '###########' },
  { label: "کلمه عبور", type: "password", icon: "lock", value: ref("") }
]);
const mobile_number = ref("")
const password = ref("")
const pageStyle = computed(() => ({
  background: `url(${bg}) center/cover no-repeat`
}));

const submitForm = async () => {
  loading.value = true;
  const authStore = useAuthStore();
  await authStore.login({
    mobile_number: "09" + mobile_number.value,
    password: password.value
  });
  loading.value = false;
};  
</script>