<?php

namespace App\Http\Controllers;

use Modules\Good\Entities\Attribute;
use Modules\Good\Entities\Good;

class ChangeAttributeController extends Controller
{
    public function index()
    {

        Attribute::query()->whereNotNull('key')->with(['items' => function ($q) {
            $q->whereNotNull('key');
        }])->get()->map(function ($attribute) {
            $k = $attribute['id'];
            $m = $attribute['key'];
            \DB::update("UPDATE `goo__goods` SET `default_attribute` = REPLACE(`default_attribute`, '\"" . $k . "\":', '\"" . $m . "\":') WHERE `default_attribute` LIKE '%\"" . $k . "\":%'");
            \DB::update("UPDATE `goo__groups` SET `default_attribute` = REPLACE(`default_attribute`, '\"" . $k . "\":', '\"" . $m . "\":') WHERE `default_attribute` LIKE '%\"" . $k . "\":%'");
            \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '\"" . $k . "\":', '\"" . $m . "\":') WHERE `attributes` LIKE '%\"" . $k . "\":%'");

            $attribute->items->map(function ($attribute) use ($m) {
                $attribute_key = $m;
                $k = $attribute['id'];
                $m = $attribute['key'];
                \DB::update("UPDATE `goo__goods` SET `default_attribute` = REPLACE(`default_attribute`, '\"" . $attribute_key . "\":\"" . $k . "\",', '\"" . $attribute_key . "\":\"" . $m . "\",') WHERE `default_attribute` LIKE '%\"" . $attribute_key . "\":" . $k . ",%'");
                \DB::update("UPDATE `goo__goods` SET `default_attribute` = REPLACE(`default_attribute`, '\"" . $attribute_key . "\":\"" . $k . "\"}', '\"" . $attribute_key . "\":\"" . $m . "\"}') WHERE `default_attribute` LIKE '%\"" . $attribute_key . "\":" . $k . "}%'");

                \DB::update("UPDATE `goo__groups` SET `default_attribute` = REPLACE(`default_attribute`, '\"" . $attribute_key . "\":\"" . $k . "\",', '\"" . $attribute_key . "\":\"" . $m . "\",') WHERE `default_attribute` LIKE '%\"" . $attribute_key . "\":" . $k . ",%'");
                \DB::update("UPDATE `goo__groups` SET `default_attribute` = REPLACE(`default_attribute`, '\"" . $attribute_key . "\":\"" . $k . "\"}', '\"" . $attribute_key . "\":\"" . $m . "\"}') WHERE `default_attribute` LIKE '%\"" . $attribute_key . "\":" . $k . "}%'");

                \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '\"" . $attribute_key . "\":" . $k . ",', '\"" . $attribute_key . "\":\"" . $m . "\",') WHERE `attributes` LIKE '%\"" . $attribute_key . "\":" . $k . ",%';");
                \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '\"" . $attribute_key . "\":" . $k . "}', '\"" . $attribute_key . "\":\"" . $m . "\"}') WHERE `attributes` LIKE '%\"" . $attribute_key . "\":" . $k . "}%';");
                \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '\"" . $attribute_key . "\":\"" . $k . "\",', '\"" . $attribute_key . "\":\"" . $m . "\",') WHERE `attributes` LIKE '%\"" . $attribute_key . "\":\"" . $k . "\",%'");
                \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '\"" . $attribute_key . "\":\"" . $k . "\"}', '\"" . $attribute_key . "\":\"" . $m . "\"}') WHERE `attributes` LIKE '%\"" . $attribute_key . "\":\"" . $k . "\"}%'");
            });
        });


        $good_ids = Good::query()->whereHas('group', function ($q) {
            $q->where('key', 'door');
        })->pluck('id')->toArray();
        if ($good_ids) {
            \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '\"typeRokoob\":', '\"typeParvaz\":') WHERE `attributes` LIKE '%\"typeRokoob\":%' and good_id in (" . implode(',', $good_ids) . ")");
            \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '\"typeMaterial\":', '\"typeMaterialDoor\":') WHERE `attributes` LIKE '%\"typeMaterial\":%' and good_id in (" . implode(',', $good_ids) . ")");
            \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '\"typeMaterialDoor\":\"mdf\"', '\"typeMaterialDoor\":\"mdf\",\"typeMaterialFrame\":\"mdf\",\"typeMaterialRokoob\":\"mdf\"') WHERE `attributes` LIKE '%\"typeMaterialDoor\":\"mdf\"%' and `attributes` LIKE '%\"hasFrame\":true%' and good_id in (" . implode(',', $good_ids) . ")");
            \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '\"typeMaterialDoor\":\"fomizeh\"', '\"typeMaterialDoor\":\"fomizeh\",\"typeMaterialFrame\":\"fomizeh\",\"typeMaterialRokoob\":\"fomizeh\"') WHERE `attributes` LIKE '%\"typeMaterialDoor\":\"fomizeh\"%' and `attributes` LIKE '%\"hasFrame\":true%' and good_id in (" . implode(',', $good_ids) . ")");
        }

        $good_ids = Good::query()->whereHas('group', function ($q) {
            $q->where('key', 'rokoob');
        })->pluck('id')->toArray();
        if ($good_ids) {
            \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '\"typeRokoob\":\"french\"', '\"typeParvaz\":\"french\",\"typeDakheli\":\"french\"') WHERE `attributes` LIKE '%\"typeRokoob\":\"french\"%' and good_id in (" . implode(',', $good_ids) . ")");
            \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '\"typeRokoob\":\"mexic\"', '\"typeParvaz\":\"mexic\",\"typeDakheli\":\"mexic\"') WHERE `attributes` LIKE '%\"typeRokoob\":\"mexic\"%' and good_id in (" . implode(',', $good_ids) . ")");
            \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '\"typeMaterial\":', '\"typeMaterialRokoob\":') WHERE `attributes` LIKE '%\"typeMaterial\":%' and good_id in (" . implode(',', $good_ids) . ")");
        }



        $good_ids = Good::query()->whereHas('group', function ($q) {
            $q->where('key', 'frame');
        })->pluck('id')->toArray();
        if ($good_ids) {
            \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '\"typeRokoob\":', '\"typeParvaz\":') WHERE `attributes` LIKE '%\"typeRokoob\":%' and good_id in (" . implode(',', $good_ids) . ")");
            \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '\"typeMaterial\":', '\"typeMaterialFrame\":') WHERE `attributes` LIKE '%\"typeMaterial\":%' and good_id in (" . implode(',', $good_ids) . ")");

            \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '\"typeMaterialFrame\":\"mdf\"', '\"typeMaterialFrame\":\"mdf\",\"typeMaterialRokoob\":\"mdf\"') WHERE `attributes` LIKE '%\"typeMaterialFrame\":\"mdf\"%' and good_id in (" . implode(',', $good_ids) . ")");
            \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '\"typeMaterialFrame\":\"fomizeh\"', '\"typeMaterialFrame\":\"fomizeh\",\"typeMaterialRokoob\":\"fomizeh\"') WHERE `attributes` LIKE '%\"typeMaterialFrame\":\"fomizeh\"%' and good_id in (" . implode(',', $good_ids) . ")");
        }


        \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '}', ',\"countParvaz\":2.5,\"countDakheli\":2.5}') WHERE `attributes` not LIKE '%\"countParvaz\":%' and good_id in (201)");
        \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '}', ',\"perLengthParvaz\":\"237\"}') WHERE `attributes` not LIKE '%\"perLengthParvaz\":%' and good_id in (201,199)");
        \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '}', ',\"perLengthDakheli\":\"237\"}') WHERE `attributes` not LIKE '%\"perLengthDakheli\":%' and good_id in (201,200)");
        \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '}', ',\"widthParvaz\":\"10.5\"}') WHERE `attributes` LIKE '%\"typeParvaz\":\"mexic\"%' and `attributes` not LIKE '%\"widthParvaz\":%' and  good_id in (201,199)");
        \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '}', ',\"widthParvaz\":\"7\"}') WHERE `attributes` LIKE '%\"typeParvaz\":\"french\"%' and `attributes` not LIKE '%\"widthParvaz\":%' and  good_id in (201,199)");
        \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '}', ',\"widthDakheli\":\"10.5\"}') WHERE `attributes` LIKE '%\"typeDakheli\":\"mexic\"%' and `attributes` not LIKE '%\"widthDakheli\":%' and  good_id in (201,200)");
        \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, '}', ',\"widthDakheli\":\"9.5\"}') WHERE `attributes` LIKE '%\"typeDakheli\":\"french\"%' and `attributes` not LIKE '%\"widthDakheli\":%' and  good_id in (201,200)");



        // AttributeItem::query()->whereNotNull('key')->pluck('key', 'id')->map(function ($m, $k) {
        //     \DB::update("UPDATE `goo__goods` SET `default_attribute` = REPLACE(`default_attribute`, ':" . $k . ",', ':\"" . $m . "\",') WHERE `default_attribute` LIKE '%:" . $k . ",%'");
        //     \DB::update("UPDATE `goo__goods` SET `default_attribute` = REPLACE(`default_attribute`, ':" . $k . "}', ':\"" . $m . "\"}') WHERE `default_attribute` LIKE '%:" . $k . "}%'");

        //     \DB::update("UPDATE `goo__groups` SET `default_attribute` = REPLACE(`default_attribute`, ':" . $k . ",', ':\"" . $m . "\",') WHERE `default_attribute` LIKE '%:" . $k . ",%'");
        //     \DB::update("UPDATE `goo__groups` SET `default_attribute` = REPLACE(`default_attribute`, ':" . $k . "}', ':\"" . $m . "\"}') WHERE `default_attribute` LIKE '%:" . $k . "}%'");

        //     \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, ':" . $k . ",', ':\"" . $m . "\",') WHERE `attributes` LIKE '%:" . $k . ",%'");
        //     \DB::update("UPDATE `pro__production_order_items` SET `attributes` = REPLACE(`attributes`, ':" . $k . "}', ':\"" . $m . "\"}') WHERE `attributes` LIKE '%:" . $k . "}%'");
        // });
    }
}
