<template>  
    <q-page>  
      <q-table  
        :rows="filteredRows"  
        :columns="columns"  
        row-key="id"  
        :pagination="pagination"  
        :dense="true"  
        :loading="loading"  
      >  
        <template v-slot:header="props">  
          <q-tr>  
            <q-th v-for="col in props.cols" :key="col.name">  
              <div class="q-gutter-xs flex items-center">  
                <q-input  
                  v-model="filters[col.field]"  
                  placeholder="جستجو"  
                  debounce="300"  
                  dense  
                  class="q-mb-xs"  
                />  
                <q-select  
                  v-model="filterConditions[col.field]"  
                  label="شرط فیلتر"  
                  dense  
                  @change="closeMenu(col.field)"  
                  class="q-mb-xs"  
                  :options="filterOptionsWithIcons"  
                  option-value="value"  
                  option-label="label"  
                  :emit-value="true"  
                  :map-options="true"  
                >  
                  <template v-slot:option="props">  
                    <q-item clickable v-close-popup>  
                      <q-item-section avatar>  
                        <q-icon :name="props.opt.icon" />  
                      </q-item-section>  
                      <q-item-section>  
                        {{ props.opt.label }}  
                      </q-item-section>  
                    </q-item>  
                  </template>  
                </q-select>  
              </div>  
            </q-th>  
          </q-tr>  
        </template>  
      </q-table>  
    </q-page>  
  </template>  
  
  <script>  
  import { ref, computed } from 'vue';  
  
  export default {  
    setup() {  
      const loading = ref(false);  
      const pagination = ref({  
        rowsPerPage: 10,  
      });  
      const filters = ref({  
        code: '',  
        name: '',  
        category: '',  
        unit: '',  
        mainUnit: '',  
        brand: '',  
      });  
      const filterConditions = ref({  
        code: 'includes',  
        name: 'includes',  
        category: 'includes',  
        unit: 'includes',  
        mainUnit: 'includes',  
        brand: 'includes',  
      });  
      const filterOptionsWithIcons = ref([  
        { label: 'شامل شود', value: 'includes', icon: 'check_circle' },  
        { label: 'شامل نشود', value: 'excludes', icon: 'cancel' },  
        { label: 'شروع شود با', value: 'startsWith', icon: 'arrow_forward' },  
        { label: 'پایان یابد با', value: 'endsWith', icon: 'arrow_back' },  
        { label: 'برابر باشد با', value: 'equals', icon: 'equalizer' },  
        { label: 'برابر نباشد با', value: 'notEquals', icon: 'not_equal' },  
      ]);  
      const columns = ref([  
        { name: 'id', label: '#', align: 'left', field: 'id', sortable: true },  
        { name: 'code', label: 'کد کالا', align: 'left', field: 'code', sortable: true },  
        { name: 'name', label: 'نام', align: 'left', field: 'name', sortable: true },  
        { name: 'category', label: 'دسته بندی', align: 'left', field: 'category', sortable: true },  
        { name: 'unit', label: 'واحد فرعی', align: 'left', field: 'unit', sortable: true },  
        { name: 'mainUnit', label: 'واحد اصلی', align: 'left', field: 'mainUnit', sortable: true },  
        { name: 'brand', label: 'بارکد', align: 'left', field: 'brand', sortable: true },  
      ]);  
      const rows = ref([  
        { id: 1, code: '00001', name: 'بیسکویت', category: 'کالاها', unit: 'عدد', mainUnit: 'عدد', brand: '123456' },  
        { id: 2, code: '00002', name: 'زیتون', category: 'کالاها', unit: 'عدد', mainUnit: 'عدد', brand: '654321' },  
        { id: 3, code: '00003', name: 'پاستا', category: 'کالاها', unit: 'عدد', mainUnit: 'عدد', brand: '789012' },  
        { id: 4, code: '00004', name: 'کیک', category: 'کالاها', unit: 'عدد', mainUnit: 'عدد', brand: '345678' },  
        { id: 5, code: '00005', name: 'پنیر', category: 'کالاها', unit: 'عدد', mainUnit: 'عدد', brand: '901234' },  
        { id: 6, code: '00006', name: 'ماست', category: 'کالاها', unit: 'عدد', mainUnit: 'عدد', brand: '567890' },  
      ]);  
  
      const filteredRows = computed(() => {  
        return rows.value.filter(row => {  
          return Object.keys(filters.value).every(field => {  
            const filterValue = filters.value[field].toLowerCase().trim();  
            const condition = filterConditions.value[field];  
            const rowValue = String(row[field]).toLowerCase();  
  
            if (!filterValue) return true; // If no filter, include all  
  
            switch (condition) {  
              case 'includes':  
                return rowValue.includes(filterValue);  
              case 'excludes':  
                return !rowValue.includes(filterValue);  
              case 'startsWith':  
                return rowValue.startsWith(filterValue);  
              case 'endsWith':  
                return rowValue.endsWith(filterValue);  
              case 'equals':  
                return rowValue === filterValue;  
              case 'notEquals':  
                return rowValue !== filterValue;  
              default:  
                return true;  
            }  
          });  
        });  
      });  
  
      return {  
        loading,  
        pagination,  
        filters,  
        filterConditions,  
        filterOptionsWithIcons,  
        columns,  
        rows,  
        filteredRows,  
      };  
    },  
  };  
  </script>  
  
  <style scoped>  
  .q-table {  
    font-family: 'Vazir', sans-serif; /* برای فونت فارسی */  
  }  
  </style>