import QRCodeStyling from "qr-code-styling";
import QRCode from "qrcode";
import { print } from "@/helpers";
import slitter from "./slitter";
import design from "./design";
import cutter from "./cutter";
import raping from "./raping";
import anbar from "./anbar";
import kalaf from "./kalaf";

export function getFieldByKey({ items = [], key = "id", value }) {
    const find = items.findIndex((f) => f[key] == value);
    if (find >= 0) return items[find];
    return null;
}
export function getField(attributes, key, value) {
    const items = getFieldByKey({
        items: attributes,
        value: key,
    }).items;
    if (items) {
        const temp = getFieldByKey({
            items,
            value,
        });
        if (temp) return temp.name;
    }

    return null;
}

export function cmFormat(number) {
    number = Math.ceil(number);
    let res = [];
    if (Math.floor(number / 100))
        res.push(
            Math.floor(number / 100)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
        );
    res.push(number % 100);
    return res.join(",");
}
export function printTemplate(data, attributes, template = null) {
    let content = "";
    let style = "";
    if (template) {
        switch (template.station_id) {
            case 16: // اسلایتر
                content = slitter(data, attributes, template);
                //style = "@media print{@page {size: A5}}";
                break;
            case 24:
            case 15:
                content = design(data, attributes, template);
                //style = "@media print{@page {size: portrait}}";
                break;
            case 1:
                content = cutter(data, attributes, template);
                // content += `<div style="height:3cm"></div>`;
                // content += content;
                //style = "@media print{@page {size: portrait}}";
                break;
            case 17:
                content = anbar(data, attributes, template);
                //style = "@media print{@page {size: portrait}}";
                break;
            case 19:
                content = kalaf(data, attributes, template);
                break;
            case 21:
            case 5:
            case 22:
                content = raping(data, attributes, template);
                //style = "@media print{@page {size: landscape}}";
                break;
        }

        //content = `<div style="padding:0.7cm">${content}</div>`;
    } else {
        content = design(data, attributes, template);
    }
    //return content;
    print(content, {
        //specs: ["width=500px", "height=700px"],
        style
    });
}

function repeatLabelLenge(temp, type) {
    let res = [temp];
    switch (
    type * 1 // نوع چهارچوب
    ) {
        case 1.5: // 1.5 و 2 لنگه
        case 2:
            res.push(temp);
            break;
        case 3: // سه لنگه
            res.push(temp);
            res.push(temp);
            break;
        case 4: // چهار لنگه
            res.push(temp);
            res.push(temp);
            res.push(temp);
            break;
    }
    return res;
}
export async function printLabel(data, attributes, itm = null) {
    let res = [];
    let raping = [];
    let cnc = [];
    let charchob = [];
    let maghta = [];
    let count = 1;
    for (let item of data.items.filter((f) =>
        [8, 16, 17, 18, 20, 24].includes(f.good.group_id)
    )) {
        if (itm && itm.id !== item.id) continue;
        for (let i = 1; i <= item.count; i++) {

            const temp = await label({
                has_logo: data.has_logo,
                message:
                    item.attributes.typeMaterialDoor == 'fomizeh'
                        ? "<strong style='font-size: 12px;'>جهت فعال سازی ضمانت نامه به سایت ذیل مراجعه نمایید <h6 style='line-height: 18px;'>www.iranpars.co</h6></strong>"
                        : "<h6 style='font-size: 18px;line-height: 25px;'>ایران پارس <br>نوآوری و کیفیت</h6>",
                serial: data.code,
                code: item.id + (i > 1 ? "." + i : ""),
                qrcode: item.id + (i > 1 ? "." + i : ""),
                party_name: data.party_name,
                customer_name: data.customer_name,
                delivery_date: data.delivery_date,
                //color: item.attributes_label.pvcColor,
                color: item.attributes?.pvcColor !== 'two_color' || !item.attributes_label?.coverFrontDoor ? item.attributes_label?.pvcColor : ["<b>نما: </b>" + item.attributes_label?.coverFrontDoor, "<b>پشت: </b>" + item.attributes_label?.coverBackDoor].join('<br>'),

                model: item.good.name,
                height: item.attributes.doorHeight,
                width: item.attributes.doorWidth,
                type: item.attributes_label.typeMaterialDoor,
                align: item.attributes_label.doorAlign,
                lock: item.attributes_label.hasLockPlace,
                type_label: item.attributes_label.typeEdge,
                thickness_wall: item.attributes_label.doorThickness,
                gablabeh: item.attributes_label.hasEdgeOfDoor,
                align_gablabeh:
                    item.attributes.alignEdgeOfDoor !== "threeSide" &&
                        item.attributes.hasEdgeOfDoor
                        ? item.attributes_label.alignEdgeOfDoor
                        : undefined,
                barjestegi: item.attributes_label.hasBarjestegi,
                vahed: item.attributes.vahed,
                thickness:
                    item.attributes_label.centerLayerThickness ||
                    item.attributes_label.sheetCNCThickness,
                description: [
                    item.attributes.doorLengeh == 1
                        ? ""
                        : item.attributes_label.doorLengeh,
                    data.description ?? "",
                    item.description ?? "",
                ]
                    .filter((f) => f)
                    .join(" - "),
            });

            if ([8, 18, 24].includes(item.good.group_id)) {
                raping.push(...repeatLabelLenge(temp, item.attributes.doorLengeh));
            } else if ([16, 20].includes(item.good.group_id))
                cnc.push(...repeatLabelLenge(temp, item.attributes.doorLengeh));

            count++;
        }
    }

    for (let item of data.items.filter(
        (f) => [8, 16, 17, 18, 20].includes(f.good.group_id) && f.attributes.hasFrame
    )) {
        if (itm && itm.id !== item.id) continue;
        for (let i = 1; i <= item.count; i++) {
            const temp = await frame({
                has_logo: data.has_logo,
                message:
                    item.attributes.typeMaterialFrame == 'fomizeh'
                        ? "<strong style='font-size: 12px;'>جهت فعال سازی ضمانت نامه به سایت ذیل مراجعه نمایید <h6 style='line-height: 18px;'>www.iranpars.co</h6></strong>"
                        : "<h6>ایران پارس نوآوری و کیفیت</h6>",
                serial: data.code,
                code: item.id + (i > 1 ? "." + i : ""),
                qrcode: item.id + (i > 1 ? "." + i : ""),

                party_name: data.party_name,
                customer_name: data.customer_name,
                delivery_date: data.delivery_date,
                //color: item.attributes_label.pvcColor,
                color: item.attributes?.pvcColor !== 'two_color' || !item.attributes_label?.coverFrontDoor ? item.attributes_label?.pvcColor : item.attributes_label?.coverFrontDoor,

                model:
                    "چهارچوب کشویی کف" +
                    item.attributes_label.widthFloor,
                height: item.attributes_label.frameHeight,
                width: item.attributes_label.frameWidth,
                type: item.attributes_label.typeMaterialFrame,
                gablabeh: item.attributes_label.thicknessEdgeOfFrame,
                astaneh: item.attributes_label.hasThreshold,
                mokamel: item.attributes_label.mokammelWidth,
                parvaz: item.attributes_label.countParvaz,
                vahed: item.attributes.vahed,
                description: [
                    data.description ?? "",
                    item.description ?? "",
                    item.attributes_label.mokammelWidth
                        ? " عرض مکمل: " + item.attributes_label.mokammelWidth + " سانتی"
                        : "",
                ]
                    .filter((f) => f)
                    .join(" - "),
            });
            if (item.attributes.hasThreshold)
                charchob.push(temp.repeat(4)); // 4 آستانه داشت
            else charchob.push(temp.repeat(3)); // آستانه نداشت 3 تا

            count++;
        }
    }
    for (let item of data.items.filter((f) => [10].includes(f.good.group_id))) {
        if (itm && itm.id !== item.id) continue;
        for (let i = 1; i <= item.count; i++) {
            const temp = await frame({
                has_logo: data.has_logo,
                message:
                    item.attributes.typeMaterialFrame == 'fomizeh'
                        ? "<strong style='font-size: 12px;'>جهت فعال سازی ضمانت نامه به سایت ذیل مراجعه نمایید <h6 style='line-height: 18px;'>www.iranpars.co</h6></strong>"
                        : "<h6>ایران پارس نوآوری و کیفیت</h6>",
                serial: data.code,
                code: item.id + (i > 1 ? "." + i : ""),
                qrcode: item.id + (i > 1 ? "." + i : ""),

                party_name: data.party_name,
                customer_name: data.customer_name,
                delivery_date: data.delivery_date,
                color: item.attributes_label.pvcColor,
                model:
                    item.good.name +
                    "<br>کف " +
                    item.attributes_label.widthFloor,
                height: item.attributes_label.frameHeight,
                width: item.attributes_label.frameWidth,
                type: item.attributes_label.typeMaterialFrame,
                gablabeh: item.attributes_label.thicknessEdgeOfFrame,
                astaneh: item.attributes_label.hasThreshold,
                mokamel: item.attributes_label.mokammelWidth,
                parvaz: item.attributes_label.countParvaz,
                vahed: item.attributes.vahed,
                description: [
                    data.description ?? "",
                    item.description ?? "",
                    item.attributes_label.mokammelWidth
                        ? " عرض مکمل: " + item.attributes_label.mokammelWidth + " سانتی"
                        : "",
                ]
                    .filter((f) => f)
                    .join(" - "),
            });
            if (item.attributes.hasThreshold)
                charchob.push(temp.repeat(4)); // 4 آستانه داشت
            else charchob.push(temp.repeat(3)); // آستانه نداشت 3 تا

            count++;
        }
    }


    for (let item of data.items.filter((f) =>
        ['maghta'].includes(f.good?.group?.key)
    )) {
        if (itm && itm.id !== item.id) continue;
        for (let i = 1; i <= item.count; i++) {
            const temp = await labelMaghta({
                has_logo: data.has_logo,
                message: "<h6>ایران پارس <br>نوآوری و کیفیت</h6>",
                serial: data.code,
                code: item.id + (i > 1 ? "." + i : ""),
                qrcode: item.id + (i > 1 ? "." + i : ""),
                party_name: data.party_name,
                customer_name: data.customer_name,
                delivery_date: data.delivery_date,
                color: item.attributes_label.pvcColor,
                model: item.good.name,
                height: item.attributes.doorHeight,
                width: item.attributes.doorWidth,
                type: item.attributes_label.typeMaterialDoor,
                thickness_wall: item.attributes_label.doorThickness,
                widthFloor: item.attributes_label.widthFloor,
                gablabeh: item.attributes_label.thicknessEdgeOfFrame,

                vahed: item.attributes.vahed,
                thickness:
                    item.attributes_label.centerLayerThickness ||
                    item.attributes_label.sheetCNCThickness,
                description: [
                    '1 شاخه پرواز 7 سانتی به قد 237',
                    data.description ?? "",
                    item.description ?? "",
                ]
                    .filter((f) => f)
                    .join(" - "),
            });

            maghta.push(temp.repeat(4));
            count++;
        }


    }


    print(
        raping.join("").repeat(3) +
        raping.map((m) => m.repeat(8)).join("") +
        cnc.map((m) => m.repeat(4)).join("") +
        charchob
            //.map((m) => m.repeat(3))
            .join("") +
        maghta.join(''),

        {
            // specs: ["width=500px", "height=700px"],
            style: `
                body{
                    padding-right: 6px
                }
            `
        }
    );
}

async function label({
    has_logo,
    message,
    serial,
    code,
    qrcode,
    party_name,
    customer_name,
    delivery_date,
    color,
    model,
    height,
    width,
    type,
    align,
    lock,
    type_label,
    thickness,
    thickness_wall,
    barjestegi,
    gablabeh,
    align_gablabeh,
    vahed,
    description,
}) {
    const qrCode = new QRCodeStyling({
        type: "svg",
        data: qrcode + "",
    });
    const raw = await qrCode.getRawData("svg");
    const qr = URL.createObjectURL(raw);

    const qrcode_svg = await QRCode.toString(`${qrcode}`, {
        type: "svg",
        margin: 0,
    });

    let customer = [];
    if (party_name) customer.push(party_name);
    if (customer_name) customer.push(customer_name);
    if (vahed) customer.push(vahed);

    return `<section><div style="width:10.16cm;padding:10px">
    <table class="asd w-full text-center">
        <tbody>
            ${has_logo
            ? `
            <tr style="height:50px">
                <td colspan="5" style="border:0">
                    <div class="flex">
                        <div style="width: 1.9cm;font-size: 9px;">
                        <svg height="55" width="80" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xodm="http://www.corel.com/coreldraw/odm/2003" xml:space="preserve" width="16.9332mm" height="11.8532mm" version="1.1" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd" viewBox="0 0 1693.32 1185.32"><defs><style type="text/css"><![CDATA[.fil1 {fill:#FEFEFE}.fil0 {fill:#2B2A29}.fil2 {fill:#000000;fill-rule:nonzero}]]></style></defs><g><path d="M1685.8 976.22c0,0 -64.33,-53.74 -232.9,-109.05 -168.57,-55.3 -209.66,-61.53 -209.66,-61.53l-1.04 -670.05 -282.33 -113.95 -366.85 218.21 1.83 566.49c0,0 -94.43,4.98 -315.76,51.65 -36.13,7.62 -67.85,15.82 -95.62,24.2 -142.35,42.96 -181.24,90.82 -181.24,90.82 0,0 85.19,-41.84 206.97,-70.4 264.9,-62.14 442.09,-71.49 442.09,-71.49l-0.09 -564.63 312.6 -185.72 227.31 80.59 0.19 672.75c0,0 213.71,43.2 308.9,75.77 95.2,32.56 185.6,66.34 185.6,66.34z"/><polygon points="961.2,125.52 692.26,289.87 692.26,832.73 959.96,832.73 "/><circle cx="743.21" cy="554.07" r="19.74" class="fil1"/><path d="M170.33 1055.18c0,27.17 -8.79,50.23 -26.38,69.18 -2.64,2.88 -4.71,4.71 -6.19,5.51 -1.48,0.76 -4.04,1.2 -7.64,1.28 -7.43,0.24 -16.3,-0.24 -26.62,-1.4 -14.74,-1.63 -22.14,-4.11 -22.14,-7.39 0,-2.72 3.16,-5.31 9.48,-7.83 12.66,-5 23.42,-10.03 32.21,-15.11 22.9,-13.11 34.33,-24.38 34.33,-33.81 0,-7.96 -10.59,-19.59 -31.77,-34.9l17.02 -25.78c1.12,-1.71 2.24,-2.55 3.4,-2.55 1.12,0 2.64,0.84 4.6,2.55 4.67,4.16 8.91,10.28 12.67,18.39 4.67,10.23 7.03,20.86 7.03,31.86zm29.54 -111.19l-11.23 136.08c-0.56,6.72 -2.36,10.08 -5.4,10.08 -3.12,0 -5.2,-4.24 -6.19,-12.75l-13.35 -113.87 31.61 -22.94c1.4,-1 2.52,-1.52 3.28,-1.52 1.08,0 1.52,1.64 1.28,4.92zm-3.32 108.31c3.68,2.2 9.15,3.27 16.51,3.27 18.26,0 33.01,-5.23 44.24,-15.66l-18.14 -48.96 16.18 -17.79c1.16,-1.32 2.04,-2 2.68,-2 0.76,0 1.44,0.84 2,2.56l19.18 58.11c4.76,14.51 11.47,21.75 20.15,21.75l5.03 0 0 35.73 -8.91 0c-9.43,0 -17.07,-4.52 -22.94,-13.59 -2.64,-4.12 -6.2,-12.23 -10.63,-24.34 -5.8,10.75 -11.47,18.78 -17.11,23.98 -10.63,9.83 -23.86,14.79 -39.69,14.79 -3.03,0 -5.91,-0.28 -8.55,-0.84l0 -37.01zm173.06 37.01l-8.79 0c-13.27,0 -22.35,-7.52 -27.26,-22.58 -4.48,15.06 -12.99,22.58 -25.54,22.58l-10.07 0 0 -35.73 6.43 0c12.51,0 20.47,-8.2 23.9,-24.58 1.08,-5.24 3.04,-7.84 5.84,-7.84 2.84,0 4.55,2.6 5.15,7.84 1.88,16.38 9.08,24.58 21.55,24.58l8.79 0 0 35.73zm-38.17 29.61c0.92,0.88 0.88,1.8 -0.12,2.84l-13.59 13.55c-0.56,0.56 -1.04,0.84 -1.52,0.84 -0.48,0 -0.96,-0.28 -1.52,-0.84l-13.11 -13.11 -13.11 13.11c-0.95,0.96 -1.91,0.96 -2.95,0l-13.79 -13.35c-1.2,-1.08 -1.12,-2.28 0.24,-3.6l12.63 -12.91c1.16,-1.15 2.36,-1.15 3.52,0l12.87 12.91 12.86 -12.91c1.2,-1.15 2.36,-1.15 3.52,0l14.07 13.47zm107.71 -91.68c0,19.74 -4.95,34.97 -14.86,45.68 -10.32,10.91 -25.26,16.39 -44.85,16.39l-16.38 0 0 -35.73 27.17 0c6.24,0 12.83,-0.84 19.79,-2.56 9.67,-2.44 14.5,-5.84 14.5,-10.31 -11.55,1.24 -19.7,1.88 -24.45,1.88 -19.15,0 -28.7,-6.44 -28.7,-19.31 0,-9.67 2.36,-19.9 7.03,-30.69 6,-13.83 13.83,-20.71 23.54,-20.71 9.92,0 18.35,4.4 25.26,13.23 8,10.07 11.95,24.1 11.95,42.13zm-20.58 -13.79c-5.32,1.4 -10.99,2.08 -16.99,2.08 -10.31,0 -15.46,-2.04 -15.46,-6.2 0,-2.88 1.04,-5.27 3.11,-7.15 2.08,-1.84 4.56,-2.8 7.44,-2.8 8.43,0 15.71,4.68 21.9,14.07zm0.56 -68.07c1.04,1.04 1.04,2.04 0,3.08l-14.63 14.63c-1,1 -2.08,1 -3.16,0l-14.86 -14.43c-1.16,-1.08 -1.04,-2.32 0.36,-3.72l13.59 -13.95c1.15,-1.23 2.39,-1.23 3.71,0l14.99 14.39zm129.29 174.94c1,1.04 0.96,2.08 -0.12,3.2l-14.54 14.51c-1,1 -2.04,1 -3.16,0l-14.87 -14.43c-1.16,-1.08 -1.04,-2.32 0.36,-3.72l13.59 -13.83c1.16,-1.23 2.4,-1.23 3.76,0l14.98 14.27zm64.15 -138.17c7.88,4.08 14.19,12.35 18.87,24.82 1.8,4.32 2.68,9.2 2.68,14.67l0 0.92c0,28.5 -10.32,47.44 -30.9,56.79 -13.27,6.64 -34.17,9.96 -62.67,9.96l-87.45 0 -0.12 0.12 0 -35.25 0.12 -0.13 84.41 0.36c37.85,0 63.43,-3.63 76.7,-10.87 1.96,-1.79 2.92,-3.55 2.92,-5.27 0,-4 -7.03,-11.39 -21.18,-22.26l-0.36 -0.36 0 -0.32c10.79,-22.11 16.42,-33.18 16.98,-33.18zm73.58 73.03c0,27.17 -8.79,50.23 -26.38,69.18 -2.63,2.88 -4.71,4.71 -6.19,5.51 -1.48,0.76 -4.04,1.2 -7.63,1.28 -7.44,0.24 -16.31,-0.24 -26.62,-1.4 -14.75,-1.63 -22.14,-4.11 -22.14,-7.39 0,-2.72 3.15,-5.31 9.47,-7.83 12.67,-5 23.42,-10.03 32.21,-15.11 22.9,-13.11 34.33,-24.38 34.33,-33.81 0,-7.96 -10.59,-19.59 -31.77,-34.9l17.03 -25.78c1.12,-1.71 2.23,-2.55 3.39,-2.55 1.12,0 2.64,0.84 4.6,2.55 4.68,4.16 8.91,10.28 12.67,18.39 4.67,10.23 7.03,20.86 7.03,31.86zm25.94 -84.94l2.12 0c9.59,1.56 23.1,12.95 40.53,34.17 8.67,11.56 12.99,22.03 12.99,31.38 0,25.46 -6.12,41.37 -18.39,47.76 -7.03,4 -14.87,6 -23.54,6l-30.53 0c-3.08,0 -4.6,-0.32 -4.6,-0.96l0 -37.57 0.36 0c5.32,1.8 11.51,2.68 18.62,2.68l10.88 0c22.34,0 34.21,-2.8 35.61,-8.4l0 -0.36c-0.48,-6.19 -13.63,-17.42 -39.45,-33.73 -3.76,-1.88 -6.92,-3 -9.51,-3.4l-1.4 0 -7 2c-2.19,0 -3.47,-1.44 -3.87,-4.36 0,-2.47 4.31,-13.5 12.99,-33.13 1.32,-1.4 2.71,-2.08 4.19,-2.08zm277.9 73.18c0,11.79 -2.48,22.03 -7.4,30.78 -5.71,10.07 -13.47,15.11 -23.3,15.11 -8.59,0 -14.99,-4.16 -19.22,-12.51 -3.88,8.35 -9.43,12.51 -16.63,12.51 -4.75,0 -9.23,-0.76 -13.43,-2.36 0,23.74 -6.51,41.85 -19.46,54.28 -12.95,12.46 -31.34,18.7 -55.16,18.7 -18.7,0 -34.05,-3.6 -46,-10.79 -14.59,-8.83 -21.9,-21.94 -21.9,-39.33 0,-17.19 3.95,-31.65 11.83,-43.44 2.04,-3.04 4.19,-4.56 6.43,-4.56 1.8,0 2.72,1.16 2.72,3.48 0,1.35 -0.28,2.83 -0.84,4.47 -2.8,8.52 -4.2,16.07 -4.2,22.7 0,21.31 17.47,31.98 52.44,31.98 13.91,0 26.54,-1.6 37.93,-4.8 16.95,-4.75 25.42,-12.11 25.42,-22.02 0,-6.95 -9.71,-20.78 -29.14,-41.57l15.43 -25.85c1.24,-2.04 2.32,-3.08 3.16,-3.08 0.88,0 1.92,0.8 3.16,2.36 6.55,8.43 11.23,13.9 13.95,16.38 5.63,5.24 10.99,7.84 16.14,7.84 4,0 7.12,-3.76 9.39,-11.36 2.24,-7.55 4.12,-11.35 5.6,-11.35 2.36,0 3.92,1.96 4.72,5.88 1,5.2 2.43,8.83 4.31,10.87 3.6,4 9.84,5.96 18.75,5.96 7.39,0 11.11,-2.96 11.11,-8.88 0,-4.59 -3.72,-11.19 -11.11,-19.66l13.11 -17.95c0.84,-1.16 1.68,-1.76 2.44,-1.76 0.87,0 1.55,0.68 2.11,2 5.08,11.63 7.64,23.62 7.64,35.97zm52.48 11.76c0,27.17 -8.8,50.23 -26.38,69.18 -2.64,2.88 -4.72,4.71 -6.2,5.51 -1.48,0.76 -4.04,1.2 -7.63,1.28 -7.44,0.24 -16.31,-0.24 -26.62,-1.4 -14.75,-1.63 -22.14,-4.11 -22.14,-7.39 0,-2.72 3.15,-5.31 9.47,-7.83 12.67,-5 23.42,-10.03 32.21,-15.11 22.9,-13.11 34.34,-24.38 34.34,-33.81 0,-7.96 -10.6,-19.59 -31.78,-34.9l17.03 -25.78c1.12,-1.71 2.24,-2.55 3.4,-2.55 1.11,0 2.63,0.84 4.59,2.55 4.68,4.16 8.91,10.28 12.67,18.39 4.68,10.23 7.04,20.86 7.04,31.86zm43.04 34.13l-11.47 0c-20.66,0 -31.02,-16.67 -31.02,-50 0,-26.54 -1,-54.52 -2.95,-83.93l21.34 -14.99c2.64,-1.88 4.4,-2.84 5.27,-2.84 1.08,0 1.64,1.96 1.64,5.87l-0.12 93.45c0,11.15 4.68,16.71 14.03,16.71l3.28 0 0 35.73zm-4.56 -35.73l17.79 0 0.72 0.95 0.12 33.86 -0.6 0.92 -17.79 0 -0.72 -0.92 -0.12 -33.86 0.6 -0.95zm59.84 -15.07c0,12.07 -2.8,22.94 -8.4,32.53 -7.11,12.19 -17.22,18.27 -30.33,18.27l-8.68 0 0 -35.73 7.28 0c15.43,0 23.18,-2.24 23.18,-6.76 0,-4 -6.96,-12.39 -20.86,-25.18l16.74 -25.78c0.96,-1.4 2.04,-1.6 3.28,-0.56 6.39,5.2 11.19,12.91 14.43,23.06 2.24,7.16 3.36,13.87 3.36,20.15zm-60.04 102.2c-0.96,-0.92 -0.92,-1.92 0.08,-2.92l13.47 -13.35c0.96,-0.92 1.92,-0.92 2.96,0l13.55 13.23c1.04,1 0.92,2.16 -0.32,3.4l-12.55 12.74c-1,1.12 -2.08,1.12 -3.28,0l-13.91 -13.1zm-14.3 -24.59c-0.92,-0.91 -0.88,-1.91 0.12,-2.91l13.7 -13.59c1,-1.04 2,-1.04 2.92,0l13.11 13.23 13.11 -13.23c0.96,-0.96 1.92,-0.96 2.96,0l13.79 13.47c1.12,1.08 1.04,2.24 -0.24,3.51l-12.63 12.88c-1.16,1.15 -2.36,1.15 -3.52,0l-12.87 -12.88 -12.87 12.88c-1.2,1.15 -2.36,1.15 -3.52,0l-14.06 -13.36zm184.69 -118.86c0.92,0.88 0.87,1.8 -0.12,2.84l-13.71 13.67c-1,1.04 -2,1.04 -2.92,0l-13.11 -13.23 -13.15 13.23c-0.92,0.96 -1.88,0.96 -2.92,0l-13.91 -13.43c-1.11,-1.12 -1,-2.32 0.32,-3.64l12.67 -12.79c1.16,-1.16 2.32,-1.16 3.52,0l12.87 12.79 12.87 -12.79c1.16,-1.16 2.36,-1.16 3.52,0l14.07 13.35zm74.58 33.37c2.47,0.88 3.75,2.6 3.75,5.16l-2.36 10.31c0,5 6.56,7.48 19.67,7.48l0.48 0.47 0 34.22 -0.96 0.92 -3.72 0c-12.67,0 -21.54,-7.04 -26.7,-21.07l-0.48 0c-7.47,8.8 -20.14,15.23 -37.93,19.19 -10.95,1.88 -23.9,3.12 -38.88,3.76l0 -0.48 -0.48 0.48 -0.44 0 -84.78 -0.48 0 -0.44 0 -35.13 0 -0.61 5.6 0.12 48.72 0.49 25.3 0c30.22,0 54.08,-2.84 71.66,-8.44 2.04,-0.72 7.48,-5.23 16.39,-13.59 2.36,-1.56 4.08,-2.36 5.16,-2.36zm95.2 -55.47l-46.6 16.15c2.27,3.59 6.15,10.15 11.71,19.66 5.71,10.15 8.55,19.86 8.55,29.18 0,13.35 -3.48,24.54 -10.43,33.57 -7.95,10.39 -19.07,15.59 -33.37,15.59l-9.84 0 0 -35.73 3.76 0c21.78,0 32.65,-2.28 32.65,-6.88 0,-3.2 -9.95,-19.34 -29.85,-48.36 -0.16,-0.32 -0.24,-0.68 -0.24,-1.08 0,-1.96 1.28,-6.71 3.8,-14.27 2.55,-7.59 4.39,-11.55 5.59,-11.95l64.27 -22.14 0 26.26zm0 -26.26l0 26.26 0 -26.26zm59.11 140.41l-6.55 0c-2.08,0 -3.84,-0.72 -5.24,-2.12 0.28,25.62 -7.95,47.48 -24.74,65.59 -2.64,2.87 -4.64,4.67 -5.96,5.39 -1.63,0.92 -4.23,1.44 -7.83,1.52 -6.79,0.24 -15.67,-0.28 -26.58,-1.52 -14.75,-1.64 -22.14,-4.12 -22.14,-7.39 0,-2.72 3.16,-5.36 9.47,-7.84 12.67,-4.99 23.38,-10.03 32.22,-15.1 22.86,-13.11 34.29,-24.38 34.29,-33.86 0,-7.95 -10.55,-19.58 -31.73,-34.89l17.82 -26.46c1.24,-1.88 2.2,-2.79 2.92,-2.79 0.72,0 1.88,0.91 3.52,2.79 11.95,13.99 21.02,20.95 27.25,20.95l3.28 0 0 35.73zm-2.08 -35.73l20.83 0 0.72 0.95 0.23 33.86 -0.71 0.92 -20.83 0 -0.72 -0.92 -0.24 -33.86 0.72 -0.95zm137.33 -10.16c0,11.71 -2.48,21.95 -7.47,30.78 -5.72,10.07 -13.43,15.11 -23.18,15.11 -9.51,0 -16.31,-4.84 -20.39,-14.51 -1.71,4.12 -4.59,7.59 -8.67,10.35 -4.04,2.76 -8.31,4.16 -12.75,4.16 -9.43,0 -16.79,-4.52 -22.02,-13.59 -1.4,3.51 -4,6.67 -7.8,9.43 -3.75,2.76 -7.55,4.16 -11.27,4.16l-12.07 0 0 -35.73 12.19 0c5.92,0 10.08,-1.92 12.39,-5.84 0.96,-1.56 2.04,-5.15 3.28,-10.79 0.88,-3.88 2.48,-5.84 4.8,-5.84 2.28,0 3.75,1.96 4.47,5.84 1,5.87 1.96,9.51 2.8,10.91 2.36,3.92 6.72,5.84 13.11,5.84 5.56,0 9.47,-1.96 11.83,-5.96 0.84,-1.4 1.92,-5.04 3.16,-10.87 0.88,-3.92 2.4,-5.88 4.55,-5.88 2.6,0 4.2,1.96 4.8,5.88 0.88,5.2 2.16,8.83 3.88,10.87 3.43,4 9.79,5.96 19.1,5.96 7.4,0 11.11,-2.96 11.11,-8.88 0,-4.59 -3.71,-11.19 -11.11,-19.66l12.99 -17.95c0.84,-1.16 1.72,-1.76 2.56,-1.76 0.8,0 1.44,0.68 2,2 5.15,11.79 7.71,23.78 7.71,35.97zm-53.83 -74.5c0.99,0.96 0.99,1.92 0,2.96l-13.47 13.35c-0.92,0.92 -1.92,0.92 -2.92,0l-13.59 -13.15c-1.08,-1.08 -1.04,-2.24 0.24,-3.48l12.51 -12.79c1.12,-1.07 2.24,-1.07 3.4,0l13.83 13.11zm14.26 22.27c0.96,0.92 0.92,1.92 -0.12,2.91l-13.54 13.59c-0.96,0.92 -1.92,0.92 -2.96,0l-13.23 -13.23 -12.99 13.23c-1,1 -2.04,1 -3.04,0l-13.83 -13.47c-1.08,-1 -0.96,-2.19 0.36,-3.51l12.63 -12.87c1.2,-1.16 2.36,-1.16 3.52,0l12.87 12.87 12.91 -12.87c1.08,-1.08 2.2,-1.08 3.4,0l14.02 13.35z" class="fil2"/></g></svg>
                        </div>
                        <div style="width: 6cm; line-height: 17px;">${message ?? ""}</div>
                <div style="width: 1.5cm;height: auto;float: left;padding-right: 5px;">${qrcode_svg}</div>
                    </div>
                </td>
            </tr> 
                `
            : ""
        }
            
            <tr class="tr-title text-xs text-left h-1">
                <td colspan="3">
                    نمایندگی / مشتری ${vahed ? " / واحد" : ""}
                </td>
                <td colspan="2">
                    تاریخ سفارش
                </td>
            </tr>
            <tr>
                <td colspan="3" style="width: 6.5cm;">
                    <strong>${customer.join(" / ")}</strong>
                </td>
                <td colspan="2" style="width: 3cm;">
                    <strong style="font-size:16px">${delivery_date ?? ""
        }</strong>
                </td>
            </tr>
            <tr class="tr-title text-xs text-left h-1">
                <td colspan="2" style="width:3cm">
                    <span>سریال </span>
                    <span style="float: left; font-weight: 900;font-size: 11px;">${serial ?? ""
        }</span>
                    </td>
                <td>
                    مدل
                </td>
                <td>
                    عرض
                </td>
                <td>
                قد
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <strong style="font-size: 18px;">${code ?? ""}</strong>
                </td>
                <td style="width: 4cm;">
                    <strong>${model ?? ""}</strong>
                </td>
                <td>
                    <strong style="font-size:16px">${width ?? ""}</strong>
                </td>
                <td>
                    <strong style="font-size:16px">${height ?? ""}</strong>
                </td>
            </tr>
            <tr class="tr-title text-xs text-left h-1">
                <td>
                    ضخامت
                </td>
                <td>
                    نوع لبه
                </td>   
                <td>
                    رنگ
                </td>
                <td colspan="2">
                    نوع جنس
                </td>
            </tr>
            <tr>
                <td>
                    <strong style="font-size:16px">${thickness ?? ""}</strong>
                </td>
                <td>
                    <strong style="font-size:16px">${type_label ?? ""}</strong>
                </td>
                <td>
                    <strong style="font-size: 16px;">${color ?? ""}</strong>
                </td>
                <td colspan="2">
                    <strong style="font-size:16px">${type ?? ""}</strong>
                </td>
            </tr>
            <tr class="tr-title text-xs text-left h-1">
                <td>
                    قطر درب
                </td>
                <td>
                ${barjestegi == "دارد" ? "برجستگی" : ""}

                    
                </td>
                <td>
                ${gablabeh == "دارد" ? "قابلبه" : ""}
                </td>
                <td>
                ${lock == "دارد" ? "جای قفل" : ""}
                </td>
                <td>
                    بازشو
                </td>
            </tr>
            <tr>
                <td>
                    <strong style="font-size:16px">${thickness_wall ?? ""
        }</strong>
                </td>
                <td>
                    <strong style="font-size:16px">
                    ${barjestegi == "دارد" ? barjestegi : ""}
                    </strong>
                </td>
                <td>
                    <strong style="font-size:16px">
                    ${align_gablabeh ?? gablabeh}</strong>
                </td>
                <td >
                    <strong style="font-size:16px">
                    ${lock == "دارد" ? lock : ""}
                    </strong>
                </td>
                <td>
                    <strong style="font-size:16px">${align ?? ""}</strong>
                </td>
            </tr>
            <tr>
            <td colspan="5" style="border:0;font-size: 12px;line-height: 12px;">
                    ${description ?? ""}
                </td>
            </tr>
        </tbody>
    </table>
</div></section>`;
}

async function frame({
    has_logo,
    message,
    serial,
    code,
    qrcode,
    party_name,
    customer_name,
    delivery_date,
    color,
    model,
    height,
    width,
    type,
    astaneh,
    gablabeh,
    vahed,
    mokamel,
    parvaz,
    description,
}) {
    const qrCode = new QRCodeStyling({
        type: "svg",
        data: qrcode + "",
    });
    const raw = await qrCode.getRawData("svg");
    const qr = URL.createObjectURL(raw);
    let customer = [];
    if (party_name) customer.push(party_name);
    if (customer_name) customer.push(customer_name);
    if (vahed) customer.push(vahed);
    const qrcode_svg = await QRCode.toString(`${qrcode}`, {
        type: "svg",
        margin: 0,
    });
    return `<section><div style="width:10.16cm;padding:10px">
    <table class="asd w-full text-center">
        <tbody>
            ${has_logo
            ? `
            <tr style="height:50px">
                <td colspan="5" style="border:0">
                    <div class="flex">
                        <div style="width: 1.9cm;font-size: 9px;">
                        <svg height="55" width="80" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xodm="http://www.corel.com/coreldraw/odm/2003" xml:space="preserve" width="16.9332mm" height="11.8532mm" version="1.1" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd" viewBox="0 0 1693.32 1185.32"><defs><style type="text/css"><![CDATA[.fil1 {fill:#FEFEFE}.fil0 {fill:#2B2A29}.fil2 {fill:#000000;fill-rule:nonzero}]]></style></defs><g><path d="M1685.8 976.22c0,0 -64.33,-53.74 -232.9,-109.05 -168.57,-55.3 -209.66,-61.53 -209.66,-61.53l-1.04 -670.05 -282.33 -113.95 -366.85 218.21 1.83 566.49c0,0 -94.43,4.98 -315.76,51.65 -36.13,7.62 -67.85,15.82 -95.62,24.2 -142.35,42.96 -181.24,90.82 -181.24,90.82 0,0 85.19,-41.84 206.97,-70.4 264.9,-62.14 442.09,-71.49 442.09,-71.49l-0.09 -564.63 312.6 -185.72 227.31 80.59 0.19 672.75c0,0 213.71,43.2 308.9,75.77 95.2,32.56 185.6,66.34 185.6,66.34z"/><polygon points="961.2,125.52 692.26,289.87 692.26,832.73 959.96,832.73 "/><circle cx="743.21" cy="554.07" r="19.74" class="fil1"/><path d="M170.33 1055.18c0,27.17 -8.79,50.23 -26.38,69.18 -2.64,2.88 -4.71,4.71 -6.19,5.51 -1.48,0.76 -4.04,1.2 -7.64,1.28 -7.43,0.24 -16.3,-0.24 -26.62,-1.4 -14.74,-1.63 -22.14,-4.11 -22.14,-7.39 0,-2.72 3.16,-5.31 9.48,-7.83 12.66,-5 23.42,-10.03 32.21,-15.11 22.9,-13.11 34.33,-24.38 34.33,-33.81 0,-7.96 -10.59,-19.59 -31.77,-34.9l17.02 -25.78c1.12,-1.71 2.24,-2.55 3.4,-2.55 1.12,0 2.64,0.84 4.6,2.55 4.67,4.16 8.91,10.28 12.67,18.39 4.67,10.23 7.03,20.86 7.03,31.86zm29.54 -111.19l-11.23 136.08c-0.56,6.72 -2.36,10.08 -5.4,10.08 -3.12,0 -5.2,-4.24 -6.19,-12.75l-13.35 -113.87 31.61 -22.94c1.4,-1 2.52,-1.52 3.28,-1.52 1.08,0 1.52,1.64 1.28,4.92zm-3.32 108.31c3.68,2.2 9.15,3.27 16.51,3.27 18.26,0 33.01,-5.23 44.24,-15.66l-18.14 -48.96 16.18 -17.79c1.16,-1.32 2.04,-2 2.68,-2 0.76,0 1.44,0.84 2,2.56l19.18 58.11c4.76,14.51 11.47,21.75 20.15,21.75l5.03 0 0 35.73 -8.91 0c-9.43,0 -17.07,-4.52 -22.94,-13.59 -2.64,-4.12 -6.2,-12.23 -10.63,-24.34 -5.8,10.75 -11.47,18.78 -17.11,23.98 -10.63,9.83 -23.86,14.79 -39.69,14.79 -3.03,0 -5.91,-0.28 -8.55,-0.84l0 -37.01zm173.06 37.01l-8.79 0c-13.27,0 -22.35,-7.52 -27.26,-22.58 -4.48,15.06 -12.99,22.58 -25.54,22.58l-10.07 0 0 -35.73 6.43 0c12.51,0 20.47,-8.2 23.9,-24.58 1.08,-5.24 3.04,-7.84 5.84,-7.84 2.84,0 4.55,2.6 5.15,7.84 1.88,16.38 9.08,24.58 21.55,24.58l8.79 0 0 35.73zm-38.17 29.61c0.92,0.88 0.88,1.8 -0.12,2.84l-13.59 13.55c-0.56,0.56 -1.04,0.84 -1.52,0.84 -0.48,0 -0.96,-0.28 -1.52,-0.84l-13.11 -13.11 -13.11 13.11c-0.95,0.96 -1.91,0.96 -2.95,0l-13.79 -13.35c-1.2,-1.08 -1.12,-2.28 0.24,-3.6l12.63 -12.91c1.16,-1.15 2.36,-1.15 3.52,0l12.87 12.91 12.86 -12.91c1.2,-1.15 2.36,-1.15 3.52,0l14.07 13.47zm107.71 -91.68c0,19.74 -4.95,34.97 -14.86,45.68 -10.32,10.91 -25.26,16.39 -44.85,16.39l-16.38 0 0 -35.73 27.17 0c6.24,0 12.83,-0.84 19.79,-2.56 9.67,-2.44 14.5,-5.84 14.5,-10.31 -11.55,1.24 -19.7,1.88 -24.45,1.88 -19.15,0 -28.7,-6.44 -28.7,-19.31 0,-9.67 2.36,-19.9 7.03,-30.69 6,-13.83 13.83,-20.71 23.54,-20.71 9.92,0 18.35,4.4 25.26,13.23 8,10.07 11.95,24.1 11.95,42.13zm-20.58 -13.79c-5.32,1.4 -10.99,2.08 -16.99,2.08 -10.31,0 -15.46,-2.04 -15.46,-6.2 0,-2.88 1.04,-5.27 3.11,-7.15 2.08,-1.84 4.56,-2.8 7.44,-2.8 8.43,0 15.71,4.68 21.9,14.07zm0.56 -68.07c1.04,1.04 1.04,2.04 0,3.08l-14.63 14.63c-1,1 -2.08,1 -3.16,0l-14.86 -14.43c-1.16,-1.08 -1.04,-2.32 0.36,-3.72l13.59 -13.95c1.15,-1.23 2.39,-1.23 3.71,0l14.99 14.39zm129.29 174.94c1,1.04 0.96,2.08 -0.12,3.2l-14.54 14.51c-1,1 -2.04,1 -3.16,0l-14.87 -14.43c-1.16,-1.08 -1.04,-2.32 0.36,-3.72l13.59 -13.83c1.16,-1.23 2.4,-1.23 3.76,0l14.98 14.27zm64.15 -138.17c7.88,4.08 14.19,12.35 18.87,24.82 1.8,4.32 2.68,9.2 2.68,14.67l0 0.92c0,28.5 -10.32,47.44 -30.9,56.79 -13.27,6.64 -34.17,9.96 -62.67,9.96l-87.45 0 -0.12 0.12 0 -35.25 0.12 -0.13 84.41 0.36c37.85,0 63.43,-3.63 76.7,-10.87 1.96,-1.79 2.92,-3.55 2.92,-5.27 0,-4 -7.03,-11.39 -21.18,-22.26l-0.36 -0.36 0 -0.32c10.79,-22.11 16.42,-33.18 16.98,-33.18zm73.58 73.03c0,27.17 -8.79,50.23 -26.38,69.18 -2.63,2.88 -4.71,4.71 -6.19,5.51 -1.48,0.76 -4.04,1.2 -7.63,1.28 -7.44,0.24 -16.31,-0.24 -26.62,-1.4 -14.75,-1.63 -22.14,-4.11 -22.14,-7.39 0,-2.72 3.15,-5.31 9.47,-7.83 12.67,-5 23.42,-10.03 32.21,-15.11 22.9,-13.11 34.33,-24.38 34.33,-33.81 0,-7.96 -10.59,-19.59 -31.77,-34.9l17.03 -25.78c1.12,-1.71 2.23,-2.55 3.39,-2.55 1.12,0 2.64,0.84 4.6,2.55 4.68,4.16 8.91,10.28 12.67,18.39 4.67,10.23 7.03,20.86 7.03,31.86zm25.94 -84.94l2.12 0c9.59,1.56 23.1,12.95 40.53,34.17 8.67,11.56 12.99,22.03 12.99,31.38 0,25.46 -6.12,41.37 -18.39,47.76 -7.03,4 -14.87,6 -23.54,6l-30.53 0c-3.08,0 -4.6,-0.32 -4.6,-0.96l0 -37.57 0.36 0c5.32,1.8 11.51,2.68 18.62,2.68l10.88 0c22.34,0 34.21,-2.8 35.61,-8.4l0 -0.36c-0.48,-6.19 -13.63,-17.42 -39.45,-33.73 -3.76,-1.88 -6.92,-3 -9.51,-3.4l-1.4 0 -7 2c-2.19,0 -3.47,-1.44 -3.87,-4.36 0,-2.47 4.31,-13.5 12.99,-33.13 1.32,-1.4 2.71,-2.08 4.19,-2.08zm277.9 73.18c0,11.79 -2.48,22.03 -7.4,30.78 -5.71,10.07 -13.47,15.11 -23.3,15.11 -8.59,0 -14.99,-4.16 -19.22,-12.51 -3.88,8.35 -9.43,12.51 -16.63,12.51 -4.75,0 -9.23,-0.76 -13.43,-2.36 0,23.74 -6.51,41.85 -19.46,54.28 -12.95,12.46 -31.34,18.7 -55.16,18.7 -18.7,0 -34.05,-3.6 -46,-10.79 -14.59,-8.83 -21.9,-21.94 -21.9,-39.33 0,-17.19 3.95,-31.65 11.83,-43.44 2.04,-3.04 4.19,-4.56 6.43,-4.56 1.8,0 2.72,1.16 2.72,3.48 0,1.35 -0.28,2.83 -0.84,4.47 -2.8,8.52 -4.2,16.07 -4.2,22.7 0,21.31 17.47,31.98 52.44,31.98 13.91,0 26.54,-1.6 37.93,-4.8 16.95,-4.75 25.42,-12.11 25.42,-22.02 0,-6.95 -9.71,-20.78 -29.14,-41.57l15.43 -25.85c1.24,-2.04 2.32,-3.08 3.16,-3.08 0.88,0 1.92,0.8 3.16,2.36 6.55,8.43 11.23,13.9 13.95,16.38 5.63,5.24 10.99,7.84 16.14,7.84 4,0 7.12,-3.76 9.39,-11.36 2.24,-7.55 4.12,-11.35 5.6,-11.35 2.36,0 3.92,1.96 4.72,5.88 1,5.2 2.43,8.83 4.31,10.87 3.6,4 9.84,5.96 18.75,5.96 7.39,0 11.11,-2.96 11.11,-8.88 0,-4.59 -3.72,-11.19 -11.11,-19.66l13.11 -17.95c0.84,-1.16 1.68,-1.76 2.44,-1.76 0.87,0 1.55,0.68 2.11,2 5.08,11.63 7.64,23.62 7.64,35.97zm52.48 11.76c0,27.17 -8.8,50.23 -26.38,69.18 -2.64,2.88 -4.72,4.71 -6.2,5.51 -1.48,0.76 -4.04,1.2 -7.63,1.28 -7.44,0.24 -16.31,-0.24 -26.62,-1.4 -14.75,-1.63 -22.14,-4.11 -22.14,-7.39 0,-2.72 3.15,-5.31 9.47,-7.83 12.67,-5 23.42,-10.03 32.21,-15.11 22.9,-13.11 34.34,-24.38 34.34,-33.81 0,-7.96 -10.6,-19.59 -31.78,-34.9l17.03 -25.78c1.12,-1.71 2.24,-2.55 3.4,-2.55 1.11,0 2.63,0.84 4.59,2.55 4.68,4.16 8.91,10.28 12.67,18.39 4.68,10.23 7.04,20.86 7.04,31.86zm43.04 34.13l-11.47 0c-20.66,0 -31.02,-16.67 -31.02,-50 0,-26.54 -1,-54.52 -2.95,-83.93l21.34 -14.99c2.64,-1.88 4.4,-2.84 5.27,-2.84 1.08,0 1.64,1.96 1.64,5.87l-0.12 93.45c0,11.15 4.68,16.71 14.03,16.71l3.28 0 0 35.73zm-4.56 -35.73l17.79 0 0.72 0.95 0.12 33.86 -0.6 0.92 -17.79 0 -0.72 -0.92 -0.12 -33.86 0.6 -0.95zm59.84 -15.07c0,12.07 -2.8,22.94 -8.4,32.53 -7.11,12.19 -17.22,18.27 -30.33,18.27l-8.68 0 0 -35.73 7.28 0c15.43,0 23.18,-2.24 23.18,-6.76 0,-4 -6.96,-12.39 -20.86,-25.18l16.74 -25.78c0.96,-1.4 2.04,-1.6 3.28,-0.56 6.39,5.2 11.19,12.91 14.43,23.06 2.24,7.16 3.36,13.87 3.36,20.15zm-60.04 102.2c-0.96,-0.92 -0.92,-1.92 0.08,-2.92l13.47 -13.35c0.96,-0.92 1.92,-0.92 2.96,0l13.55 13.23c1.04,1 0.92,2.16 -0.32,3.4l-12.55 12.74c-1,1.12 -2.08,1.12 -3.28,0l-13.91 -13.1zm-14.3 -24.59c-0.92,-0.91 -0.88,-1.91 0.12,-2.91l13.7 -13.59c1,-1.04 2,-1.04 2.92,0l13.11 13.23 13.11 -13.23c0.96,-0.96 1.92,-0.96 2.96,0l13.79 13.47c1.12,1.08 1.04,2.24 -0.24,3.51l-12.63 12.88c-1.16,1.15 -2.36,1.15 -3.52,0l-12.87 -12.88 -12.87 12.88c-1.2,1.15 -2.36,1.15 -3.52,0l-14.06 -13.36zm184.69 -118.86c0.92,0.88 0.87,1.8 -0.12,2.84l-13.71 13.67c-1,1.04 -2,1.04 -2.92,0l-13.11 -13.23 -13.15 13.23c-0.92,0.96 -1.88,0.96 -2.92,0l-13.91 -13.43c-1.11,-1.12 -1,-2.32 0.32,-3.64l12.67 -12.79c1.16,-1.16 2.32,-1.16 3.52,0l12.87 12.79 12.87 -12.79c1.16,-1.16 2.36,-1.16 3.52,0l14.07 13.35zm74.58 33.37c2.47,0.88 3.75,2.6 3.75,5.16l-2.36 10.31c0,5 6.56,7.48 19.67,7.48l0.48 0.47 0 34.22 -0.96 0.92 -3.72 0c-12.67,0 -21.54,-7.04 -26.7,-21.07l-0.48 0c-7.47,8.8 -20.14,15.23 -37.93,19.19 -10.95,1.88 -23.9,3.12 -38.88,3.76l0 -0.48 -0.48 0.48 -0.44 0 -84.78 -0.48 0 -0.44 0 -35.13 0 -0.61 5.6 0.12 48.72 0.49 25.3 0c30.22,0 54.08,-2.84 71.66,-8.44 2.04,-0.72 7.48,-5.23 16.39,-13.59 2.36,-1.56 4.08,-2.36 5.16,-2.36zm95.2 -55.47l-46.6 16.15c2.27,3.59 6.15,10.15 11.71,19.66 5.71,10.15 8.55,19.86 8.55,29.18 0,13.35 -3.48,24.54 -10.43,33.57 -7.95,10.39 -19.07,15.59 -33.37,15.59l-9.84 0 0 -35.73 3.76 0c21.78,0 32.65,-2.28 32.65,-6.88 0,-3.2 -9.95,-19.34 -29.85,-48.36 -0.16,-0.32 -0.24,-0.68 -0.24,-1.08 0,-1.96 1.28,-6.71 3.8,-14.27 2.55,-7.59 4.39,-11.55 5.59,-11.95l64.27 -22.14 0 26.26zm0 -26.26l0 26.26 0 -26.26zm59.11 140.41l-6.55 0c-2.08,0 -3.84,-0.72 -5.24,-2.12 0.28,25.62 -7.95,47.48 -24.74,65.59 -2.64,2.87 -4.64,4.67 -5.96,5.39 -1.63,0.92 -4.23,1.44 -7.83,1.52 -6.79,0.24 -15.67,-0.28 -26.58,-1.52 -14.75,-1.64 -22.14,-4.12 -22.14,-7.39 0,-2.72 3.16,-5.36 9.47,-7.84 12.67,-4.99 23.38,-10.03 32.22,-15.1 22.86,-13.11 34.29,-24.38 34.29,-33.86 0,-7.95 -10.55,-19.58 -31.73,-34.89l17.82 -26.46c1.24,-1.88 2.2,-2.79 2.92,-2.79 0.72,0 1.88,0.91 3.52,2.79 11.95,13.99 21.02,20.95 27.25,20.95l3.28 0 0 35.73zm-2.08 -35.73l20.83 0 0.72 0.95 0.23 33.86 -0.71 0.92 -20.83 0 -0.72 -0.92 -0.24 -33.86 0.72 -0.95zm137.33 -10.16c0,11.71 -2.48,21.95 -7.47,30.78 -5.72,10.07 -13.43,15.11 -23.18,15.11 -9.51,0 -16.31,-4.84 -20.39,-14.51 -1.71,4.12 -4.59,7.59 -8.67,10.35 -4.04,2.76 -8.31,4.16 -12.75,4.16 -9.43,0 -16.79,-4.52 -22.02,-13.59 -1.4,3.51 -4,6.67 -7.8,9.43 -3.75,2.76 -7.55,4.16 -11.27,4.16l-12.07 0 0 -35.73 12.19 0c5.92,0 10.08,-1.92 12.39,-5.84 0.96,-1.56 2.04,-5.15 3.28,-10.79 0.88,-3.88 2.48,-5.84 4.8,-5.84 2.28,0 3.75,1.96 4.47,5.84 1,5.87 1.96,9.51 2.8,10.91 2.36,3.92 6.72,5.84 13.11,5.84 5.56,0 9.47,-1.96 11.83,-5.96 0.84,-1.4 1.92,-5.04 3.16,-10.87 0.88,-3.92 2.4,-5.88 4.55,-5.88 2.6,0 4.2,1.96 4.8,5.88 0.88,5.2 2.16,8.83 3.88,10.87 3.43,4 9.79,5.96 19.1,5.96 7.4,0 11.11,-2.96 11.11,-8.88 0,-4.59 -3.71,-11.19 -11.11,-19.66l12.99 -17.95c0.84,-1.16 1.72,-1.76 2.56,-1.76 0.8,0 1.44,0.68 2,2 5.15,11.79 7.71,23.78 7.71,35.97zm-53.83 -74.5c0.99,0.96 0.99,1.92 0,2.96l-13.47 13.35c-0.92,0.92 -1.92,0.92 -2.92,0l-13.59 -13.15c-1.08,-1.08 -1.04,-2.24 0.24,-3.48l12.51 -12.79c1.12,-1.07 2.24,-1.07 3.4,0l13.83 13.11zm14.26 22.27c0.96,0.92 0.92,1.92 -0.12,2.91l-13.54 13.59c-0.96,0.92 -1.92,0.92 -2.96,0l-13.23 -13.23 -12.99 13.23c-1,1 -2.04,1 -3.04,0l-13.83 -13.47c-1.08,-1 -0.96,-2.19 0.36,-3.51l12.63 -12.87c1.2,-1.16 2.36,-1.16 3.52,0l12.87 12.87 12.91 -12.87c1.08,-1.08 2.2,-1.08 3.4,0l14.02 13.35z" class="fil2"/></g></svg>
                        </div>
                        <div style="width: 6cm; line-height: 17px;">${message ?? ""}</div>
                <div style="width: 1.5cm;height: auto;float: left;padding-right: 5px;">${qrcode_svg}</div>
                    </div>
                </td>
            </tr> 
            `
            : ""
        }
            
            <tr class="tr-title text-xs text-left h-1">
                <td colspan="3">
                    نمایندگی / مشتری ${vahed ? " / واحد" : ""}
                </td>
                <td colspan="2">
                    تاریخ سفارش
                </td>
            </tr>
            <tr>
                <td style="width: 6.5cm;" colspan="3">
                    <strong>${customer.join(" / ")}</strong>
                </td>
                <td style="width: 3cm;" colspan="2">
                    <strong style="font-size:20px">${delivery_date ?? ""
        }</strong>
                </td>
            </tr>
            <tr class="tr-title text-xs text-left h-1">
                <td colspan="2" style="width:3cm"> 
                <span>سریال </span>
                <span style="float: left; font-weight: 900;font-size: 11px;">${serial ?? ""
        }</span>
                </td>
                <td>
                    مدل
                </td>
                <td style="width: 1.5cm;padding: 0" colspan="2" rowspan="2" class="interal-border">
                    <table class="w-full" style="height:100%">
                        <tbody>
                           
                            <tr class="tr-title">
                                <td style="width:1.25cm;line-height: 9px;">عرض</td>
                                <td style="width:1.25cm;line-height: 9px;">قد</td>
                                <td style="writing-mode: vertical-lr;text-align: center;transform: rotate(180deg);width: 0.5cm;line-height: 0px; padding:0" rowspan="2">
                                اندازه
                                </td>
                            </tr>
                            <tr>
                                <td class="td-value"><strong style="font-size:20px">${width ?? ""
        }</strong></td>
                                <td class="td-value"><strong style="font-size:20px">${height ?? ""
        }</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <strong style="font-size: 18px;">${code ?? ""}</strong>
                </td>
                <td style="width: 4cm;">
                    <strong style="font-size: 15px;">${model ?? ""}</strong>
                </td>
                
            </tr>
            <tr class="tr-title text-xs text-left h-1">
                <td>
                   ${astaneh == "دارد" ? "آستانه" : ""}
                </td>
                <td>
                    قابلبه
                </td>   
                <td>
                رنگ
                </td>
                <td style="width: 1.5cm;padding: 0" colspan="2" rowspan="2" class="interal-border">
                    <table class="w-full" style="height:100%">
                        <tbody>
                      
                            <tr class="tr-title">
                                <td style="width:1.25cm;line-height: 9px;">عرض</td>
                                <td style="width:1.25cm;line-height: 9px;">قد</td>
                                <td style="writing-mode: vertical-lr;text-align: center;transform: rotate(180deg);width: 0.5cm;line-height:  0px; padding:0;height:37px" rowspan="2">
                                فارسی بر
                                </td>
                            </tr>
                            <tr>
                                <td class="td-value"><strong style="font-size:20px">${Math.ceil((width - 8.3) * 10) / 10 ?? ""
        }</strong></td>
                                <td class="td-value"><strong style="font-size:20px">${Math.ceil((height - (astaneh == "دارد" ? 8.3 : 4)) * 10) / 10 ?? ""
        }</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="width: 50px;">
                    <strong style="font-size:20px">
                    ${astaneh == "دارد" ? astaneh : ""}
                    </strong>
                </td>
                <td>
                    <strong style="font-size:20px">${gablabeh ?? ""}</strong>
                </td>
                <td>
                <strong style="font-size: 16px;">${color ?? ""}</strong>
                </td>
               
            </tr>
            <tr class="tr-title text-xs text-left h-1">
                <td colspan="2">
                   ${mokamel ? "مکمل" : ""}
                </td>
                <td>
                    پرواز
                </td>   
               
                <td colspan="2">
                    نوع جنس
                </td>
            </tr>
            <tr>
                <td  colspan="2">
                    <strong style="font-size:20px">${mokamel ? mokamel + " سانتی" : ""
        }</strong>
                </td>
                <td>
                    <strong style="font-size:20px">${parvaz ? parvaz + " شاخه" : ""
        }</strong>
                </td>
                
                <td colspan="2">
                    <strong style="font-size:20px">${type ?? ""}</strong>
                </td>
            </tr>
            
            <tr>
                <td colspan="5" style="border:0;font-size: 12px;line-height: 12px;">
                    ${description ?? ""}
                </td>
            </tr>
        </tbody>
    </table>
</div></section>`;
}



async function labelMaghta({
    has_logo,
    message,
    serial,
    code,
    qrcode,
    party_name,
    customer_name,
    delivery_date,
    color,
    model,
    height,
    width,
    widthFloor,
    type,
    thickness,
    thickness_wall,
    gablabeh,
    vahed,
    description,
}) {
    const qrCode = new QRCodeStyling({
        type: "svg",
        data: qrcode + "",
    });
    const raw = await qrCode.getRawData("svg");
    const qr = URL.createObjectURL(raw);

    const qrcode_svg = await QRCode.toString(`${qrcode}`, {
        type: "svg",
        margin: 0,
    });

    let customer = [];
    if (party_name) customer.push(party_name);
    if (customer_name) customer.push(customer_name);
    if (vahed) customer.push(vahed);

    return `<section><div style="width:10.16cm;padding:10px">
    <table class="asd w-full text-center">
        <tbody>
            ${has_logo
            ? `
            <tr style="height:50px">
                <td colspan="5" style="border:0">
                    <div class="flex">
                        <div style="width: 2.4cm;font-size: 9px;">
                        <svg viewBox="0 0 99 40" height="40" width="100" xmlns="http://www.w3.org/2000/svg" style="margin: 0 auto;"><path xmlns="http://www.w3.org/2000/svg" d="M61.54 4.926 54.07 9.37v.457c.012 1.66.075 20.223.075 20.223a10.53 10.53 0 0 1-.497.02c-3.18.074-7.136.347-10.71.73a117.17 117.17 0 0 0-9.145 1.324c-7.406 1.375-16.879 3.543-26.785 6.133-2.203.578-5.317 1.414-5.29 1.422.005 0 .184-.043.395-.098a312.34 312.34 0 0 1 4.68-1.16c9.777-2.36 19.402-4.27 26.906-5.344 4.239-.605 8.82-1.012 13.758-1.215 1.32-.054 2.031-.078 3.844-.117a207.468 207.468 0 0 1 5.094 0c0-.004.011-4.637.02-10.297.007-5.66.019-10.449.023-10.64l.003-.352 12.743-7.57 4.632 1.64 4.633 1.645v13.71c0 7.544.004 13.712.008 13.712.043 0 .95.168 1.555.289 3.195.637 7.652 1.765 10.59 2.676a124.308 124.308 0 0 1 8.156 2.882c.008-.007-.43-.296-.793-.523-2.172-1.352-4.707-2.574-7.02-3.379-.808-.281-2.925-.953-4.605-1.46a181.433 181.433 0 0 0-5.766-1.645l-.039-.012V18.77L80.531 5.12l-5.746-2.324c-3.16-1.277-5.754-2.32-5.762-2.32-.007 0-3.375 2.003-7.484 4.449Zm0 0"></path><path xmlns="http://www.w3.org/2000/svg" d="M63.508 8.008c-3.051 1.805-5.551 3.281-5.555 3.285-.008.004-.133 20.285-.125 20.293.008.004 11.11.805 11.113.8.008-.003.121-26.074.118-26.94v-.723ZM59.316 20.66c.094.024.153.063.246.16.137.137.22.301.27.54.023.1.023.14.023.304 0 .164 0 .203-.023.3-.05.243-.133.407-.27.544-.101.105-.175.148-.285.168-.254.043-.515-.172-.644-.528a1.245 1.245 0 0 1-.07-.484c0-.184.003-.223.023-.312.11-.485.414-.774.73-.692Zm0 0"></path></svg>
                        شرکت پارس درب فیدار
                        </div>
                        <div style="width: 5.3cm">${message ?? ""}</div>
                <div style="width: 1.7cm;height: auto;float: left;padding-right: 5px;">${qrcode_svg}</div>
                    </div>
                </td>
                
            </tr>`
            : ""
        }
            
            <tr class="tr-title text-xs text-left h-1">
                <td colspan="3">
                    نمایندگی / مشتری ${vahed ? " / واحد" : ""}
                </td>
                <td colspan="2">
                    تاریخ سفارش
                </td>
            </tr>
            <tr>
                <td colspan="3" style="width: 6.5cm;">
                    <strong>${customer.join(" / ")}</strong>
                </td>
                <td colspan="2" style="width: 3cm;">
                    <strong style="font-size:20px">${delivery_date ?? ""
        }</strong>
                </td>
            </tr>
            <tr class="tr-title text-xs text-left h-1">
                <td colspan="2" style="width:3cm">
                    <span>سریال </span>
                    <span style="float: left; font-weight: 900;font-size: 11px;">${serial ?? ""
        }</span>
                    </td>
                <td >
                    مدل
                </td>
                <td>
                    عرض
                </td>
                <td>
                قد
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <strong style="font-size: 20px;">${code ?? ""}</strong>
                </td>
                <td style="width: 4cm;">
                    <strong>${model ?? ""}</strong>
                </td>
                <td>
                    <strong style="font-size:20px">${width ?? ""}</strong>
                </td>
                <td>
                    <strong style="font-size:20px">${height ?? ""}</strong>
                </td>
            </tr>
            <tr class="tr-title text-xs text-left h-1">
                <td colspan="2">
                    ضخامت
                </td>
                
                <td >
                    رنگ
                </td>
                <td colspan="2">
                    نوع جنس
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <strong style="font-size:20px">${thickness ?? ""}</strong>
                </td>
               
                <td>
                    <strong style="font-size: 16px;">${color ?? ""}</strong>
                </td>
                <td colspan="2">
                    <strong style="font-size:20px">${type ?? ""}</strong>
                </td>
            </tr>
            <tr class="tr-title text-xs text-left h-1">
                <td colspan="2">
                    قطر مقطع
                </td>
       

                <td>
               کف چهارچوب
                </td>
   
                <td colspan="2">
                    قابلبه چهارچوب
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <strong style="font-size:20px">${thickness_wall ?? ""
        }</strong>
                </td>
                
                <td>
                    <strong style="font-size:20px">
                    ${widthFloor}</strong>
                </td>
            
                <td  colspan="2">
                <strong style="font-size:18px">${gablabeh ?? ""}</strong>
            </td>
            </tr>
            <tr>
            <td colspan="5" style="border:0;font-size: 12px;">
                    ${description ?? ""}
                </td>
            </tr>
        </tbody>
    </table>
</div></section>`;
}




const cellTable = ({ label, value }) => {
    return `<div class="flex items-center"><div class="text-vertical" style="
    width: 10px;
    border-right: 0.1px solid;
    line-height: 12px;
    padding: 2px 0;
    font-size: 9px;
">${label}</div><div class="flex-auto text-bold">${value}</div></div>`
}


async function label4444({
    has_logo,
    message,
    serial,
    code,
    qrcode,
    party_name,
    customer_name,
    delivery_date,
    color,
    model,
    height,
    width,
    type,
    align,
    lock,
    type_label,
    thickness,
    thickness_wall,
    barjestegi,
    gablabeh,
    align_gablabeh,
    vahed,
    description,
}) {
    const qrCode = new QRCodeStyling({
        type: "svg",
        data: qrcode + "",
    });
    const raw = await qrCode.getRawData("svg");
    const qr = URL.createObjectURL(raw);

    const qrcode_svg = await QRCode.toString(`${qrcode}`, {
        type: "svg",
        margin: 0,
    });

    let customer = [];
    if (party_name) customer.push(party_name);
    if (customer_name) customer.push(customer_name);
    if (vahed) customer.push(vahed);

    return `<section><div style="width:10.16cm;padding:10px">
    <table class="asd w-full text-center">
        <tbody>
            ${has_logo
            ? `
            <tr style="height:50px">
                <td colspan="5" style="border:0">
                    <div class="flex">
                        <div style="width: 2.4cm;font-size: 9px;">
                        <svg viewBox="0 0 99 40" height="40" width="100" xmlns="http://www.w3.org/2000/svg" style="margin: 0 auto;"><path xmlns="http://www.w3.org/2000/svg" d="M61.54 4.926 54.07 9.37v.457c.012 1.66.075 20.223.075 20.223a10.53 10.53 0 0 1-.497.02c-3.18.074-7.136.347-10.71.73a117.17 117.17 0 0 0-9.145 1.324c-7.406 1.375-16.879 3.543-26.785 6.133-2.203.578-5.317 1.414-5.29 1.422.005 0 .184-.043.395-.098a312.34 312.34 0 0 1 4.68-1.16c9.777-2.36 19.402-4.27 26.906-5.344 4.239-.605 8.82-1.012 13.758-1.215 1.32-.054 2.031-.078 3.844-.117a207.468 207.468 0 0 1 5.094 0c0-.004.011-4.637.02-10.297.007-5.66.019-10.449.023-10.64l.003-.352 12.743-7.57 4.632 1.64 4.633 1.645v13.71c0 7.544.004 13.712.008 13.712.043 0 .95.168 1.555.289 3.195.637 7.652 1.765 10.59 2.676a124.308 124.308 0 0 1 8.156 2.882c.008-.007-.43-.296-.793-.523-2.172-1.352-4.707-2.574-7.02-3.379-.808-.281-2.925-.953-4.605-1.46a181.433 181.433 0 0 0-5.766-1.645l-.039-.012V18.77L80.531 5.12l-5.746-2.324c-3.16-1.277-5.754-2.32-5.762-2.32-.007 0-3.375 2.003-7.484 4.449Zm0 0"></path><path xmlns="http://www.w3.org/2000/svg" d="M63.508 8.008c-3.051 1.805-5.551 3.281-5.555 3.285-.008.004-.133 20.285-.125 20.293.008.004 11.11.805 11.113.8.008-.003.121-26.074.118-26.94v-.723ZM59.316 20.66c.094.024.153.063.246.16.137.137.22.301.27.54.023.1.023.14.023.304 0 .164 0 .203-.023.3-.05.243-.133.407-.27.544-.101.105-.175.148-.285.168-.254.043-.515-.172-.644-.528a1.245 1.245 0 0 1-.07-.484c0-.184.003-.223.023-.312.11-.485.414-.774.73-.692Zm0 0"></path></svg>
                        شرکت پارس درب فیدار
                        </div>
                        <div style="width: 5.3cm">${message ?? ""}</div>
                <div style="width: 1.7cm;height: auto;float: left;padding-right: 5px;">${qrcode_svg}</div>
                    </div>
                </td>
                
            </tr>`
            : ""
        }
            
           <!-- <tr class="tr-title text-xs text-left h-1">
                <td colspan="3">
                    نمایندگی / مشتری ${vahed ? " / واحد" : ""}
                </td>
                <td colspan="2">
                    تاریخ سفارش
                </td>
            </tr> -->
            <tr>
                <td colspan="3" style="width: 6.5cm;">
                    ${cellTable({ label: 'نمایندگی', value: customer.join(" / ") })}
                </td>
                <td colspan="2" style="width: 3cm;">
                ${cellTable({ label: 'ت تحویل', value: delivery_date ?? '' })}
                </td>
            </tr>
            <!--   <tr class="tr-title text-xs text-left h-1">
                <td colspan="2" style="width:3cm">
                    <span>سریال </span>
                    <span style="float: left; font-weight: 900;font-size: 11px;">${serial ?? ""
        }</span>
                    </td>
                <td>
                    مدل
                </td>
                <td>
                    عرض
                </td>
                <td>
                قد
                </td>
            </tr> -->
            <tr>
                <td colspan="2" class="font-sm">
                    ${cellTable({ label: 'سریال', value: `<span style="font-size:10px">${(serial ?? '')}<br><span style="font-size:14px">${code ?? ''}</span>` })}
                </td>
                <td style="width: 4cm;">
                    ${cellTable({ label: 'مدل', value: model ?? '' })}
                </td>
                <td>
                    ${cellTable({ label: 'عرض', value: width ?? '' })}
                </td>
                <td>
                    ${cellTable({ label: 'قد', value: height ?? '' })}
                </td>
            </tr>
            <!--  <tr class="tr-title text-xs text-left h-1">
                <td>
                    ضخامت
                </td>
                <td>
                    نوع لبه
                </td>   
                <td>
                    رنگ
                </td>
                <td colspan="2">
                    نوع جنس
                </td>
            </tr> -->
            <tr>
                <td>
                    ${cellTable({ label: 'ضخامت', value: thickness ?? '' })}
                </td>
                <td>
                    ${cellTable({ label: 'لبه', value: type_label ?? '' })}
                </td>
                <td>
                    ${cellTable({ label: 'رنگ', value: color ?? '' })}
                </td>
                <td colspan="2">
                    ${cellTable({ label: 'جنس', value: type ?? '' })}

                </td>
            </tr>
            <!--   <tr class="tr-title text-xs text-left h-1">
                <td>
                    قطر درب
                </td>
                <td>
                ${barjestegi == "دارد" ? "برجستگی" : ""}

                    
                </td>
                <td>
                ${gablabeh == "دارد" ? "قابلبه" : ""}
                </td>
                <td>
                ${lock == "دارد" ? "جای قفل" : ""}
                </td>
                <td>
                    بازشو
                </td>
            </tr> -->
            <tr>
                <td>
                ${cellTable({ label: 'قطر درب', value: thickness_wall ?? '' })}
                </td>
                <td>
                    ${barjestegi !== "دارد" ? "" : cellTable({ label: "برجستگی", value: barjestegi == "دارد" ? barjestegi : "" })}
                </td>
                <td>
                    ${gablabeh !== "دارد" ? "" : cellTable({ label: "قابلبه", value: align_gablabeh ?? gablabeh })}

                </td>
                <td >
                    ${lock !== "دارد" ? "" : cellTable({ label: 'جای قفل', value: lock == "دارد" ? lock : "" })}
                </td>
                <td>
                    ${lock !== "دارد" ? "" : cellTable({ label: 'بازشو', value: align ?? "" })}
                </td>
            </tr>
            <tr>
            <td colspan="5" style="border:0;font-size: 12px;">
                    ${description ?? ""}
                </td>
            </tr>
        </tbody>
    </table>
</div></section>`;
}
