<template>
  <!-- {{ hidden_attributes }} -->
  <!-- {{ conditions }} -->
  <!-- <pre v-if="good" dir="ltr">{{ good.default_attribute }}</pre> -->
  <!-- <pre dir="ltr">hidden_attribute_items{{ hidden_attribute_items }}</pre> -->
  <!-- <pre dir="ltr">hidden_attributes{{ hidden_attributes }}</pre> -->
  <!-- <pre dir="ltr">{{ form.attributes }}</pre> -->
  <!-- {{ good.default_attribute }} -->
  <j-form @submit="submit" class="w-full" ref="ref_form">
    <div class="grid grid-cols-1 gap-3">
      <div class="flex gap-2">
        <select-product ref="ref_good" v-model:value="form.good_id" url="/good/good/search" dense hide-bottom-space
          autofocus @update:model-value="(res) => selectGood(res, true)" label="نام کالا"
          :params="{ type: 'PRODUCT', is_active: true }" outlined required size="sm" search-local sort-by="name"
          :disable="is_edit" class="flex-1" />
        <!-- <j-input v-if="form.good_id && ![12, 15, 21, 22, 29, 30].includes(good?.group_id)" v-model="form.count" dense hide-bottom-space
          label="تعداد" type="number" step="1" outlined required :min="1" class="w-20 text-center" /> -->
      </div>
      <template v-for="(groupAttribute, index) in groupAttributes" :key="index">

        <fieldset v-if="form.good_id &&
          (groupAttribute.conditions == undefined ||
            (groupAttribute.conditions && groupAttribute.conditions(good))) &&
          good_attributes.filter(
            (f) =>
              f.group_name &&
              f.group_name.includes(groupAttribute.key) &&
              !hidden_attributes[f.key] &&
              (f.pivot?.showing ?? f.showing)
          ).length > 0
          " class="border-gray-300 border rounded-md">
          <legend>
            {{
              typeof groupAttribute.label == "function"
              ? groupAttribute.label(good)
              : groupAttribute.label
            }}
          </legend>
          <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            <template v-for="(attribute, key) in good_attributes.filter(
              (f) =>
                (!f.group_name ||
                  (f.group_name &&
                    f.group_name.includes(groupAttribute.key))) &&
                !hidden_attributes[f.key] &&
                (f.pivot?.showing ?? f.showing)
            )" :key="key">
              <j-select v-if="attribute.type == 'SELECT'" v-model="form.attributes[attribute.key]" dense :bg-color="good.default_attribute &&
                  good?.default_attribute[attribute.key] !== undefined &&
                  good.default_attribute[attribute.key] !==
                  form.attributes[attribute.key]
                  ? 'amber-2'
                  : ''
                " :options="attribute.items.filter(
    (f) =>
      !hidden_attribute_items[attribute.key] ||
      (hidden_attribute_items[attribute.key] &&
        hidden_attribute_items[attribute.key].includes(f.key))
  )
    " option-label="name" option-value="key" search-local hide-bottom-space :label="attribute.name" outlined
                :required="attribute.pivot.required ?? attribute.required"
                @update:model-value="(val) => change(attribute.key, val)" />
              <j-select-image v-if="attribute.type == 'SELECT_IMAGE'" v-model:value="form.attributes[attribute.key]"
                :image-key="(row) => row.data.image" dense :bg-color="good.default_attribute &&
                    good?.default_attribute[attribute.key] !== undefined &&
                    good.default_attribute[attribute.key] !==
                    form.attributes[attribute.key]
                    ? 'amber-2'
                    : ''
                  " :options="attribute.items.filter((f) => !f.hidden)" option-label="name" option-value="key"
                search-local hide-bottom-space :label="attribute.name" outlined
                :required="attribute.pivot.required ?? attribute.required"
                @update:model-value="(val) => change(attribute.key, val)" />
              <j-input v-else-if="attribute.type == 'INPUT'" v-model="form.attributes[attribute.key]" dense
                hide-bottom-space :label="attribute.name" outlined
                :required="attribute.pivot.required ?? attribute.required" :disable="(is_edit && false) ||
                  disable_attributes.includes(attribute.key)
                  " @update:model-value="(val) => change(attribute.key, val)" :bg-color="good.default_attribute &&
      good?.default_attribute[attribute.key] !== undefined &&
      good.default_attribute[attribute.key] !==
      form.attributes[attribute.key]
      ? 'amber-2'
      : ''
    " />
              <j-input v-else-if="attribute.type == 'NUMBER'" v-model="form.attributes[attribute.key]" dense
                hide-bottom-space :label="attribute.name" type="number" step="0.1" min="0" outlined
                :required="attribute.pivot.required ?? attribute.required" :disable="(is_edit && false) ||
                  disable_attributes.includes(attribute.key)
                  " @update:model-value="(val) => change(attribute.key, val)" :bg-color="good.default_attribute &&
      good?.default_attribute[attribute.key] !== undefined &&
      good.default_attribute[attribute.key] !==
      form.attributes[attribute.key]
      ? 'amber-2'
      : ''
    " />
              <j-toggle v-else-if="attribute.type == 'SWITCH'" v-model="form.attributes[attribute.key]" dense
                hide-bottom-space :label="attribute.name" outlined
                :required="attribute.pivot.required ?? attribute.required" :disable="(is_edit && false) ||
                  disable_attributes.includes(attribute.key)
                  " @update:model-value="(val) => change(attribute.key, val)" :class="good.default_attribute &&
      good?.default_attribute[attribute.key] !== undefined &&
      good.default_attribute[attribute.key] !==
      form.attributes[attribute.key]
      ? 'bg-amber-2'
      : ''
    " />

              <j-field v-else-if="attribute.type == 'FILE'" v-model="form.attributes[attribute.key]" required
                class="sm:col-span-2">
                <template v-slot:control>
                  <j-upload v-model:value="form.attributes[attribute.key]" :label="attribute.name" auto-upload
                    accept="image/*" url="/api/upload-file" field-name="file" class="col-span-full" style="width: 100%"
                    flat bordered dense />
                </template>
              </j-field>
            </template>
          </div>
        </fieldset>
      </template>
    </div>

    <br />
    <j-input v-model="form.description" label="توضیحات کالا" type="textarea" dense class="col-span-full" />

    <j-btn size="md" :color="is_edit ? 'secondary' : 'primary'" type="submit" class="full-width text-white mt-4"
      :label="is_edit ? 'ویرایش' : 'افزودن'" @shortkey="ref_form.submit()" v-shortkey="['ctrl', 'space']" />
  </j-form>
</template>

<script>
import { api } from "@/boot/axios";
import { checkPermission } from "@/helpers";
import { onMounted, ref, watch } from "vue";
import { conditionCompute } from "./index";
import SelectProduct from "./SelectProduct.vue";
import {
  dakheliItems,
  parvazItems,
} from "@/Modules/Production/computeProductionFormula/rokoob";
import {
  doCenterLayerThickness,
  doDoorThickness,
  doZevardamageh,
} from "@/Modules/Production/computeProductionFormula/door";
import { doTypeDoor } from "@/Modules/Production/computeProductionFormula";
import {
  doMokammelWidth,
  doThicknessEdgeOfFrame,
  doWidthFloor,
} from "@/Modules/Production/computeProductionFormula/frame";
export default {
  components: { SelectProduct },
  setup(props, context) {
    const good = ref({});
    const is_edit = ref(false);
    const form = ref({
      attributes: {},
      count: 1,
    });
    const ref_good = ref(null);
    const ref_form = ref(null);

    const conditions = ref([]);

    const onEdit = (value) => {
      is_edit.value = true;
      good.value = {};
      selectGood(value.good_id);
      form.value = JSON.parse(JSON.stringify(value));
      ref_good.value.focus();
    };

    const onCopy = (value) => {
      const temp = JSON.parse(JSON.stringify(value));
      delete temp.id;
      onEdit(temp);
      is_edit.value = false;
      ref_good.value.focus();
    };
    const good_attributes = ref([]);
    const disable_attributes = ref([]);
    const hidden_attributes = ref({});
    const computeCondition = (attribute_id = null) => {
      good_attributes.value = JSON.parse(JSON.stringify(good.value.attributes));
    };

    const selectGood = (value, default_attribute = false) => {
      form.value.attributes = {};
      if (!value) {
        good.value = {};
        return;
      }
      api.get(`good/good/${value}/selectForOrder`).then((res) => {

        good.value = res.result;

        computeCondition();
        defaultAttribute();
      });
    };

    let old_attributes = {};
    watch(
      () => form.value.attributes,
      (newVal, oldValue) => {
        if (!good.value.attributes) return;
        Object.keys(old_attributes).forEach((old_attr) => {
          if (form.value.attributes[old_attr] !== old_attributes[old_attr]) {
            computeCondition(old_attr);
          }
        });
        old_attributes = Object.assign({}, newVal);

        //
      },
      {
        deep: true,
      }
    );

    const reset = () => {
      is_edit.value = false;
      form.value = {
        attributes: {},
        count: 1,
      };
      good.value = {};
      ref_good.value.focus();
    };

    const groupAttributes = [
      {
        key: "byDoor",
        conditions: (good) => good && ["door"].includes(good?.group?.key),
        label: "همراه با",
      },
      {
        key: "frame",
        conditions: (good) =>
          good &&
          (form.value.attributes["hasFrame"] ||
            ["frame"].includes(good?.group?.key)),
        label: "چهارچوب",
      },
      {
        key: "rokoob",
        conditions: (good) => {
          return (
            good &&
            (form.value.attributes["hasRokoob"] ||
              ["rokoob"].includes(good?.group?.key))
          );
        },
        label: "روکوب",
      },
      {
        key: "door",
        conditions: (good) => good && ["door"].includes(good?.group?.key),
        label: (good) => good?.group?.name,
      },

      {
        key: "maghta",
        conditions: (good) => good && ["maghta"].includes(good?.group?.key),
        label: (good) => good?.group?.name,
      },
      {
        key: "zehvar",
        conditions: (good) => {
          return (
            good &&
            ["zehvar"].includes(good?.group?.key)
          );
        },
        label: "زهوار",
      },
      {
        key: "",
        conditions: (good) =>
          good && ["other", null].includes(good?.group?.key),
        label: (good) => good?.group?.name,
      },
      {
        key: "public",
        label: "سایر",
      },
      {
        key: "production",
        label: "تولید",
      },
    ];


    const defaultAttribute = () => {
      groupAttributes.forEach((groupAttribute) => {
        if (
          groupAttribute.conditions == undefined ||
          (groupAttribute.conditions && groupAttribute.conditions(good.value))
        ) {
          good_attributes.value
            .forEach((f) => {
              if (f.key == 'hasBarjestegi' && !good.value.default_attribute[f.key]) {
                hidden_attributes.value.hasBarjestegi = true;

              }
              if (
                good.value.default_attribute[f.key] !== undefined &&
                form.value.attributes[f.key] === undefined
              ) {
                form.value.attributes[f.key] =
                  good.value.default_attribute[f.key];
              }
            });
        }
      });


      if (form.value.attributes["pvcColor"] !== 'two_color' && good.value.attributes?.findIndex(f => f.key == "coverFrontDoor") >= 0) {
        hidden_attributes.value['coverFrontDoor'] = true;
        hidden_attributes.value['coverBackDoor'] = true;
      }
      if (form.value.attributes["pvcColor"] !== 'two_color' && good.value.attributes?.findIndex(f => f.key == "coverMiddleSheet") >= 0) {
        hidden_attributes.value['coverMiddleSheet'] = true;
        hidden_attributes.value['coverBaghalBazoo'] = true;
      }
    };

    const defaultAttributeSecond = () => {
      if (form.value.attributes["hasFrame"])
        form.value.attributes["typeMaterialFrame"] =
          form.value.attributes["typeMaterialDoor"];
      if (form.value.attributes["hasRokoob"])
        form.value.attributes["typeMaterialRokoob"] =
          form.value.attributes["typeMaterialDoor"];
    };

    const hidden_attribute_items = ref({});
    const oldFormAttribute = ref({});


    watch(
      () => form.value.attributes,
      (newVal) => {
        Object.keys(newVal).forEach((key) => {

          if (oldFormAttribute.value[key] + '' == newVal[key] + '') return;
          let group_key = "";

          switch (key) {
            case "hasFrame":
              group_key = "frame";
              break;
            case "hasRokoob":
              group_key = "rokoob";
              break;
          }
          if (group_key) {
            if (newVal[key]) {
              good_attributes.value
                .filter((f) => f.group_name && f.group_name.includes(group_key))
                .forEach((f) => {
                  if (
                    good.value?.default_attribute &&
                    good.value?.default_attribute[f.key]
                  ) {
                    form.value.attributes[f.key] =
                      good.value.default_attribute[f.key];
                  }
                });
            } else {
              good_attributes.value
                .filter((f) => f.group_name && f.group_name == group_key)
                .forEach((f) => {
                  delete form.value.attributes[f.key];
                });
            }
            //  defaultAttributeSecond();
          }


          if (form.value.attributes["hasEdgeOfDoor"]) {
            delete hidden_attributes.value.alignEdgeOfDoor;
          } else {
            delete form.value.attributes.alignEdgeOfDoor;
            hidden_attributes.value.alignEdgeOfDoor = true;
          }

          if (form.value.attributes["countZevardamageh"]) {
            delete hidden_attributes.value.typeZehvar;
          } else {
            delete form.value.attributes.typeZehvar;
            hidden_attributes.value.typeZehvar = true;
          }

          if (form.value.attributes["hasParvaz"]) {

            delete hidden_attributes.value.typeMaterialRokoob;
            delete hidden_attributes.value.typeParvaz;
            delete hidden_attributes.value.countParvaz;
            delete hidden_attributes.value.widthParvaz;
            delete hidden_attributes.value.perLengthParvaz;

          } else if (form.value.attributes["hasParvaz"] === false) {

            delete form.value.attributes.typeMaterialRokoob;
            delete form.value.attributes.typeParvaz;
            delete form.value.attributes.countParvaz;
            delete form.value.attributes.widthParvaz;
            delete form.value.attributes.perLengthParvaz;

            hidden_attributes.value.typeMaterialRokoob = true;
            hidden_attributes.value.typeParvaz = true;
            hidden_attributes.value.countParvaz = true;
            hidden_attributes.value.widthParvaz = true;
            hidden_attributes.value.perLengthParvaz = true;
          }


          if (form.value.attributes["hasBarjestegi"]) {
            hidden_attributes.value[65] = false;
            hidden_attributes.value[66] = false;
            hidden_attributes.value[67] = false;
            hidden_attributes.value[68] = false;
            hidden_attributes.value[69] = false;
            hidden_attributes.value[70] = false;
            hidden_attributes.value[71] = false;
            hidden_attributes.value[75] = false;
            hidden_attributes.value[76] = false;
            hidden_attributes.value[77] = false;
          } else {
            delete form.value.attributes[65];
            delete form.value.attributes[66];
            delete form.value.attributes[67];
            delete form.value.attributes[68];
            delete form.value.attributes[69];
            delete form.value.attributes[70];
            delete form.value.attributes[71];
            delete form.value.attributes[75];
            delete form.value.attributes[76];
            delete form.value.attributes[77];

            hidden_attributes.value[65] = true;
            hidden_attributes.value[66] = true;
            hidden_attributes.value[67] = true;
            hidden_attributes.value[68] = true;
            hidden_attributes.value[69] = true;
            hidden_attributes.value[70] = true;
            hidden_attributes.value[71] = true;
            hidden_attributes.value[75] = true;
            hidden_attributes.value[76] = true;
            hidden_attributes.value[77] = true;
          }

          if (
            (form.value.attributes["hasFrame"] ||
              ["frame"].includes(good.value?.group?.key)) &&
            form.value.attributes["coverFrame"]
          ) {
            if (
              [
                17, 18, 20, 22, 24, 25, 27, 28, 33, 37, 38, 39, 45, 46, 48, 50,
                51, 53, 56, 58, 59, 61, 64, 87, 93, 192,
              ].includes(form.value.attributes["coverFrame"] * 1)
            )
              form.value.attributes["colorNavardarzgir"] = "111";
            if (
              [34, 52, 54, 55, 60, 92, 94, 95, 193].includes(
                form.value.attributes["coverFrame"] * 1
              )
            )
              form.value.attributes["colorNavardarzgir"] = "112";
            if (
              [
                19, 21, 26, 29, 30, 31, 35, 36, 40, 42, 43, 44, 47, 49, 57, 62,
                63, 66, 67, 68, 69, 70, 71, 73, 74, 75, 76, 77, 78, 79, 80, 81,
                82, 83, 84, 85, 86, 89, 90, 91, 96,
              ].includes(form.value.attributes["coverFrame"] * 1)
            )
              form.value.attributes["colorNavardarzgir"] = "113";
          }

          if (form.value.attributes["typeDakheli"] == "french") {
            form.value.attributes["widthDakheli"] = 9.5;
          }
          if (form.value.attributes["typeDakheli"] == "mexic") {
            form.value.attributes["widthDakheli"] = 10.5;
          }

          if (form.value.attributes["typeParvaz"] == "french") {
            form.value.attributes["widthParvaz"] = 7;
          }
          if (form.value.attributes["typeParvaz"] == "mexic") {
            form.value.attributes["widthParvaz"] = 10.5;
          }

          if (form.value.attributes["typeMaterialFrame"]) {
            form.value.attributes["typeMaterialRokoob"] =
              form.value.attributes["typeMaterialFrame"];
          }
          hidden_attribute_items.value = {};

          parvazItems.forEach((parvaz) => {
            if (
              form.value.attributes["typeParvaz"] == parvaz.typeParvaz &&
              parvaz.typeMaterialRokoob.includes(
                form.value.attributes["typeMaterialRokoob"]
              )
            ) {
              const find = good_attributes.value.findIndex(
                (f) => f.key == "perLengthParvaz"
              );
              if (find >= 0) {
                hidden_attribute_items.value[good_attributes.value[find].key] =
                  good_attributes.value[find].items
                    .filter((f) => parvaz.includes.includes(f.name * 1))
                    .map((m) => m.key);
                if (
                  form.value.attributes["perLengthParvaz"] &&
                  good_attributes.value[find].items.filter(
                    (ff) =>
                      hidden_attribute_items.value[
                        good_attributes.value[find].key
                      ].includes(ff.key) &&
                      ff.key == form.value.attributes["perLengthParvaz"]
                  ).length == 0
                ) {
                  delete form.value.attributes["perLengthParvaz"];
                }
              }
            }
          });

          dakheliItems.forEach((dakheli) => {
            if (
              form.value.attributes["typeDakheli"] == dakheli.typeDakheli &&
              dakheli.typeMaterialRokoob.includes(
                form.value.attributes["typeMaterialRokoob"]
              )
            ) {
              const find = good_attributes.value.findIndex(
                (f) => f.key == "perLengthDakheli"
              );
              if (find >= 0) {
                hidden_attribute_items.value[good_attributes.value[find].key] =
                  good_attributes.value[find].items
                    .filter((f) => dakheli.includes.includes(f.name * 1))
                    .map((m) => m.key);
                if (
                  form.value.attributes["perLengthDakheli"] &&
                  good_attributes.value[find].items.filter(
                    (ff) =>
                      hidden_attribute_items.value[
                        good_attributes.value[find].key
                      ].includes(ff.key) &&
                      ff.key == form.value.attributes["perLengthDakheli"]
                  ).length == 0
                ) {
                  delete form.value.attributes["perLengthDakheli"];
                }
              }
            }
          });


          if (form.value.attributes["pvcColor"] == 'two_color' && good.value.attributes.findIndex(f => f.key == "coverFrontDoor") >= 0) {

            delete hidden_attributes.value["coverFrontDoor"]
            delete hidden_attributes.value["coverBackDoor"]

          } else {
            hidden_attributes.value["coverFrontDoor"] = true;
            hidden_attributes.value["coverBackDoor"] = true;

            // delete form.value.attributes["coverFrontDoor"]
            // delete form.value.attributes["coverBackDoor"]
          }

          if (form.value.attributes["pvcColor"] == 'two_color' && good.value.attributes.findIndex(f => f.key == "coverMiddleSheet") >= 0) {

            delete hidden_attributes.value["coverMiddleSheet"]
            delete hidden_attributes.value["coverBaghalBazoo"]

          } else {
            hidden_attributes.value['coverMiddleSheet'] = true;
            hidden_attributes.value['coverBaghalBazoo'] = true;

            //delete form.value.attributes["coverMiddleSheet"]
            //delete form.value.attributes["coverBaghalBazoo"]
          }






          const typeDoor = doTypeDoor(good.value.group_id);
          const hasEdgeOfDoor = form.value.attributes["hasEdgeOfDoor"];
          const hasAbzar = form.value.attributes["hasAbzar"];
          const typeMaterialDoor = form.value.attributes["typeMaterialDoor"];
          const goodId = good.value.id;

          const doorThickness = doDoorThickness({
            typeDoor,
            hasEdgeOfDoor,
            goodId,
            hasAbzar,
            typeMaterialDoor,
          });

          if (doorThickness)
            form.value.attributes["doorThickness"] = doorThickness + "";
          if (doorThickness && form.value.attributes["hasFrame"])
            form.value.attributes["thicknessEdgeOfFrame"] =
              doThicknessEdgeOfFrame({ doorThickness });


          const centerLayerThickness = doCenterLayerThickness({
            typeMaterialDoor,
            doorThickness,
            typeDoor,
          });
          if (centerLayerThickness)
            form.value.attributes["centerLayerThickness"] =
              centerLayerThickness.label;

          if (doorThickness) form.value.attributes["baghalBazooThickness"] = (doorThickness * 10 - form.value.attributes["centerLayerThickness"]) / 2
          if (form.value.attributes["widthFloor"]) {
            delete form.value.attributes.mokammelWidth;
            delete form.value.attributes.hasMokammel;
            hidden_attributes.value.mokammelWidth = true;

            const wallThickness = form.value.attributes["wallThickness"];
            const widthFloor = form.value.attributes["widthFloor"];
            if (widthFloor) {
              const mokammelWidth = doMokammelWidth({ wallThickness, widthFloor })
              if (mokammelWidth > 0) {
                form.value.attributes.mokammelWidth = mokammelWidth;
                form.value.attributes.hasMokammel = true;
                hidden_attributes.value.mokammelWidth = false;
              }


            }

          }
        });

        oldFormAttribute.value = Object.assign({}, newVal);
      },
      {
        deep: true,
      }
    );
    return {
      checkPermission,
      selectGood,
      submit: () => {
        context.emit(
          "confirm",
          {
            ...Object.assign({}, form.value),
            good: Object.assign({}, good.value),
            //count: 1,
          },
          is_edit.value
        );
        reset();
      },
      good,
      form,
      ref_good,
      onEdit,
      onCopy,
      is_edit,
      ref_form,
      good_attributes,
      hidden_attributes,
      disable_attributes,
      hidden_attribute_items,
      change(attribute_key, newVal) {
        if (attribute_key == "hasFrame" && newVal) {
          if (form.value.attributes["hasRokoob"])
            form.value.attributes["hasRokoob"] = false;
          form.value.attributes["hasEdgeOfDoor"] = false;
        }

        if (attribute_key == "hasFrame" && !newVal) {
          form.value.attributes["hasEdgeOfDoor"] = true;
          form.value.attributes["alignEdgeOfDoor"] = 'threeSide';
        }

        if (attribute_key == "hasRokoob" && newVal) {
          if (form.value.attributes["hasFrame"])
            form.value.attributes["hasFrame"] = false;
        }

        if (attribute_key == "hasEdgeOfDoor" && newVal) {
          if (form.value.attributes["hasFrame"])
            form.value.attributes["hasFrame"] = false;
          form.value.attributes["alignEdgeOfDoor"] = 'threeSide';

        }

        if (attribute_key == "typeDakheli") {
          if (form.value.attributes["typeDakheli"] == "none_standard") {
            form.value.attributes["widthDakheli"] = '';
          }
        }
        if (attribute_key == "typeParvaz") {
          if (form.value.attributes["typeParvaz"] == "none_standard") {
            form.value.attributes["widthParvaz"] = '';
          }
        }

        if (form.value.attributes["hasFrame"]) {
          if (["hasThreshold", "frameHeight"].includes(attribute_key)) {
            form.value.attributes["doorHeight"] =
              form.value.attributes["frameHeight"] * 1 -
              7 +
              (form.value.attributes["hasThreshold"] ? 0 : 2);
          }
          if (attribute_key == "frameWidth") {
            form.value.attributes["doorWidth"] =
              form.value.attributes["frameWidth"] * 1 - 7;
          }
          // if (attribute_key == "typeMaterialDoor") {
          //   form.value.attributes["typeMaterialFrame"] =
          //     form.value.attributes["typeMaterialDoor"];
          // }

          if (attribute_key == 'doorLengeh' || attribute_key == 'hasEdgeOfDoor') {

            form.value.attributes["countZevardamageh"] = doZevardamageh({ doorLengeh: form.value.attributes["doorLengeh"], hasEdgeOfDoor: form.value.attributes["hasEdgeOfDoor"], })
          }

          if (attribute_key == "pvcColor" && newVal != 'two_color') {
            if (good.value.attributes.findIndex(f => f.key == "coverFrontDoor") >= 0) {
              form.value.attributes["coverFrontDoor"] = newVal;
              form.value.attributes["coverBackDoor"] = newVal;
            }

            if (good.value.attributes.findIndex(f => f.key == "coverMiddleSheet") >= 0) {
              form.value.attributes["coverMiddleSheet"] = newVal;
              form.value.attributes["coverBaghalBazoo"] = newVal;
            }
            if (form.value.attributes["hasFrame"]) {
              form.value.attributes["coverFrame"] = newVal;

            }
          }


          if (attribute_key == 'doorLengeh' || attribute_key == 'hasEdgeOfDoor') {
            form.value.attributes["countZevardamageh"] = doZevardamageh({ doorLengeh: form.value.attributes["doorLengeh"], hasEdgeOfDoor: form.value.attributes["hasEdgeOfDoor"], })
          }

          if (attribute_key == "coverBaghalBazoo" && form.value.attributes["hasFrame"]) {
            form.value.attributes["coverFrame"] = newVal;
          }
          if (attribute_key == "coverFrontDoor" && form.value.attributes["hasFrame"]) {
            form.value.attributes["coverFrame"] = newVal;
          }

          if (attribute_key == "coverBaghalBazoo" && form.value.attributes["hasRokoob"]) {
            form.value.attributes["coverRokoob"] = newVal;
          }
          if (attribute_key == "coverFrontDoor" && form.value.attributes["hasRokoob"]) {
            form.value.attributes["coverRokoob"] = newVal;
          }



          // if (form.value.attributes["hasRokoob"] && attribute_key == "typeMaterialDoor")
          //   form.value.attributes["typeMaterialRokoob"] = form.value.attributes["typeMaterialDoor"];

          if (attribute_key == "wallThickness") {
            const wallThickness = form.value.attributes["wallThickness"];
            const widthFloor = doWidthFloor({ wallThickness });
            if (widthFloor) {
              form.value.attributes["widthFloor"] = widthFloor.label;
            }
          }

          if (!good.value.attributes) return;
          computeCondition();
        }
      },
      groupAttributes,

    };
  },
};
</script>