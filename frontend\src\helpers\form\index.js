import { nextTick, ref, watch } from "vue";
import { api } from "@/boot/axios";

export const formApi = ({ id = null, uri, props = {}, context = null }) => {
    //console.log('open formApi')
    const url = ref(uri ?? "");
    const form = ref(props.form ?? {});
    const formOptions = ref(props.formOptions ?? {});
    watch(() => props.form, newVal => {
        //console.log('props', newVal)
        form.value = newVal;
    })
    const submit = async () => {
        if (form.value && form.value.id) {
            await api.put(url.value + "/" + form.value.id, form.value);
            load();
        } else {
            await api.post(url.value, form.value).then(res => {
                form.value = res.result;
            });
            load();
        }
    };

    const load = async () => {
        // console.log('open load', form.value, id)
        //if (form.value.id)
        if (id ?? form.value.id)
            api.get(url.value + "/" + (id ?? form.value.id)).then((res) => {
                form.value = Object.assign(form.value, res.result.form);
                formOptions.value = Object.assign(formOptions.value, res.result.formOptions);
                // if (context) {
                //     context.emit('update:form', form.value);
                //     context.emit('form', form.value);
                // }

            });

        else if (props.hasCreateRoute)
            api.get(url.value + "/create").then((res) => {
                formOptions.value = Object.assign(formOptions.value, res.result.formOptions);
            });
    };
    nextTick(() => {
        load();
    })
    return { url, form, formOptions, submit, load };
};
