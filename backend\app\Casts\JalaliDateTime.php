<?php

namespace App\Casts;

use Morilog\Jalali\Jalalian;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;

class JalaliDateTime implements CastsAttributes
{
    // تبدیل مقدار از دیتابیس به شمسی
    public function get($model, $key, $value, $attributes)
    {
        if ($value === null) {
            return null;
        }

        return Jalalian::fromDateTime($value)->format('Y/m/d H:i:s');
    }

    // تبدیل مقدار از شمسی به میلادی برای ذخیره در دیتابیس
    public function set($model, $key, $value, $attributes)
    {
    

        // اگر آبجکت Carbon/Jalalian است، مستقیماً استفاده کنید
        return $value;
    }
}