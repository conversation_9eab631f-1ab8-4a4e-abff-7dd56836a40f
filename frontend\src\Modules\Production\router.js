
import { iconImage } from '@/helpers'
import { OrderForm, OrderView, PartyForm, PartyView } from './views/index';

export default [
    {
        path: '/order',
        name: 'production_order',
        //component: OrderView,
        meta: {
            title: 'سفارشات',
            icon: iconImage('invoice'),
            permissions: 'production_order',
        },
        children: [
            {
                path: '',
                name: 'production_order.index',
                component: OrderView,
                meta: {
                    title: 'سفارشات',
                    icon: iconImage('invoice'),
                    //permissions: 'production_order.index',
                },
            },
            {
                path: 'create',
                name: 'production_order.create',
                component: OrderForm,
                hidden: true,
                meta: {
                    title: 'ایجاد سفارش',
                    icon: 'add',
                    permissions: 'production_order.create',

                },
            },
            {
                path: ':id/edit',
                name: 'production_order.edit',
                component: OrderForm,
                props: true,
                hidden: true,
                meta: {
                    title: 'ویرایش سفارش',
                    icon: 'edit',
                    permissions: 'production_order.edit',

                },
            },
        ]
        // children: [
        //     {
        //         path: 'index',
        //         name: 'production_order.index',
        //         component: OrderView,
        //         meta: {
        //             title: 'لیست سفارشات',
        //             icon: 'list',

        //         },
        //     },

        //     {
        //         path: 'parts',
        //         name: 'production_order_part.create',
        //         component: OrderForm,
        //         meta: {
        //             title: 'پارت',
        //             icon: 'list',
        //             permissions: 'production_order_part.create',

        //         },
        //     },

        // ],
    },
    // {
    //     path: '/order/create',
    //     name: 'production_order.create',
    //     component: OrderForm,
    //     hidden: true,
    //     meta: {
    //         title: 'ایجاد سفارش',
    //         icon: 'add',
    //         permissions: 'production_order.create',

    //     },
    // },
    // {
    //     path: '/order/:id/edit',
    //     name: 'production_order.edit',
    //     component: OrderForm,
    //     props: true,
    //     hidden: true,
    //     meta: {
    //         title: 'ویرایش سفارش',
    //         icon: 'edit',
    //         permissions: 'production_order.edit',

    //     },
    // },
    {
        path: '/parties',
        name: 'parties',
        //component: PartyView,
        meta: {
            title: 'نمایندگان',
            icon: iconImage('boss-man'),
            permissions: 'parties',
        },
        children: [
            {
                path: '',
                name: 'parties.index',
                component: PartyView,
                meta: {
                    title: 'نمایندگان',
                    icon: iconImage('boss-man'),
                    //permissions: 'parties',
                },
            },
            {
                path: 'create',
                name: 'parties.create',
                component: PartyForm,
                hidden: true,
                meta: {
                    title: 'ایجاد نماینده',
                    icon: 'add',
                    permissions: 'parties.create',

                },
            },
            {
                path: ':id/edit',
                name: 'parties.edit',
                component: PartyForm,
                props: true,
                hidden: true,
                meta: {
                    title: 'ویرایش نماینده',
                    icon: 'edit',
                    permissions: 'parties.edit',

                },
            },
        ]
    },


];
