<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\BuyAndSell\Entities\ConditionPriceList;
use Modules\Good\Entities\Good;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create($this->table(), function (Blueprint $table) {
            $table->foreignIdFor(ConditionPriceList::class)->constrained(ConditionPriceList::getTableName())->cascadeOnUpdate();
            $table->foreignIdFor(Good::class)->constrained(Good::getTableName())->cascadeOnUpdate();
            $table->unique([ConditionPriceList::getForeignKeyName(), Good::getForeignKeyName()], ConditionPriceList::getForeignKeyName() . '_' . Good::getForeignKeyName());
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists($this->table());
    }

    public function table()
    {
        $prefix = config('buyandsell.prefix');
        return ($prefix ? $prefix . '__' : '') . 'condition_price_list_goods';
    }
};
