<?php

namespace Modules\Good\Http\Controllers;

use App\Http\Controllers\API\BaseController;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Good\Entities\Attribute;
use Modules\Good\Entities\AttributeItem;

class AttributeItemController extends BaseController
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index(Attribute $attribute)
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Attribute $attribute, Request $request)
    {
        $attribute = AttributeItem::create($request->all());
        return $this->handleResponse($attribute, trans('request.done'));
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(Attribute $attribute, AttributeItem $item)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, Attribute $attribute, AttributeItem $item)
    {
        $item->update($request->all());
        return $this->handleResponse($item, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy(Attribute $attribute, AttributeItem $item)
    {
        //
    }
}
