<?php

namespace Modules\Good\Entities;

use App\Casts\ObjectCast;

class AttributeItem extends BModel
{
   protected $fillable = [
      'attribute_id',
      'name',
      'data',
      'key',
      'is_active',
      'is_active_customer',
   ];
 
   protected $casts = [ 
      'data' =>  ObjectCast::class,
      'is_active' => 'boolean',
      'is_active_customer' => 'boolean',
   ];

   public function conditions()
   {
      return $this->hasMany(AttributeItemCondition::class);
   }

   public function metas()
   {
      return $this->hasMany(AttributeItemMeta::class);
   }

   public function attribute(){
      return $this->belongsTo(Attribute::class);
   }
}
