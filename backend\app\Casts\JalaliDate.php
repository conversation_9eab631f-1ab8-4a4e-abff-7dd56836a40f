<?php

namespace App\Casts;

use Morilog\Jalali\Jalalian;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;

class JalaliDate implements CastsAttributes
{
    // تبدیل مقدار از دیتابیس به شمسی
    public function get($model, $key, $value, $attributes)
    {
        if ($value === null) {
            return null;
        }

        return Jalalian::fromDateTime($value)->format('Y/m/d');
    }

    // تبدیل مقدار از شمسی به میلادی برای ذخیره در دیتابیس
    public function set($model, $key, $value, $attributes)
    {
        if ($value === null) {
            return null;
        }

        // اگر ورودی رشته شمسی است، آن را به میلادی تبدیل کنید
        if (is_string($value)) {
            return Jalalian::fromFormat('Y/m/d', $value)->toCarbon();
        }

        // اگر آبجکت Carbon/Jalalian است، مستقیماً استفاده کنید
        return $value;
    }
}