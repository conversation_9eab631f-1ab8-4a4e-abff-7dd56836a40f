<?php
// app/Repositories/ProductionOrderRepository.php

namespace Modules\Production\Repositories;

use App\Models\ProductionOrder;
use App\Repositories\BaseRepository;
use Modules\Production\Entities\ProductionOrder as EntitiesProductionOrder;

class ProductionOrderRepository extends BaseRepository implements ProductionOrderRepositoryInterface
{
    public function __construct(EntitiesProductionOrder $model)
    {
        parent::__construct($model);
    }
}
