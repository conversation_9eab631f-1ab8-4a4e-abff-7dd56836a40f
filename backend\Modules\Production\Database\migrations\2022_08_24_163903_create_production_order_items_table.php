<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Good\Entities\Good;
use Modules\Production\Entities\ProductionOrder;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create($this->table(), function (Blueprint $table) {
            $table->id();
            $table->string('code');
            $table->foreignIdFor(ProductionOrder::class)->constrained(ProductionOrder::getTableName());
            $table->foreignIdFor(Good::class)->constrained(Good::getTableName());
            $table->json('attributes')->nullable();
            // $table->unsignedFloat('count');
            $table->unsignedInteger('count');
            $table->decimal('price', 13, 2, true);
            $table->decimal('total_price', 13, 2, true);
            $table->json('price_details')->nullable();

            $table->text('description')->default('')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists($this->table());
    }

    public function table(){
        $prefix = config('production.prefix');
        return ($prefix ? $prefix .'__' : '').'production_order_items';
    }
};
