<?php

namespace Modules\Good\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Good\Entities\Good;

class GoodFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = \Modules\Good\Entities\Good::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => fake()->word(),
            //'group_id' => null,
            'type' => Good::PRODUCT,
            //'default_attribute',
            'is_active' => true,
            'is_active_customer' => true,
            //'image_src',
        ];
    }
}
