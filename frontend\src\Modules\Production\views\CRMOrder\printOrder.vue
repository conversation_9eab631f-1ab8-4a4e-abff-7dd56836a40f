<template>
    <j-dialog-bar v-model="dialogPrint" persistent content-class="p-0">
        <!-- <template #title>پرینت</template> -->
        <template #bar>
            <j-btn v-if="!['DRAFT', 'SEND_DRAFT', 'PREORDER'].includes(data.status)" flat dense icon="print"
                label="پرینت سفارش" title="پرینت سفارش" @click="print('content')" />
        </template>

        <div class="p-3 overflow-x-auto" v-if="!loading">
            <div id="content" style="font-size:10px">
                <table class="w-full text-center">
                    <thead>
                        <tr>
                            <td class="no-border p-0 pb-4" :colspan="columns.length + 1">
                                <table class="j-table w-full text-center odd-highlight">
                                    <tr class="h-7">
                                        <td class="no-border w-1/3 bg-white">
                                            <table class="mr-auto ml-0 text-sm">
                                                <tr>
                                                    <th class="no-border bg-white text-left w-28">شناسه سفارش: </th>
                                                    <td class="no-border bg-white text-right">{{ data.code }}</td>
                                                </tr>
                                                <tr>
                                                    <th class="no-border bg-white text-left">نام نمایندگی: </th>
                                                    <td class="no-border bg-white text-right">{{ data.party_name }}</td>
                                                </tr>
                                                <tr>
                                                    <th class="no-border bg-white text-left">نام مشتری: </th>
                                                    <td class="no-border bg-white text-right">{{ data.customer_name }}
                                                    </td>
                                                </tr>
                                                <tr v-if="data.user_name">
                                                    <th class="no-border bg-white text-left">نام کاربر:</th>
                                                    <td class="no-border bg-white text-right">{{ data.user_name }}</td>
                                                </tr>
                                            </table>
                                        </td>
                                        <td class="no-border w-1/3 bg-white">
                                            <h5 class="font-bold">فاکتور</h5>
                                        </td>
                                        <td class="no-border w-1/3 bg-white">
                                            <img src="@/assets/logo.svg" class="h-16 ml-auto" />
                                        </td>
                                        <!-- <td class="no-border w-1/3 bg-white text-right">
                                            <table class="ml-auto mr-0 text-sm">
                                                <tr v-if="data.submit_date">
                                                    <th class="no-border bg-white text-left">تاریخ سفارش:</th>
                                                    <td class="no-border bg-white">{{ data.submit_date }}</td>
                                                </tr>
                                                <tr v-if="data.delivery_date">
                                                    <th class="no-border bg-white text-left">تاریخ سفارش:</th>
                                                    <td class="no-border bg-white">{{ data.delivery_date }} ({{
                                                        data.diff_delivery_date
                                                    }} روز)</td>
                                                </tr>
                                                <tr v-if="data.user_name">
                                                    <th class="no-border bg-white text-left">نام کاربر:</th>
                                                    <td class="no-border bg-white">{{ data.user_name }}</td>
                                                </tr>
                                            </table>
                                        </td> -->
                                    </tr>


                                </table>
                            </td>
                        </tr>

                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="grid gap-5">
                                    <template v-for="item, good_id in data.items.group(m => m.good.id ?? '')"
                                        :key="good_id">
                                        <j-table :columns="new_columns(item, good_id)" :rows="item" flat bordered
                                            separator="cell" hide-bottom row-key="code" :rows-per-page-options="[0]"
                                            dense>

                                            <template #body-cell-index="props">
                                                <q-td :props="props">
                                                    {{ props.rowIndex + 1 }}
                                                </q-td>
                                            </template>
                                            <template #body-cell-good="props">
                                                <template v-if="props.rowIndex === 0">
                                                    <q-td :rowspan="item.length" :props="props">
                                                        {{ props.value }}
                                                    </q-td>
                                                </template>
                                            </template>
                                            <template v-if="item && item.length > 0" #bottom-row="props">
                                                <q-tr>
                                                    <q-td class="text-bold text-center" colspan="3">
                                                        جمع
                                                    </q-td>

                                                    <q-td class="text-center">
                                                        {{ item.reduce((a, b) => a + b.count * 1, 0) }}
                                                    </q-td>

                                                        <q-td />
                                                        <q-td class="text-center">
                                                            {{ String.currencyFormat(item.reduce((a, b) => a + b.count *
                                                                b.price, 0)) }}
                                                        </q-td>

                                                    <q-td :colspan="props.cols.length - 6" />
                                                </q-tr>
                                            </template>
                                        </j-table>
                                    </template>
                                </div>
                            </td>
                        </tr>


                    </tbody>

                </table>
            </div>
            <template v-if="authStore?.user?.is_customer && selected.status == 'DRAFT'">
                <div class="m-auto text-center">
                    <j-btn label="ارسال سفارش به کارخانه" color="primary" @click="sendDraft" />
                </div>
            </template>


        </div>


    </j-dialog-bar>
</template>
<script>
import { computed, ref, watch } from 'vue';
import { api } from '@/boot/axios';
import { print, checkRole } from '@/helpers'
import { printLabel, printTemplate } from './prints'
import { useAuthStore } from '@/stores';
import ImageByText from '../productionOrder/components/ImageByText.vue';
import { computeProductionOrderItem } from '../../computeProductionFormula';
import Checklist from './checklist.vue';
import { attributeColumns } from '../Order/selectForm/index';

export default {
    components: { ImageByText, Checklist },
    props: {
        selected: Object,
        callback: Function,
    },
    setup(props) {

        const data = ref({})
        const attributes = ref([])
        const doors = ref([])
        const group_doors = ref({})
        const frames = ref([])
        const rokoob = ref([])
        const others = ref([])



        const imageColor = row => {
            //  console.log(66666)
            const find = attributes.value.findIndex(f => f.key == "pvcColor")
            if (find >= 0) return attributes.value[find].items[attributes.value[find].items.findIndex(f => f.key == row.attributes.pvcColor)]?.data?.image;
            return ''
        }
        const columns = computed(() => {
            if (!data.value.items) {
                return []
            }
            return [
                {
                    name: "id",
                    label: "ردیف",
                    field: (row, index) => index + 1,
                    style: 'width: 25px;min-width: 25px',
                    verticalLabel: true,
                    merge: true,
                },
                {
                    name: "code",
                    label: "سریال",
                    field: (row, index) => row.id,
                    style: 'width: 35px;min-width: 35px',
                    verticalLabel: true,
                    merge: true,

                },
                {
                    name: "good",
                    label: "کالا/خدمات",
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    image: row => row.good.image_src || row.attributes_label?.template,
                    field: row => row.good.name,
                    merge: true,

                },
                {
                    label: "جنس",
                    field: row => row.attributes_label?.typeMaterialDoor,
                    style: 'width: 40px;width: min-40px;',

                },
                {
                    label: "ارتفاع درب",
                    field: row => row.attributes_label?.doorHeight,
                    //verticalLabel: true,
                    style: 'width: 35px;min-width: 35px;font-weight:900;border-right:2px solid',

                },
                {
                    label: "عرض درب",
                    field: row => row.attributes_label?.doorWidth,
                    //verticalLabel: true,
                    style: 'width: 35px;min-width: 35px;font-weight:900;border-left:2px solid',

                },
                // {
                //     name: "count",
                //     label: "تعداد",
                //     field: "count",
                //     //verticalLabel: true,

                //     style: 'width: 30px;min-width: 30px',

                // },
                {
                    label: "جهت درب",
                    field: row => row.attributes_label?.doorAlign,
                    //verticalLabel: true,

                    style: 'width: 40px;min-width: 40px',

                },
                {
                    label: "جای قفل",
                    field: row => row.attributes_label?.hasLockPlace,

                    //verticalLabel: true,
                    style: 'width: 25px;min-width: 25px',

                },
                {
                    label: "برجستگی",
                    field: row => row.attributes_label?.hasBarjestegi,
                    style: 'width: 25px;min-width: 25px',
                    verticalLabel: true,
                },
                {
                    label: "ابزار",
                    field: row => row.attributes_label?.hasAbzar,

                    //verticalLabel: true,
                    style: 'width: 25px;min-width: 25px',

                },
                {
                    label: "ضخامت ورق",
                    field: row => row.attributes_label?.centerLayerThickness ?? row.attributes_label?.sheetCNCThickness,
                    //verticalLabel: true,
                    style: 'width: 40px;min-width: 40px',

                },
                {
                    label: "قطر درب",
                    field: row => row.attributes_label.doorThickness,
                    //verticalLabel: true,
                    style: 'width: 25px;min-width: 25px',

                },

                {
                    label: "نوع لبه",
                    field: row => row.attributes_label?.typeEdge,
                    style: 'width: 40px;width: min-40px;',

                },
                {
                    label: "نوع لنگه",
                    field: row => row.attributes_label?.doorLengeh,
                    style: 'width: 50px;min-width: 50px',

                },
                {
                    label: "زهوار دماغه",
                    field: row => row.attributes_label.countZevardamageh > 0 ? row.attributes_label.countZevardamageh : '',
                    verticalLabel: true,
                    style: 'width: 25px;min-width: 25px',

                },
                {
                    label: "قابلبه",
                    field: row => row.attributes.alignEdgeOfDoor !== "threeSide" && row.attributes.hasEdgeOfDoor ? row.attributes_label?.alignEdgeOfDoor : row.attributes_label?.hasEdgeOfDoor,
                    style: 'width: 50px',
                },
                {
                    label: "رنگ",
                    field: row => row.attributes_label?.pvcColor,// row.attributes?.pvcColor !== 'two_color' ? row.attributes_label?.pvcColor : ["<b>نما: </b>" + row.attributes_label?.coverFrontDoor, "<b>پشت: </b>" + row.attributes_label?.coverBackDoor].join('<br>'),
                    style: 'width: 90px;min-width: 70px;font-size:12px',
                    image: imageColor,
                },
                {
                    label: "جنس چهارچوب",
                    field: row => row.attributes_label?.typeMaterialFrame,
                    group_key: 'byFrame',
                    style: 'width: 50px;border-right-width:2px',
                },
                {
                    label: "قطر دیوار",
                    field: row => row.attributes_label?.wallThickness,
                    //verticalLabel: true,
                    group_key: 'byFrame',
                    style: 'width: 30px;',
                },
                {
                    label: "کف چهارچوب",
                    field: row => row.attributes_label?.widthFloor,
                    //verticalLabel: true,
                    group_key: 'byFrame',
                    style: 'width: 25px;min-width: 25px',

                },
                {
                    label: "مکمل",
                    field: row => !row.attributes_label.hasMokammel ? '' : row.attributes_label.mokammelCount + ' شاخه ' + row.attributes_label.mokammelWidth + ' سانتی',
                    //verticalLabel: true,
                    group_key: 'byFrame',
                    style: 'width: 55px;min-width: 25px',

                },
                {
                    label: "آستانه",
                    field: row => row.attributes_label?.hasThreshold,
                    //verticalLabel: true,
                    group_key: 'byFrame',
                    style: 'width: 25px;min-width: 25px',

                },
                {
                    label: "رنگ نوار درزگیر",
                    field: row => row.attributes_label?.colorNavardarzgir,
                    // verticalLabel: true,
                    group_key: 'byFrame',
                    style: 'width: 45px;min-width: 45px',

                },
                {
                    label: "ارتفاع چهارچوب",
                    field: row => row.attributes_label?.frameHeight,
                    //verticalLabel: true,
                    group_key: 'byFrame',
                    style: 'width: 35px;font-weight:900;min-width: 35px',

                },
                {
                    label: "عرض چهارچوب",
                    field: row => row.attributes_label?.frameWidth,
                    //verticalLabel: true,
                    group_key: 'byFrame',
                    style: 'width: 35px;font-weight:900;min-width: 35px',
                },
                {
                    label: "عرض پرواز",
                    field: row => row.attributes_label?.widthParvaz,
                    // verticalLabel: true,
                    group_key: 'byFrame',
                    style: 'width: 30px;min-width: 30px',
                },
                // {
                //     label: "قد پرواز",
                //     field: row => Object.keys(row.attributes.parvazCounts).join(' , '),
                //     verticalLabel: true,
                //     group_key: 'byFrame',
                //     style: 'width: 30px;min-width: 30px',
                // },
                {
                    name: "countParvaz",
                    label: "تعداد پرواز",
                    field: row => row.attributes_label.countParvaz + ' شاخه ' + row.attributes_label.perLengthParvaz,
                    group_key: 'byFrame',
                    style: 'width: 60px;min-width: 60px',

                },
                {
                    label: "جنس روکوب",
                    field: row => row.attributes_label?.typeMaterialRokoob,
                    group_key: 'byRokoob',
                    style: 'width: 40px;border-right-width:2px',
                },
                {
                    label: "نوع پرواز",
                    field: row => row.attributes_label?.typeParvaz,
                    group_key: 'byRokoob',
                    style: 'width: 40px;min-width: 40px',

                },
                {
                    label: "عرض پرواز",
                    field: row => row.attributes_label?.widthParvaz,
                    // verticalLabel: true,
                    group_key: 'byRokoob',
                    style: 'width: 30px;min-width: 30px',

                },
                // {
                //     label: "قد پرواز",
                //     field: row => Object.keys(row.attributes.parvazCounts).join(' , '),
                //     verticalLabel: true,
                //     group_key: 'byRokoob',
                //     style: 'width: 30px;min-width: 30px',

                // },
                {
                    label: "نوع داخلی",
                    field: row => row.attributes_label?.typeDakheli,
                    group_key: 'byRokoob',
                    style: 'width: 40px;min-width: 40px',

                },
                {
                    label: "عرض داخلی",
                    field: row => row.attributes_label?.widthDakheli,
                    //verticalLabel: true,
                    group_key: 'byRokoob',
                    style: 'width: 30px;min-width: 30px',

                },
                // {
                //     label: "قد داخلی",
                //     field: row => Object.keys(row.attributes.dakheliCounts).join(' , '),
                //     verticalLabel: true,
                //     group_key: 'byRokoob',
                //     style: 'width: 30px;min-width: 30px',

                // },
                {
                    label: "تعداد پرواز",
                    field: row => row.attributes_label.countParvaz + ' شاخه ' + row.attributes_label.perLengthParvaz,
                    group_key: 'byRokoob',
                    style: 'width: 60px;min-width: 60px',

                },
                {
                    label: "تعداد داخلی",
                    field: row => row.attributes_label.countDakheli + ' شاخه ' + row.attributes_label.perLengthDakheli,
                    group_key: 'byRokoob',
                    style: 'width: 60px;min-width: 60px',

                },
                {
                    label: "واحد",
                    field: row => row.attributes_label.vahed,
                    style: 'width: 60px;min-width: 60px',
                },
                /*{
                    label: "قیمت",
                    field: row => String.currencyFormat(row.total_price),
                    permissions: 'show price order',
                    style: 'width: 70px;min-width: 70px',
                },*/
                {
                    name: "description",
                    label: "توضیحات",
                    row: true,
                    field: row => {
                        //const countZevardamageh = row.attributes.countZevardamageh > 0 ? 'زهوار دماغه : ' + row.attributes.countZevardamageh + ' عدد' : '';
                        //const mokamel = row.attributes[47] > 0 ? row.attributes[47] + ' شاخه مکمل ' + row.attributes_label.mokammelWidth + ' سانتی ' + row.attributes_label?.typeMaterialDoor : '';
                        //const parvaz = row.attributes.countParvaz > 0 ? row.attributes.countParvaz + ' شاخه پرواز ' + row.attributes_label?.perLengthParvaz + ' ' + row.attributes_label?.typeMaterialDoor : '';
                        return [
                            //countZevardamageh,
                            //mokamel,
                            // ...Object.entries(row.attributes_label_column)
                            //     .filter(f => ['61'].includes(f[0]))
                            //     .map(m => `<b>${m[1].attribute_name}</b>` + ' : ' + m[1].label),
                            (row.description ?? '')].filter(f => f).join('<br>')
                    },
                }
            ];

        })

        const maghta_columns = computed(() => {
            if (!data.value.items) {
                return []
            }
            return [
                {
                    name: "id",
                    label: "ردیف",
                    field: (row, index) => index + 1,
                    style: 'width: 25px;min-width: 25px',
                    verticalLabel: true,
                    merge: true,
                },
                {
                    name: "code",
                    label: "سریال",
                    field: (row, index) => row.id,
                    style: 'width: 35px;min-width: 35px',
                    verticalLabel: true,
                    merge: true,

                },
                {
                    name: "good",
                    label: "کالا/خدمات",
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    image: row => row.good.image_src || row.attributes_label?.template,
                    field: row => row.good.name,
                    merge: true,

                },
                {
                    label: "جنس",
                    field: row => row.attributes_label?.typeMaterialDoor,
                    style: 'width: 40px;width: min-40px;',

                },
                // {
                //     label: "ارتفاع درب",
                //     field: row => row.attributes_label?.doorHeight,
                //     //verticalLabel: true,
                //     style: 'width: 35px;min-width: 35px;font-weight:900;border-right:2px solid',

                // },
                // {
                //     label: "عرض درب",
                //     field: row => row.attributes_label?.doorWidth,
                //     //verticalLabel: true,
                //     style: 'width: 35px;min-width: 35px;font-weight:900;border-left:2px solid',

                // },
                // {
                //     name: "count",
                //     label: "تعداد",
                //     field: "count",
                //     //verticalLabel: true,

                //     style: 'width: 30px;min-width: 30px',

                // },

                // {
                //     label: "ابزار",
                //     field: row => row.attributes_label?.hasAbzar,

                //     //verticalLabel: true,
                //     style: 'width: 25px;min-width: 25px',

                // },
                {
                    label: "ضخامت ورق",
                    field: row => row.attributes_label?.centerLayerThickness ?? row.attributes_label?.sheetCNCThickness,
                    //verticalLabel: true,
                    style: 'width: 60px;min-width: 60px',

                },
                {
                    label: "قطر مقطع",
                    field: row => row.attributes_label.doorThickness,
                    //verticalLabel: true,
                    style: 'width: 80px;min-width: 80px',

                },


                {
                    label: "رنگ",
                    field: row => row.attributes_label?.pvcColor,
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    image: imageColor,
                },
                // {
                //     label: "جنس چهارچوب",
                //     field: row => row.attributes_label?.typeMaterialFrame,
                //     group_key: 'byFrame',
                //     style: 'width: 50px;border-right-width:2px',
                // },

                // {
                //     label: "کف چهارچوب",
                //     field: row => row.attributes_label?.widthFloor,
                //     //verticalLabel: true,
                //     group_key: 'byFrame',
                //     style: 'width: 25px;min-width: 25px',

                // },


                {
                    label: "واحد",
                    field: row => row.attributes_label.vahed,
                    style: 'width: 60px;min-width: 60px',
                },
                /*{
                    label: "قیمت",
                    field: row => String.currencyFormat(row.total_price),
                    permissions: 'show price order',
                    style: 'width: 70px;min-width: 70px',
                },*/
                {
                    name: "description",
                    label: "توضیحات",
                    row: true,
                    field: row => {
                        //const countZevardamageh = row.attributes.countZevardamageh > 0 ? 'زهوار دماغه : ' + row.attributes.countZevardamageh + ' عدد' : '';
                        //const mokamel = row.attributes[47] > 0 ? row.attributes[47] + ' شاخه مکمل ' + row.attributes_label.mokammelWidth + ' سانتی ' + row.attributes_label?.typeMaterialDoor : '';
                        //const parvaz = row.attributes.countParvaz > 0 ? row.attributes.countParvaz + ' شاخه پرواز ' + row.attributes_label?.perLengthParvaz + ' ' + row.attributes_label?.typeMaterialDoor : '';
                        return [
                            //countZevardamageh,
                            //mokamel,
                            // ...Object.entries(row.attributes_label_column)
                            //     .filter(f => ['61'].includes(f[0]))
                            //     .map(m => `<b>${m[1].attribute_name}</b>` + ' : ' + m[1].label),
                            (row.description ?? '')].filter(f => f).join('<br>')
                    },
                }
            ];

        })

        const frame_columns = computed(() => {
            if (!data.value.items) {
                return []
            }
            return [
                {
                    name: "id",
                    label: "ردیف",
                    field: (row, index) => index + 1,
                    style: 'width: 25px;min-width: 25px',
                    verticalLabel: true,
                },
                {
                    name: "code",
                    label: "سریال",
                    field: (row, index) => row.id,
                    style: 'width: 35px;min-width: 35px',
                    verticalLabel: true,
                },
                {
                    name: "good",
                    label: "کالا/خدمات",
                    style: 'width: 90px;min-width: 70px;font-size:12px',
                    image: row => row.good.image_src || row.attributes_label?.template,
                    field: row => row.good.name,
                },
                // {
                //     name: "count",
                //     label: "تعداد",
                //     field: "count",
                //     style: 'width: 30px;min-width: 30px',

                // },
                {
                    label: "رنگ",
                    field: row => row.attributes_label?.pvcColor,
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    image: imageColor,
                },

                {
                    label: "جنس چهارچوب",
                    field: row => row.attributes_label?.typeMaterialFrame,
                    group_key: 'byFrame',
                    style: 'width: 50px;border-right-width:2px',

                },
                {
                    label: "قطر دیوار",
                    field: row => row.attributes_label?.wallThickness,
                    group_key: 'byFrame',
                    style: 'width: 40px;',

                },
                {
                    label: "کف چهارچوب",
                    field: row => row.attributes_label?.widthFloor,
                    group_key: 'byFrame',
                    style: 'width: 45px;min-width: 25px',

                },
                {
                    label: "مکمل",
                    field: row => !row.attributes_label.hasMokammel ? '' : row.attributes_label.mokammelCount + ' شاخه ' + row.attributes_label.mokammelWidth + ' سانتی',
                    group_key: 'byFrame',
                    style: 'width: 45px;min-width: 25px',

                },
                {
                    label: "آستانه",
                    field: row => row.attributes_label?.hasThreshold,
                    group_key: 'byFrame',
                    style: 'width: 35px;min-width: 25px',

                },
                {
                    label: "رنگ نوار درزگیر",
                    field: row => row.attributes_label?.colorNavardarzgir,
                    group_key: 'byFrame',
                    style: 'width: 60px;min-width: 45px',

                },
                {
                    label: "ارتفاع چهارچوب",
                    field: row => row.attributes_label?.frameHeight,
                    group_key: 'byFrame',
                    style: 'width: 55px;font-weight:900;min-width: 35px',

                },
                {
                    label: "عرض چهارچوب",
                    field: row => row.attributes_label?.frameWidth,
                    group_key: 'byFrame',
                    style: 'width: 55px;font-weight:900;min-width: 35px',
                },
                {
                    label: "عرض پرواز",
                    field: row => row.attributes_label?.widthParvaz,
                    group_key: 'byFrame',
                    style: 'width: 50px;min-width: 30px',
                },
                // {
                //     label: "قد پرواز",
                //     field: row => row.attributes_label?.perLengthParvaz,
                //     verticalLabel: true,
                //     group_key: 'byFrame',
                //     style: 'width: 30px;min-width: 30px',
                // },
                // {
                //     name: "count",
                //     label: "تعداد پرواز",
                //     field: row => (row.count * (row.attributes_label?.countParvaz ?? 2.5)) + ' شاخه',
                //     group_key: 'byFrame',
                //     style: 'width: 40px;min-width: 40px',

                // },
                {
                    name: "count",
                    label: "تعداد پرواز",
                    field: row => row.attributes_label.countParvaz + ' شاخه ' + row.attributes_label.perLengthParvaz,
                    group_key: 'byFrame',
                    style: 'width: 80px;min-width: 60px',

                },
                {
                    label: "واحد",
                    field: row => row.attributes_label.vahed,
                    style: 'width: 60px;min-width: 60px',
                },
                /*{
                    label: "قیمت",
                    field: row => String.currencyFormat(row.total_price),
                    permissions: 'show price order',
                    style: 'width: 70px;min-width: 70px',
                },*/
                {
                    name: "description",
                    label: "توضیحات",
                    field: row => {
                        const mokamel = row.attributes[47] > 0 ? row.attributes[47] + ' شاخه مکمل ' + row.attributes_label.mokammelWidth + ' سانتی ' + row.attributes_label?.typeMaterialDoor : '';
                        //const parvaz = row.attributes.countParvaz > 0 ? row.attributes.countParvaz + ' شاخه پرواز ' + row.attributes_label?.perLengthParvaz + ' ' + row.attributes_label?.typeMaterialDoor : '';
                        return [mokamel, ...Object.entries(row.attributes_label_column)
                            .filter(f => ['61'].includes(f[0]))
                            .map(m => `<b>${m[1].attribute_name}</b>` + ' : ' + m[1].label), (row.description ?? '')].filter(f => f).join('<br>')
                    },
                    style: 'min-width: 50px;border-right-width:2px'

                }
            ];

        })

        const rokoob_columns = computed(() => {
            if (!data.value.items) {
                return []
            }
            return [
                {
                    name: "id",
                    label: "ردیف",
                    field: (row, index) => index + 1,
                    style: 'width: 25px;min-width: 25px',
                    verticalLabel: true,
                },
                {
                    name: "code",
                    label: "سریال",
                    field: (row, index) => row.id,
                    style: 'width: 35px;min-width: 35px',
                    verticalLabel: true,
                },
                {
                    name: "good",
                    label: "کالا/خدمات",
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    field: row => row.good.name ?? '',
                },
                {
                    label: "رنگ",
                    field: row => row.attributes_label?.pvcColor,
                    image: imageColor,
                    style: 'width: 70px;min-width: 70px;font-size:12px',

                },
                {
                    label: "جنس",
                    field: row => row.attributes_label?.typeMaterialRokoob ?? '-',
                    style: 'width: 50px;min-width: 50px',

                },
                {
                    label: "نوع پرواز",
                    field: row => row.attributes_label?.typeParvaz ?? '-',
                    style: 'width: 50px;min-width: 50px',

                },
                {
                    label: "عرض پرواز",
                    field: row => row.attributes_label?.widthParvaz ?? '-',
                    style: 'width: 60px;min-width: 60px',

                },
                {
                    label: "قد پرواز",
                    field: row => row.attributes_label?.perLengthParvaz ?? '-',
                    style: 'width: 60px;min-width: 60px',

                },
                {
                    label: "نوع داخلی",
                    field: row => row.attributes_label?.typeDakheli ?? '-',
                    style: 'width: 50px;min-width: 50px',

                },
                {
                    label: "عرض داخلی",
                    field: row => row.attributes_label?.widthDakheli ?? '-',
                    style: 'width: 60px;min-width: 60px',

                },
                {
                    label: "قد داخلی",
                    field: row => row.attributes_label?.perLengthDakheli ?? '-',
                    style: 'width: 60px;min-width: 60px',

                },
                // {
                //     name: "count",
                //     label: "تعداد",
                //     field: "count",
                //     style: 'width: 40px;min-width: 40px',

                // },
                {
                    name: "count",
                    label: "تعداد پرواز",
                    field: row => row.attributes_label.countParvaz > 0 ? row.attributes_label.countParvaz + ' شاخه ' : '-',

                    //field: row => !row.attributes_label?.countParvaz ? '-' : (row.count * (row.attributes_label?.countParvaz)) + ' شاخه',
                    style: 'width: 70px;min-width: 70px',

                },
                {
                    name: "count",
                    label: "تعداد داخلی",
                    field: row => row.attributes_label.countDakheli > 0 ? row.attributes_label.countDakheli + ' شاخه ' : '-',

                    //field: row => !row.attributes_label?.countDakheli ? '-' : (row.count * (row.attributes_label?.countDakheli)) + ' شاخه',
                    style: 'width: 70px;min-width: 70px',

                },
                {
                    label: "واحد",
                    field: row => row.attributes_label.vahed,
                    style: 'width: 60px;min-width: 60px',
                },
                /*{
                    label: "قیمت",
                    field: row => String.currencyFormat(row.total_price),
                    permissions: 'show price order',
                    style: 'width: 70px;min-width: 70px',
                },*/
                {
                    name: "description",
                    label: "توضیحات",
                    field: row => {
                        return [(row.description ?? '')].filter(f => f).join(' - ')
                    }
                    ,
                    style: 'min-width: 50px',

                }
            ];

        })


        const other_columns = computed(() => {
            if (!data.value.items) {
                return []
            }
            return [
                {
                    name: "id",
                    label: "ردیف",
                    field: (row, index) => index + 1,
                    style: 'width: 30px;min-width: 30px',
                    verticalLabel: true,
                },
                {
                    name: "code",
                    label: "سریال",
                    field: (row, index) => row.id,
                    style: 'width: 35px;min-width: 35px',
                    verticalLabel: true,
                },
                {
                    name: "good",
                    label: "کالا/خدمات",
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    field: row => row.good.name ?? '',
                },
                // {
                //     name: "count",
                //     label: "تعداد",
                //     field: "count",
                //     style: 'width: 50px;min-width: 50px',

                // },
                {
                    label: "واحد",
                    field: row => row.attributes_label.vahed,
                    style: 'width: 60px;min-width: 60px',
                },
                /*{
                    label: "قیمت",
                    field: row => row.total_price,
                    permissions: 'show price order',
                    style: 'width: 60px;min-width: 60px',
                },*/
                {
                    name: "description",
                    label: "توضیحات",
                    field: "description",
                    field: row =>
                        `<div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 grid-cols-1">${[...Object.entries(row.attributes_label_column)
                            .filter(f => !['3', '15'].includes(f[0]))
                            .map(m => `<div></b><b>${m[1].attribute_name}</b>` + ' : ' + m[1].label + '</div>')].filter(f => f).join('')}</div><div>${row.description ?? ''}</div>`,

                    style: 'min-width: 50px',

                }
            ];

        })

        const isShowImage = ref(false)
        const urlImage = ref('')
        const loading = ref(false)

        watch(() => props.selected, (selected) => {
            props.selected = selected
        })

        const dialogPrint = ref(false)
        const statuses = ref([])
        const result = ref({})
        dialogPrint.value = true;
        loading.value = true;
        api.get(`/production/crm_production_order/${props.selected.id}/print`).then(res => {
            result.value = res.result
            // res.result.items = computeProductionOrderItem(res.result.items.sortBy('code'))
            statuses.value = res.statuses
            data.value = res.result;
            doors.value = res.result.items.filter(f => ['door'].includes(f.good?.group?.key));
            group_doors.value = res.result.items.filter(f => ['door'].includes(f.good?.group?.key)).group(g => (g.attributes.hasFrame ? 'byFrame' : (g.attributes.hasRokoob ? 'byRokoob' : 'door')))
            frames.value = res.result.items.filter(f => ['frame'].includes(f.good?.group?.key))
            rokoob.value = res.result.items.filter(f => ['rokoob'].includes(f.good?.group?.key))
            others.value = res.result.items.filter(f => !['door', 'frame', 'rokoob', 'maghta', 'zehvar', 'garniz'].includes(f.good?.group?.key))
            attributes.value = res.attributes
            loading.value = false;
        })

        const authStore = useAuthStore();

        const new_columns = (items, good_id) => {
            //console.log('items',items)
            // if (groups.value.length == 0)
            //     return [];
            return [

                // {
                //     name: "group",
                //     label: "محصول",
                //     style: 'width: 30px',
                //     field: row => row?.good?.name ?? '-'
                // },
                {
                    name: "index",
                    label: 'ردیف',
                    style: 'width: 30px',
                    //field: (row,index) => index,
                    align: 'center',
                    summary: true,
                    checkbox_label: true
                },
                {
                    name: "id",
                    label: 'شناسه',
                    style: 'width: 30px',

                    field: row => row.id ?? '-',
                    align: 'center',
                },
                {
                    name: "good",
                    label: "محصول",
                    style: 'width: 120px',

                    field: row => row.good.name ?? '',
                    align: 'center',
                    hasImage: true,
                    image: row => row.good?.image_src ?? '',
                    summary: true,
                    checkbox_label: true,
                },
                {
                    name: "count",
                    label: "تعداد",
                    style: 'width: 50px',

                    field: "count",
                    align: 'center',
                    summary: true

                }, {
                    name: "price",
                    label: "فی",
                    style: 'width: 120px',

                    field: row => Intl.NumberFormat().format(row.price) + ' ریال',
                    align: 'center',
                    summary: true,
                    //permissions: 'show price order',

                }
                , {
                    name: "sum_price",
                    label: "جمع",
                    style: 'width: 120px',

                    field: row => Intl.NumberFormat().format(row.price * row.count) + ' ریال',
                    align: 'center',
                    summary: true,
                    //permissions: 'show price order',

                },

                // ...attributeColumns({
                //     attributes: items[0].attributes, items, extra: {
                //         //hide_table: true
                //     }
                // }),
                ...attributeColumns({
                    attributes: attributes.value.filter(f => items.map(m => Object.keys(m.attributes)).flat().unique().includes(f.key)), items, extra: {
                        //hide_table: true
                    }
                }),
                {
                    name: "description",
                    label: "توضیحات",
                    field: "description",
                    align: 'center',

                }
            ];

        }
        return {
            loading,
            new_columns,
            result,
            statuses,
            checkRole,
            dialogPrint,
            authStore,
            doors,
            frame_columns,
            frames,
            group_doors,
            others,
            other_columns,
            rokoob,
            rokoob_columns,
            data,
            columns,
            print,
            attributes,
            printLabel,
            printTemplate,
            isShowImage,
            urlImage,
            showImage(value) {
                urlImage.value = value
                isShowImage.value = true
            },
            show() {
                //console.log('dialog hide')
                dialogPrint.value = true;
            },
        }
    },
}
</script>
