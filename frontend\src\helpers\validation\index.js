import { defineProps, ref } from "vue";
import { useRequestStore } from "@/stores";

export const validationApi = (props) => {
    const requestStore = useRequestStore();


    const rules = ref([
        (val) => {
            return (props.required && !!val) || !props.required || "اجباری است";
        },
        (val) => {
            return (props.requiredIf && !!val) || !props.requiredIf || "اجباری است";
        },
        (val) => {
            if (props.compareField) {
                return val === props.compareField || 'برابر نیست';
            }
            return true; // در صورتی که فیلد مقایسه وجود نداشته باشد
        },
    ]);

    return { props, rules, requestStore };
};
