<template>
    <div class="flex gap-3">
        <j-btn flat dense size="md" icon="add" :to="{ name: 'crm_production_order.create' }" title="جدید"
            :label="$q.screen.lt.sm ? '' : 'جدید'" />
        <template v-if="props.selected?.length > 0">
            <j-btn v-if="props.selected[0].status == 'DRAFT' && props.selected?.length == 1" flat dense size="md"
                icon="edit" title="ویرایش" :label="$q.screen.lt.sm ? '' : 'ویرایش'"
                :to="{ name: 'crm_production_order.edit', params: { id: props.selected[0].id } }" />
            <j-btn v-if="props.selected[0].status == 'DRAFT'" flat dense size="md" icon="delete" title="حذف"
                :label="$q.screen.lt.sm ? '' : 'حذف'" @click="doDelete" />
            <j-btn v-if="props.selected[0].status == 'DRAFT' && props.selected?.length == 1" flat dense size="md"
                icon="file_upload" title="ارسال به کارخانه" :label="$q.screen.lt.sm ? '' : 'ارسال به کارخانه'"
                @click="sendToProduction" />

                <j-btn flat dense size="md" icon="print" title="پرینت"
                :label="$q.screen.lt.sm ? '' : 'پرینت'" @click="doPrint" />
        </template>
    </div>
</template>
<script>
import { computed } from 'vue';
import { useTableStore } from '@/stores/table.store';
import { useQuasar } from "quasar";
import { api } from '@/boot/axios';
import PrintOrder from './printOrder.vue';

export default {
    setup() {
        const tableStore = useTableStore()
        const props = computed(() => tableStore.props);
        const $q = useQuasar()
        return {
            props,
            doDelete() {
                if (props.value.selected.filter(f => f.status != 'DRAFT').length > 0)
                    $q.notify({
                        type: 'negative',
                        message: 'یکی از سفارش ها به کارخانه ارسال شده است، فقط سفارش های ارسال نشده امکان حذف دارند.'
                    })
                else
                    tableStore.actions.doDelete(props.value.selected)
            },
            sendToProduction() {
                $q.dialog({
                    title: "آیا سفارش به کارخانه ارسال شود؟",
                    cancel: true,
                    ok: 'بله',
                    cancel: 'خیر',
                }).onOk(() => {
                    api.post(`production/crm_production_order/${props.value.selected[0].id}/sendToProduction`).then(res => {
                        tableStore.actions.fetchData();
                    });
                })
            },
            doPrint() {
                $q.dialog({
                    component: PrintOrder,
                    componentProps: {
                        selected: props.value.selected[0]
                    },
                })
                    .onDismiss(async () => {
                        console.log('onHide')
                        tableStore.actions.fetchData()
                    })
            }
        }
    }
}
</script>