<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Good\Entities\Attribute;
use Modules\Good\Entities\AttributeItem;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create($this->table(), function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Attribute::class)->constrained(Attribute::getTableName());
            $table->foreignIdFor(AttributeItem::class)->nullable()->constrained(AttributeItem::getTableName());
            $table->boolean('is_true')->default(true);
            $table->json('condition')->default('{}');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists($this->table());
    }

    public function table(){
        $prefix = config('good.prefix');
        return ($prefix ? $prefix .'__' : '').'attribute_item_conditions';
    }
};
