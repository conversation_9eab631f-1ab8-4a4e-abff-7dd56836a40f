<?php

namespace Modules\Production\Entities;

class InstructionItem extends BModel
{
    protected $fillable = [
        'name',
        'station_id',
        'instruction_id',
        'station_work_id',
    ];

    protected $appends = [
        //'station_label',
    ];

    public function getStationLabelAttribute()
    {
        return $this->station->name;
    }
    public function station()
    {
        return $this->belongsTo(Station::class);
    }
    public function instruction()
    {
        return $this->belongsTo(Instruction::class);
    }
    public function stationWork()
    {
        return $this->belongsTo(StationWork::class);
    }
    public function parents()
    {
        return $this->belongsToMany(InstructionItem::class, InstructionItemParent::class, null, 'parent_id');
    }
    public function childs()
    {
        return $this->belongsToMany(InstructionItem::class, InstructionItemParent::class, 'parent_id');
    }
    public function treeParents()
    {
        return $this->belongsToMany(InstructionItem::class, InstructionItemParent::class, null, 'parent_id')->with('parents');
    }
    public function treeChilds()
    {
        return $this->belongsToMany(InstructionItem::class, InstructionItemParent::class, 'parent_id')->with('childs');
    }
    public function conditions()
    {
        return $this->hasMany(InstructionItemCondition::class); //->withPivot(['condition', 'items']);
    }
}
