<template>
  <div class="q-gutter-sm">
    <j-table-data :url="`/production/instruction/${$route.params.id}/items`" dense :columns="columns"
      @loaded="loadedData">
      <template #dialog="props">
        <table-form v-bind="props" :instruction-id="$route.params.id" />
      </template>
      <template #body-cell-parents="props">
        <q-td :props="props">
          <q-chip v-for="parent, index in props.row.parents" :key="index" square flat text-color="white" color="primary" dense>{{
    parent.station_work ? parent.station_work.name : parent.name
            }}</q-chip>
        </q-td>
      </template>
    </j-table-data>
  </div>
</template>

<script>
import { usePublicStore } from "@/stores";
import TableForm from "./form.vue";

export default {
  setup() {

    const columns = [
      {
        name: 'name',
        required: true,
        label: 'نام',
        field: row => row.station_work ? row.station_work.name : row.name,
        sortable: true,
        filter: 'FilterInput',
      },
      {
        name: 'station',
        required: true,
        label: 'ایستگاه',
        field: 'station_label',
        sortable: true,
        filter: 'FilterInput',
      },
      {
        name: 'parents',
        required: true,
        label: 'پیش نیازها',
        field: row => row.parents.map(m => (m.station_work ? m.station_work.name : m.name)).join(' , '),
      },
      {
        name: 'conditions',
        required: true,
        label: 'شرط ها',
        field: row => JSON.stringify(row.conditions.map(m => [m.attribute_id, m.items])),
      },

    ]
    const publicStore = usePublicStore();
    const loadedData = (value) => {
      publicStore.titleWindow = value.model.name
    }
    return {
      columns,
      loadedData,
    };
  },
  components: { TableForm },
};
</script>
