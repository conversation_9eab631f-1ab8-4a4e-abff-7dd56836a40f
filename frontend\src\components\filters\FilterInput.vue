<template>

    <div class="text-center">
        <slot />
    </div>
    <j-input ref="input" v-model="data" dense hide-bottom-space @keyup.enter="save" @clear="save" @blur="save"
        class="h-6 important bg-white" input-class="text-xs" style="min-width:50px" />

</template>
<script>
import { ref } from 'vue'

export default {
    props: {
        value: String,
        label: String,
    },
    setup(props, context) {
        const popup = ref(null);
        const input = ref(null);
        const data = ref(props.value ?? '');

        const hide = () => {
            popup.value.hide();
            save();
        }
        const save = () => {
            context.emit("input", data.value);
            context.emit("update:input", data.value);
            context.emit("update:value", data.value);
        }
        const show = () => {
            input.value.focus()
        }
        return {
            data,
            popup,
            save,
            hide,
            input,
            show,
        };
    },
}
</script>