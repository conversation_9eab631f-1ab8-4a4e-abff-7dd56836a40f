<template>
    <!-- <div class="bg-gray-100 p-3 rounded-md grid grid-cols-1 sm:grid-cols-3">
        <template v-for="attribute, i in attributes" :key="i">
            <q-checkbox v-model="value" :label="attribute.name" :val="attribute.id" />
            
        </template>
    </div> -->
    <q-list bordered separator dense>
        <template v-for="attribute, i in attributes" :key="i">
            <q-item>
                <q-item-section>
                    <div class="content-center flex">
                        <q-checkbox v-model="selected" :label="attribute.name" :val="attribute.id" />
                    </div>
                    <template v-if="selected.findIndex(f => f == attribute.id) >= 0 && attribute.items.length">
                        <q-separator />

                        <div v-if="selected.findIndex(f => f == attribute.id) >= 0 && attribute.items.length"
                            class="grid md:grid-cols-4">
                            <q-checkbox v-for="item, j in attribute.items" v-model="options[attribute.id].options"
                                :label="item.name" :val="item.id" :key="j" />
                        </div>
                    </template>
                </q-item-section>
            </q-item>
        </template>
    </q-list>
</template>
<script>
import { ref } from '@vue/reactivity'
import { watch } from 'vue'
export default {
    props: {
        value: {
            type: Object,
            default: () => { }
        },
        attributes: {
            type: Array,
            default: () => []
        },
    },
    setup(props, { emit }) {

        const selected = ref([])
        const options = ref(props.value ?? {})

        watch(() => props.value, (newVal) => {
            options.value = newVal;
            selected.value = Object.keys(newVal).map(m => m * 1);
        })

        watch(() => options.value, (value) => {
            emit('update:value', value)
        }, { deep: true })
        watch(() => selected.value, () => {
            selected.value.forEach(k => {
                if (!options.value[k] || !options.value[k].options)
                    options.value[k] = { options: [] }
            })
            Object.keys(options.value).forEach(k => {
                if (selected.value.findIndex(f => f == k) < 0)
                    delete options.value[k]
            })
        })

        return {
            selected,
            options,
        }
    },
}
</script>