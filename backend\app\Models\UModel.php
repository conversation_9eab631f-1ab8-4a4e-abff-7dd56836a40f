<?php

namespace App\Models;

use App\Casts\JalaliDateTime;
use App\Traits\DataFilter;
use App\Traits\DataPagination;
use App\Traits\ModuleTrait;
use Hek<PERSON><PERSON>ser\Verta\Facades\Verta;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Morilog\Jalali\Jalalian;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class UModel extends Model
{
    use HasFactory, LogsActivity, SoftDeletes, DataFilter, DataPagination, ModuleTrait;
    protected $merge_casts = [];
    protected $casts = [
        'created_at' => JalaliDateTime::class,
        'updated_at' => JalaliDateTime::class,
    ];
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->casts = [...$this->casts, ...$this->merge_casts];
    }
    public function getJCreatedAtAttribute($date)
    {
        //return !$date ? null : Verta::instance($date)->format('Y/m/d H:i:s');
        return Jalalian::fromCarbon($date)->format('Y/m/d H:i:s');
    }

    public function getJUpdatedAtAttribute($date)
    {
        return !$date ? null : Jalalian::fromCarbon($date)->format('Y/m/d H:i:s');
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()->logAll();
    }
    public function onDelete() {}
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($model) {
            Log::debug($model->id);
            // Cache::forget($model->getTableName().'.'.$model->id);
            $model->onDelete();
        });

        // static::updating(function ($model) {
        //     // update cache content
        //     Cache::put($model->getTableName().'.'.$model->id,$model);
        // });


    }

    public static function getTableName()
    {
        return (new static)->getTable();
    }

    public static function getForeignKeyName()
    {
        return (new static)->getForeignKey();
    }

    public function createdAtActivity()
    {
        return $this->activities()->where('event', 'created');
    }

    public function updatedAtActivity()
    {
        return $this->activities()->where('event', 'updated');
    }
}
