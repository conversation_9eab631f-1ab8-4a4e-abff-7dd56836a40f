import { usePublicStore } from "@/stores";
import { crm_home_page as home_page } from "../index";
import {
    page403View
} from "@/views/index";
export default function middlewarePermissions({ next, router, auth, to }) {
    console.log('middlewarePermissions')

    // Skip permission check for login page
    if (to.name === 'login') {
        return next();
    }

    // Skip if user is not authenticated
    if (!auth.token || !auth.user) {
        return next();
    }

    if (!checkMatchedPermissions(to.matched, auth)) {
        console.log('Permission denied for route:', to.name);
        const publicStore = usePublicStore()
        publicStore.setNotAccess(true)
        return next();
    }
    else {
        next();
    }
}

function checkPermission(auth, permissions) {
    console.log('middlewarePermissions checkPermission')

    if (auth.user && auth.user.is_super_admin) return true;
    if (Array.isArray(permissions)) {
        let find = false;
        permissions.map((m) => {
            if (auth.user.permissions.includes(m)) find = true;
        });
        if (!find) return false;
    } else if (typeof permissions == "string") {
        if (!auth.user.permissions.includes(permissions)) return false;
    }
    return true;
}

function checkMatchedPermissions(matched, auth){
    return matched.filter(route => {
        return !checkPermission(auth, route.meta.permissions);
    }).length == 0
}