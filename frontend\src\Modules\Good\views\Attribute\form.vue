<template>

    <j-form-data url="good/attribute" :form="form" hasCreateRoute>
        <template v-slot="{ form, formOptions }">
            <j-input v-model="form.name" error-field="name" label="نام ویژگی" />
            <j-input v-model="form.key" error-field="key" label="کد" />
            <j-input v-model="form.group_name" error-field="group_name" label="نام گروه" />
            <j-toggle v-model="form.required" label="اجباری" />
            <j-toggle v-model="form.showing" label="قابل نمایش" />
            <j-input v-model="form.sort" label="ترتیب" />
            <j-select v-model="form.type" :options="formOptions.types" error-field="type" label="نوع" emit-value
            map-options />
            

            <j-select-remote v-if="form.type" v-model="form.parent_id" dense url="good/attribute/search"
                :params="{ type: form.type }" label="والد" />

            <attribute-select v-model:value="form.items"
                v-if="['SELECT', 'SELECT_IMAGE'].includes(form.type) && formOptions.types && formOptions.types.findIndex(f => f.value == form.type) >= 0"
                :columns="formOptions.types[formOptions.types.findIndex(f => f.value == form.type)].columns ?? []" />
        </template>
    </j-form-data>
</template>
<script setup>
import { ref } from 'vue'
import AttributeSelect from './AttributeSelect.vue';
const form = ref({
    items:[]
})

</script>
