<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Good\Entities\Attribute;
use Modules\Good\Entities\AttributeItem;
use Modules\Good\Entities\Good;
use Modules\Good\Entities\Group;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create($this->table(), function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Good::class)->constrained(Good::getTableName());
            $table->foreignIdFor(Attribute::class)->constrained(Attribute::getTableName());
            $table->json('options')->nullable();
            $table->integer('sort')->nullable();
            //$table->foreignIdFor(AttributeItem::class)->nullable()->constrained(AttributeItem::getTableName());
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists($this->table());
    }

    public function table(){
        $prefix = config('good.prefix');
        return ($prefix ? $prefix .'__' : '').'attribute_goods';
    }
};
