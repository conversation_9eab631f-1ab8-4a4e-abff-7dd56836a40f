<?php

namespace Modules\Production\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\API\BaseController;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Production\Entities\ProductionOrder;
use Modules\Production\Entities\ProductionOrderItemWorkInstruction;
use Modules\Production\Entities\Station;

class ProductionChecklistController extends BaseController
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index(Station $station)
    {
        return JsonResource::collection($station->productionOrderItemWorkInstruction()
            ->whereHas('productionOrderItem', fn ($query) => $query->whereHas('productionOrder', fn ($query) => $query->where('status', ProductionOrder::PRODUCTION)))
            ->where('status', productionOrderItemWorkInstruction::PROCESSING)->with([
                //'instructionItem',
                'productionOrderItem.productionOrder',
                'productionOrderItem.good',
                'workInstruction.stationWork.attributes'
                //'instructionItem.parents',
            ])
            ->get()
            ->groupBy(function ($q) {
                return $q->productionOrderItem->production_order_id;
            })->map(function ($m) {
                $productionOrder = $m[0]->productionOrderItem->productionOrder;
                $productionOrder->items = $productionOrder->items()->with('good')->get();
                return [
                    'order' =>  [
                        'code' => $m[0]->productionOrderItem->productionOrder['code'],
                        'customer_name' => $m[0]->productionOrderItem->productionOrder['customer_name'],
                        'party_name' => $m[0]->productionOrderItem->productionOrder['party_name'],
                        'delivery_date' => $m[0]->productionOrderItem->productionOrder['delivery_date'],
                    ],
                    'items' => $m->groupBy(fn ($m) => $m['workInstruction']['stationWork']['id'])->values()->map(fn ($m) => [

                        'attribute_columns' => $m[0]['workInstruction']['stationWork']['attributes']->pluck('id'),
                        'station_work_name' => $m[0]['workInstruction']['stationWork']['name'],
                        'production_order_items' => $m->map(fn ($mm) => [
                            'id' => $mm['id'],
                            'good_name' => $mm['productionOrderItem']['good']['name'],
                            'attributes' => $mm['productionOrderItem']['attributes'],
                        ])

                    ]),
                    // 'productionOrder' => $productionOrder,

                ];
            })->values()
            ->sortBy(function ($m) {
                return $m['order'] ? $m['order']['delivery_date'] : null;
            })->values())->additional([
            'station' => $station,
            // 'attributes' => $station->attributes()->with('items')->get(),

        ]);
    }


    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, Station $station)
    {
        $data = $request->input('data');
        $ids = collect($data)->pluck('id');
        $items = ProductionOrderItemWorkInstruction::whereIn('id', $ids)->with(['workInstruction.treeChilds.treeParents.conditions',
         'workInstruction.treeChilds.conditions', 'productionOrderItem'])->get();

        foreach ($items as $item) {
            $status = collect($data)->where('id', $item['id'])->pluck('status')->first();
            $item['status'] = $status;
            $item->save();
            foreach ($item->workInstruction->treeChilds as $child) {
                $this->instruction_child($child, $item);
            }
        }
        //dd(1);
    }
    public static function checkCondition($child, $attributes)
    {
        $condition_done = true;
        foreach ($child->conditions as $condition) {

            if (
                !isset($attributes->{$condition->attribute_id})
                || ($condition->condition && !in_array($attributes->{$condition->attribute_id}, $condition->items))
                || (!$condition->condition && in_array($attributes->{$condition->attribute_id}, $condition->items))
            ) {
                $condition_done = false;
            }
        }
        return $condition_done;
    }
    public static function instruction_child($child, $item, $a = 0)
    {




        if (self::checkCondition($child, $item->productionOrderItem->attributes)) {
            $parent_done = true;
            foreach ($child->treeParents as $parent) {
                if (
                    productionOrderItemWorkInstruction::where('work_instruction_id', $parent->id)
                    ->where('production_order_item_id', $item->production_order_item_id)
                    ->where('code', $item->code)
                    ->pluck('status')->first() !== productionOrderItemWorkInstruction::DONE
                    &&
                    self::checkCondition($parent, $item->productionOrderItem->attributes)
                ) {
                    $parent_done = false;
                }
            }

            if ($parent_done) {
                //dd(5,$child);
                productionOrderItemWorkInstruction::updateOrCreate([
                    'production_order_item_id' => $item->production_order_item_id,
                    'work_instruction_id' => $child['id'],
                    'code' => $item->code
                ], [
                    'status' => productionOrderItemWorkInstruction::PROCESSING,

                ]);
            }
        } else {
            //var_dump($child);
            //dd($child);
            $child->childs = $child->treeChilds()->with(['treeParents.conditions', 'conditions'])->get();
            // if ($child->id == 9)
            //     dd($child->toArray(), $child->childs->toArray());
            foreach ($child->childs as $chld) {
                self::instruction_child($chld, $item, 1);
            }
        }
    }
}
