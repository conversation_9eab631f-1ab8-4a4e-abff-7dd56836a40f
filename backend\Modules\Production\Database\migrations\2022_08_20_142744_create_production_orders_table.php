<?php

use App\Models\User;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Production\Entities\Party;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create($this->table(), function (Blueprint $table) {
            $table->id();
            $table->string('code')->nullable();
            $table->foreignIdFor(Party::class)->constrained(Party::getTableName());
            $table->string('customer_name');
            $table->string('status');
            $table->boolean('is_created_by_customer')->default(false);
            $table->text('description')->default('')->nullable();
            $table->dateTime('delivery_date')->nullable();
            $table->dateTime('submit_date')->nullable();
            $table->double('discount')->default(0);
            $table->foreignIdFor(User::class)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists($this->table());
    }

    public function table()
    {
        $prefix = config('production.prefix');
        return ($prefix ? $prefix . '__' : '') . 'production_orders';
    }
};
