<?php

namespace Modules\Production\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\API\BaseController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Modules\BuyAndSell\Entities\Order;
use Modules\BuyAndSell\Entities\Party;
use Modules\Production\Entities\MachineProblemReport;
use Modules\Production\Entities\ProductionOrder;
use Modules\Production\Entities\ProductionOrderItem;
use Modules\Production\Entities\Station;

class StationStatisticController extends BaseController
{
    public static function getStationStatistic()
    {

        $response = Cache::get("stationStatistic", null);
        if ($response)
            return $response;

        $stations = Station::query();

        $response =  $stations
            ->with(['instructionItemChecklists' => function ($m) {
                $m->where('status', 'PROCESSING');
                $m->whereHas('productionOrderItem', function ($m) {
                    $m->whereHas('productionOrder', function ($m) {
                        $m->where('status', ProductionOrder::PRODUCTION);
                    });
                });
                $m->with(['productionOrderItem' => function ($m) {
                    $m->whereHas('productionOrder', function ($m) {
                        $m->where('status', ProductionOrder::PRODUCTION);
                    });
                }]);
            }])
            ->get()->map(function ($q) {
                return [
                    'id' => $q['id'],
                    'name' => $q['name'],
                    'production_order_item_count' => $q->instructionItemChecklists->where('status', 'PROCESSING')->count(),
                    'production_order_count' =>  $q->instructionItemChecklists->where('status', 'PROCESSING')->groupBy(function ($m) {
                        return $m->productionOrderItem['production_order_id'];
                    })->count(),
                ];
            });

        Cache::put("stationStatistic", $response, 60);
        return $response;
    }

    public static function getOrderStatistic()
    {

        $response = Cache::get("orderStatistic", null);
        if ($response)
            return $response;


        $count_order_production = ProductionOrder::query()->where('status', ProductionOrder::PRODUCTION)
            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
            ->count();
        $count_order_near_delivery = ProductionOrder::query()->where('status', ProductionOrder::PRODUCTION)->whereRaw('DATEDIFF(now(), delivery_date)>-5 and DATEDIFF(now(), delivery_date)<=-1')->count();
        $count_order_delay = ProductionOrder::query()->whereHas('party')->whereNot('party_id', 234)->where('status', ProductionOrder::PRODUCTION)->whereRaw('DATEDIFF(now(), delivery_date)>-1')->count();
        $count_order_finished = ProductionOrder::query()->whereHas('party')->whereNot('party_id', 234)
            ->where('status', ProductionOrder::FINISH_JOB)
            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
            ->count();

        $response = [
            [
                'label' => 'سفارشات در مرحله خط تولید',
                'value' => $count_order_production,
                'count' => ProductionOrderItem::query()
                    ->whereHas('good', function ($q) {
                        $q->whereIn('group_id', [8, 16, 18, 20, 24]);
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::PRODUCTION)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_frame' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [10]);
                        })->orWhere('attributes', 'like', '%"hasFrame":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::PRODUCTION)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_rookob' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [12]);
                        })->orWhere('attributes', 'like', '%"hasRokoob":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::PRODUCTION)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'query' => [
                    'status' => ProductionOrder::PRODUCTION,
                ],
                'color' =>  $count_order_production >= 150 ? 'red' : ''
            ],
            [
                'label' => 'سفارشات نزدیک به تحویل',
                'value' => $count_order_near_delivery,
                'color' => $count_order_near_delivery >= 50 ? 'red' : '',
                'count' => ProductionOrderItem::query()
                    ->whereHas('good', function ($q) {
                        $q->whereIn('group_id', [8, 16, 18, 20, 24]);
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::PRODUCTION)->whereRaw('DATEDIFF(now(), delivery_date)>-5 and DATEDIFF(now(), delivery_date)<=-1');
                    })->sum('count'),
                'count_frame' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [10]);
                        })->orWhere('attributes', 'like', '%"hasFrame":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::PRODUCTION)->whereRaw('DATEDIFF(now(), delivery_date)>-5 and DATEDIFF(now(), delivery_date)<=-1');
                    })->sum('count'),
                'count_rookob' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [12]);
                        })->orWhere('attributes', 'like', '%"hasRokoob":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::PRODUCTION)->whereRaw('DATEDIFF(now(), delivery_date)>-5 and DATEDIFF(now(), delivery_date)<=-1');
                    })->sum('count'),
                'query' => [
                    'status' => ProductionOrder::PRODUCTION,
                    'delivery_date' => '{"date_of":"' . verta()->addDay()->format('Y/m/d') . '","date_to":"' . verta()->addDays(4)->format('Y/m/d') . '"}',
                    'sortBy' => 'delivery_date',
                    'descending' => false,
                ]
            ],
            [
                'label' => 'سفارشات تاخیر افتاده',
                'value' => $count_order_delay,
                'color' => $count_order_delay >= 35 ? 'red' : '',
                'count' => ProductionOrderItem::query()
                    ->whereHas('good', function ($q) {
                        $q->whereIn('group_id', [8, 16, 18, 20, 24]);
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::PRODUCTION)->whereRaw('DATEDIFF(now(), delivery_date)>-1');
                    })->sum('count'),
                'count_frame' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [10]);
                        })->orWhere('attributes', 'like', '%"hasFrame":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::PRODUCTION)->whereRaw('DATEDIFF(now(), delivery_date)>-1');
                    })->sum('count'),
                'count_rookob' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [12]);
                        })->orWhere('attributes', 'like', '%"hasRokoob":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::PRODUCTION)->whereRaw('DATEDIFF(now(), delivery_date)>-1');
                    })->sum('count'),
                'query' => [
                    'status' => ProductionOrder::PRODUCTION,
                    'delivery_date' => '{"date_to":"' . verta()->format('Y/m/d') . '"}',
                    'sortBy' => 'delivery_date',
                    'descending' => false,
                ]

            ],
            [
                'label' => 'سفارشات در مرحله اتمام کار',
                'value' => $count_order_finished,
                'color' => $count_order_finished >= 40 ? 'red' : '',
                'count' => ProductionOrderItem::query()
                    ->whereHas('good', function ($q) {
                        $q->whereIn('group_id', [8, 16, 18, 20, 24]);
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::FINISH_JOB)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_frame' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [10]);
                        })->orWhere('attributes', 'like', '%"hasFrame":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::FINISH_JOB)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_rookob' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [12]);
                        })->orWhere('attributes', 'like', '%"hasRokoob":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::FINISH_JOB)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'query' => [
                    'status' => ProductionOrder::FINISH_JOB,
                ],
            ],
            [
                'label' => 'سفارشات آنلاین ',
                'value' => ProductionOrder::query()->where('is_created_by_customer', true)
                    //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                    ->count(),
                'count' => ProductionOrderItem::query()
                    ->whereHas('good', function ($q) {
                        $q->whereIn('group_id', [8, 16, 18, 20, 24]);
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('is_created_by_customer', true)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_frame' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [10]);
                        })->orWhere('attributes', 'like', '%"hasFrame":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('is_created_by_customer', true)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_rookob' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [12]);
                        })->orWhere('attributes', 'like', '%"hasRokoob":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('is_created_by_customer', true)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'query' => [
                    'is_created_by_customer' => '[true]',
                ],
            ],
            [
                'label' => 'سفارشات در مرحله پیش فاکتور',
                'value' => ProductionOrder::query()->where('status', ProductionOrder::PREORDER)
                    //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                    ->count(),
                'count' => ProductionOrderItem::query()
                    ->whereHas('good', function ($q) {
                        $q->whereIn('group_id', [8, 16, 18, 20, 24]);
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::PREORDER)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_frame' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [10]);
                        })->orWhere('attributes', 'like', '%"hasFrame":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::PREORDER)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_rookob' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [12]);
                        })->orWhere('attributes', 'like', '%"hasRokoob":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::PREORDER)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'query' => [
                    'status' => ProductionOrder::PREORDER,
                ],
            ],
            [
                'label' => 'سفارشات در مرحله تایید مشتری',
                'value' => ProductionOrder::query()->where('status', ProductionOrder::CONFIRM_CUSTOMER)
                    //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                    ->count(),
                'count' => ProductionOrderItem::query()
                    ->whereHas('good', function ($q) {
                        $q->whereIn('group_id', [8, 16, 18, 20, 24]);
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::CONFIRM_CUSTOMER)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_frame' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [10]);
                        })->orWhere('attributes', 'like', '%"hasFrame":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::CONFIRM_CUSTOMER)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_rookob' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [12]);
                        })->orWhere('attributes', 'like', '%"hasRokoob":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::CONFIRM_CUSTOMER)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'query' => [
                    'status' => ProductionOrder::CONFIRM_CUSTOMER,
                ],
            ],
            [
                'label' => 'سفارشات در مرحله تاییده مدیریت',
                'value' => ProductionOrder::query()->where('status', ProductionOrder::CONFIRM)
                    //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                    ->count(),
                'count' => ProductionOrderItem::query()
                    ->whereHas('good', function ($q) {
                        $q->whereIn('group_id', [8, 16, 18, 20, 24]);
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::CONFIRM)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_frame' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [10]);
                        })->orWhere('attributes', 'like', '%"hasFrame":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::CONFIRM)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_rookob' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [12]);
                        })->orWhere('attributes', 'like', '%"hasRokoob":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::CONFIRM)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'query' => [
                    'status' => ProductionOrder::CONFIRM,
                ],
            ],

            [
                'label' => 'سفارشات در مرحله ارسال پیش فاکتور',
                'value' => ProductionOrder::query()->where('status', ProductionOrder::SEND_PREORDER)
                    //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                    ->count(),
                'count' => ProductionOrderItem::query()
                    ->whereHas('good', function ($q) {
                        $q->whereIn('group_id', [8, 16, 18, 20, 24]);
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::SEND_PREORDER)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_frame' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [10]);
                        })->orWhere('attributes', 'like', '%"hasFrame":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::SEND_PREORDER)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_rookob' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [12]);
                        })->orWhere('attributes', 'like', '%"hasRokoob":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::SEND_PREORDER)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'query' => [
                    'status' => ProductionOrder::SEND_PREORDER,
                ],
            ],
            [
                'label' => 'سفارشات در مرحله تایید مالی',
                'value' => ProductionOrder::query()->where('status', ProductionOrder::FINANCIAL_APPROVAL)
                    //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                    ->count(),
                'count' => ProductionOrderItem::query()
                    ->whereHas('good', function ($q) {
                        $q->whereIn('group_id', [8, 16, 18, 20, 24]);
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::FINANCIAL_APPROVAL)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_frame' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [10]);
                        })->orWhere('attributes', 'like', '%"hasFrame":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::FINANCIAL_APPROVAL)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_rookob' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [12]);
                        })->orWhere('attributes', 'like', '%"hasRokoob":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::FINANCIAL_APPROVAL)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'query' => [
                    'status' => ProductionOrder::FINANCIAL_APPROVAL,
                ],
            ],


            [
                'label' => 'سفارشات در مرحله حمل شده',
                'value' => ProductionOrder::query()->where('status', ProductionOrder::DELIVERED)
                    //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                    ->count(),
                'count' => ProductionOrderItem::query()
                    ->whereHas('good', function ($q) {
                        $q->whereIn('group_id', [8, 16, 18, 20, 24]);
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::DELIVERED);
                    })->sum('count'),
                'count_frame' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [10]);
                        })->orWhere('attributes', 'like', '%"hasFrame":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::DELIVERED)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'count_rookob' => ProductionOrderItem::query()
                    ->where(function ($q) {

                        $q->whereHas('good', function ($q) {
                            $q->whereIn('group_id', [12]);
                        })->orWhere('attributes', 'like', '%"hasRokoob":true%');
                    })
                    ->whereHas('productionOrder', function ($q) {
                        $q->where('status', ProductionOrder::DELIVERED)

                            //->where('delivery_date', '<', verta()->parse('1402-01-01')->formatGregorian('Y-m-d'))
                        ;
                    })->sum('count'),
                'query' => [
                    'status' => ProductionOrder::DELIVERED,
                ],
            ],
        ];

        Cache::put("orderStatistic", $response, 60);
        return $response;
    }
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {

        //dd($response);

        $getStationStatistic =  self::getStationStatistic();
        $station_user = Auth::user()->stations()->pluck('id');
        if (!Auth::user()->is_super_admin && Auth::user()->roles()->where('name', 'manage_production')->count() == 0)
            $getStationStatistic = self::getStationStatistic()->whereIn('id', $station_user)->values();


        $getOrderStatistic =  [];
        $station_user = Auth::user()->stations()->pluck('id');
        if (
            Auth::user()->hasPermissionTo('dashboard_info', 'api')
            || Auth::user()->is_super_admin
            || Auth::user()->roles()->where('name', 'manage_production')->count() > 0
        )
            $getOrderStatistic = self::getOrderStatistic();

        $response = [
            'stations' => $getStationStatistic,
            'total_machine_problem_report' => MachineProblemReport::where('status', MachineProblemReport::REPORTED)->count(),
            'total_production_order' => ProductionOrder::whereNot('status', ProductionOrder::PREORDER)->count(),
            'total_party' => Party::count(),
            'orders' => $getOrderStatistic,
        ];






        return $this->handleResponse($response);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy($id)
    {
        //
    }
}
