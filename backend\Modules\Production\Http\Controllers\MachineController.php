<?php

namespace Modules\Production\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\API\BaseController;
use App\Repositories\ModelRepositoryInterface;
use Arr;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Production\Entities\Machine;

class MachineController extends BaseController
{
    public function __construct(ModelRepositoryInterface $repository)
    {
        $repository->model = Machine::class;
        $this->repository = $repository;
    }

    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return $this->repository->getAll();
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        $item = Machine::create($request->all());
        $item->problems()->sync(collect($request->input('problems') ?? [])->map(fn ($m) => Arr::only($m, ['name', 'id']))->toArray());
        $item->problems;
        return $this->handleResponse($item, trans('request.done'));
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(Machine $machine)
    {
        $machine->problems;
        return $this->handleResponse($machine);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, Machine $machine)
    {
        $machine->update($request->all());
        $machine->problems()->sync(collect($request->input('problems') ?? [])->map(fn ($m) => Arr::only($m, ['name', 'id']))->toArray());
        $machine->problems;

        return $this->handleResponse($machine, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy(Machine $machine)
    {
        return $this->handleResponse($machine->delete());
    }
}
