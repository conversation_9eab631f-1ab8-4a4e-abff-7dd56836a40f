<?php

namespace Modules\Production\Entities;

use Modules\Good\Entities\Good;

class WorkInstruction extends BModel
{
    protected $fillable = [
        'station_work_id',
        'good_id',
        'station_id',
    ];
    public function stationWork()
    {
        return $this->belongsTo(StationWork::class);
    }
    public function good()
    {
        return $this->belongsTo(Good::class);
    }

    public function parents()
    {
        return $this->belongsToMany(WorkInstruction::class, WorkInstructionParent::class, null, 'parent_id');
    }
    public function childs()
    {
        return $this->belongsToMany(WorkInstruction::class, WorkInstructionParent::class, 'parent_id');
    }
    public function conditions()
    {
        return $this->hasMany(WorkInstructionCondition::class);
    }
}
