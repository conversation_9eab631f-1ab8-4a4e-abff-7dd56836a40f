<?php

namespace Modules\Production\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\API\BaseController;
use App\Repositories\ModelRepositoryInterface;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Production\Entities\ProductionOrder;
use Modules\Production\Entities\ProductionOrderItem;

class ProductionOrderItemController extends BaseController
{
    public function __construct(ModelRepositoryInterface $repository)
    {
        $repository->model = ProductionOrderItem::class;
        $this->repository = $repository;
    }
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function list()
    {
        return $this->repository->getAll([], ['good', 'productionOrder']);

        //return $this->handleResponse(ProductionOrderItem::query()->with(['good'])->paginate());
        return JsonResource::collection(ProductionOrderItem::query()->whereHas('productionOrder', function ($q) {
            $q->where('status', ProductionOrder::PRODUCTION);
        })->with(['good', 'productionOrder'])->paginate(request('per_page') ?? 15));
    }

    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index(ProductionOrder $productionOrder)
    {
        return $productionOrder;
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy($id)
    {
        //
    }
}
