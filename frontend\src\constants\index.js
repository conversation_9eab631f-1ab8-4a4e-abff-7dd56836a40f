// API Endpoints
export const API_ENDPOINTS = {
    AUTH: '/auth',
    LOGIN: '/login',
    LOGOUT: '/logout',
    USER: '/user',
    USERS: '/users',
    ORDERS: '/orders',
    PARTIES: '/production/production_order',
    PRODUCTION: '/production',
    GOODS: '/goods',
    INVENTORY: '/inventory'
};

// Permissions
export const PERMISSIONS = {
    PRODUCTION_ORDER: 'production_order',
    PRODUCTION_ORDER_INDEX: 'production_order.index',
    PRODUCTION_ORDER_CREATE: 'production_order.create',
    PRODUCTION_ORDER_EDIT: 'production_order.edit',
    PRODUCTION_ORDER_DELETE: 'production_order.delete',

    PARTIES: 'parties',
    PARTIES_INDEX: 'parties.index',
    PARTIES_CREATE: 'parties.create',
    PARTIES_EDIT: 'parties.edit',
    PARTIES_DELETE: 'parties.delete',

    GOODS: 'goods',
    GOODS_INDEX: 'goods.index',
    GOODS_CREATE: 'goods.create',
    GOODS_EDIT: 'goods.edit',
    GOODS_DELETE: 'goods.delete'
};

// Route Names
export const ROUTES = {
    LOGIN: 'login',
    DASHBOARD: 'dashboard',
    PROFILE: 'profile',

    PRODUCTION_ORDER: 'production_order',
    PRODUCTION_ORDER_INDEX: 'production_order.index',
    PRODUCTION_ORDER_CREATE: 'production_order.create',
    PRODUCTION_ORDER_EDIT: 'production_order.edit',

    PARTIES: 'parties',
    PARTIES_INDEX: 'parties.index',
    PARTIES_CREATE: 'parties.create',
    PARTIES_EDIT: 'parties.edit',

    GOODS: 'goods',
    GOODS_INDEX: 'goods.index',
    GOODS_CREATE: 'goods.create',
    GOODS_EDIT: 'goods.edit'
};

// Status Constants
export const ORDER_STATUS = {
    PENDING: 'pending',
    IN_PROGRESS: 'in_progress',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled'
};

export const PRODUCTION_STATUS = {
    NOT_STARTED: 'not_started',
    IN_PROGRESS: 'in_progress',
    COMPLETED: 'completed',
    ON_HOLD: 'on_hold'
};

// UI Constants
export const BREAKPOINTS = {
    XS: 475,
    SM: 600,
    MD: 1024,
    LG: 1440,
    XL: 1920,
    XXL: 2048
};

export const NOTIFICATION_TYPES = {
    SUCCESS: 'positive',
    ERROR: 'negative',
    WARNING: 'warning',
    INFO: 'info'
};

// Form Validation Messages
export const VALIDATION_MESSAGES = {
    REQUIRED: 'این فیلد اجباری است',
    EMAIL: 'فرمت ایمیل صحیح نیست',
    MIN_LENGTH: 'حداقل {min} کاراکتر وارد کنید',
    MAX_LENGTH: 'حداکثر {max} کاراکتر مجاز است',
    NUMERIC: 'فقط عدد وارد کنید',
    PHONE: 'شماره تلفن صحیح نیست',
    PASSWORD_MISMATCH: 'رمز عبور و تکرار آن یکسان نیست'
};

// Date Formats
export const DATE_FORMATS = {
    JALALI_SHORT: 'jYYYY/jMM/jDD',
    JALALI_LONG: 'jYYYY/jMM/jDD HH:mm:ss',
    GREGORIAN_SHORT: 'YYYY/MM/DD',
    GREGORIAN_LONG: 'YYYY/MM/DD HH:mm:ss'
};

// File Upload
export const FILE_UPLOAD = {
    MAX_SIZE: 5 * 1024 * 1024, // 5MB
    ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
    ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.pdf']
};

// Table Settings
export const TABLE_SETTINGS = {
    DEFAULT_PAGE_SIZE: 15,
    PAGE_SIZE_OPTIONS: [10, 15, 25, 50, 100],
    MAX_ROWS_PER_PAGE: 100
};

// Local Storage Keys
export const STORAGE_KEYS = {
    TOKEN: 'token',
    USER: 'user',
    THEME: 'theme',
    LANGUAGE: 'language',
    TABLE_SETTINGS: 'table_settings'
};

// Theme Constants
export const THEME_MODES = {
    LIGHT: 'light',
    DARK: 'dark',
    AUTO: 'auto'
};

export const THEME_DENSITIES = {
    COMPACT: 'compact',
    COMFORTABLE: 'comfortable',
    SPACIOUS: 'spacious'
};

export const THEME_BORDER_RADIUS = {
    SMALL: 'small',
    MEDIUM: 'medium',
    LARGE: 'large'
};

export const THEME_FONT_SIZES = {
    SMALL: 'small',
    MEDIUM: 'medium',
    LARGE: 'large'
};
