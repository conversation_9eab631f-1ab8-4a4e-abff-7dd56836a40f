<template>
    <div class="q-gutter-sm">
        <j-table-data :grid="false" url="/production/machine_problem_reports" :columns="columns" :canBeAdd="false"
            :canBeEdit="() => false" v-model:selected="selected" :pagination="{rowsPerPage: 0}">
            <template #body-cell-problems="props">
                <q-td :props="props">
                    <q-chip v-for="problem, index in props.row.problems" :key="index" square flat text-color="white"
                        color="primary" dense>{{
                            problem.name
                        }}</q-chip>
                </q-td>
            </template>
            <template #body-cell-status="props">
                <q-td :props="props">
                    <q-chip square flat text-color="white"
                        :color="props.row.status == 'REPORTED' ? 'red' : (props.row.status == 'REPARING' ? 'orange' : 'green')"
                        dense>{{
                            props.value
                        }}</q-chip>
                </q-td>
            </template>
            <template #bar="{ data }">
                <j-btn dense icon="print" title="چاپ" color="primary" @click="printReport(data)" />

            </template>
            <template #select_bar>
                <j-btn v-if="selected && selected.id && ['REPARING','DONE'].includes(selected.status)" dense icon="content_paste_search"
                    title="برگشت به گزارش شده" color="orange" @click="doProgress(selected.id, 'REPORTED')" />
                <j-btn v-if="selected && selected.id && ['REPORTED','DONE'].includes(selected.status)" dense icon="content_paste_search"
                    title="پیگیری" color="orange" @click="doProgress(selected.id, 'REPARING')" />
                <j-btn v-if="selected && selected.id && selected.status == 'REPARING'" dense icon="assignment_turned_in"
                    title="انجام شد" color="green" @click="doProgress(selected.id, 'DONE')" />

            </template>
        </j-table-data>
    </div>
</template>
  
<script>
//   import TableForm from "./form.vue";
import { ref } from 'vue'
import { useQuasar } from "quasar";
import { api } from '@/boot/axios';
import { usePublicStore } from '@/stores';
import printReport from './print.js'
export default {
    setup() {
        const $q = useQuasar();

        const columns = [
            {
                name: 'id',
                label: 'شماره',
                field: row => row.id ?? '',
                sortable: true,
                filter: {
                    type: 'FilterInput',
                    column: 'id',
                },

            },
            {
                name: 'station_name',
                label: 'ایستگاه',
                field: row => row.station?.name ?? '',
                sortable: true,
                filter: {
                    type: 'FilterInput',
                    relation: 'station',
                    column: 'name',
                },

            },
            {
                name: 'machine_name',
                required: true,
                label: 'دستگاه / ابزار',
                field: row => row.machine?.name ?? '',
                sortable: true,
                filter: {
                    type: 'FilterInput',
                    relation: 'machine',
                    column: 'name',
                },

            },
            {
                name: 'status',
                required: true,
                label: 'وضعیت',
                field: 'label_status',
                sortable: true,
                filter: 'FilterSelect',
                filterOption: 'statuses'
            },
            {
                name: 'problems',
                required: true,
                label: 'مشکلات',
                // field: 'problems',
                sortable: true,
            },
            {
                name: 'description',
                required: true,
                label: 'توضیحات',
                field: 'description',
                sortable: true,
                filter: 'FilterInput',
                style: 'white-space: pre-line;min-width: 250px;'
            },
            {
                name: 'created_at',
                required: true,
                label: 'تاریخ ثبت',
                field: 'created_at',
                sortable: true,
                filter: 'FilterDate'
            },
            {
                name: 'updated_at',
                required: true,
                label: 'تاریخ ویرایش',
                field: 'updated_at',
                sortable: true,
                filter: 'FilterDate'
            },
            {
                name: 'user',
                required: true,
                label: 'کاربر',
                field: row => row.created_at_activity[0]?.causer?.full_name ?? '',
                sortable: true,
                filter: {
                    type: 'FilterInput',
                    relation: 'createdAtActivity.causer',
                    column: 'full_name',
                },
            },

        ]

        return {
            columns,
            selected: ref({}),
            doProgress(id, status) {
                $q.dialog({
                    title: `آیا وضعیت به ${status == 'REPARING' ? 'پیگیری' : 'انجام شده'} تغییر کند؟`,
                    cancel: true,
                })
                    .onOk(() => {
                        api.put(`/production/machine_problem_reports/${id}`, {
                            status,
                        }).then(
                            () => {
                                usePublicStore().reloadViewRouter()
                                //  getAll();
                            }
                        );
                    })

            },
            printReport,
        };
    },
    //components: { TableForm },
};
</script>
  