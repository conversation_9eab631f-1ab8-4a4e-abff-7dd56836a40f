<?php

namespace Modules\BuyAndSell\Entities;

use App\Models\UModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class BModel extends UModel
{
    use HasFactory;
    public function getTable()
    {
        return $this->table ?? (config('buyandsell.prefix') ?config('buyandsell.prefix') .'__' : '') . Str::snake(Str::pluralStudly(class_basename($this)));

    }
}
