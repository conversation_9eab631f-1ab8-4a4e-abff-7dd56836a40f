<?php

namespace Modules\BuyAndSell\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\API\BaseController;
use App\Repositories\ModelRepositoryInterface;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\BuyAndSell\Entities\Party;
use Modules\BuyAndSell\Http\Requests\PartyRequest;

class PartyController extends BaseController
{
    public function __construct(ModelRepositoryInterface $repository)
    {
        $repository->model = Party::class;
        $this->repository = $repository;
    }
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return $this->repository->getAll();
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(PartyRequest $request)
    {
        $data = Party::create($request->all());
        return $this->handleResponse($data, trans('request.done'));
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(Party $party)
    {
        return $this->handleResponse($party);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(PartyRequest $request, Party $party)
    {
        $party->update($request->all());
        return $this->handleResponse($party, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy(Party $party)
    {
        return $this->handleResponse($party->delete(), trans('request.done'));
    }

    public function search()
    {
        $data = Party::query()->get();
        return $this->handleResponse($data);
    }
}
