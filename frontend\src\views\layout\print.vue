<template>
  <q-dialog maximized>

    <q-card>
      <j-btn @click="print" label="fdsfsd" color="primary" />
      <div ref="content" v-html="message"></div>
    </q-card>
  </q-dialog>
</template>
<script>
import { ref } from 'vue'
import VueHtmlToPaper from '../../htmlToPaper';

export default {
  props: {
    message: String,
  },
  setup() {
    const content = ref(null);
    return {
      content,
      print() {
        VueHtmlToPaper(content.value)
      }
    }
  },

}


</script>



