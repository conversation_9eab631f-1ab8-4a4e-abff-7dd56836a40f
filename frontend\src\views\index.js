// Import views based on current domain
const currentHost = typeof window !== 'undefined' ? window.location.host : '';
const panelDomain = import.meta.env.VITE_PANEL_DOMAIN;
const crmDomain = import.meta.env.VITE_CRM_DOMAIN;

// Shared views (available to both domains)
export const LoginView = () => import('./shared/LoginView.vue');
export const page404View = () => import('./shared/Page404View.vue');
export const page403View = () => import('./shared/Page403View.vue');

// Domain-specific views
if (currentHost === panelDomain || currentHost === 'localhost:5173' || currentHost.includes('localhost')) {
    // Panel views
    export const DashboardView = () => import('./panel/DashboardView.vue');
    export const profileView = () => import('./panel/ProfileView.vue');

} else if (currentHost === crmDomain) {
    // CRM views
    export const DashboardView = () => import('./crm/DashboardView.vue');
    export const CrmDashboardView = () => import('./crm/DashboardView.vue');
    export const profileView = () => import('./crm/ProfileView.vue');
    export const CRMprofileView = () => import('./crm/ProfileView.vue');
    export const CrmLoginView = () => import('./shared/LoginView.vue');

} else {
    // Default to panel views for development
    export const DashboardView = () => import('./panel/DashboardView.vue');
    export const profileView = () => import('./panel/ProfileView.vue');
}

// Additional legacy exports that might be needed
export const membershipRequestView = () => import('./pages/membershipRequestView.vue');
export const partyOnlineView = () => import('./crm/partyOnline/partyOnlineView.vue');


