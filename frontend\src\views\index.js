// Dynamic view imports - domain detection will be handled at component level
import { getCurrentDomainType, DOMAIN_TYPES } from '@/config/domains';

// Shared views (available to both domains)
export const LoginView = () => import('./shared/LoginView.vue');
export const page404View = () => import('./shared/Page404View.vue');
export const page403View = () => import('./shared/Page403View.vue');

// Dynamic view loader based on domain
function getDomainView(panelView, crmView) {
    return () => {
        const domainType = getCurrentDomainType();
        if (domainType === DOMAIN_TYPES.CRM) {
            return import(/* @vite-ignore */ crmView);
        }
        return import(/* @vite-ignore */ panelView);
    };
}

// Domain-aware views
export const DashboardView = getDomainView('./panel/DashboardView.vue', './crm/DashboardView.vue');
export const profileView = getDomainView('./panel/ProfileView.vue', './crm/ProfileView.vue');

// Legacy exports for backward compatibility
export const CrmDashboardView = () => import('./crm/DashboardView.vue');
export const CRMprofileView = () => import('./crm/ProfileView.vue');
export const CrmLoginView = () => import('./shared/LoginView.vue');

// Additional legacy exports
export const membershipRequestView = () => import('./pages/membershipRequestView.vue');
export const partyOnlineView = () => import('./crm/partyOnline/partyOnlineView.vue');


