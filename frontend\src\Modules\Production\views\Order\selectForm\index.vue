<template>
    <div class="grid  gap-5">
        <q-card v-if="false" flat bordered>

            <q-tabs v-model="tab" inline-label align="left" class="text-primary">
                <!-- <q-tab name="all" icon="list" label="لیست" /> -->
                <q-tab v-for="group_tab in group_tabs" :name="group_tab.name" :label="group_tab.title" />
                <!-- <q-tab name="door_rapingi" label="درب رپینگی" />
                <q-tab name="frame" label="چهارچوب" />
                <q-tab name="cover" label="روکوب" /> -->
            </q-tabs>
            <q-separator />

            <q-tab-panels v-model="tab" animated swipeable vertical transition-prev="jump-up" transition-next="jump-up">
                <!-- <q-tab-panel name="all">
                    لیست
                </q-tab-panel> -->
                <q-tab-panel v-for="group_tab in group_tabs" :name="group_tab.name">
                    <component v-if="group_tab.component" :is="group_tab.component" />
                </q-tab-panel>

            </q-tab-panels>
        </q-card>
        <select-good ref="ref_select_good" @confirm="addToTable" v-model="add" />


        <div class="flex sticky -top-4 p-2 gap-2 border-b bg-white" style="z-index: 1;">
            <!-- <j-btn dense icon="add" outline color="primary" :label="!$q.screen.xs ? 'ایجاد' : ''" style="width: 100px;">
                <q-menu fit>
                    <q-list dense>
                        <q-item v-for="group_tab in group_tabs" clickable v-close-popup @click="onAdd(group_tab.name)">
                            <q-item-section>{{ group_tab.title }}</q-item-section>
                        </q-item>
                    </q-list>
                </q-menu>
            </j-btn> -->

            <j-btn dense icon="add" outline color="primary" :label="!$q.screen.xs ? 'افزودن' : ''" @click="onAdd()"
                @shortkey="onAdd()" v-shortkey="['insert']" />

            <template v-if="selected && selected.length > 0">
                <j-btn outline dense icon="edit" :label="!$q.screen.xs ? 'ویرایش' : ''" color="green"
                    @click="onEdit()" />
                <j-btn outline dense icon="file_copy" :label="!$q.screen.xs ? 'کپی' : ''" color="primary"
                    @click="onCopy()" />
                <j-btn outline dense icon="delete" :label="!$q.screen.xs ? 'حذف' : ''" color="red"
                    @click="onDelete()" />
            </template>
        </div>
        <!-- <pre>{{ data }}</pre> -->

        <template v-for="item, good_id in data.group(m => m.good.id + '_' + m.attributes?.hasFrame ?? '')">
            <div class="flex">
                <j-table v-model:selected="selected" :columns="columns(item, good_id)" :rows="item" flat bordered
                    separator="cell" hide-bottom row-key="code" selection="single" :rows-per-page-options="[0]" dense
                    class="flex-auto w-12">

                    <template #body-cell-index="props">
                        <q-td :props="props">
                            {{ props.rowIndex + 1 }}
                        </q-td>
                    </template>

                    <template #body-cell-good="props">
                        <template v-if="props.rowIndex === 0">
                            <q-td :rowspan="item.length" :props="props">
                                {{ props.value }}
                            </q-td>
                        </template>
                    </template>

                    <template #body-cell-price="props">
                        <q-td :props="props">
                            <!-- <detail-price v-model:price_details="props.row.price_details"
                                v-model:price="props.row.price" @update:price_details="updateData" /> -->


                            {{ String.currencyFormat(props.row.price) }}
                            <q-btn icon="edit" flat size="sm" dense>
                                <q-popup-edit v-model="props.row.price" @update:model-value="updateData" buttons
                                    v-slot="scope">
                                    <q-input v-model="scope.value" autofocus @keyup.enter="scope.set"
                                        hide-bottom-space />
                                </q-popup-edit>
                            </q-btn>
                        </q-td>
                    </template>
                    <!-- <template #expand="props">
                    <q-td colspan="100%" style="padding:5px">

                        <q-scroll-area style="max-width: 100%;height:75px;" visible>
                            <detail-attribute v-bind="props" :attributes="attributes" class="bg-white rounded-lg" />
                        </q-scroll-area>
                    </q-td>
                </template> -->

                    <template #body-cell-ModelDoorRapingi="props">
                        <q-td :props="props">
                            <j-image-viewer :title="props.value"
                                :src="getImageAttribute('ModelDoorRapingi', props.row.attributes['ModelDoorRapingi'])" />
                        </q-td>
                    </template>
                    <template #body-cell-modelDoorCNC="props">
                        <q-td :props="props">
                            <j-image-viewer :title="props.value"
                                :src="getImageAttribute('modelDoorCNC', props.row.attributes['modelDoorCNC'])" />
                        </q-td>
                    </template>
                    <template #body-cell-cover="props">
                        <q-td :props="props">
                            <j-image-viewer :title="props.value"
                                :src="getImageAttribute('cover', props.row.attributes['cover'])" />
                        </q-td>
                    </template>
                    <template #body-cell-customModelDoor="props">
                        <q-td :props="props">
                            <j-image-viewer :src="props.row.attributes['customModelDoor']" />
                        </q-td>
                    </template>
                    <template #body-cell-count="props">
                        <q-td :props="props">
                            {{ props.row.count }}
                            <!-- <q-popup-edit v-model="props.row.count" @update:model-value="updateData" buttons v-slot="scope">
                                <j-input v-model="scope.value" dense autofocus @keyup.enter="scope.set" step="0.1"
                                    type="number" min="1" hide-bottom-space />
                            </q-popup-edit> -->
                        </q-td>
                    </template>

                    <template #body-cell-description="props">
                        <q-td :props="props">
                            {{ props.row.description }}
                            <q-btn icon="edit" flat size="sm" dense>
                                <q-popup-edit v-model="props.row.description" @update:model-value="updateData" buttons
                                    v-slot="scope">
                                    <q-input v-model="scope.value" autofocus @keyup.enter="scope.set"
                                        hide-bottom-space />
                                </q-popup-edit>
                            </q-btn>
                        </q-td>
                    </template>

                    <template v-if="item && item.length > 0" #bottom-row>
                        <q-tr>
                            <q-td class="text-bold text-center" colspan="4">
                                جمع
                            </q-td>
                            <!-- <q-td />
                        <q-td /> -->
                            <q-td class="text-center">
                                {{item.reduce((a, b) => a + b.count * 1, 0)}}
                            </q-td>
                            <template v-if="checkPermission('show price order')">

                                <q-td />
                                <q-td class="text-center">
                                    {{String.currencyFormat(item.reduce((a, b) => a + b.count * b.price, 0))}}
                                </q-td>
                            </template>

                            <q-td colspan="50" />
                        </q-tr>
                    </template>
                </j-table>
            </div>
        </template>
        <!-- <q-inner-loading :showing="requestStore.loading" /> -->


    </div>
</template>

<script>
import { computed, ref, watch } from 'vue';
import { api } from '@/boot/axios';
import SelectGood from './selectGood.vue';
import { useQuasar } from "quasar";
import DetailPrice from '../detailPrice.vue';
import DetailAttribute from './detailAttribute.vue';
import { attributeColumns } from '.';
import { checkPermission } from '@/helpers';
import { useRequestStore } from '@/stores';
import DoorCNC from './Groups/DoorCNC/index.vue'
import DoorRapingi from './Groups/DoorRapingi/index.vue'
export default {
    props: {
        id: Number,
        data: {
            type: Array,
            default: () => []
        },
        formOptions: {
            type: Object,
            default: () => { }
        },
        onSubmit: {
            type: Function,
        }
    },
    setup(props, { emit }) {
        const selected = ref([])
        const ref_select_good = ref(null)
        const add = ref(false)
        const $q = useQuasar();
        const groups = ref([])
        const tab = ref('all')
        const requestStore = useRequestStore();


        const columns = (items, good_id) => {
            //console.log('items',items)
            // if (groups.value.length == 0)
            //     return [];
            console.log(attributeColumns({
                attributes: props.formOptions.attributes.filter(f => items.map(m => Object.keys(m.attributes)).flat().unique().includes(f.key)), items, extra: {
                    //hide_table: true
                }
            }))
            return [

                // {
                //     name: "group",
                //     label: "محصول",
                //     style: 'width: 30px',
                //     field: row => row?.good?.name ?? '-'
                // },
                {
                    name: "index",
                    label: 'ردیف',
                    style: 'width: 30px',
                    //field: (row,index) => index,
                    align: 'center',
                    summary: true,
                    checkbox_label: true
                },
                {
                    name: "id",
                    label: 'شناسه',
                    style: 'width: 30px',

                    field: row => row.id ?? '-',
                    align: 'center',
                },
                {
                    name: "good",
                    label: "محصول",
                    style: 'width: 120px',

                    field: row => row.good.name ?? '',
                    align: 'center',
                    hasImage: true,
                    image: row => row.good?.image_src ?? '',
                    summary: true,
                    checkbox_label: true,
                },
                {
                    name: "count",
                    label: "تعداد",
                    style: 'width: 50px',

                    field: "count",
                    align: 'center',
                    summary: true

                }, {
                    name: "price",
                    label: "فی",
                    style: 'width: 120px',

                    field: row => Intl.NumberFormat().format(row.price) + ' ریال',
                    align: 'center',
                    summary: true,
                    permissions: 'show price order',

                }
                , {
                    name: "sum_price",
                    label: "جمع",
                    style: 'width: 120px',

                    field: row => Intl.NumberFormat().format(row.price * row.count) + ' ریال',
                    align: 'center',
                    summary: true,
                    permissions: 'show price order',

                },

                // ...attributeColumns({
                //     attributes: items[0].attributes, items, extra: {
                //         //hide_table: true
                //     }
                // }),
                ...attributeColumns({
                    attributes: props.formOptions.attributes.filter(f => items.map(m => Object.keys(m.attributes)).flat().unique().includes(f.key)), items, extra: {
                        //hide_table: true
                    }
                }),
                {
                    name: "description",
                    label: "توضیحات",
                    field: "description",
                    align: 'center',

                }
            ];

        }
        const data = ref(props.data.map((m, i) => ({ ...m, code: i + 1 })) ?? [])
        watch(() => props.data, (newVal) => {
            data.value = newVal.map((m, i) => ({ ...m, code: i + 1 }))
        })



        // const conditions = ref([])
        // api.get('good/condition').then(res => {
        //     conditions.value = res.result
        // })
        const updateData = (asd) => {
            console.log('updateData', data.value)
            emit('update:data', data.value)
            //props.onSubmit()
        }

        const addToTable = async (value, is_edit) => {
            console.log('addToTable', value, is_edit)
            add.value = false;
            await api.post('production/computePriceOrder', {
                good: value.good, attributes: value.attributes
            }).then(res => {
                value.price = res.sum;
            })
            // const find = data.value.findIndex(f => f.good_id == value.good_id && JSON.stringify(f.attributes) == JSON.stringify(value.attributes))
            // if (find >= 0)
            //     Object.assign(data.value[find], value)
            // else
            //     data.value.push(value)
            if (is_edit) {
                const find = data.value.findIndex(f => f.code == selected.value[0].code);
                if (find >= 0) {
                    Object.assign(data.value[find], value)
                }
            } else
                data.value.push({ ...value, code: data.value.length + 1 })

            emit('update:data', data.value)
            // props.onSubmit()
            selected.value = []

        }

        const onDelete = () => {
            $q.dialog({
                title: "مطمئن هستید؟",
                cancel: true,
                persistent: true,
            }).onOk(() => {
                const value = selected.value[0];
                //const find = data.value.findIndex(f => f.good_id == value.good_id && JSON.stringify(f.attributes) == JSON.stringify(value.attributes))
                const find = data.value.findIndex(f => f.code == value.code)

                if (find >= 0)
                    data.value.splice(find, 1)
                selected.value = []

                emit('update:data', data.value)
                // props.onSubmit()
            });
        }

        const onEdit = () => {
            add.value = true;
            ref_select_good.value.onEdit(selected.value[0])
        }
        const onCopy = () => {
            add.value = true;
            ref_select_good.value.onCopy(selected.value[0])
        }
        const onAdd = (name) => {
            add.value = true
            ref_select_good.value.onAdd()

        }
        // const attribute_columns = computed(() => {
        //     return data.value.map(m => Object.keys(m.attribute)).flat().unique()
        // })

        const group_tabs = ref([
            {
                title: "درب CNC",
                name: "cnc",
                component: DoorCNC
            },
            {
                title: "درب رپینگی",
                name: "rapingi",
                component: DoorRapingi,
            },
            {
                title: "چهارچوب",
                name: "frame",
            },
            {
                title: "روکوب",
                name: "rokoob",
            }
        ])
        const getImageAttribute = (attributeKey, attributeItemKey) => {
            console.log(attributeKey, attributeItemKey)
            return props.formOptions.attributes.find(f => f.key == attributeKey)?.items.find(f => f.key == attributeItemKey)?.data?.image;
        }
        return {
            getImageAttribute,
            group_tabs,
            columns,
            data,
            add,
            // attributes,
            groups,
            // conditions,
            // attribute_columns,
            addToTable,
            onEdit,
            onAdd,
            onDelete,
            onCopy,
            updateData,
            selected,
            ref_select_good,
            checkPermission,
            requestStore,
            tab,
        };
    },
    components: { SelectGood, DetailPrice, DetailAttribute }
}
</script>