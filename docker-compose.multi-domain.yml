# Docker Compose for Multi-Domain Frontend Testing
version: '3.8'

services:
  # Frontend build and serve
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.multi-domain
    ports:
      - "80:80"
    volumes:
      - ./nginx-multi-domain.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - backend
    networks:
      - erp-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=production
      - DB_CONNECTION=mysql
      - DB_HOST=db
      - DB_PORT=3306
      - DB_DATABASE=erp
      - DB_USERNAME=erp_user
      - DB_PASSWORD=erp_password
    depends_on:
      - db
    networks:
      - erp-network

  # Database
  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=erp
      - MYSQL_USER=erp_user
      - MYSQL_PASSWORD=erp_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - erp-network

  # Redis for caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - erp-network

networks:
  erp-network:
    driver: bridge

volumes:
  mysql_data:
