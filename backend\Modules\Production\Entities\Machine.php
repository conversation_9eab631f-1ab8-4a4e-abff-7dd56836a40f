<?php

namespace Modules\Production\Entities;

class Machine extends BModel
{
    protected $fillable = [
        'name',
        //'station_id',
    ];
    public function problems()
    {
        return $this->hasMany(MachineProblem::class);
    }
    public function reports()
    {
        return $this->hasMany(MachineProblemReport::class);
    }
    public function stations(){
        return $this->belongsToMany(Station::class, StationMachine::class);
    }
}
