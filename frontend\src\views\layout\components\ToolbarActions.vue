<template>
  <div class="flex gap-2 flex-row-reverse">
    <q-btn flat round dense icon="arrow_back" size="sm" @click="back" :disable="!canGoBack">
      <q-tooltip>برگشت</q-tooltip>
    </q-btn>
    <q-btn flat round dense icon="refresh" @click="reloadView" title="shift + alt + r" size="sm"
      v-shortkey="['alt', 'shift', 'r']">
      <q-tooltip>بارگذاری مجدد</q-tooltip>
    </q-btn>

    <component v-if="toolbarComponent" :is="toolbarComponent"></component>

    <!-- <q-btn
      flat
      round
      dense
      icon="arrow_forward"
      size="sm"
      @click="forward"
      :disable="!canGoForward"
    >
      <q-tooltip>جلو</q-tooltip>
    </q-btn>
     -->
    
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { usePublicStore, useTableStore } from '@/stores';

const publicStore = usePublicStore();
const tableStore = useTableStore();

const toolbarComponent = computed(() => tableStore.tools);
const canGoForward = computed(() => publicStore.canGoForward());
const canGoBack = computed(() => publicStore.canGoBack());

function reloadView() {
  publicStore.reloadViewRouter();
}

function forward() {
  publicStore.forward();
}

function back() {
  publicStore.back();
}
</script>
