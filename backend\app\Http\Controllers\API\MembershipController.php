<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\API\BaseController;
use App\Models\MembershipRequest;
use App\Repositories\ModelRepositoryInterface;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\BuyAndSell\Entities\Party;
use Modules\BuyAndSell\Http\Requests\PartyRequest;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class MembershipController extends BaseController
{
    public function __construct(ModelRepositoryInterface $repository)
    {
        $repository->model = Party::class;
        $this->repository = $repository;
    }
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return
            JsonResource::collection($this->repository
                ->query()
                ->whereNotNull('status')
                ->orderByDesc('id')
                ->paginate(request('rowsPerPage') == 0 ? 1000 : request('rowsPerPage')))->additional([
                'formOption' => [
                    'statuses' => Party::$statuses,
                    'permissions' => Permission::get(['id', 'label']),
                    'roles' => Role::with('permissions')->get(['id', 'label']),
                ]
            ]);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(PartyRequest $request)
    {
        $request->validate([
            'address' => 'required',
            'city_id' => 'required',
            'full_name' => 'required',
            'landline_number' => 'required',
            'phone_number' => 'required',
            'province_id' => 'required',
            'location' => 'array',
            'image' => 'required',
        ]);

        $data = Party::create($request->all());
        $data->permissions()->sync($request->permissions);
        $data->roles()->sync($request->roles);
        $data->permissions = $data->permissions()->get(['id'])->pluck('id');
        $data->roles = $data->roles()->get(['id'])->pluck('id');
        return $this->handleResponse($data, trans('request.done'));
    }
    
    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(Party $membership)
    {
        $membership->permissions = $membership->permissions()->get(['id'])->pluck('id');
        $membership->roles = $membership->roles()->get(['id'])->pluck('id');
        return $this->handleResponse($membership);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(PartyRequest $request, Party $membership)
    {
        $request->validate([
            'address' => 'required',
            'city_id' => 'required',
            'full_name' => 'required',
            'landline_number' => 'required',
            'phone_number' => 'required',
            'province_id' => 'required',
            'location' => 'array',
            'image' => 'required',
        ]);

        $membership->update($request->all());

        $membership->permissions()->sync($request->permissions);
        $membership->roles()->sync($request->roles);
        $membership->permissions = $membership->permissions()->get(['id'])->pluck('id');
        $membership->roles = $membership->roles()->get(['id'])->pluck('id');
        
        return $this->handleResponse($membership, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy(Party $membership)
    {
        return $this->handleResponse($membership->delete(), trans('request.done'));
    }

    public function search()
    {
        $data = Party::query()->get();
        return $this->handleResponse($data);
    }
}
