<?php

namespace Modules\Good\Http\Controllers;

use App\Http\Controllers\API\BaseController;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Response;
use Modules\Good\Entities\Attribute;
use Modules\Good\Entities\Group;
use Modules\Good\Http\Requests\GroupRequest;
use stdClass;

class GroupController extends BaseController
{

    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        $query = Group::query()->whereNull('parent_id')->with('children');
        return JsonResource::collection($query->get());
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(GroupRequest $request)
    {
        $model = Group::create(request()->all());
        $attributes = request('attributes') ?? [];
        $model->attributes()->sync($attributes);
        $model->instructions()->sync($request->input('instructions'));

        return $this->handleResponse($model, trans('request.done'));
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(Group $group)
    {
        $attributes = $group->attributes()->get();
        // $group->attributes = count($attributes) > 0 ? $attributes->groupBy('id') : new stdClass();
        //$group->attributes = $group->attributes()->get()->pluck('id');
        $group->attributes = count($attributes) > 0 ? $attributes->groupBy('id')->map(function ($m) {
            return [
                "options" => $m[0]->pivot->options,
                "showing" => $m[0]->pivot->showing,
                "required" => $m[0]->pivot->required,
                "sort" => $m[0]->pivot->sort,
            ];
        }) : new stdClass();
        $group->instructions = $group->groupInstructions()->get(); //->pluck('pivot');

        return $this->handleResponse($group, null, [
            'additional' => [
                'attributes' => Attribute::with(['items', 'parent.items'])->get()->map(function ($m) {
                    return array_merge($m->toArray(), [
                        "items" => collect($m['items'])->merge($m->parent?->items)->toArray(),
                    ]);
                })
            ],
        ]);


        // $attributes = $group->attributes()->get();

        // $group->attributes = count($attributes) > 0 ? $attributes->groupBy('id')->map(function ($m) {
        //     return [
        //         "options" => $m[0]->pivot->options,
        //         "showing" => $m[0]->pivot->showing,
        //         "required" => $m[0]->pivot->required,
        //         "sort" => $m[0]->pivot->sort,
        //     ];
        // }) : new stdClass();

        // //$group_attributes = $good->group()->with('attributes.items')->get()->pluck('attributes')->first();
        // $group_attributes = $good->group->attributes()->with(['items', 'parent.items'])->get();

        // $visible_attributes = !$group_attributes ? [] : $group_attributes->map(function ($m) {
        //     $items = collect($m['items'])->merge($m->parent?->items);
        //     if (isset($m['pivot']['options']) && count($m['pivot']['options']) > 0)
        //         $items = $m['items']->filter(function ($f) use ($m) {
        //             return in_array($f['id'], $m['pivot']['options']);
        //         })->values();
        //     return array_merge($m->toArray(), [
        //         "items" => $items->toArray(),
        //     ]);
        // });
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(GroupRequest $request, Group $group)
    {

        $params = $request->all();
        $group_attributes = $group->attributes()->pluck('key')->toArray();
        $params['default_attribute'] = collect($params['default_attribute'])->filter(function ($m, $k) use ($group_attributes) {
            return $m != null && in_array($k, $group_attributes);
        })->toArray();

        $group->update($params);


        $attributes = request('attributes') ?? [];
        $group->attributes()->sync($attributes);
        // $group->attributes = count($attributes) > 0 ? $attributes->groupBy('id')->map(function ($m) {
        //     return ["options" => $m[0]->pivot->options];
        // }) : new stdClass();

        $group->groupInstructions()->sync(collect($request->input('instructions'))->map(function ($m) use ($group) {
            $res = [
                'group_id' => $group['id'],
                'attribute_id' => $m['attribute_id'] ?? null,
                'attribute_item_id' => $m['attribute_item_id'] ?? null,
                'instruction_id' => $m['instruction_id'],
            ];
            if (isset($m['id']))
                $res['id'] = $m['id'];
            return $res;
        })->toArray());
        $group->instructions = $group->groupInstructions()->get(); //->pluck('pivot');

        // $group->attributes = $group->attributes()->get()->pluck('id');

        $attributes = $group->attributes()->get();
        $group->attributes = count($attributes) > 0 ? $attributes->groupBy('id')->map(function ($m) {
            return [
                "options" => $m[0]->pivot->options,
                "showing" => $m[0]->pivot->showing,
                "required" => $m[0]->pivot->required,
                "sort" => $m[0]->pivot->sort,
            ];
        }) : new stdClass();


        return $this->handleResponse($group, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy(Group $group)
    {
        $group->delete();
    }

    public function search()
    {
        $data = Group::query()->doesntHave('children')->get();
        return $this->handleResponse($data);
    }

    public function attributes()
    {
        return Group::query()->with(['attributes.items'])->get();
    }
}
