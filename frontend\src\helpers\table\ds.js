import { onBeforeMount, onUnmounted, ref, watch } from "vue";
import { api } from "@/boot/axios";
import { useQuasar } from "quasar";
import { useRoute, useRouter } from "vue-router";
import { useTableStore } from "@/stores/table.store";

export const tableApi = (uri, props = {}, context = null) => {
    const $q = useQuasar();
    const route = useRoute();
    const router = useRouter(); // Get the router instance  
    const tableStore = useTableStore();
    const tableRef = ref()

    const url = ref(uri ?? "");
    const rows = ref(props.rows ?? []);
    watch(() => props.rows, newVal => {
        rows.value = newVal
    })
    const rowKey = ref(props.rowKey ?? 'id');
    const columns = ref(props.columns ?? []);
    const selected = ref([]);
    const formOption = ref({});
    const additional = ref({});
    //const filters = reactive({});

    const filters = ref({});
    const filterConditions = ref({});

    const paramFilter = ref({});
    const form = ref(props.form ?? {});
    const hasPagination = ref(false);
    const dialogDelete = ref(false);
    const hasChange = ref(false);
    const loading = ref(false);


    const filterOptions = ref([
        { label: 'شامل شود', value: 'includes', icon: 'contains', type: 'text' },
        { label: 'شامل نشود', value: 'excludes', icon: 'not-contains', type: 'text' },
        { label: 'شروع شود با', value: 'startsWith', icon: 'starts-with', type: 'text' },
        { label: 'پایان یابد با', value: 'endsWith', icon: 'ends-with', type: 'text' },
        { label: 'کوچکتر باشد از', value: 'lessThan', icon: 'less', type: 'number' },
        { label: 'کوچکتر یا مساوی باشد با', value: 'lessThanOrEqual', icon: 'less-equal', type: 'number' },
        { label: 'بزرگتر باشد از', value: 'greaterThan', icon: 'greater', type: 'number' },
        { label: 'بزرگتر یا مساوی باشد با', value: 'greaterThanOrEqual', icon: 'greater-equal', type: 'number' },
        { label: 'برابر باشد با', value: 'equals', icon: 'equals' },
        { label: 'برابر نباشد با', value: 'notEquals', icon: 'not-equals' },
        { label: 'حذف فیلتر', value: null, icon: 'default' },
    ]);

    // const pagination = ref({
    //     sortBy: null,
    //     descending: false,
    //     page: 1,
    //     rowsPerPage: 10,
    //     rowsNumber: 0
    // })

    const pagination = ref({
        sortBy: props.pagination?.sortBy ?? route.query.sortBy ?? "id",
        descending: props.pagination?.descending ?? route.query.descending ?? true,
        page: props.pagination?.page ?? route.query.page ?? 1,
        rowsPerPage: props.pagination?.rowsPerPage ?? route.query.rowsPerPage ?? 10,
        rowsNumber: 10,
    });
    let oldForm = {};
    let orginalForm = {};

    const resetForm = () => {
        form.value = {};
        hasChange.value = false;
        oldForm = {};
        orginalForm = {};
        // console.log(hasChange.value);
    };

    const submitForm = async () => {
        if (url.value) {
            if (form.value && form.value[rowKey.value]) {
                await api
                    .put(url.value + "/" + form.value[rowKey.value], form.value)
                    .then((res) => {
                        form.value = res.result;
                        context.emit('afterSubmit')
                    });
            } else {
                await api.post(url.value, form.value).then((res) => {
                    form.value = res.result;
                    context.emit('afterSubmit')
                });
            }
            // fetchData();
        } else {
            if (selected.value[0] && selected.value[0][rowKey.value]) {
                const find = rows.value.findIndex((f) => f[rowKey.value] == selected.value[0][rowKey.value]);
                if (find >= 0) rows.value.splice(find, 1, form.value);
            } else {
                rows.value.push(form.value);
            }
        }
        context.emit('update:rows', rows.value)
        context.emit('input:rows', rows.value)
    };

    const callShow = ref(false);
    const show = async () => {
        if (!url.value) return;
        if (form.value[rowKey.value])
            api.get(url.value + "/" + form.value[rowKey.value]).then((res) => {
                Object.assign(form.value, res.result);
                additional.value = res.additional;
                orginalForm = JSON.parse(JSON.stringify(res.result));
                callShow.value = true;
            });
    };

    const onFilter = () => {
        // const oldParamFilter = Object.assign({}, paramFilter.value);

        // console.log('onFilter')
        paramFilter.value = Object.assign({}, filters);
        Object.keys(paramFilter.value).filter((f) => {
            if (!paramFilter.value[f].value) delete paramFilter.value[f];
        });
        // pagination.value.page = 1;
        // if (
        //     !(
        //         Object.keys(paramFilter.value).length == 0 &&
        //         Object.keys(oldParamFilter) == 0
        //     )
        // )
        fetchData();
    };
    const fetchData = async (params = {}) => {
        if (!url.value) return;


        loading.value = true;
        try {
            const response = await api.get(url.value, {
                params: {
                    filters: filters.value,
                    conditions: filterConditions.value,
                    page: pagination.value.page,
                    page: pagination.value.page,
                    sortBy: pagination.value.sortBy,
                    descending: pagination.value.descending,
                    rowsPerPage: pagination.value.rowsPerPage,
                }
            });
            rows.value = response.data;


            pagination.value.page = response.meta.current_page
            pagination.value.rowsPerPage = response.meta.per_page
            pagination.value.rowsNumber = response.meta.total



        } catch (error) {
            console.error('Error fetching data:', error);
            //errorMessage.value = 'خطا در بارگذاری داده‌ها. لطفاً دوباره تلاش کنید.';
            //errorDialog.value = true;
        } finally {
            loading.value = false;
        }




        // if (context) context.emit("loaded", res);

        selected.value = [];
    };

    const onDelete = () => {
        if (dialogDelete.value) return;
        dialogDelete.value = true;
        $q.dialog({
            title: "مطمئن هستید؟",
            cancel: true,
        })
            .onOk(() => {
                if (url.value) {
                    api.delete(url.value + "/" + selected.value[0][rowKey.value]).then(
                        () => {
                            fetchData();
                        }
                    );
                } else {
                    const find = rows.value.findIndex(
                        (f) => f[rowKey.value] == selected.value[0][rowKey.value]
                    );
                    if (find >= 0) rows.value.splice(find, 1);

                    context.emit('update:rows', rows.value)
                    context.emit('input:rows', rows.value)

                }
                dialogDelete.value = false;
            })
            .onCancel(() => {
                dialogDelete.value = false;
            });
    };

    watch(
        () => columns.value,
        (newVal) => {
            // console.log('watchColumn', newVal.filter((f) => f.filter).length == 0 ||
            // Object.keys(route.query).length == 0)
            if (
                newVal.filter((f) => f.filter).length == 0 ||
                Object.keys(route.query).length == 0
            )
                fetchData();
        }
    );
    onUnmounted(() => {
        tableStore.unmount()

    })

    watch(() => selected.value, newVal => {
        //console.log('watch selected.value', newVal);
        tableStore.props = { selected: newVal }
    })
    tableStore.actions.doDelete = async function (value) {
        //console.log('delete')
        $q.dialog({
            title: 'حذف',
            message: 'شما در حال حذف اطلاعات انتخابی می باشید، مطمئن هستید؟',
            cancel: true,
            persistent: true
        }).onOk(() => {
            api.post(props.url + '/delete', { ids: value.map(m => m.id) }).then(res => {
                console.log('empty selected');
                selected.value = []
                fetchData();
            });
        })


    }


    const setFilterCondition = (field, value) => {
        filterConditions.value[field] = value;
        if (value === null) {
            filters.value[field] = ''; // Clear the search field if filter is null  
        }
        fetchData();
        updateQueryParams(); // Update query params in the URL  
    };

    const handleSearch = (field) => {
        if (!filterConditions.value[field]) {
            setFilterCondition(field, 'includes'); // Change filter type to includes  
        }
        //fetchData(); // Reload data after changing filter settings  
        updateQueryParams(); // Update query params in the URL  
    };

    const updateQueryParams = () => {
        const query = objectToQueryObject({
            filters: filters.value,
            filterConditions: filterConditions.value,
            page: pagination.value.page,
            sortBy: pagination.value.sortBy,
            descending: pagination.value.descending,
            rowsPerPage: pagination.value.rowsPerPage,
        })


        router.push({ query }); // Update the query parameters in the URL  
    };


    onBeforeMount(async () => {
        console.log('onBeforeMount JTableDataCrud')
        const currentQuery = queryObjectToObject(router.currentRoute.value.query);
        if (currentQuery.filters) filters.value = currentQuery.filters;
        if (currentQuery.filterConditions) filterConditions.value = currentQuery.filterConditions;
        if (currentQuery.page) pagination.value.page = currentQuery.page;
        if (currentQuery.sortBy) pagination.value.sortBy = currentQuery.sortBy;
        if (currentQuery.descending) pagination.value.descending = currentQuery.descending;
        if (currentQuery.rowsPerPage) pagination.value.rowsPerPage = currentQuery.rowsPerPage;

        await fetchData();
    })



    const onRequest = (props) => {
        pagination.value.sortBy = props.pagination.sortBy
        pagination.value.descending = props.pagination.descending
        pagination.value.rowsPerPage = props.pagination.rowsPerPage
        pagination.value.page = props.pagination.page
        fetchData()

    }



    let storedSelectedRow

    const handleSelection = ({ rows, added, evt }) => {
        // ignore selection change from header of not from a direct click event
        if (rows.length !== 1 || evt === void 0) {
            return
        }

        const oldSelectedRow = storedSelectedRow
        const [newSelectedRow] = rows
        const { ctrlKey, shiftKey } = evt

        if (shiftKey !== true) {
            storedSelectedRow = newSelectedRow
        }

        // wait for the default selection to be performed
        nextTick(() => {
            if (shiftKey === true) {
                const tableRows = tableRef.value.filteredSortedRows
                let firstIndex = tableRows.indexOf(oldSelectedRow)
                let lastIndex = tableRows.indexOf(newSelectedRow)

                if (firstIndex < 0) {
                    firstIndex = 0
                }

                if (firstIndex > lastIndex) {
                    [firstIndex, lastIndex] = [lastIndex, firstIndex]
                }

                const rangeRows = tableRows.slice(firstIndex, lastIndex + 1)
                // we need the original row object so we can match them against the rows in range
                const selectedRows = selected.value.map(toRaw)

                selected.value = added === true
                    ? selectedRows.concat(rangeRows.filter(row => selectedRows.includes(row) === false))
                    : selectedRows.filter(row => rangeRows.includes(row) === false)
            }
            else if (ctrlKey !== true && added === true) {
                selected.value = [newSelectedRow]
            }

        })
    }

    // fetchData();
    return {
        tableRef,
        url,
        onDelete,
        handleSelection,
        rows,
        form,
        fetchData,
        columns,
        onDelete,
        selected,
        formOption,
        additional,
        filters,
        filterOptions,
        filterConditions,
        setFilterCondition,
        handleSearch,
        onFilter,
        submitForm,
        show,
        pagination,
        hasPagination,
        hasChange,
        resetForm,
        onRequest,
    };
};


function objectToQueryObject(obj, prefix = '') {
    return Object.keys(obj).reduce((acc, key) => {
        const value = obj[key];
        const fullKey = prefix ? `${prefix}[${key}]` : key;

        if (Array.isArray(value)) {
            value.forEach((val, index) => {
                acc[`${fullKey}[${index}]`] = val;
            });
        } else if (typeof value === 'object' && value !== null) {
            Object.assign(acc, objectToQueryObject(value, fullKey));
        } else {
            acc[fullKey] = value;
        }
        return acc;
    }, {});
}



function queryObjectToObject(queryObj) {
    const result = {};

    Object.keys(queryObj).forEach(key => {
        const keys = key.split(/\[(.+?)\]/).filter(Boolean); // جداسازی کلیدها
        let current = result;

        keys.forEach((k, index) => {
            if (index === keys.length - 1) {
                current[k] = queryObj[key]; // مقدار را در انتها قرار می‌دهیم
            } else {
                if (!current[k]) {
                    current[k] = isNaN(keys[index + 1]) ? {} : []; // اگر کلید بعدی عدد است، آرایه بساز
                }
                current = current[k]; // به سطح بعدی آبجکت برو
            }
        });
    });

    return result;
}
