<?php

namespace Modules\Good\Entities;

class Category extends BModel
{
    const PRODUCT = 'PRODUCT';
    const MATERIAL = 'MATERIAL';
    const SERVICE = 'SERVICE';
    const types = [
        [
            'value' => self::PRODUCT,
            'label' => 'محصول',
        ],
        [
            'value' => self::MATERIAL,
            'label' => 'مواد اولیه',
        ],
        [
            'value' => self::SERVICE,
            'label' => 'خدمات',
        ],
    ];
    protected $fillable = [
        'name',
        'group_id',
        'type',
    ];
    protected $appends = [
        'label_type',
        'label_group',
    ];
    public function group()
    {
        return $this->belongsTo(Group::class);
    }
    public function getLabelTypeAttribute()
    {
        return collect(self::types)->where('value', $this->type)->pluck('label')->first() ?? '';
    }
    public function getLabelGroupAttribute()
    {
        return $this->group->name;
    }

    public function goods()
    {
        return $this->hasMany(Good::class);
    }
}
