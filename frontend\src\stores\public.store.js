import { defineStore } from "pinia";
import { router } from "@/helpers";

export const usePublicStore = defineStore({
    id: "public",
    state: () => ({
        titleWindow: "",
        icon: "",
        componentKey: 0,
        isNotFound: false,
        isNotAccess: false,
    }),
    actions: {
        async reloadViewRouter() {
            this.componentKey += 1;
        },
        setNotFound(value) {
            console.log("setNotFound("+value+")")
            this.isNotFound = value;
        },
        setNotAccess(value) {
            console.log("setNotAccess("+value+")")
            this.isNotAccess = value;
        },
        back() {
            router.back()
        },
        canGoBack() {
            // موقعیت فعلی را بررسی کنید  
            const currentIndex = window.history.state ? window.history.state.position : null;

            // چک کنید آیا حرکت به عقب ممکن است  
            return currentIndex !== null && currentIndex > 0;
        },
        forward() {
            console.log(router)
            router.forward()
        },
        canGoForward() {
            // موقعیت فعلی را بررسی کنید  
            const currentIndex = window.history.state ? window.history.state.position : null;
            console.log(currentIndex, window.history.length, window.history)
            // چک کنید آیا حرکت به جلو ممکن است  
            return currentIndex !== null && currentIndex < window.history.length - 1;
        },
        setTilteWindow(title) {
            document.title = title
        }
    },
});
