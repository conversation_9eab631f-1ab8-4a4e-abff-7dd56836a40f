<?php

namespace Modules\Good\Entities;

use App\Casts\ObjectCast;
use Modules\Production\Entities\GroupInstruction;
use Modules\Production\Entities\Instruction;

class Group extends BModel
{
    protected $fillable = [
        'name', 'parent_id', 'default_attribute',
        'is_active',
        'is_active_customer',
    ];
    protected $casts = [
        'default_attribute' => ObjectCast::class,
        'is_active' => 'boolean',
        'is_active_customer' => 'boolean',
    ];

    public function parent()
    {
        return $this->belongsTo(static::class);
    }

    public function children()
    {
        return $this->hasMany(static::class, 'parent_id')->with('children');
    }
    public function onDelete()
    {
        foreach ($this->children as $child) {
            $child->delete();
        }
    }

    public function attributes()
    {
        return $this->belongsToMany(Attribute::class, AttributeGroup::class)->withPivot(['showing', 'sort', 'options', 'required'])->using(GroupPivot::class);
    }

    public function groupInstructions()
    {
        return $this->hasMany(GroupInstruction::class); //->withPivot(['attribute_id', 'attribute_item_id']);
    }
    public function instructions()
    {
        return $this->belongsToMany(Instruction::class, GroupInstruction::class)->withPivot(['attribute_id', 'attribute_item_id']);
    }
    public function goods()
    {
        return $this->hasMany(Good::class);
    }
}
