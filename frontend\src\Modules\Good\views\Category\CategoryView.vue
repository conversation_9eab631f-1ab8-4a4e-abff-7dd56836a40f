<template>
  <j-table-data url="/good/category" :columns="columns">
    <template #dialog="props">
      <table-form v-bind="props" v-model:form="props.form" />
    </template>
      <template #body-cell-name="props">
      <q-td :props="props">
         <router-link :to="{name:'list_inventory_items', params: {id: props.row.id} }">{{ props.row.name }}</router-link> 
        </q-td>
      </template>
  </j-table-data>
</template>

<script>
import TableForm from "./form.vue";

export default {
  setup() {

    const columns = [
      {
        name: 'name',
        required: true,
        label: 'نام',
        field: 'name',
        sortable: true,
      },
      {
        name: 'type',
        required: true,
        label: 'نوع',
        field: 'label_type',
        sortable: true,
      },
      {
        name: 'group',
        required: true,
        label: 'گروه',
        field: 'label_group',
        sortable: true,
      },
    ]

    return {
      columns,
    };
  },
  components: { TableForm },
};
</script>
