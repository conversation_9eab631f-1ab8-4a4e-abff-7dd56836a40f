<template>
  
  <j-table-data-crud :columns="columns" url="production/production_order" :tools="Tools">
    <template #body-cell-status="props">
      <q-td :props="props">
        <div>
          {{ props.value }}
          <q-badge v-if="props.row.is_created_by_customer" color="green" outlined label="آنلاین" />
        </div>

      </q-td>
    </template>
  </j-table-data-crud>
</template>
<script setup>
import Tools from './tools.vue'

const columns = [
  {
    name: 'code',
    required: true,
    label: 'شماره سفارش',
    field: 'code',
    sortable: true,
    style: 'width: 50px',
    //filter: 'FilterInput',
    filterType: 'text',
  },
  {
    name: 'status',
    required: true,
    label: 'وضعیت سفارش',
    field: 'label_status',
    sortable: true,
    style: 'width: 80px',
    filterType: 'select',
    filterOptions: 'statuses'
  },
  {
    name: 'party_name',
    required: true,
    label: 'کد نمایندگی',
    field: 'party_name',
    sortable: true,
    filterType: 'text',
    // filter: {
    //   type: 'FilterInput',
    //   relation: 'party',
    // },
  },
  {
    name: 'customer_name',
    required: true,
    label: 'نام مشتری',
    field: 'customer_name',
    sortable: true,
    //filter: 'FilterInput',
    filterType: 'text',
  },


  {
    name: 'created_at',
    required: true,
    label: 'تاریخ ثبت',
    field: 'created_at',
    sortable: true,
    style: 'width: 50px',
            filterType: 'date'

  },
  // {
  //   name: 'submit_date',
  //   required: true,
  //   label: 'تاریخ شروع',
  //   field: 'submit_date',
  //   sortable: true,
  //   style: 'width: 50px',
  //   filter: 'FilterDate'
  // },
  // {
  //   name: 'delivery_date',
  //   required: true,
  //   label: 'تاریخ سفارش',
  //   field: 'delivery_date',
  //   sortable: true,
  //   style: 'width: 50px',
  //   filter: 'FilterDate'
  // },
  {
    name: 'user_name',
    label: 'نام کاربر',
    field: 'user_name',
    sortable: true,
    filterType: 'text',
    // filter: {
    //   type: 'FilterInput',
    //   relation: 'user',
    // },
  },
]


</script>
