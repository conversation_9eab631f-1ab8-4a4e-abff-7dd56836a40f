<template>

  <div class="q-gutter-sm">
    <j-table-data url="/good/group" :columns="columns">
      <template #dialog="props">
        <table-form v-bind="props" v-model:form="props.form" />
      </template>
    </j-table-data>
  </div>
</template>

<script>
import TableForm from "./form.vue";

export default {
  setup() {

    const columns = [
      {
        name: 'name',
        required: true,
        label: 'نام',
        field: 'name',
        sortable: true,
      },
    ]

    return {
      columns,
    };
  },
  components: { TableForm },
};
</script>
