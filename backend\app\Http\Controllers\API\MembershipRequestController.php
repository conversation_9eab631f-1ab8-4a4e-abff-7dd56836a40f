<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\API\BaseController;
use App\Models\MembershipRequest;
use Auth;
use Illuminate\Http\Request;
use Modules\BuyAndSell\Entities\Party;

class MembershipRequestController extends BaseController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return $this->handleResponse(auth()->user());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'address' => 'required',
            'city_id' => 'required',
            'full_name' => 'required',
            'mobile_number' => 'required',
            'phone_number' => 'required',
            'province_id' => 'required',
            'location' => 'array',
            'image' => 'required',
        ]);
        $data = array_merge(
            [
                ...$request->all(),
                'user_id' => Auth::user()->id
            ]
        );
        return $this->handleResponse(Party::create($data), 'با موفقیت ارسال شد!');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function get()
    {
        $membershipRequest = Auth::user();

        return $this->handleResponse($membershipRequest);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function request(Request $request)
    {
        $request->validate([
            'address' => 'required',
            'city_id' => 'required',
            'full_name' => 'required',
            'landline_number' => 'required',
            'phone_number' => 'required',
            'province_id' => 'required',
            'location' => 'array',
            'image' => 'required',
        ]);
        $data = array_merge(
            [
                ...$request->all(),
                'status' => 'PENDING'
                //  'user_id' => Auth::user()->id
            ]
        );
        $membershipRequest = Auth::user();
        $membershipRequest->update($data);
        return $this->handleResponse($membershipRequest, 'با موفقیت ارسال شد!');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    /**
     * Search the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function search()
    {
        //
    }
}
