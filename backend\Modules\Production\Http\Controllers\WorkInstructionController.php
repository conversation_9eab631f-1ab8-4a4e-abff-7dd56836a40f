<?php

namespace Modules\Production\Http\Controllers;

use App\Http\Controllers\API\BaseController;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Routing\Controller;
use Modules\Good\Entities\Good;
use Modules\Production\Entities\WorkInstruction;

class WorkInstructionController extends BaseController
{
    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(Good $good)
    {
        return JsonResource::collection($good->workInstructions()
            ->with(['parents.stationWork', 'stationWork.station'])
            ->get())->additional([
            'model' => $good,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request, Good $good)
    {
        $item = WorkInstruction::create(array_merge($request->all(), [
            'good_id' => $good->id,
        ]));
        $item->parents()->sync($request->parents);
        if ($request->has('conditions')) $item->conditions()->sync(collect($request->input('conditions'))->map(function ($m) {
            return [
                'attribute_id' => $m['attribute_id'],
                'items' => $m['items'],
                'condition' => $m['condition'] ?? false,
            ];
        })->toArray());
        $item->conditions = $item->conditions()->get();
        $item['parents'] = $item->parents()->get()->pluck('id');

        return $this->handleResponse($item, trans('request.done'));
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(Good $good, WorkInstruction $workInstruction)
    {
        $workInstruction['parents'] = $workInstruction->parents()->get()->pluck('id');
        $workInstruction->conditions = $workInstruction->conditions()->get();

        return $this->handleResponse($workInstruction);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, Good $good, WorkInstruction $workInstruction)
    {
        $workInstruction->update(array_merge($request->all(), [
            'good' => $good->id,
        ]));
        $workInstruction->parents()->sync($request->parents);
        $workInstruction['parents'] = $workInstruction->parents()->get()->pluck('id');
        if ($request->has('conditions')) $workInstruction->conditions()->sync(collect($request->input('conditions'))->map(function ($m) {
            return [
                'attribute_id' => $m['attribute_id'],
                'items' => $m['items'],
                'condition' => $m['condition'] ?? false,
                'id' => $m['id'] ?? null,
            ];
        })->toArray());
        $workInstruction->conditions = $workInstruction->conditions()->get();
        return $this->handleResponse($workInstruction, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy(Good $good, WorkInstruction $workInstruction)
    {
        return $this->handleResponse($workInstruction->delete());
    }

    public function search(Good $good)
    {
        return $this->handleResponse($good->workInstructions()->with('stationWork')->get());
    }
}
