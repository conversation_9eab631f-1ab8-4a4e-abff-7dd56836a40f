<?php

namespace App\Traits;

use Illuminate\Support\Facades\Validator;

trait DataFilter {

    public function scopeFilter($query, $filters = [], $conditions = [])
    {
        $request = request();
        $validator = Validator::make($request->all(), [
            'filters' => 'array',
            'conditions' => 'array',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // دریافت فیلترها از درخواست
        $filters = $request->input('filters', $filters);
        $conditions = $request->input('conditions', $conditions);

        foreach ($filters as $field => $value) {
            if (!empty($value) && isset($conditions[$field])) {
                switch ($conditions[$field]) {
                    case 'includes':
                        $query->where($field, 'LIKE', '%' . $value . '%');
                        break;
                    case 'excludes':
                        $query->where($field, 'NOT LIKE', '%' . $value . '%');
                        break;
                    case 'startsWith':
                        $query->where($field, 'LIKE', $value . '%');
                        break;
                    case 'endsWith':
                        $query->where($field, 'LIKE', '%' . $value);
                        break;
                    case 'equals':
                        $query->where($field, '=', $value);
                        break;
                    case 'notEquals':
                        $query->where($field, '!=', $value);
                        break;
                    case 'lessThan':
                        $query->where($field, '<', $value);
                        break;
                    case 'greaterThan':
                        $query->where($field, '>', $value);
                        break;
                }
            }
        }
        return $query;
    }
}
