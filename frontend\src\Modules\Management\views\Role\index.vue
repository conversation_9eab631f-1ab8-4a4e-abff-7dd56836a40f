<template>
  <j-table-data-crud :columns="columns" url="roles" :permissions="permissions" />
</template>
<script setup>

const columns = [
  // {
  //   name: 'id', label: 'شناسه', field: 'id', sortable: true, filterType: 'number',
  // },
  {
    name: 'name', label: 'نام', field: 'label', sortable: true, filterType: 'text',
  },
  {
    name: 'guard_name', label: 'گارد', field: 'guard_name', sortable: true, filterType: 'text',
  },
]

const permissions = {
  create: 'roles.create',
  edit: 'roles.edit',
  delete: 'roles.delete',
}
</script>