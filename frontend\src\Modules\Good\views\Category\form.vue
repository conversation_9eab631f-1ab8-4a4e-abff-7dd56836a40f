<template>
  <j-input v-model="form.name" dense error-field="name" label="نام" />
  <j-select v-model="form.type" dense :options="formOption.types" error-field="type" label="نوع" emit-value
            map-options />
        <j-select-remote v-model="form.group_id" dense url="good/group/search" label="گروه" error-field="group_id" />
</template>
<script>
export default {
    props: {
        form: {
            type: Object,
            default: () => { }
        },
        formOption: {
            type: Object,
            default: () => { }
        },
    },
};
</script>