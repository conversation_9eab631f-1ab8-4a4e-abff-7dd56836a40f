<?php

namespace App\Casts;

use Carbon\Carbon;
use DateTimeZone;
use Hek<PERSON>inasser\Verta\Verta;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;

class AsJalaliDate implements CastsAttributes
{
    /**
     * Cast the given value.
     *
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @param  string  $key
     * @param  mixed  $value
     * @param  array  $attributes
     * @return mixed
     */
    public function get($model, string $key, $value, array $attributes)
    {
        return !$value ? null : Verta::instance($value)
            ->timezone('Asia/Tehran')
            ->format('Y/m/d');
    }

    /**
     * Prepare the given value for storage.
     *
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @param  string  $key
     * @param  mixed  $value
     * @param  array  $attributes
     * @return mixed
     */
    public function set($model, string $key, $value, array $attributes)
    {
        return !$value ? null : (new Carbon(Verta::parse($value)->datetime(), 'Asia/tehran'))->format('Y-m-d');
    }
}
