<template>
    <div class="flex sm:q-table__card md:border-b-2" style="background: none;border-radius: 10px;">
        <div v-if="false" class="w-12 p-2 md:rounded-lg" style="background:#2e3d50; ">
            <div class="sticky top-28 flex gap-2">
                <!-- <j-btn v-if="$q.screen.xs && filter_columns && filter_columns.length > 0" class="text-white" flat dense icon="tune"
                   title="فیلتر"  @click="showFilter = true">
                    <q-badge v-if="Object.values(filters).map(f => f.value).filter(f => f && f.length > 0).length > 0"
                        color="red" rounded floating />
                </j-btn> -->

                <j-btn v-if="canBeAdd && !readonly" dense icon="add" color="blue" @click="onAdd()" title="افزودن" />
                <template v-if="selected.length > 0">
                    <j-btn v-if="canBeEdit(selected[0]) && !readonly" dense icon="edit" color="green" @click="onEdit()"
                        title="ویرایش" />
                    <j-btn v-if="canBeDelete(selected[0]) && !readonly" dense icon="delete" color="red" @click="onDelete()"
                        title="حذف" />
                    <slot name="select_bar" v-bind="{ callback: getAll, selected: selected[0], url }" />
                </template>
                <slot name="bar" v-bind="{ callback: getAll, url, data: list }" />

            </div>
        </div>
        <j-dialog v-model="showFilter">
            <q-card class="p-5">
                <template v-for="(column, index) in filter_columns" :key="index">
                    <div v-if="column.filter" class="col-6 text-center border p-3">
                        <component v-if="typeof column.filter == 'object'" :is="column.filter.type"
                            v-model:value="filters[column.column].value" :label="column.label">
                            {{ column.label }}
                        </component>
                        <component v-else-if="typeof column.filter == 'string'" :is="column.filter"
                            v-model:value="filters[column.column].value" :label="column.label"
                            :filter-option="column.filterOption">
                            {{ column.label }}
                        </component>
                    </div>
                </template>
            </q-card>
        </j-dialog>

        <j-table v-model:selected="selected" :rows="list" :columns="columns" selection="single" :row-key="rowKey ?? 'id'"
            separator="cell" v-model:filters="filters" v-model:formOption="formOption" @onFilter="onFilter"
            class="flex-auto w-12" flat @request="onRequest" v-model:pagination="pagination" @row-click="dblClick"
            :hide-pagination="!hasPagination" :dense="dense">

            <template v-for="_, slot in $slots" v-slot:[slot]="props" :key="slot">
                <slot :name="slot" v-bind="props" :key="slot" />
            </template>
            <!-- <template #top="props">
                <slot name="top" v-bind="props" />
                <slot name="select_bar" v-bind="{ load: getAll, selected: selected[0] }" />

            </template> -->
            <!-- <template v-if="$q.screen.xs" v-slot:top>
                <j-btn flat dense icon="add" color="blue" @click="onAdd()" label="افزودن" />
                <q-space />
                <div class="space-x-2">
                    <template v-if="selected.length > 0">
                        <slot name="select_bar" v-bind="{ load: getAll, selected: selected[0] }" />
                        <j-btn flat dense icon="edit" color="green" @click="onEdit()" />
                        <j-btn flat dense icon="delete" color="red" @click="onDelete()" />
                    </template>
                </div>
            </template> -->
        </j-table>
    </div>
    <j-dialog-bar ref="ref_dialog" v-model="add" @hide="hideDialog" persistent :close="closeDialog">
        <template #title>
            <template v-if="form && form.id">
                ویرایش
                <slot name="prefix_title" /> {{ form.code }}
            </template>
            <template v-else>
                ثبت
                <slot name="prefix_title" />
            </template>

        </template>

        <template #bar>
            <slot name="bar" v-bind="{ selected: form }" />
            <j-btn :label="!$q.screen.xs ? 'ذخیره' : ''" icon="save" color="primary" @click="onSubmit" title="shift + s"
                @shortkey="onSubmit()" v-shortkey="['shift', 's']" />
        </template>
        <q-form ref="ref_form" @submit="submitForm">

            <slot name="dialog" v-bind="{ formOption, additional, load: getAll, form, url, onSubmit }"
                @update:form="updateForm" />
        </q-form>

        <q-dialog v-model="exit">
            <q-card>
                <q-card-section class="row items-center">
                    <span class="q-ml-sm">تغییرات ذخیره شود؟</span>
                </q-card-section>

                <q-card-actions align="right">
                    <q-btn flat label="ذخیره شود" color="primary" @click="save" />
                    <q-btn flat label="خیر" color="primary" @click="close" />
                    <!-- <q-btn flat label="لغو" color="primary" v-close-popup /> -->
                </q-card-actions>
            </q-card>

        </q-dialog>
        <q-inner-loading :showing="requestStore.loading">
            <div>
                <q-spinner-facebook color="primary" size="4em" />
                منتظر بمانید...
            </div>
        </q-inner-loading>
    </j-dialog-bar>
</template>
<script>
import { tableApi } from '@/helpers';
import { useRequestStore } from '@/stores';
import { ref, watch, computed } from 'vue';
import { useRoute } from "vue-router";

export default {
    props: {
        url: String,
        dense: {
            type: Boolean,
            default: false
        },
        columns: {
            type: Array,
            default: () => []
        },
        list: {
            type: Array,
            default: () => []
        },
        rowKey: String | Function,
        readonly: {
            type: Boolean,
            default: false,
        },
        canBeAdd: {
            type: Boolean,
            default: true,
        },
        canBeEdit: {
            type: Function,
            default: () => true,
        },
        canBeDelete: {
            type: Function,
            default: () => true,
        },
        selected: {
            type: Array,
            default: () => []
        },
        pagination: {
            type: Object,
            default: () => { }
        },
    },
    setup(props, context) {
        const requestStore = useRequestStore();
        const add = ref(false);
        const ref_form = ref(null);
        const {
            list,
            columns,
            selected,
            onDelete,
            formOption,
            additional,
            getAll,
            onFilter,
            filters,
            url,
            submitForm,
            form,
            show,
            pagination,
            hasPagination,
            hasChange,
            resetForm,
        } = tableApi(props.url, props, context)

        columns.value = props.columns;
        selected.value = [props.selected];
        watch(() => selected.value, () => {
            context.emit('update:selected', selected.value[0])
        })

        const onAdd = () => {
            if (add.value)
                return;
            add.value = true;
            // form.value = {}
            selected.value = []
        }
        const onEdit = async () => {
            if (props.url) {
                form.value.id = selected.value[0].id;//Object.assign({}, selected.value[0]);

            } else {
                form.value = Object.assign({}, selected.value[0]);

            }
            show();
            add.value = true;
            // selected.value = []
        }
        const onSubmit = () => {
            ref_form.value.submit()
        }
        const updateForm = (val) => {
            form.value = val;

        }
        const hideDialog = () => {
            getAll()
        }
        const onRequest = (props) => {
            const { page, rowsPerPage, sortBy, descending } = props.pagination
            pagination.value.page = page
            pagination.value.rowsPerPage = rowsPerPage
            pagination.value.sortBy = sortBy
            pagination.value.descending = descending

            getAll()
        }
        const dblClick = (evt, row, index) => {
            selected.value[0] = row
            context.emit('update:selected', selected.value[0])
        }
        const exit = ref(false)
        const ref_dialog = ref(null)

        const close = () => {
            ref_dialog.value.hide()
            exit.value = false
            resetForm()

        }
        const save = () => {
            onSubmit();
            close();
        }




        return {
            // filter_columns,
            showFilter: ref(false),
            add,
            onAdd,
            onEdit,
            list,
            onDelete,
            selected,
            getAll,
            onFilter,
            formOption,
            additional,
            form,
            filters,
            url,
            form,
            submitForm,
            onSubmit,
            ref_form,
            updateForm,
            hideDialog,
            onRequest,
            pagination,
            hasPagination,
            dblClick,
            exit,
            ref_dialog,
            requestStore,
            close, save,
            closeDialog() {
                close();
                // if (hasChange.value)
                //     exit.value = true
                // else
                //     close()
            }
        }
    },
}
</script>