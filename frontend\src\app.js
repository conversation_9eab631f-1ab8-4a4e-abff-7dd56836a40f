import { createApp } from "vue";
import { Quasar, Notify, Dialog, Loading, LoadingBar, Dark } from "quasar";
import quasarLang from "quasar/lang/fa-IR";
import { createPinia } from "pinia";

import App from "./App.vue";
import { router } from "./helpers";
import JComponent from "./components";
import Filters from "./components/filters";
import Directive from "@/directives";
import LayoutAuth from "@/views/layout/index.vue";
import { useThemeStore, useAuthStore } from "@/stores";

import "./styles/main.scss";
// import "./styles/app.scss";
// import "./styles/style.css";
import InlineSvg from "vue-inline-svg";
//import ShortKey from "vue3-shortkey";
//import { IMaskDirective } from 'vue-imask';

//import OpenLayersMap from "vue3-openlayers";
//import "vue3-openlayers/dist/vue3-openlayers.css"; // vue3-openlayers version >= 1.0.0-*
//import.meta.glob('@/assets/svg-icons/*.svg');

const app = createApp(App)
    .component("layout-auth", LayoutAuth)
    .component("inline-svg", InlineSvg)
    .use(createPinia())
    .use(router)
    .use(JComponent)
    .use(Filters)
    .use(Directive)
    //.use(ShortKey)
    //.use(OpenLayersMap /* options */)
    //.directive("imask", IMaskDirective)
    .use(Quasar, {
        plugins: {
            Notify,
            Dialog,
            Loading,
            LoadingBar,
            Dark,
        },
        lang: quasarLang,
        config: {
            loadingBar: {
                color: 'blue',
                size: '5px',
                position: 'bottom'
            }
        },
    })
    ;



// Initialize stores before mounting
const themeStore = useThemeStore();
const authStore = useAuthStore();

// Initialize theme store to prevent flash
themeStore.init();

// Initialize auth store to check login status and get user data
// Don't await here to prevent blocking app mount
authStore.initializeAuth();

app.mount("#app");