export const doCenterLayerThickness = ({ typeMaterialDoor, doorThickness, typeDoor, good_id = null }) => {

    if (!["RAPINGI", "KOMODI"].includes(typeDoor)) return false;


    if ([136, 140].includes(good_id) && [4, 4.8].includes(doorThickness * 1))
        return {
            value: 16,
            label: "12+4",
        };
    else if ([136, 140].includes(good_id) && [4.4].includes(doorThickness * 1))
        return {
            value: 20,
            label: "16+4",
        };
    else if ([4, 4.8].includes(doorThickness * 1))
        return {
            value: 16,
            label: "16",
        };
    // else if ([4.4].includes(doorThickness * 1) && typeMaterialDoor == "fomizeh")
    //     return {
    //         value: 20,
    //         label: "20",
    //     };
    // else if ([4.4].includes(doorThickness * 1) && typeMaterialDoor == "mdf")
    //     return {
    //         value: 20,
    //         label: "8+4+8",
    //     };
    else if ([4.4].includes(doorThickness * 1))
        return {
            value: 20,
            label: "8+4+8",
        };
    else if ([3.6].includes(doorThickness * 1) && typeMaterialDoor == "mdf")
        return {
            value: 12,
            label: "12",
        };

    else if ([3.2].includes(doorThickness * 1) && typeMaterialDoor == "mdf")
        return {
            value: 16,
            label: "16",
        };

    return {
        value: 0,
        label: "-",
    };
}


export const doDoorThickness = ({ typeDoor, hasEdgeOfDoor, goodId, hasAbzar, typeMaterialDoor, sheetCNCThickness }) => {
    if (typeDoor == 'MAGHTA' && typeMaterialDoor == "fomizeh")
        return 4.8;
    else if (typeDoor == 'MAGHTA' && typeMaterialDoor == "mdf")
        return 4;
    if (typeDoor == "CNC") {
        if (!hasEdgeOfDoor) return 4;
        if (sheetCNCThickness == '4') return 4.5;
        if (hasEdgeOfDoor) return 4.8;

    }
    if (typeDoor == "KOMODI") {
        return 3.6;
    }
    if (typeDoor != "RAPINGI") return 0;

    if (
        [121, 165, 138].includes(goodId) && // tw-12 , tw-64
        !hasAbzar &&
        (hasEdgeOfDoor || (!hasEdgeOfDoor && typeMaterialDoor == "fomizeh"))
    )
        return 4.8;

    if (
        [138].includes(goodId) // tw-29
    )
        return 4.8;

    if (hasEdgeOfDoor && hasAbzar) return 4.8;
    else if (hasEdgeOfDoor && !hasAbzar) return 4.4;
    else if (!hasEdgeOfDoor && typeMaterialDoor == "mdf") return 4;
    else if (!hasEdgeOfDoor && hasAbzar && typeMaterialDoor == "fomizeh")
        return 4.8;
    else if (!hasEdgeOfDoor && !hasAbzar && typeMaterialDoor == "fomizeh")
        return 4.4;

}

export const doZevardamageh = ({ hasEdgeOfDoor, doorLengeh }) => {
    if (hasEdgeOfDoor)
        return 0;
    switch (doorLengeh * 1) {
        case 1:
            return 0;
        case 1.5:
        case 2:
            return 1;
        case 3:
            return 2;
        case 4:
            return 3;
    }
    return 0;
}

export default function (
    { goodId, typeDoor },
    {
        doorWidth,
        doorHeight,
        typeMaterialDoor,
        doorLengeh,
        hasEdgeOfDoor,
        alignEdgeOfDoor,
        hasAbzar,
        isTopPasar13,
        baghalBazooThickness,
        baghalBazooHeight,
        countBaghalBazoo13,
        countBaghalBazoo17,
        countGheyd17,
        pasarThickness,
        pasarHeight,
        pasarPerLength,
        countPasar13,
        countPasar17,
        countGheyd13,
        doorThickness,
        countZevardamageh,
        // centerLayerThickness,
    },
    count = 1
) {
    doorWidth *= 1;
    doorHeight *= 1;
    doorLengeh *= 1;

    doorThickness = doorThickness ?? doDoorThickness({ typeDoor, hasEdgeOfDoor, goodId, hasAbzar, typeMaterialDoor });
    const centerLayerThickness = doCenterLayerThickness({ typeMaterialDoor, doorThickness, typeDoor, good_id: goodId });

    countZevardamageh = (countZevardamageh ?? doZevardamageh({ hasEdgeOfDoor, doorLengeh })) * count;


    const poshtdarposhtSize = (() => {
        let width = doorWidth * 1;
        let height = doorHeight * 1;


        if (hasEdgeOfDoor) {
            switch (alignEdgeOfDoor) {
                case "threeSide":
                    width += 3;
                    height += 2;
                    break;
                case "fourSide":
                    width += 3;
                    height += 3;
                    break;
                case "onlyTopSide":
                    width += 1.5;
                    height += 2;
                    break;
                case "onlyLolaSide":
                    width += 2;
                    height += 1.5;
                    break;
                case "onlyLockSide":
                    width += 2;
                    height += 1.5;
                    break;
                case "onlyTopAndLolaSide":
                    width += 2;
                    height += 2;
                    break;
                case "onlyTopAndLockSide":
                    width += 2;
                    height += 2;
                    break;
                case "onlyLockAndLolaSide":
                    width += 3;
                    height += 1.5;
                    break;
            }

            if (doorLengeh * 1) {
                switch (doorLengeh * 1) {
                    case 1.5:
                    case 2:
                        width += 2;
                        break;
                    case 3:
                        width += 4;
                        break;
                    case 4:
                        width += 6;
                        break;
                }
            }
        } else {
            let per = 1.5;
            if (typeDoor == "CNC") {
                height += 1.5;
                per = 1.5;
            } else height += 1.5;

            if (doorLengeh * 1) {
                switch (doorLengeh * 1) {
                    case 1:
                        width += per;
                        break;
                    case 1.5:
                    case 2:
                        width += 2 * per;
                        break;
                    case 3:
                        width += 3 * per;
                        break;
                    case 4:
                        width += 4 * per;
                        break;
                }
            }
        }


        if (typeDoor == "CNC" && typeMaterialDoor == "fomizeh") {
            width = doorWidth * 1;
            height = doorHeight * 1;
            if (hasEdgeOfDoor) {
                width += 4;
                height += 3;
            }
            else {
                width += 2.5;
                height += 2.5;
            }

        }

        return { height, width };
    })();

    const baghalBazoo = (() => {
        if (!["RAPINGI", "KOMODI"].includes(typeDoor)) return false;
        return {
            thickness: (doorThickness * 10 - centerLayerThickness.value) / 2,
            height: (() => {
                switch (true) {
                    case doorHeight <= 195:
                        return 201;
                    case doorHeight <= 199:
                        return 205;
                    case doorHeight <= 203:
                        return 209;
                    case doorHeight <= 207:
                        return 213;
                    case doorHeight <= 211:
                        return 217;
                    case doorHeight <= 218:
                        return 224;
                    case doorHeight <= 244:
                        return 244;
                }
                return 0;
            })(),
            counts: (() => {
                let counts = { 13.5: 0, 17: 0, gheyd17: 0 };
                switch (doorLengeh * 1) {
                    case 1:
                        if (doorWidth <= 80) counts[13.5] = 4;
                        else counts[17] = 4;
                        break;
                    case 1.5:
                        counts.gheyd17 = 2;
                        if ((doorWidth / 3) * 2 <= 80) counts[13.5] = 4;
                        else counts[17] = 4;
                        break;
                    case 2:
                        if (poshtdarposhtSize.width / 2 <= 80) counts[13.5] = 4 * 2;
                        else counts[17] = 4 * 2;
                        break;
                    case 3:
                        if (doorWidth / 3 <= 80) counts[13.5] = 4 * 3;
                        else counts[17] = 4 * 3;
                        break;
                    case 4:
                        if (doorWidth / 4 <= 80) counts[13.5] = 4 * 4;
                        else counts[17] = 4 * 4;
                        break;
                }
                counts["13.5"] *= count;
                counts["17"] *= count;
                counts["gheyd17"] *= count;
                return counts;
            })(),
        };
    })();

    const pasar = (() => {
        if (!["RAPINGI", "KOMODI"].includes(typeDoor)) return false;
        return {
            thickness: (doorThickness * 10 - centerLayerThickness.value) / 2,
            height: (() => {
                const prefix = (() => {
                    switch (true) {
                        case hasEdgeOfDoor && hasAbzar:
                            return 10;
                        case !hasEdgeOfDoor && hasAbzar:
                            return 8;
                        case hasEdgeOfDoor && !hasAbzar:
                            return 6;
                        case !hasEdgeOfDoor && !hasAbzar:
                            return 4;
                    }
                })();

                if (doorLengeh == 1.5) {
                    return doorWidth - 44 + 2 * prefix;
                } else {
                    return (
                        doorWidth / doorLengeh -
                        2 * (baghalBazoo.counts["13.5"] > 0 ? 13.5 : 16.5) +
                        prefix
                    );
                }
            })(),
            perLength: '',
            counts: (() => {
                let counts = { 13.5: 0, 17: 0, gheyd13: countGheyd13 ?? 0 };

                if (doorLengeh == 1.5) {
                    if (isTopPasar13) {
                        counts["13.5"] = 2;
                        counts[17] = 2;
                    } else {
                        counts[17] = 4;
                    }
                } else {
                    if (isTopPasar13) {
                        counts["13.5"] = 2 * doorLengeh;
                        counts[17] = 2 * doorLengeh;
                    } else {
                        counts[17] = 4 * doorLengeh;
                    }
                }
                counts["13.5"] *= count;
                counts["17"] *= count;
                counts["gheyd13"] *= count;
                return counts;
            })(),
        };
    })();
    const res = {
        //doorLengeh,
        doorThickness,
        centerLayerThickness: centerLayerThickness.label,
        centerLayerThicknessValue: centerLayerThickness.value,
        //typeMaterialDoor,
        doorWidth,
        doorHeight,
        //doorAlign,
        countZevardamageh,
        // baghalBazoo,
        //baghalBazooHeight: baghalBazoo?.height,

        poshtdarposhtSize,
    };

    if (pasar && baghalBazoo) {
        if (!baghalBazooThickness) res.baghalBazooThickness = baghalBazoo?.thickness > 0 ? baghalBazoo?.thickness : '';
        if (!baghalBazooHeight) res.baghalBazooHeight = baghalBazoo?.height ?? '';
        if (!countBaghalBazoo13) res.countBaghalBazoo13 = baghalBazoo?.counts['13.5'] > 0 ? baghalBazoo?.counts['13.5'] : '';
        if (!countBaghalBazoo17) res.countBaghalBazoo17 = baghalBazoo?.counts['17'] > 0 ? baghalBazoo?.counts['17'] : '';

        if (!countGheyd17) res.countGheyd17 = baghalBazoo?.counts['gheyd17'] > 0 ? baghalBazoo?.counts['gheyd17'] : '';
        if (!pasarThickness) res.pasarThickness = pasar?.thickness > 0 ? pasar?.thickness : '';
        res.pasarHeight = pasarHeight ? Math.round(pasarHeight) : pasar?.height > 0 ? Math.round(pasar?.height * 100) / 100 : '';
        if (!pasarPerLength) res.pasarPerLength = pasar?.perLength > 0 ? pasar?.perLength : '';
        if (!countPasar13) res.countPasar13 = pasar?.counts['13.5'] > 0 ? pasar?.counts['13.5'] : '';
        if (!countPasar17) res.countPasar17 = pasar?.counts['17'] > 0 ? pasar?.counts['17'] : '';
        if (!countGheyd13) res.countGheyd13 = pasar?.counts['gheyd13'] > 0 ? pasar?.counts['gheyd13'] : '';
    }

    return res;
}
