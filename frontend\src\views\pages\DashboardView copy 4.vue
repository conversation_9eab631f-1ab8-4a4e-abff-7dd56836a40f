<!-- LoanManager.vue -->
<template>
  <div class="loan-manager">
    <h2>مدیریت وام‌ها</h2>

    <!-- فرم افزودن/ویرایش وام -->
    <div class="loan-form">
      <input v-model="newLoan.name" placeholder="نام وام" />
      <input v-model.number="newLoan.totalInstallments" placeholder="تعداد اقساط" type="number" min="1" />
      <select v-model="newLoan.loanType" @change="resetFields">
        <option value="" disabled>نوع وام را انتخاب کنید</option>
        <option value="qarz">قرض الحسنه</option>
        <option value="murabaha">مرابحه</option>
      </select>
      <input v-model.number="newLoan.principalAmount" placeholder="مبلغ اصل وام (ریال)" type="number" min="0" />
      <input v-model.number="newLoan.interestRate"
        :placeholder="`درصد ${newLoan.loanType === 'qarz' ? 'کارمزد' : 'سود'} سالانه`" type="number" min="0"
        step="0.1" />
      <input v-model="newLoan.startDate" placeholder="تاریخ شروع (مثال: 1403/01/05)" @input="validateDate" />
      <button @click="isEditing ? updateLoan() : addLoan()" :disabled="!isFormValid">
        {{ isEditing ? 'ذخیره تغییرات' : 'ثبت وام جدید' }}
      </button>
      <button v-if="isEditing" @click="cancelEdit" class="cancel-btn">لغو</button>
    </div>

    <!-- جدول وام‌ها -->
    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th class="sticky-column">روز</th>
            <th class="sticky-column">نام وام</th>
            <th class="sticky-column">نوع</th>
            <th class="sticky-column">اقساط</th>
            <th class="sticky-column">مبلغ کل</th>
            <th class="sticky-column">پرداخت نشده</th>
            <th class="sticky-column">مانده</th>
            <th v-for="month in sortedMonthColumns" :key="month">{{ month }}</th>
            <th class="sticky-column">عملیات</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="loan in loans" :key="loan.id">
            <td class="sticky-column">{{ loan.day }}</td>
            <td class="sticky-column">{{ loan.name }}</td>
            <td class="sticky-column">{{ loanTypeNames[loan.loanType] }}</td>
            <td class="sticky-column">{{ loan.totalInstallments }}</td>
            <td class="sticky-column">{{ formatCurrency(loan.totalAmount) }}</td>
            <td class="sticky-column">{{ unpaidInstallments(loan) }}</td>
            <td class="sticky-column">{{ formatCurrency(remainingAmount(loan)) }}</td>
            <td v-for="month in sortedMonthColumns" :key="month" @click="togglePayment(loan, month)" :class="{
              'paid': isPaid(loan, month),
              'payment-cell': hasPayment(loan, month),
              'unpaid': !isPaid(loan, month) && hasPayment(loan, month)
            }">
              {{ hasPayment(loan, month) ? formatCurrency(getPaymentAmount(loan, month)) : '' }}
            </td>
            <td class="sticky-column">
              <button @click="editLoan(loan)" class="edit-btn">✎ ویرایش</button>
            </td>
          </tr>
          <tr class="totals-row">
            <td colspan="7" class="sticky-column">جمع کل پرداخت نشده</td>
            <td v-for="month in sortedMonthColumns" :key="month">
              {{ formatCurrency(getTotalForMonth(month)) }}
            </td>
            <td class="sticky-column"></td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoanManager',
  data() {
    return {
      persianMonths: [
        'فروردین', 'اردیبهشت', 'خرداد', 'تیر', 'مرداد', 'شهریور',
        'مهر', 'آبان', 'آذر', 'دی', 'بهمن', 'اسفند'
      ],
      loanTypeNames: {
        qarz: 'قرض الحسنه',
        murabaha: 'مرابحه'
      },
      loans: [],
      newLoan: {
        name: '',
        totalInstallments: 0,
        loanType: '',
        principalAmount: 0,
        interestRate: 0,
        startDate: ''
      },
      isValidDate: false,
      isEditing: false,
      editingLoanId: null
    };
  },
  computed: {
    sortedMonthColumns() {
      const allMonths = this.loans.flatMap(loan => loan.payments.map(p => p.monthYear));
      return [...new Set(allMonths)].sort(this.compareMonths);
    },
    isFormValid() {
      return this.isValidDate &&
        this.newLoan.loanType &&
        this.newLoan.totalInstallments > 0 &&
        this.newLoan.principalAmount > 0 &&
        this.newLoan.interestRate >= 0;
    }
  },
  methods: {
    // ----------- ابزارهای کمکی -----------
    formatCurrency(value) {
      return new Intl.NumberFormat('fa-IR').format(value) + ' ریال';
    },

    compareMonths(a, b) {
      const [aMonth, aYear] = a.split(' ');
      const [bMonth, bYear] = b.split(' ');
      const yearDiff = parseInt(aYear) - parseInt(bYear);
      if (yearDiff !== 0) return yearDiff;
      return this.persianMonths.indexOf(aMonth) - this.persianMonths.indexOf(bMonth);
    },

    validateDate() {
      const regex = /^14\d{2}\/(0[1-9]|1[0-2])\/(0[1-9]|[12]\d|3[01])$/;
      if (!regex.test(this.newLoan.startDate)) {
        this.isValidDate = false;
        return;
      }

      const [year, month, day] = this.newLoan.startDate.split('/').map(Number);
      const persianMonth = month - 1;

      // بررسی تعداد روزهای هر ماه شمسی
      const monthLengths = [31, 31, 31, 31, 31, 31, 30, 30, 30, 30, 30, 29];
      const isLeap = (year - 1395) % 4 === 0; // محاسبه سال کبیسه
      if (persianMonth === 11 && isLeap) monthLengths[11] = 30;

      this.isValidDate = day <= monthLengths[persianMonth];
    },

    resetFields() {
      this.newLoan.principalAmount = 0;
      this.newLoan.interestRate = 0;
    },

    // ----------- محاسبات مالی -----------
    calculateAnnuity(principal, annualRate, installments) {
      const monthlyRate = annualRate / 12 / 100;
      if (monthlyRate === 0) return principal / installments; // حالت بدون سود
      const discountFactor = (Math.pow(1 + monthlyRate, installments) - 1);
      return (principal * monthlyRate * Math.pow(1 + monthlyRate, installments)) / discountFactor;
    },

    generatePayments() {
      const [year, month, day] = this.newLoan.startDate.split('/').map(Number);
      const payments = [];
      const principal = this.newLoan.principalAmount;
      const rate = this.newLoan.interestRate;
      const totalInstallments = this.newLoan.totalInstallments;

      if (this.newLoan.loanType === 'qarz') {
        const totalYears = Math.ceil(totalInstallments / 12);
        const principalInstallments = totalInstallments - totalYears;
        const principalPayment = principal / principalInstallments;

        let currentYear = year;
        let currentMonth = month - 1;
        let remainingPrincipal = principal;

        for (let yearCounter = 0; yearCounter < totalYears; yearCounter++) {
          const monthsInYear = Math.min(12, totalInstallments - payments.length);

          // کارمزد سالانه
          payments.push({
            monthYear: `${this.persianMonths[currentMonth]} ${currentYear}`,
            amount: Math.round(remainingPrincipal * (rate / 100) * (monthsInYear / 12)),
            type: 'interest'
          });

          // اقساط اصل
          for (let m = 1; m < monthsInYear; m++) {
            if (payments.length >= totalInstallments) break;

            payments.push({
              monthYear: `${this.persianMonths[(currentMonth + m) % 12]} ${currentYear + Math.floor((currentMonth + m) / 12)}`,
              amount: Math.round(principalPayment),
              type: 'principal'
            });

            remainingPrincipal -= principalPayment;
          }

          currentMonth = (currentMonth + monthsInYear) % 12;
          currentYear += Math.floor((currentMonth + monthsInYear) / 12);
        }

      } else if (this.newLoan.loanType === 'murabaha') {
        const installment = this.calculateAnnuity(principal, rate, totalInstallments);
        let currentYear = year;
        let currentMonth = month - 1;

        for (let i = 0; i < totalInstallments; i++) {
          payments.push({
            monthYear: `${this.persianMonths[currentMonth]} ${currentYear}`,
            amount: Math.round(installment),
            type: 'mixed'
          });
          currentMonth = (currentMonth + 1) % 12;
          if (currentMonth === 0) currentYear++;
        }
      }

      return payments;
    },

    // ----------- عملیات CRUD -----------
    addLoan() {
      if (!this.isFormValid) return;

      const payments = this.generatePayments();
      const totalAmount = payments.reduce((sum, p) => sum + p.amount, 0);

      this.loans.push({
        id: Date.now(),
        day: String(this.newLoan.startDate.split('/')[2]).padStart(2, '0'),
        ...this.newLoan,
        totalAmount,
        payments: payments.map(p => ({ ...p, paid: false }))
      });

      this.resetForm();
    },

    editLoan(loan) {
      this.isEditing = true;
      this.editingLoanId = loan.id;
      this.newLoan = {
        name: loan.name,
        totalInstallments: loan.totalInstallments,
        loanType: loan.loanType,
        principalAmount: loan.principalAmount,
        interestRate: loan.interestRate,
        startDate: loan.payments[0].monthYear.replace(
          /(\w+) (\d+)/,
          (_, month, year) =>
            `${year}/${String(this.persianMonths.indexOf(month) + 1)
              .padStart(2, '0')}/${loan.day}`
        )
      };
      this.validateDate();
    },

    updateLoan() {
      const index = this.loans.findIndex(l => l.id === this.editingLoanId);
      if (index === -1) return;

      const payments = this.generatePayments();
      const totalAmount = payments.reduce((sum, p) => sum + p.amount, 0);

      this.loans.splice(index, 1, {
        ...this.newLoan,
        totalAmount,
        payments: payments.map(p => ({
          ...p,
          paid: this.loans[index].payments.find(op =>
            op.monthYear === p.monthYear
          )?.paid || false
        }))
      });

      this.cancelEdit();
    },

    cancelEdit() {
      this.isEditing = false;
      this.editingLoanId = null;
      this.resetForm();
    },

    // ----------- مدیریت وضعیت پرداخت -----------
    togglePayment(loan, monthYear) {
      const payment = loan.payments.find(p => p.monthYear === monthYear);
      if (payment) payment.paid = !payment.paid;
    },

    unpaidInstallments(loan) {
      return loan.payments.filter(p => !p.paid).length;
    },

    remainingAmount(loan) {
      return loan.payments
        .filter(p => !p.paid)
        .reduce((sum, p) => sum + p.amount, 0);
    },

    isPaid(loan, monthYear) {
      return loan.payments.some(p => p.monthYear === monthYear && p.paid);
    },

    hasPayment(loan, monthYear) {
      return loan.payments.some(p => p.monthYear === monthYear);
    },

    getPaymentAmount(loan, monthYear) {
      return loan.payments.find(p => p.monthYear === monthYear)?.amount || 0;
    },

    getTotalForMonth(month) {
      return this.loans.reduce((sum, loan) => {
        const payment = loan.payments.find(p =>
          p.monthYear === month && !p.paid
        );
        return sum + (payment?.amount || 0);
      }, 0);
    }
  }
};
</script>

<style scoped>
.loan-manager {
  padding: 2rem;
  max-width: 100vw;
  overflow-x: hidden;
}

.loan-form {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

input,
select {
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
}

button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

button:not(:disabled) {
  background: #4a90e2;
  color: white;
}

button:disabled {
  background: #e9ecef;
  cursor: not-allowed;
}

.cancel-btn {
  background: #dc3545 !important;
}

.edit-btn {
  background: #28a745 !important;
}

.table-container {
  border: 1px solid #dee2e6;
  border-radius: 12px;
  overflow-x: auto;
  background: white;
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

th,
td {
  padding: 1rem;
  text-align: center;
  border-bottom: 1px solid #dee2e6;
  background: white;
}

th {
  background: #f8f9fa;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 2;
}

.sticky-column {
  position: sticky;
  left: 0;
  z-index: 1;
  background: white !important;
  box-shadow: 2px 0 5px -1px rgba(0, 0, 0, 0.1);
}

.payment-cell {
  cursor: pointer;
  transition: background 0.2s ease;
}

.paid {
  background: #d4edda !important;
}

.unpaid {
  background: #fff3cd !important;
}

.totals-row td {
  background: #e9ecef;
  font-weight: 700;
}

.sticky-column {
  position: sticky;
  left: 0;
  z-index: 3;
  background: white;
  box-shadow: 2px 0 5px rgba(0,0,0,0.1);
  min-width: 120px;
}

th.sticky-column {
  z-index: 4;
  background: #f8f9fa;
}

/* تنظیم موقعیت برای هر ستون */
td:nth-child(1), th:nth-child(1) { left: 0; }
td:nth-child(2), th:nth-child(2) { left: 120px; }
td:nth-child(3), th:nth-child(3) { left: 240px; }
td:nth-child(4), th:nth-child(4) { left: 360px; }
td:nth-child(5), th:nth-child(5) { left: 480px; }
td:nth-child(6), th:nth-child(6) { left: 600px; }
td:nth-child(7), th:nth-child(7) { left: 720px; }
</style>