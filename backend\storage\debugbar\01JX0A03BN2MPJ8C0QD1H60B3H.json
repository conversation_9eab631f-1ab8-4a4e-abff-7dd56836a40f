{"__meta": {"id": "01JX0A03BN2MPJ8C0QD1H60B3H", "datetime": "2025-06-05 18:35:20", "utime": **********.502407, "method": "GET", "uri": "/api/user", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749135919.96381, "end": **********.50243, "duration": 0.5386199951171875, "duration_str": "539ms", "measures": [{"label": "Booting", "start": 1749135919.96381, "relative_start": 0, "end": **********.35207, "relative_end": **********.35207, "duration": 0.****************, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.352086, "relative_start": 0.****************, "end": **********.502435, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.380883, "relative_start": 0.*****************, "end": **********.385922, "relative_end": **********.385922, "duration": 0.0050389766693115234, "duration_str": "5.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.494306, "relative_start": 0.****************, "end": **********.499141, "relative_end": **********.499141, "duration": 0.004834890365600586, "duration_str": "4.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.499178, "relative_start": 0.****************, "end": **********.499247, "relative_end": **********.499247, "duration": 6.914138793945312e-05, "duration_str": "69μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.0.1", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "api.erp.test:8000", "Timezone": "Asia/Tehran", "Locale": "fa"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04147000000000001, "accumulated_duration_str": "41.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '107' limit 1", "type": "query", "params": [], "bindings": ["107"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.414526, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "raasa", "explain": null, "start_percent": 0, "width_percent": 8.609}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4362118, "duration": 0.024820000000000002, "duration_str": "24.82ms", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "raasa", "explain": null, "start_percent": 8.609, "width_percent": 59.85}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-06-05 18:35:20', `personal_access_tokens`.`updated_at` = '2025-06-05 18:35:20' where `id` = 107", "type": "query", "params": [], "bindings": ["2025-06-05 18:35:20", "2025-06-05 18:35:20", 107], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.4638548, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "raasa", "explain": null, "start_percent": 68.459, "width_percent": 5.281}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/API/UserController.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Http\\Controllers\\API\\UserController.php", "line": 196}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.477473, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "UserController.php:196", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/API/UserController.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Http\\Controllers\\API\\UserController.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FHttp%2FControllers%2FAPI%2FUserController.php&line=196", "ajax": false, "filename": "UserController.php", "line": "196"}, "connection": "raasa", "explain": null, "start_percent": 73.74, "width_percent": 8.922}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/API/UserController.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Http\\Controllers\\API\\UserController.php", "line": 202}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.485375, "duration": 0.00719, "duration_str": "7.19ms", "memory": 0, "memory_str": null, "filename": "UserController.php:202", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/API/UserController.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Http\\Controllers\\API\\UserController.php", "line": 202}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FHttp%2FControllers%2FAPI%2FUserController.php&line=202", "ajax": false, "filename": "UserController.php", "line": "202"}, "connection": "raasa", "explain": null, "start_percent": 82.662, "width_percent": 17.338}]}, "models": {"data": {"Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://api.erp.test:8000/api/user", "action_name": null, "controller_action": "App\\Http\\Controllers\\API\\UserController@show_current", "uri": "GET api/user", "controller": "App\\Http\\Controllers\\API\\UserController@show_current<a href=\"phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FHttp%2FControllers%2FAPI%2FUserController.php&line=192\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api", "file": "<a href=\"phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FHttp%2FControllers%2FAPI%2FUserController.php&line=192\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/API/UserController.php:192-204</a>", "middleware": "api, auth:sanctum", "duration": "542ms", "peak_memory": "26MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-201636536 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-201636536\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-271105500 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-271105500\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-524416976 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">api.erp.test:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 107|t******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://panel.erp.test:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://panel.erp.test:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">fa-IR,fa;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-524416976\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1986711717 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1986711717\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1426718532 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 05 Jun 2025 15:05:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426718532\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1378407830 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1378407830\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://api.erp.test:8000/api/user", "controller_action": "App\\Http\\Controllers\\API\\UserController@show_current"}, "badge": null}}