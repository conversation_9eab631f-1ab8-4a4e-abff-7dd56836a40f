import axios from "axios";
import { useRequestStore, useAuthStore } from "@/stores";
import { Notify } from "quasar";
import { router } from "@/helpers/router/index";
import { usePublicStore } from "@/stores";

const api = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL || "http://localhost:8000/api",
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
});

api.interceptors.request.use(
    (config) => {
        if (localStorage.getItem("token")) {
            config.headers["Authorization"] =
                "Bearer " + localStorage.getItem("token");
        }
        const requestStore = useRequestStore();
        requestStore.errors = {};
        requestStore.loading = true;
        return config;
    },
    (error) => {
        console.log(error); // for debug
        Promise.reject(error);
    }
);
api.interceptors.response.use(
    (response) => {
        const requestStore = useRequestStore();
        requestStore.loading = false;
        if (response.data.message) {
            Notify.create({
                type: "positive",
                message: response.data.message,
            });
        }
        return response.data;
    },
    (error) => {
        const authStore = useAuthStore();
        if (error.message == 'Network Error') {
            Notify.create({
                type: "negative",
                message: "عدم برقراری ارتباط، لطفا اینترنت خود را بررسی کنید",
            });
        }
        else if ([401].includes(error.response.status)) {


            if (error.response?.data?.message == 'Unauthenticated.') {
                Notify.create({
                    type: "negative",
                    message: "دوباره وارد شوید",
                });
                authStore.logout();
            }
        }
        else if ([403].includes(error.response.status)) {
            console.log('axios 403 response')
            Notify.create({
                type: "negative",
                message: "دسترسی ندارید!",
            });
            //const publicStore = usePublicStore()
            //publicStore.setNotAccess(true)
        }
        else if ([404].includes(error.response.status)) {
            Notify.create({
                type: "negative",
                message: "پیدا نشد!",
            });
            // router.push('/404')

            // router.currentRoute.value.meta.layoutComponent = () => import('@/views/pages/page404View.vue');
            //const publicStore = usePublicStore()
            // publicStore.setNotFound(true)
            //this.$forceUpdate(); // برای به‌روزرسانی کامپوننت
        }
        else if ([500].includes(error.response.status)) {
            Notify.create({
                type: "negative",
                message: "خطا در سرور",
            });
        }
        else if ([422, 429].includes(error.response.status)) {
            let message = typeof error.response.data.message == 'object' ? Object.values(error.response.data.message).join('\n') : error.response.data.message;
            Notify.create({
                type: "negative",
                message: message,
            });
        }
        const requestStore = useRequestStore();
        requestStore.loading = false;
        requestStore.errors = error?.response?.data?.errors ?? {};
        return Promise.reject(error);
    }
);

export { axios, api };
