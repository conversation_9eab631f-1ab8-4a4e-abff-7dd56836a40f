import { crm_home_page as home_page } from "../index";

export default function auth({ next, router, auth, to }) {
    console.log('middleware auth')
    const token = localStorage.getItem('token');

    if (!token && to.name != 'login') {
        console.log('No token, redirecting to login');
        return router.push({ name: 'login' });
    }
    else if (token && to.name == 'login') {
        console.log('Has token and trying to access login, redirecting to dashboard');
        // Determine home page based on current domain
        const currentHost = window.location.host;
        const crmDomain = import.meta.env.VITE_CRM_DOMAIN;

        if (currentHost === crmDomain) {
            const homePage = home_page();
            if (homePage) {
                return router.push({ name: homePage });
            }
        }

        // Default to dashboard for panel
        return router.push({ name: 'dashboard' });
    }
    else {
        next();
    }
}