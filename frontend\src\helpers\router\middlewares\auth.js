import { crm_home_page as home_page } from "../index";
import { getCurrentDomainType, DOMAIN_TYPES } from '@/config/domains';

export default function auth({ next, router, auth, to }) {
    console.log('middleware auth')
    const token = localStorage.getItem('token');

    if (!token && to.name != 'login') {
        console.log('No token, redirecting to login');
        return router.push({ name: 'login' });
    }
    else if (token && to.name == 'login') {
        console.log('Has token and trying to access login, redirecting to dashboard');

        // Determine home page based on current domain
        const domainType = getCurrentDomainType();

        if (domainType === DOMAIN_TYPES.CRM) {
            const homePage = home_page();
            if (homePage) {
                return router.push({ name: homePage });
            }
            // Default CRM home page
            return router.push({ name: 'crm_production_order' });
        }

        // Default to dashboard for panel
        return router.push({ name: 'dashboard' });
    }
    else {
        next();
    }
}