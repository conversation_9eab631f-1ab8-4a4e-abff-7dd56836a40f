<template>
    <q-item clickable>
      <q-item-section avatar>
        <q-avatar>
          <img :src="user?.avatar ?? 'https://cdn.quasar.dev/img/avatar1.jpg'" />
        </q-avatar>
      </q-item-section>
      <q-item-section>
        <q-item-label>{{ user?.full_name ?? 'کاربر' }}</q-item-label>
        <q-item-label caption>{{ user?.role_name ?? '' }}</q-item-label>
      </q-item-section>
    </q-item>
  </template>
  
  <script setup>
  import { defineProps } from 'vue';
  
  const props = defineProps({
    user: {
      type: Object,
      required: false,
    },
  });
  </script>
  