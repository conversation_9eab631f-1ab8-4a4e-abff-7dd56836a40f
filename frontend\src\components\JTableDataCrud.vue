<template>
    <!-- Mobile Filter Button -->


    <q-table ref="tableRef" bordered :square="$q.screen.lt.sm" flat separator="cell" :rows="rows" :columns="columns"
        row-key="id" v-model:pagination="pagination" :dense="true" :loading="loading" selection="multiple"
        v-model:selected="selected" @selection="handleSelection" sbinary-state-sort @request="onRequest"
        class="my-sticky-header-column-table topRowTable">
        <template v-for="(_, slot) in $slots" v-slot:[slot]="props" :key="slot">
            <slot :name="slot" v-bind="props" :key="slot" />
        </template>
        <template v-if="true || tools" v-slot:top>
            <component v-if="tools" :is="tools" />
            <tools v-else :routeName="routeName || url" :permissions="permissions" />
            <q-space />
            <q-btn v-if="$q.screen.lt.sm && hasFilterableColumns" dense flat icon="filter_list"
                :label="$q.screen.lt.sm ? '' : 'فیلترها'" @click="mobileFilterDrawer = true">
                <q-badge v-if="activeFiltersCount > 0" color="red">{{ activeFiltersCount }}</q-badge>
            </q-btn>

            <!-- Clear Filters Button for Desktop -->
            <q-btn v-if="$q.screen.gt.xs && activeFiltersCount > 0" dense flat icon="clear_all"
                label="حذف فیلترها" @click="clearAllFilters">
            </q-btn>

        </template>

        <!-- Header with Filter Icons (Desktop Only) -->
        <template v-if="$q.screen.gt.xs" v-slot:header="props">
            <q-tr :props="props">
                <q-th auto-width>
                    <q-checkbox v-model="props.selected" />
                </q-th>
                <q-th v-for="col in props.cols" :key="col.name" :props="props" @click="col.sortable && props.sort(col.name)">
                    <div class="flex items-center justify-between">
                        <span>{{ col.label }}</span>
                        <q-btn v-if="col.filterType" dense round flat size="sm"
                            :color="hasActiveFilter(col.field) ? 'primary' : 'grey-6'"
                            icon="filter_list"
                            @click.stop>
                            <q-menu anchor="bottom end" self="top end" :offset="[0, 8]">
                                <q-card style="min-width: 300px; max-width: 400px;">
                                    <q-card-section class="q-pb-none">
                                        <div class="text-subtitle2">فیلتر {{ col.label }}</div>
                                    </q-card-section>

                                    <q-card-section>
                                        <!-- Text and Number Filters -->
                                        <div v-if="['text', 'number'].includes(col.filterType)">
                                            <q-select v-model="filterConditions[col.field]"
                                                :options="filterOptions.filter(f => f.type === col.filterType)"
                                                option-label="label" option-value="value" emit-value map-options dense
                                                outlined label="نوع فیلتر" class="q-mb-md" />

                                            <q-input v-model="filters[col.field]" dense outlined
                                                :placeholder="col.filterType === 'text' ? 'متن جستجو' : 'عدد'"
                                                @keyup.enter="applyTextFilter(col.field)" />
                                        </div>

                                        <!-- Select Filters -->
                                        <div v-if="col.filterType === 'select'">
                                            <q-select v-model="filters[col.field]" :options="getSelectOptions(col)" multiple
                                                dense outlined use-chips @update:model-value="handleSearch(col.field)"
                                                option-label="label" option-value="value" emit-value map-options
                                                label="انتخاب گزینه‌ها" />
                                        </div>

                                        <!-- Date Filters -->
                                        <div v-if="col.filterType === 'date'">
                                            <q-select v-model="filterConditions[col.field]"
                                                :options="filterOptions.filter(f => f.type === 'date')"
                                                option-label="label" option-value="value" emit-value map-options dense
                                                outlined label="نوع فیلتر تاریخ" class="q-mb-md" />
                                            <!-- Single Date Input -->
                                            <div
                                                v-if="filterConditions[col.field] && ['dateEquals', 'dateBefore', 'dateAfter'].includes(filterConditions[col.field])">
                                                <q-input v-model="filters[col.field]" dense outlined label="تاریخ"
                                                    placeholder="1402/05/15" mask="####/##/##" fill-mask="_"
                                                    :rules="dateRules" lazy-rules @keypress="onlyNumbers"
                                                    @blur="validateAndCorrectDate(col.field)">
                                                    <template v-slot:append>
                                                        <q-btn dense round flat size="sm" icon="event">
                                                            <q-menu anchor="bottom end" self="top end">
                                                                <j-date v-model="filters[col.field]" />
                                                            </q-menu>
                                                        </q-btn>
                                                    </template>
                                                </q-input>
                                            </div>

                                            <!-- Date Range Input -->
                                            <div v-if="filterConditions[col.field] === 'dateBetween'"
                                                class="grid gap-1">
                                                <q-input v-model="filters[col.field + '_from']" dense outlined
                                                    label="از تاریخ" placeholder="1402/05/01" mask="####/##/##"
                                                    fill-mask="_" :rules="dateRules" lazy-rules @keypress="onlyNumbers"
                                                    @blur="validateAndCorrectDate(col.field + '_from')">
                                                    <template v-slot:append>
                                                        <q-btn dense round flat size="sm" icon="event">
                                                            <q-menu anchor="bottom end" self="top end">
                                                                <j-date v-model="filters[col.field + '_from']" />
                                                            </q-menu>
                                                        </q-btn>
                                                    </template>
                                                </q-input>

                                                <q-input v-model="filters[col.field + '_to']" dense outlined
                                                    label="تا تاریخ" placeholder="1402/05/30" mask="####/##/##"
                                                    fill-mask="_" :rules="dateRules" lazy-rules @keypress="onlyNumbers"
                                                    @blur="validateAndCorrectDate(col.field + '_to')">
                                                    <template v-slot:append>
                                                        <q-btn dense round flat size="sm" icon="event">
                                                            <q-menu anchor="bottom end" self="top end">
                                                                <j-date v-model="filters[col.field + '_to']" />
                                                            </q-menu>
                                                        </q-btn>
                                                    </template>
                                                </q-input>
                                            </div>
                                        </div>
                                    </q-card-section>

                                    <q-card-actions align="right">
                                        <q-btn flat dense label="پاک کردن"
                                            @click="clearFilter(col.field, col.filterType)" />
                                        <q-btn v-if="['text', 'number'].includes(col.filterType)" flat dense
                                            label="اعمال" color="primary" @click="applyTextFilter(col.field)" />
                                        <q-btn v-if="col.filterType === 'date'" flat dense label="اعمال" color="primary"
                                            @click="applyDateFilter(col.field)" />
                                        <q-btn flat dense label="بستن" v-close-popup />
                                    </q-card-actions>
                                </q-card>
                            </q-menu>
                        </q-btn>
                    </div>
                </q-th>
            </q-tr>
        </template>
    </q-table>

    <!-- Mobile Filter Drawer -->
    <q-drawer v-model="mobileFilterDrawer" side="right" overlay behavior="mobile" :width="350" class="column">

        <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>فیلترها</q-toolbar-title>
            <q-btn flat round dense icon="close" @click="mobileFilterDrawer = false" />
        </q-toolbar>

        <div class="col q-pa-md" style="overflow-y: auto;">
                <div v-for="col in filterableColumns" :key="col.field" class="q-mb-lg">
                    <div class="text-subtitle2 q-mb-md">{{ col.label }}</div>

                    <!-- Text and Number Filters -->
                    <div v-if="['text', 'number'].includes(col.filterType)" class="q-gutter-y-sm">
                        <q-select v-model="filterConditions[col.field]"
                            :options="filterOptions.filter(f => f.type === col.filterType)" option-label="label"
                            option-value="value" emit-value map-options dense outlined label="نوع فیلتر" />

                        <q-input v-model="filters[col.field]" dense outlined
                            :placeholder="col.filterType === 'text' ? 'متن جستجو' : 'عدد'"
                            @keyup.enter="applyTextFilter(col.field)" />

                        <q-btn color="primary" label="اعمال" @click="applyTextFilter(col.field)" class="full-width" />
                    </div>

                    <!-- Select Filters -->
                    <div v-if="col.filterType === 'select'">
                        <q-select v-model="filters[col.field]" :options="getSelectOptions(col)" multiple dense outlined
                            use-chips @update:model-value="handleSearch(col.field)"
                            option-label="label" option-value="value" emit-value map-options
                            label="انتخاب گزینه‌ها" />
                    </div>

                    <!-- Date Filters -->
                    <div v-if="col.filterType === 'date'" class="q-gutter-y-sm">
                        <q-select v-model="filterConditions[col.field]"
                            :options="filterOptions.filter(f => f.type === 'date')" option-label="label"
                            option-value="value" emit-value map-options dense outlined label="نوع فیلتر تاریخ" />

                        <!-- Single Date Input -->
                        <div
                            v-if="filterConditions[col.field] && ['dateEquals', 'dateBefore', 'dateAfter'].includes(filterConditions[col.field])">
                            <q-input v-model="filters[col.field]" dense outlined label="تاریخ" placeholder="1402/05/15"
                                mask="####/##/##" fill-mask="_" :rules="dateRules" lazy-rules @keypress="onlyNumbers"
                                @blur="validateAndCorrectDate(col.field)">
                                <template v-slot:append>
                                    <q-btn dense round flat size="sm" icon="event">
                                        <q-menu anchor="bottom end" self="top end">
                                            <j-date v-model="filters[col.field]" />
                                        </q-menu>
                                    </q-btn>
                                </template>
                            </q-input>
                        </div>

                        <!-- Date Range Input -->
                        <div v-if="filterConditions[col.field] === 'dateBetween'" class="q-gutter-y-sm">
                            <q-input v-model="filters[col.field + '_from']" dense outlined label="از تاریخ"
                                placeholder="1402/05/01" mask="####/##/##" fill-mask="_" :rules="dateRules" lazy-rules
                                @keypress="onlyNumbers" @blur="validateAndCorrectDate(col.field + '_from')">
                                <template v-slot:append>
                                    <q-btn dense round flat size="sm" icon="event">
                                        <q-menu anchor="bottom end" self="top end">
                                            <j-date v-model="filters[col.field + '_from']" />
                                        </q-menu>
                                    </q-btn>
                                </template>
                            </q-input>

                            <q-input v-model="filters[col.field + '_to']" dense outlined label="تا تاریخ"
                                placeholder="1402/05/30" mask="####/##/##" fill-mask="_" :rules="dateRules" lazy-rules
                                @keypress="onlyNumbers" @blur="validateAndCorrectDate(col.field + '_to')">
                                <template v-slot:append>
                                    <q-btn dense round flat size="sm" icon="event">
                                        <q-menu anchor="bottom end" self="top end">
                                            <j-date v-model="filters[col.field + '_to']" />
                                        </q-menu>
                                    </q-btn>
                                </template>
                            </q-input>
                        </div>

                        <q-btn color="primary" label="اعمال" @click="applyDateFilter(col.field)" class="full-width" />
                    </div>

                    <!-- Clear Filter Button -->
                    <q-btn v-if="hasActiveFilter(col.field)" flat color="negative" label="پاک کردن فیلتر"
                        @click="clearFilter(col.field, col.filterType)" class="full-width q-mt-sm" />

                    <q-separator class="q-mt-lg" />
                </div>
        </div>
    </q-drawer>
</template>

<script setup>
import { ref, computed } from 'vue';
import Tools from './tools.vue';
import { tableApi } from '@/helpers/table/index';

// Props
const props = defineProps({
    tools: {
        type: Object,
    },
    routeName: {
        type: String,
        default: ''
    },
    permissions: {
        type: Object,
        default: {}
    },
    url: {
        type: String,
        default: ''
    },
    columns: {
        type: Array,
        default: () => []
    },
});

// Reactive references
const {
    tableRef,
    loading,
    rows,
    columns,
    selected,
    filters,
    filterConditions,
    filterOptions,
    serverData,
    pagination,
    onRequest,
    handleSelection,
    handleSearch,
} = tableApi(props.url, props);

// Mobile filter drawer
const mobileFilterDrawer = ref(false);

// Computed properties for mobile filters
const filterableColumns = computed(() => {
    return columns.value.filter(col => col.filterType);
});

const hasFilterableColumns = computed(() => {
    return filterableColumns.value.length > 0;
});

const activeFiltersCount = computed(() => {
    let count = 0;
    filterableColumns.value.forEach(col => {
        if (hasActiveFilter(col.field)) {
            count++;
        }
    });
    return count;
});

// Get select options for a column
const getSelectOptions = (col) => {
    // If filterOptions is an array, use it directly
    if (Array.isArray(col.filterOptions)) {
        return col.filterOptions;
    }

    // If filterOptions is a string (field name)
    if (typeof col.filterOptions === 'string') {
        const fieldName = col.filterOptions;
        console.error(fieldName, serverData.value[fieldName])
        // First, check if it exists in serverData (like 'statuses')
        if (serverData.value && serverData.value[fieldName]) {
            console.error(serverData.value[fieldName])
            return serverData.value[fieldName];
        }

        // If not found in serverData, extract unique values from rows
        const uniqueValues = [...new Set(
            rows.value
                .map(row => row[fieldName])
                .filter(value => value !== null && value !== undefined && value !== '')
        )];

        // Convert to option objects
        return uniqueValues.map(value => ({
            label: value,
            value: value
        }));
    }

    // Fallback: empty array
    return [];
};

// Check if filter is active
const hasActiveFilter = (field) => {
    return filters.value[field] ||
        filters.value[field + '_from'] ||
        filters.value[field + '_to'] ||
        (Array.isArray(filters.value[field]) && filters.value[field].length > 0);
};

// Apply text/number filter
const applyTextFilter = (field) => {
    if (!filterConditions.value[field]) {
        filterConditions.value[field] = 'includes'; // Default condition
    }
    handleSearch(field);
};

// Validate Persian date format
const isValidPersianDate = (dateString) => {
    if (!dateString) return false;
    const regex = /^\d{4}\/\d{2}\/\d{2}$/;
    if (!regex.test(dateString)) return false;

    const parts = dateString.split('/');
    const year = parseInt(parts[0]);
    const month = parseInt(parts[1]);
    const day = parseInt(parts[2]);

    // Basic validation
    if (year < 1300 || year > 1500) return false;
    if (month < 1 || month > 12) return false;
    if (day < 1 || day > 31) return false;

    // Advanced validation for Persian calendar
    if (month >= 1 && month <= 6) {
        // First 6 months have 31 days
        if (day > 31) return false;
    } else if (month >= 7 && month <= 11) {
        // Months 7-11 have 30 days
        if (day > 30) return false;
    } else if (month === 12) {
        // Month 12 has 29 or 30 days (leap year check)
        const isLeapYear = ((year - 979) * 683) % 1029 < 683;
        if (day > (isLeapYear ? 30 : 29)) return false;
    }

    return true;
};

// Real-time date validation
const validateDateInput = (value) => {
    if (!value) return true;

    // Allow partial input during typing
    if (value.length < 10) return true;

    // Full validation when complete
    if (value.length === 10) {
        return isValidPersianDate(value);
    }

    return true;
};

// Date input rules
const dateRules = [
    val => validateDateInput(val) || 'تاریخ وارد شده نامعتبر است'
];

// Only allow numbers and slash
const onlyNumbers = (event) => {
    const char = String.fromCharCode(event.which);
    if (!/[0-9\/]/.test(char)) {
        event.preventDefault();
    }
};

// Validate date on blur (just show warning, don't clear)
const validateAndCorrectDate = (field) => {
    const value = filters.value[field];
    if (!value) return;

    // If invalid, just show warning - don't clear the field
    if (!isValidPersianDate(value)) {
        console.warn(`تاریخ نامعتبر در فیلد ${field}: ${value}`);
        // You can show a toast notification here instead of console.warn
    }
};

// Apply date filter
const applyDateFilter = (field) => {
    const condition = filterConditions.value[field];

    if (!condition) {
        // اگر نوع فیلتر انتخاب نشده
        return;
    }

    if (condition === 'dateBetween') {
        // برای بازه تاریخ، هر دو تاریخ باید انتخاب شده باشند
        const fromDate = filters.value[field + '_from'];
        const toDate = filters.value[field + '_to'];

        if (!fromDate || !toDate) {
            return;
        }

        // اعتبارسنجی فرمت تاریخ
        if (!isValidPersianDate(fromDate) || !isValidPersianDate(toDate)) {
            // می‌توانید اینجا پیام خطا نمایش دهید
            console.warn('فرمت تاریخ نامعتبر است');
            return;
        }
    } else {
        // برای تاریخ واحد، تاریخ باید انتخاب شده باشد
        const dateValue = filters.value[field];

        if (!dateValue) {
            return;
        }

        // اعتبارسنجی فرمت تاریخ
        if (!isValidPersianDate(dateValue)) {
            console.warn('فرمت تاریخ نامعتبر است');
            return;
        }
    }

    handleSearch(field);
};



// Clear filter based on type
const clearFilter = (field, filterType) => {
    if (filterType === 'date') {
        filters.value[field] = '';
        filters.value[field + '_from'] = '';
        filters.value[field + '_to'] = '';
        filterConditions.value[field] = null;
    } else if (filterType === 'select') {
        filters.value[field] = [];
    } else {
        filters.value[field] = '';
        filterConditions.value[field] = null;
    }
    handleSearch(field);
};

// Clear all filters
const clearAllFilters = () => {
    // Clear all filters
    Object.keys(filters.value).forEach(field => {
        if (Array.isArray(filters.value[field])) {
            filters.value[field] = [];
        } else {
            filters.value[field] = '';
        }
    });

    // Clear all filter conditions
    Object.keys(filterConditions.value).forEach(field => {
        filterConditions.value[field] = null;
    });

    // Clear date range filters
    filterableColumns.value.forEach(col => {
        if (col.filterType === 'date') {
            filters.value[col.field + '_from'] = '';
            filters.value[col.field + '_to'] = '';
        }
    });

    // Trigger search to update results
    handleSearch();
};

</script>