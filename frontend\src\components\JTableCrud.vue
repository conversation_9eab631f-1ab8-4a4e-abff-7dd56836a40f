<template>
    <j-table v-model:selected="selected" :rows="list" :columns="columns" selection="single" row-key="id"
        separator="cell" v-model:filters="filters" @onFilter="onFilter">

        <template v-for="_, slot in $slots" v-slot:[slot]="props" :key="slot">
            <slot :name="slot" v-bind="props" :key="slot" />
        </template>
        <template v-slot:top>
            <j-btn flat dense icon="add" color="blue" @click="onAdd()" label="افزودن" />
            <q-space />
            <div class="space-x-2">
                <template v-if="selected.length > 0">
                    <slot name="select_bar" v-bind="{ load: getAll, selected: selected[0] }" />
                    <j-btn flat dense icon="edit" color="green" @click="onEdit()" />
                    <j-btn flat dense icon="delete" color="red" @click="onDelete()" />
                </template>
            </div>
        </template>
    </j-table>
    <j-dialog-bar v-model="add">
        <template #bar>
            <j-btn label="ذخیره" color="primary" @click="submit" />
        </template>
        <q-form ref="myForm" @submit="submitForm" class="space-y-2">
            <slot name="dialog" v-bind="{ formOption, load: getAll, form, url }" />
        </q-form>
    </j-dialog-bar>
</template>
<script>
import { tableApi } from '@/helpers';
import { ref } from 'vue';

export default {
    props: {
        url: {
            type: String,
            default: ''
        },
        columns: {
            type: Array,
            default: () => []
        },
    },
    setup(props, context) {
        const add = ref(false);
        const myForm = ref(null)

        const {
            list,
            columns,
            selected,
            onDelete,
            formOption,
            getAll,
            onFilter,
            filters,
            url,
            submitForm,
            form,
            show
        } = tableApi(props.url, props, context)

        columns.value = props.columns;


        const onAdd = () => {
            add.value = true;
            form.value = {}
        }
        const onEdit = async () => {
            form.value.id = selected.value[0].id;//Object.assign({}, selected.value[0]);
            show();
            add.value = true;
            selected.value = [];
        }

        const submit = () => {
            myForm.value.submit()
        }
        return {
            add,
            onAdd,
            onEdit,
            list,
            onDelete,
            selected,
            getAll,
            onFilter,
            formOption,
            form,
            filters,
            url,
            form,
            submitForm,
            submit,
            myForm,
        }
    },
}
</script>