<template>
    <!-- <div>{{ good }}</div> -->
    <!-- {{ good_attributes }} -->
    <div v-if="price > 0" class="text-center text-lg place-content-center grid text-bold flex gap-2 items-center">
        <detail-price :price_details="price_details" :price="price" @update:price="update"
            @update:price_details="update" />

    </div>
    <!-- <div>{{ form }}</div> -->
</template>

<script>
import { computeProductionOrder } from '@/Modules/Production/computeProductionFormula';
import { computed, watch, ref } from 'vue'
import detailPrice from './detailPrice.vue';
export default {
    components: {
        detailPrice
    },
    props: {
        form: {
            type: Object,
            default: () => { }
        },
        good_attributes: {
            type: Array,
            default: () => []
        },
        good: {
            type: Object,
            default: () => { }
        },
        price: {
            type: Number,
            default: 0
        },
        price_details: {
            type: Array,
            default: () => []
        },
    },
    setup(props, { emit }) {

        const list_prices = [
            {
                key: 'door_rapinig_mdf',
                condition: (form, good) => {
                    if (
                        //[8].includes(good.group_id)
                        // &&
                        form.attributes.typeMaterialDoor == 'mdf'
                    ) return true;
                    return false;
                },
                result: () => {
                    return 30000000;
                }
            },
            {
                key: 'door_rapinig_fomizeh',
                condition: (form, good) => {
                    if (
                        // [8].includes(good.group_id)
                        // && 
                        form.attributes.typeMaterialDoor == 'fomizeh'
                    ) return true;
                    return false;
                },
                result: () => {
                    return 41000000;
                }
            },
            {
                key: 'door_cnc_8',
                condition: (form, good) => {
                    if (
                        [16].includes(good.group_id)
                        && form.attributes.sheetCNCThickness == '8'
                    ) return true;
                    return false;
                },
                result: () => {
                    return 30000000;
                }
            },
            {
                key: 'door_cnc_5',
                condition: (form, good) => {
                    if (
                        [16].includes(good.group_id)
                        && form.attributes.sheetCNCThickness == '5'
                    ) return true;
                    return false;
                },
                result: () => {
                    return 27000000;
                }
            },
            {
                key: 'door_cnc_4',
                condition: (form, good) => {
                    if (
                        [16].includes(good.group_id)
                        && form.attributes.sheetCNCThickness == '4'
                    ) return true;
                    return false;
                },
                result: () => {
                    return 24000000;
                }
            },
            {
                key: 'door_pvc_4',
                condition: (form, good) => {
                    if (
                        //good.group.key == 'door'
                        // &&
                        form.attributes.typeCover == 'pvc_4'
                    ) return true;
                    return false;
                },
                result: () => {
                    return 8000000;
                }
            },
            {
                key: 'door_pvc_highglass',
                condition: (form, good) => {
                    if (
                        //good.group.key == 'door'
                        // &&
                        form.attributes.typeCover == 'pvc_highglass'
                    ) return true;
                    return false;
                },
                result: () => {
                    return 8000000;
                }
            }
        ];


        const door_list_prices = (form, good) => {

            if (
                [8, 18].includes(good.group_id)
                &&
                form.attributes.typeMaterialDoor == 'mdf'
            )
                return 36e6;

            if (
                [8, 18].includes(good.group_id)
                &&
                form.attributes.typeMaterialDoor == 'fomizeh'
            )
                return 49e6;

            if (
                [16, 20].includes(good.group_id)
                && form.attributes.sheetCNCThickness == '8'
                && form.attributes.typeMaterialDoor == 'mdf'

            )
                return 36e6;

            if (
                [16, 20].includes(good.group_id)
                && form.attributes.sheetCNCThickness == '8'
                && form.attributes.typeMaterialDoor == 'fomizeh'

            )
                return 40e6;

            if (
                [16, 20].includes(good.group_id)
                && form.attributes.sheetCNCThickness == '5'
            )
                return 32.4e6;

            if (
                [16, 20].includes(good.group_id)
                && form.attributes.sheetCNCThickness == '4'
            )
                return 28.8e6;

            return 0
        }

        const door_metas_prices = ({ form, good }) => {
            return good.metas.map(m => {
                return { label: m.label, price: m.value }
            })
        }

        const typeCoverPrice = ({ form, good, typeCover }) => {
            const add = 0; //form.attributes.pvcColor == 48 ? 2e6 : 0 // افرا روشن
            return add + (() => {
                if (
                    ['pvc_4'].includes(typeCover)
                )
                    return 10e6;

                if (
                    ['pvc_lux'].includes(typeCover)
                )
                    return 14e6;

                if (
                    ['pvc_highglass'].includes(typeCover)
                )
                    return 20e6;
                if (
                    ['none'].includes(typeCover)
                )
                    return -4e6;

                return 0
            })()
        }

        const door_cover_list_prices = (form, good) => {
            if (form.attributes.pvcColor) {

                return typeCoverPrice({
                    form, good, typeCover: props.good_attributes.find(f => f.key == 'pvcColor')
                        ?.items.find(f => f.key == form.attributes.pvcColor)
                        ?.metas?.find(f => f.key == 'typeCover')
                        ?.value
                })

            }

            return 0
        }

        // const door_cover_list_prices = (form, good) => {
        //     if (form.attributes.coverFrontDoor) {

        //         return typeCoverPrice({
        //             form, good, typeCover: props.good_attributes.find(f => f.key == 'coverFrontDoor')
        //                 ?.items.find(f => f.key == form.attributes.coverFrontDoor)
        //                 ?.metas?.find(f => f.key == 'typeCover')
        //                 ?.value
        //         }) / 2 + typeCoverPrice({
        //             form, good, typeCover: props.good_attributes.find(f => f.key == 'coverBackDoor')
        //                 ?.items.find(f => f.key == form.attributes.coverBackDoor)
        //                 ?.metas?.find(f => f.key == 'typeCover')
        //                 ?.value
        //         }) / 2;



        //     } else if (form.attributes.coverMiddleSheet) {

        //         return typeCoverPrice({
        //             form, good, typeCover: props.good_attributes.find(f => f.key == 'coverMiddleSheet')
        //                 ?.items.find(f => f.key == form.attributes.coverMiddleSheet)
        //                 ?.metas?.find(f => f.key == 'typeCover')
        //                 ?.value
        //         }) / 2 + typeCoverPrice({
        //             form, good, typeCover: props.good_attributes.find(f => f.key == 'coverBaghalBazoo')
        //                 ?.items.find(f => f.key == form.attributes.coverBaghalBazoo)
        //                 ?.metas?.find(f => f.key == 'typeCover')
        //                 ?.value
        //         }) / 2;
        //     }




        //     return 0
        // }

        const door_width_list_prices = (form, good) => {

            const doorWidth = form.attributes.doorWidth;
            const doorByCover = door_list_prices(form, good) + door_cover_list_prices(form, good);
            if ([16, 20].includes(good.group_id)) {

                if (
                    doorWidth >= 103 && doorWidth < 108
                    && form.attributes.typeMaterialDoor == 'mdf'
                )
                    return 700e4;


            }
            else if ([8, 18].includes(good.group_id)) {
                if (
                    doorWidth >= 96 && doorWidth < 101
                    && form.attributes.typeMaterialDoor == 'mdf'
                )
                    return doorByCover * 0.08;

                if (
                    doorWidth >= 101 && doorWidth < 106
                    && form.attributes.typeMaterialDoor == 'mdf'
                )
                    return doorByCover * 0.13;


                if (
                    doorWidth >= 84 && doorWidth < 92
                    && form.attributes.typeMaterialDoor == 'fomizeh'
                )
                    return doorByCover * 0.08;

                if (
                    doorWidth >= 92 && doorWidth < 101
                    && form.attributes.typeMaterialDoor == 'fomizeh'
                )
                    return doorByCover * 0.17;

                if (
                    doorWidth >= 101 && doorWidth < 106
                    && form.attributes.typeMaterialDoor == 'fomizeh'
                )
                    return doorByCover * 0.25;

            }



            return 0
        }

        const door_height_list_prices = (form, good) => {
            const doorHeight = form.attributes.doorHeight;
            //const doorByCover = door_list_prices(form, good) + door_cover_list_prices(form, good);

            if (
                doorHeight >= 221 && doorHeight < 226
                && form.attributes.typeMaterialDoor == 'fomizeh'
            )
                return 310e4;
            //return doorByCover * 0.05;

            if (
                doorHeight >= 219 && doorHeight < 226
                && form.attributes.typeMaterialDoor == 'mdf'
            )
                return 230e4;
            //return doorByCover * 0.10;


            if (
                doorHeight >= 226
                && form.attributes.typeMaterialDoor == 'fomizeh'
            )
                return 500e4;
            //return doorByCover * 0.05;

            if (
                doorHeight >= 226
                && form.attributes.typeMaterialDoor == 'mdf'
            )
                return 385e4;
            //return doorByCover * 0.10;


            // if (
            //     doorHeight >= 229 && doorHeight <= 232
            //     //&& form.attributes.typeMaterialDoor == 'mdf'
            // )
            //     return doorByCover * 0.15;




            return 0
        }

        const door_labeh_list_prices = (form, good) => {
            switch (form.attributes.typeEdge) {
                case '9':
                    return -2e6;
                case '10':
                    return 3.7e6;
                case '11':
                    return 2.5e6;
            }


            return 0
        }



        const frame_list_prices = (form, good) => {

            if (
                //[8].includes(good.group_id)
                // &&
                form.attributes.typeMaterialFrame == 'mdf'

            )
                return 24e6;

            if (
                // [8].includes(good.group_id)
                // && 
                form.attributes.typeMaterialFrame == 'fomizeh'
            )
                return 36e6;

            return 0

        }

        const frame_cover_list_prices = (form, good) => {

            const typeCover = props.good_attributes.find(f => f.key == 'pvcColor')
                ?.items.find(f => f.key == form.attributes.pvcColor)
                ?.metas?.find(f => f.key == 'typeCover')
                ?.value

            // const typeCover = props.good_attributes.find(f => f.key == 'coverFrame')
            //     ?.items.find(f => f.key == form.attributes.coverFrame)
            //     ?.metas?.find(f => f.key == 'typeCover')
            //     ?.value
            const add = 0 ; //form.attributes.pvcColor == 48 ? 2e6 : 0 // افراروشن
            return add + (() => {
                if (
                    ['pvc_4'].includes(typeCover)
                )
                    return 5e6;

                if (
                    ['pvc_lux'].includes(typeCover)
                )
                    return 7e6;

                if (
                    ['pvc_highglass'].includes(typeCover)
                )
                    return 10e6;

                if (
                    ['none'].includes(typeCover)
                )
                    return -2e6;

                return 0
            })()

        }

        const frame_width_floor_list_prices = (form, good) => {
            const typeCover = props.good_attributes.find(f => f.key == 'pvcColor')
                ?.items.find(f => f.key == form.attributes.pvcColor)
                ?.metas?.find(f => f.key == 'typeCover')
                ?.value

            const frameByCover = (() => {
                if (['pvc_highglass', 'pvc_lux'].includes(typeCover))
                    return frame_list_prices(form, good);
                else
                    return frame_list_prices(form, good) + frame_cover_list_prices(form, good)
            })()

            if (form.attributes.widthFloor == '17')
                return Math.round(frameByCover * 4 / 13 / 10000) * 10000
            return 0;
        }


        const frame_threshold_list_prices = (form, good) => {
            const frameByCover = frame_list_prices(form, good) + frame_cover_list_prices(form, good) + frame_width_floor_list_prices(form, good);
            if (form.attributes.hasThreshold)
                return frameByCover * 0.2;
            return 0;
        }



        const frame_width_list_prices = (form, good) => {
            const frameWidth = form.attributes.frameWidth;
            const frameByCover = frame_list_prices(form, good) + frame_cover_list_prices(form, good) + frame_width_floor_list_prices(form, good) + frame_threshold_list_prices(form, good);

            if (
                frameWidth >= 102 && frameWidth < 106
            )
                return frameByCover * 0.1;


            if (
                frameWidth >= 106 && frameWidth < 116
            )
                return frameByCover * 0.2;

            if (
                frameWidth >= 116 && frameWidth < 136
            )
                return frameByCover * 0.25;

            if (
                frameWidth >= 136 && frameWidth < 200
            )
                return frameByCover * 0.4;

            return 0
        }

        const frame_height_list_prices = (form, good) => {
            const frameHeight = form.attributes.frameHeight;
            //const frameByCover = frame_list_prices(form, good) + frame_cover_list_prices(form, good);

            if (
                frameHeight >= 226 && frameHeight < 231
                && form.attributes.typeMaterialFrame == 'fomizeh'
            )
                return 230e4;

            if (
                frameHeight >= 224 && frameHeight < 231
                && form.attributes.typeMaterialFrame == 'mdf'
            )
                return 150e4;

            if (
                frameHeight >= 230
                && form.attributes.typeMaterialFrame == 'fomizeh'
            )
                return 385e4;

            if (
                frameHeight >= 230
                && form.attributes.typeMaterialFrame == 'mdf'
            )
                return 250e4;


            return 0
        }

        const rokoob_dakheli_list_prices = (form, good) => {
            if (!form.attributes.typeMaterialRokoob)
                return 0

            const prices = {
                mdf: {
                    french: 12e6,
                    mexic: 14.4e6,
                },
                fomizeh: {
                    french: 16.8e6,
                    mexic: 22.8e6,
                }
            }

            if (form.attributes.typeDakheli == 'french' || form.attributes.typeDakheli == 'mexic')
                return prices[form.attributes.typeMaterialRokoob][form.attributes.typeDakheli];

            else if (form.attributes.typeDakheli == 'none_standard') {
                if (form.attributes.widthDakheli == 9.5)
                    return prices[form.attributes.typeMaterialRokoob].french;
                else if (form.attributes.widthDakheli == 10.5)
                    return prices[form.attributes.typeMaterialRokoob].mexic;
                else if (form.attributes.widthDakheli < 9.5 || (form.attributes.widthDakheli > 9.5 && form.attributes.widthDakheli < 10.5))
                    return Math.round(prices[form.attributes.typeMaterialRokoob].french * 1.4 / 1e5) * 1e5;

                else if (form.attributes.widthDakheli > 10.5 && form.attributes.widthDakheli < 16)
                    return Math.round(prices[form.attributes.typeMaterialRokoob].mexic * 1.4 / 1e5) * 1e5;

                else if (form.attributes.widthDakheli >= 16 && form.attributes.widthDakheli <= 20)
                    return Math.round(prices[form.attributes.typeMaterialRokoob].mexic * 1.8 / 1e5) * 1e5;
            }

            return 0
        }

        const rokoob_parvaz_list_prices = (form, good) => {
            if (!form.attributes.typeMaterialRokoob)
                return 0
            const prices = {
                mdf: {
                    french: 12e6,
                    mexic: 14.4e6,
                },
                fomizeh: {
                    french: 16.8e6,
                    mexic: 22.8e6,
                }
            }
            if (form.attributes.typeParvaz == 'french' || form.attributes.typeParvaz == 'mexic')
                return prices[form.attributes.typeMaterialRokoob][form.attributes.typeParvaz];
            else if (form.attributes.typeParvaz == 'none_standard') {
                if (form.attributes.widthParvaz == 7)
                    return prices[form.attributes.typeMaterialRokoob].french;
                else if (form.attributes.widthParvaz == 10.5)
                    return prices[form.attributes.typeMaterialRokoob].mexic;
                else if (form.attributes.widthParvaz < 7 || (form.attributes.widthParvaz > 7 && form.attributes.widthParvaz < 10.5))
                    return Math.round(prices[form.attributes.typeMaterialRokoob].french * 1.35 / 1e5) * 1e5;
            }
            return 0
        }

        const rokoob_cover_list_prices = (form, good) => {
            const typeCover = props.good_attributes.find(f => f.key == 'pvcColor')
                ?.items.find(f => f.key == form.attributes.pvcColor)
                ?.metas?.find(f => f.key == 'typeCover')
                ?.value

            //    const typeCover = props.good_attributes.find(f => f.key == 'coverRokoob')
            //         ?.items.find(f => f.key == form.attributes.coverRokoob)
            //         ?.metas?.find(f => f.key == 'typeCover')
            //         ?.value

            const add = 0;//form.attributes.pvcColor == 48 ? 1e6 : 0 // افراروشن
            return add + (() => {
                if (
                    ['pvc_4'].includes(typeCover)
                )
                    return 2.5e6;

                if (
                    ['pvc_lux'].includes(typeCover)
                )
                    return 4e6;

                if (
                    ['pvc_highglass'].includes(typeCover)
                )
                    return 5e6;

                if (
                    ['none'].includes(typeCover)
                )
                    return -1e6;

                return 0

            })()

        }

        const rokoob_width_list_prices = ({ form, good, resComputeProductionOrder, rokoobByCover }) => {
            if (resComputeProductionOrder.attributes.countParvaz + resComputeProductionOrder.attributes.countDakheli / 5 > 1)
                return (resComputeProductionOrder.attributes.countParvaz + resComputeProductionOrder.attributes.countDakheli - 5) * rokoobByCover / 5

            return 0
        }


        const mokammel_list_prices = (form, good) => {
            //console.log("mokammel")

            const mokammel_price = (() => {
                const prices = {
                    mdf: 6e6,
                    fomizeh: 10e6,
                }
                return prices[form.attributes.typeMaterialFrame ?? form.attributes.typeMaterial] ?? 0;
            })();

            const mokammel_by_cover_price = (() => {
                return (() => {

                    const prices_color = {
                        pvc_4: 1.25e6,
                        pvc_lux: 1.5e6,
                        none: -50e4,
                    }

                    const typeCover = props.good_attributes.find(f => f.key == 'pvcColor')
                        ?.items.find(f => f.key == form.attributes.pvcColor)
                        ?.metas?.find(f => f.key == 'typeCover')
                        ?.value

                    if (
                        ['pvc_4', 'pvc_lux', 'none'].includes(typeCover)
                    )
                        return prices_color[typeCover];

                    if (
                        ['pvc_highglass'].includes(typeCover)
                    )
                        return 2.5e6;

                    return 0;
                })() + mokammel_price;
            })();

            return (() => {

                if (form.attributes.mokammelWidth <= 7)
                    return mokammel_by_cover_price;

                else if (form.attributes.mokammelWidth > 7 && form.attributes.mokammelWidth <= 9)
                    return mokammel_by_cover_price * 1.35

                else if (form.attributes.mokammelWidth > 9 && form.attributes.mokammelWidth <= 11)
                    return mokammel_by_cover_price * 1.70

                else if (form.attributes.mokammelWidth > 11 && form.attributes.mokammelWidth <= 16)
                    return mokammel_by_cover_price * 1.85

                else if (form.attributes.mokammelWidth > 16 && form.attributes.mokammelWidth <= 20)
                    return mokammel_by_cover_price * 2

                return 0

            })() / 2.5 * (form.attributes.mokammelCount ?? 0)
        }



        // const price = ref(props.price ?? 0)
        const price_details = ref([])

        const computePrice = (form, good) => {
            // console.log('compute price')
            price_details.value = []
            const resComputeProductionOrder = computeProductionOrder(form, good);

            let zarib = 1;
            if (
                form.attributes.doorWidth >= 108 && form.attributes.doorWidth < 127
            )
                zarib = 1.5;

            if (
                form.attributes.doorWidth >= 127
            )
                zarib = 2;


            if (['door'].includes(good.group.key)) {
                const door_price = door_list_prices(form, good);
                const door_cover_price = door_cover_list_prices(form, good);
                const door_width_price = door_width_list_prices(form, good);
                const door_height_price = door_height_list_prices(form, good);
                const door_labeh_price = door_labeh_list_prices(form, good);


                // price.value += zarib * door_price;
                price_details.value.push({ label: 'قیمت درب', price: zarib * door_price })

                //price.value += zarib * door_cover_price;
                price_details.value.push({ label: (door_cover_price > 0 ? 'افزایش' : 'کاهش') + ' قیمت روکش درب', price: zarib * door_cover_price })

                //price.value += zarib * door_width_price;
                price_details.value.push({ label: 'افزایش قیمت عرض درب', price: zarib * door_width_price })

                //price.value += zarib * door_height_price;
                price_details.value.push({ label: 'افزایش قیمت ارتفاع درب', price: zarib * door_height_price })

                //price.value += zarib * door_labeh_price;
                price_details.value.push({ label: 'افزایش قیمت لبه درب', price: zarib * door_labeh_price })
            }






            if ((['door'].includes(good.group.key) && form.attributes.hasFrame) || [10].includes(good.group_id)) {
                const frame_price = frame_list_prices(form, good);
                const frame_cover_price = frame_cover_list_prices(form, good);
                const frame_width_price = frame_width_list_prices(form, good);
                const frame_height_price = frame_height_list_prices(form, good);
                const frame_width_floor_price = frame_width_floor_list_prices(form, good);
                const frame_threshold_price = frame_threshold_list_prices(form, good);

                // price.value += frame_price;
                price_details.value.push({ label: 'قیمت چهارچوب', price: frame_price })
                // price.value += frame_cover_price;
                price_details.value.push({ label: (frame_cover_price > 0 ? 'افزایش' : 'کاهش') + ' قیمت روکش چهارچوب', price: frame_cover_price })
                // price.value += frame_width_price;
                price_details.value.push({ label: 'افزایش قیمت کف 17 چهارچوب', price: frame_width_floor_price })

                price_details.value.push({ label: 'افزایش قیمت آستانه چهارچوب', price: frame_threshold_price })

                price_details.value.push({ label: 'افزایش قیمت عرض چهارچوب', price: frame_width_price })
                // price.value += frame_height_price;
                price_details.value.push({ label: 'افزایش قیمت ارتفاع چهارچوب', price: frame_height_price })


            }


            if (form.attributes.hasMokammel || [15].includes(good.group_id)) {
                price_details.value.push({ label: 'قیمت مکمل', price: mokammel_list_prices(form, good) })
            }

            if ((['door'].includes(good.group.key) && form.attributes.hasRokoob) || [12, 21, 22].includes(good.group_id)) {
                const rokoob_price = (rokoob_dakheli_list_prices(form, good) + rokoob_parvaz_list_prices(form, good)) / 2;
                const rokoob_cover_price = rokoob_cover_list_prices(form, good);
                // const rokoob_width_price = rokoob_width_list_prices({ form, good, resComputeProductionOrder, rokoobByCover: rokoob_price + rokoob_cover_price });
                //console.log(rokoob_cover_price);
                // price.value += rokoob_price;
                // price_details.value.push({ label: 'قیمت روکوب', price: rokoob_price })
                // price.value += rokoob_cover_price;
                // price_details.value.push({ label: (rokoob_cover_price > 0 ? 'افزایش' : 'کاهش') + ' قیمت روکش روکوب', price: rokoob_cover_price })
                //price.value += rokoob_width_price;
                // price_details.value.push({ label: 'افزایش قیمت عرض روکوب', price: rokoob_width_price })
                //price.value += rokoob_height_list_prices(form, good);

                //console.log('ffffffffffff')
                if (form.attributes.hasRokoob) {
                    price_details.value.push({ label: 'قیمت روکوب', price: rokoob_price })
                    price_details.value.push({ label: (rokoob_cover_price > 0 ? 'افزایش' : 'کاهش') + ' قیمت روکش روکوب', price: rokoob_cover_price })

                }
                else {
                    if (form.attributes.countParvaz > 0) { // پرواز 
                        price_details.value.push({ label: 'قیمت پرواز', price: rokoob_parvaz_list_prices(form, good) / 5 * form.attributes.countParvaz });
                        price_details.value.push({ label: (rokoob_cover_price > 0 ? 'افزایش' : 'کاهش') + ' قیمت روکش پرواز', price: rokoob_cover_price / 5 * form.attributes.countParvaz })

                    }

                    if (form.attributes.countDakheli > 0) { // داخلی
                        price_details.value.push({ label: 'قیمت داخلی', price: rokoob_dakheli_list_prices(form, good) / 5 * form.attributes.countDakheli });
                        price_details.value.push({ label: (rokoob_cover_price > 0 ? 'افزایش' : 'کاهش') + ' قیمت روکش داخلی', price: rokoob_cover_price / 5 * form.attributes.countDakheli })

                    }
                }

                // price_details.value.push({ label: 'افزایش قیمت عرض روکوب', price: rokoob_width_price })


            }

            // if ([21].includes(good.group_id)) { // پرواز 
            //     const rokoob_cover_price = rokoob_cover_list_prices(form, good);
            //     price_details.value.push({ label: 'قیمت پرواز', price: rokoob_parvaz_list_prices(form, good) / 5 * form.attributes.countParvaz });
            //     price_details.value.push({ label: (rokoob_cover_price > 0 ? 'افزایش' : 'کاهش') + ' قیمت روکش روکوب', price: rokoob_cover_price / 5 * form.attributes.countParvaz })

            // }

            // if ([22].includes(good.group_id)) { // داخلی
            //     const rokoob_cover_price = rokoob_cover_list_prices(form, good);
            //     price_details.value.push({ label: 'قیمت داخلی', price: rokoob_dakheli_list_prices(form, good) / 5 * form.attributes.countDakheli });
            //     price_details.value.push({ label: (rokoob_cover_price > 0 ? 'افزایش' : 'کاهش') + ' قیمت روکش روکوب', price: rokoob_cover_price / 5 * form.attributes.countDakheli })

            // }

            const temp = door_metas_prices({ form, good })
            if (temp.length > 0)
                price_details.value.push(...temp)

            const temp_price = price_details.value.reduce((s, x) => s + x.price * 1, 0);
            //if (temp_price > 0)
            //props.price = temp_price
            // list_prices.forEach(list_price => {
            //     if (list_price.condition(form, good)) price.value += list_price.result()
            // })
            if (temp_price > 0) {
                // console.log('343432432423432')
                emit('update:price', temp_price);
            }
            emit('update:price_details', price_details.value.filter(f => f.price != 0));

        }

        const oldFormAttribute = ref('');

        watch(() => ({ form: props.form, good: props.good }), ({ form, good }) => {

            if (oldFormAttribute.value && oldFormAttribute.value !== JSON.stringify(form.attributes)) {
                if (Object.keys(good).length > 0 && Object.keys(form).length > 0) {
                    //console.log(oldFormAttribute.value, JSON.stringify(form.attributes))
                    computePrice(form, good)
                }
            }
            oldFormAttribute.value = JSON.stringify(form.attributes);
        }, {
            deep: true
        })
        // const price = computed(()=>{
        //     console.log(props.form)
        //     return props.form.doorHeight
        // })
        return {
            update() {
                emit('update:price', props.price)
                emit('update:price_details', props.price_details)
            }
            // price,
            // price_details,
        }
    }
}
</script>