<template>
    <j-input v-model="form.full_name" label="نام و نام خانوادگی" error-field="full_name" dense />
    <j-input v-model="form.display_name" label="نام نمایشی" dense />
    <j-input v-model="form.mobile_number" label="شماره موبایل" mask="###########" hint="09#########" dense />
    <j-input v-model="form.address" label="آدرس" type="textarea" dense />

</template>
<script>
export default {
    props: {
        form: {
            type: Object,
            default: () => { }
        },
        formOption: {
            type: Object,
            default: () => { }
        },

    },
};
</script>