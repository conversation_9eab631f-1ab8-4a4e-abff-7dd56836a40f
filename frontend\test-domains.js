// Test script for domain configuration
import { getCurrentDomainType, DOMAIN_TYPES, getDomainConfig } from './src/config/domains.js';

console.log('Testing domain configuration...');

// Mock window.location for testing
global.window = {
    location: {
        host: 'panel.erp.test'
    }
};

console.log('Panel domain test:');
console.log('Domain type:', getCurrentDomainType());
console.log('Config:', getDomainConfig());

// Test CRM domain
global.window.location.host = 'crm.erp.test';
console.log('\nCRM domain test:');
console.log('Domain type:', getCurrentDomainType());
console.log('Config:', getDomainConfig());

console.log('\nDomain configuration test completed successfully!');
