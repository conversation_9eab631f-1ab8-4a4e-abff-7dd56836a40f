<?php

namespace App\Providers;

use App\Repositories\BaseRepository;
use App\Repositories\BaseRepositoryInterface;
use App\Repositories\ModelRepository;
use App\Repositories\ModelRepositoryInterface;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(BaseRepositoryInterface::class, BaseRepository::class);
        $this->app->bind(
            ModelRepositoryInterface::class,
            ModelRepository::class,
        );
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Route::macro('japiResource', function ($uri, $controller, array $options = []) {         
            //dd(explode('/', $uri)[1]);
            //$base = str_replace('-', '_', Str::singular(last(explode('.', end(explode('/', $uri))))));
            Route::get("{$uri}/create", "{$controller}@create")->name("{$uri}.create");
            //Route::get("{$uri}/{id}/edit", "{$controller}@edit")->name("{$uri}.edit");
            Route::post("{$uri}/delete", "{$controller}@delete")->name("{$uri}.delete");
            Route::get("{$uri}/search", "{$controller}@search")->name("{$uri}.search");
            Route::apiResource($uri, $controller, $options)->except(['destroy']);

        });
        Route::macro('jResource', function ($uri, $controller, array $options = []) {         
            //dd(explode('/', $uri)[1]);
            //$base = str_replace('-', '_', Str::singular(last(explode('.', end(explode('/', $uri))))));
            Route::post("{$uri}/delete", "{$controller}@delete")->name("{$uri}.delete");
            Route::get("{$uri}/search", "{$controller}@search")->name("{$uri}.search");
            Route::resource($uri, $controller, $options)->except(['destroy','show']);

        });
        Gate::before(function ($user, $ability) {
            return $user->is_super_admin ? true : null;
        });

    }
}
