import { print } from "@/helpers";
Array.prototype.chunk = function(groupsize){
    var sets = [], chunks, i = 0;
    chunks = Math.ceil(this.length / groupsize);

    while(i < chunks){
        sets[i] = this.splice(0, groupsize);
	i++;
    }
    return sets;
};

const itemToHtml = (m) => `
<div style="padding: 0.8cm;
/*width: 10.5cm;*/
height: 14.4cm;position:relative">
<input type="checkbox" name="select" style="
 width: 35px;
 height: 35px;
 position: absolute;
 top: 50%;
 right: 50%;
" class="noprint">
     <table style="width: 100%;text-align: center;height: 100%;" class="asd">
         <tbody>
             <tr style="height: 30px;">
                 <td><b>شماره</b></td>
                 <td><b>ایستگاه</b></td>
                 <td colspan="2"><b>دستگاه / ابزار</b></td>
                 <td colspan="2"><b>تعمیرکار</b></td>
             </tr>
             <tr style="height: 30px;">
                 <td>${m.id ?? ''}</td>
                 <td>${m.station?.name ?? ''}</td>
                 <td colspan="2">${m.machine?.name ?? ''}</td>
                 <td colspan="2"></td>
             </tr>
             <tr style="height: 30px;">
                 <td colspan="2"><b>تاریخ ثبت مشکل</b></td>
                 <td colspan="2"><b>تاریخ پیگیری مشکل</b></td>
                 <td colspan="2"><b>تاریخ حل مشکل</b></td>
             </tr>
             <tr style="height: 30px;">
                 <td colspan="2" style="width:1px">${m.created_at ?? ''}</td>
                 <td colspan="2"></td>
                 <td colspan="2"></td>
             </tr>
             <tr >
                 <td colspan="6" style="text-align: right; vertical-align: top;"><b>مشکلات :</b> ${m.problems.map(mm => mm.name).join(' , ') ?? ''}
                <div style="white-space: pre-line;"><b>شرح : </b>  ${m.description ?? ''}</div>
                 </td>
             </tr>
             <tr style="height: 100px;">
                 <td style="vertical-align: top;" colspan="3"><b>امضاء تعمیرکار</b></td>
                 <td style="vertical-align: top;" colspan="3"><b>امضاء ${m.created_at_activity[0]?.causer?.full_name}</b></td>
             </tr>
         </tbody>
     </table>
 </div>
 `;

export default (data) => {

    print(`
    


    <div class="grid grid-cols-2" id="items">
     ${
        data.map(itemToHtml).join('')
        }
    </div>


    <button class="print-btn noprint" id="print-btn"  >پرینت</button>


    <script>

   

    (function() {
        let elItems = null;
        document.getElementById("print-btn").addEventListener("click", function(){
            elItems = document.querySelector("#items").cloneNode(true);
            document.querySelectorAll("input:not(:checked)").forEach(a => {
                a.parentElement.remove();
            })
            window.print();
            document.querySelector("#items").remove();
            document.body.prepend(elItems);
        });
     
     })();

    </script>
    
    `, {
        autoPrint: false, style: `
    
      
        input:checked + table {
            background:#e4eeff;
        }
        .print-btn {
            position: sticky;
            bottom: 0;
            width: 100%;
            background: #e2e2e2;
            height: 50px;
            z-index: 1000;
            padding: 5px;
            text-align: center;
            font-size: 27px;
        }
        .print-btn:hover {
            background: #d2d2d2;
        }

        @media print {
            .noprint {
               visibility: hidden;
            }
            input:checked + table {
                background:none;
            }
         }
    
    ` });
}




