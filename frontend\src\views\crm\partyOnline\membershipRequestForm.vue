<template>
    <j-form class="grid grid-cols-1 sm:grid-cols-2 gap-x-5 gap-y-1 max-w-4xl mx-auto">
        <j-input label="نام و نام خانوادگی" v-model="form.full_name" required clearable>
            <template v-slot:prepend>
                <q-icon name="account_box" />
            </template>
        </j-input>
        <j-input label="کد ملی" v-model="form.national_code" clearable>
            <template v-slot:prepend>
                <q-icon name="badge" />
            </template>
        </j-input>
        <j-input label="نام فروشگاه / شرکت" v-model="form.second_name" clearable class="sm:col-span-2">
            <template v-slot:prepend>
                <q-icon name="apartment" />
            </template>
        </j-input>
        <j-field v-model="form.image" required class="sm:col-span-2">
            <template v-slot:control>
                <j-upload v-model:value="form.image" auto-upload accept="image/*" url="/api/upload-file" field-name="file"
                    label="تصویر سردرب فروشگاه" style="width: 100%" />
            </template>
        </j-field>
        <!-- <j-input label="شماره همراه" v-model="form.phone_number" mask="###########" hint="09#########" required clearable>
            <template v-slot:prepend>
                <q-icon name="phone_iphone" />
            </template>
        </j-input> -->
        <j-input label="شماره ثابت" v-model="form.landline_number" mask="###########" required clearable>
            <template v-slot:prepend>
                <q-icon name="phone" />
            </template>
        </j-input>
        <j-select v-model="form.province_id" :options="provinces" label="استان" autofocus option-label="name" required
            option-value="id" field="name" error-field="province_id" search-local clearable
            @update:model-value="form.city_id = null">
            <template v-slot:prepend>
                <q-icon name="location_on" />
            </template>
        </j-select>
        <j-select v-model="form.city_id" label="شهر" autofocus option-label="name" option-value="id" field="name" required
            error-field="city_id" search-local
            :options="provinces[provinces.findIndex(f => f.id == form.province_id)]?.cities" clearable>
            <template v-slot:prepend>
                <q-icon name="location_on" />
            </template>
        </j-select>
        <j-input label="آدرس کامل" v-model="form.address" type="textarea" class="sm:col-span-2 p-0" required clearable dense>
            <template v-slot:prepend>
                <q-icon name="location_on" />
            </template>
        </j-input>
        <j-field v-model="form.location" required class="sm:col-span-2">
            <template v-slot:control>
                <j-map v-model="form.location" />
            </template>
        </j-field>
        <slot />
    </j-form>
</template>
<script setup>
import { api } from '@/boot/axios';
import { ref } from 'vue'
const provinces = ref([]);
const { form } = defineProps({
    form: {
        type: Object,
        default: () => { }
    },
    formOption: {
        type: Object,
        default: () => { }
    },
})
api.get('/provinces/search').then(res => {
    provinces.value = res.result;
})

</script>
