<template>
    <!-- <j-input v-model="form.name" label="نام" error-field="name" dense /> -->
    <!-- <j-select-remote v-model="form.station_id" url="/production/station/search" label="ایستگاه" dense /> -->
    <j-select v-model="form.station_id" :options="stations" option-label="name" option-value="id" label="ایستگاه" dense
        @update:model-value="form.station_work_id = null;form.parents = []" />
    <j-select v-if="form.station_id" v-model="form.station_work_id"
        :options="stations.findIndex(f => f.id == form.station_id) >= 0 && stations[stations.findIndex(f => f.id == form.station_id)].works"
        option-label="name" option-value="id" label="کار" dense @update:model-value="form.parents = []" />
    <j-select v-model="form.parents" :options="parents.filter(f => f.station_work_id !== form.station_work_id)"
        :option-label="row => row.station_work ? row.station_work.name : ''" option-value="id" label="پیش نیازها" dense
        multiple />
    <!-- <j-select-remote v-model="form.parents" :url="`/production/instruction/${goodId}/items/search`"
        label="پیش نیازها" dense multiple /> -->

    <condition v-model:data="form.conditions" />
</template>
<script>
import condition from './condition.vue';
import { api } from '@/boot/axios';
import { ref } from 'vue';

export default {
    components: { condition },
    props: {
        form: {
            type: Object,
            default: () => { }
        },
        goodId: String,

    },
    setup(props) {
        const stations = ref([])
        const parents = ref([])
        api.get('/production/station/search', { params: { relations: ['works'] } }).then(res => {
            stations.value = res.result;
        })
        api.get(`/production/good/${props.goodId}/workInstructions/search`).then(res => {
            parents.value = res.result;
        })
        return {
            stations,
            parents,
        }
    }
};
</script>