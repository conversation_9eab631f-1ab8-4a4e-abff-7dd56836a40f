<template>
    <div class="q-gutter-sm">
        <q-tabs v-model="tab" dense active-color="white" active-bg-color="primary" indicator-color="primary" align="justify"
            narrow-indicator>
            <q-tab v-for="work_station, index in data" :name="work_station.station_work_name"
                :label="work_station.station_work_name" :key="index" class="rounded-xl" />
        </q-tabs>

        <q-tab-panels v-model="tab" animated>
            <q-tab-panel v-for="work_station, index in data" :name="work_station.station_work_name" :key="index"
                style="padding: 0" class="shadow-md">
                <Checklist :attributes="attributes" :url="url" @afterSave="getData()" v-bind="work_station" />
            </q-tab-panel>
        </q-tab-panels>

    </div>
</template> 

<script>
import { usePublicStore } from "@/stores";
import { api } from "@/boot/axios";
import TableForm from "./form.vue";
import { useRoute } from 'vue-router';
import { ref } from "vue";
import Checklist from "./checklist2.vue";
import { provide, onUnmounted, onMounted } from 'vue'
import machineView from "../machine/index.vue";
import { computeProductionOrderItem } from "../../computeProductionFormula";
export default {
    setup() {
        const route = useRoute();
        const url = '/production/station/' + route.params.id + '/new_production_checklist';


        const data = ref([])
        const attributes = ref([])
        const getData = () => {
            api.get(url).then(res => {
                data.value = res.data.map(m => {
                    m.items = computeProductionOrderItem(m.items)
                    return m;
                });
                if (!tab.value) tab.value = data.value[0].station_work_name;
                publicStore.titleWindow = res.station.name
            })
        }
        api.get('good/attribute').then(res => {
            attributes.value = res.data
            getData()
        })

        const publicStore = usePublicStore();

 
        onMounted(() => {
            console.log('onMounted')

            publicStore.toolbar_component = machineView
        })
        onUnmounted(() => {
            console.log('onUnmounted')
            delete publicStore.toolbar_component;
        })
        const tab = ref('')
        provide('afterSave', {
            getData
        })
        return {
            tab,
            attributes,
            data,
            url,
            getData,
        };
    },
    components: { TableForm, Checklist },
};
</script>
