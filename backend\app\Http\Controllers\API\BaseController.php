<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;

class BaseController extends Controller
{

    public function handleResponse($result = null, $msg = null, $additional = null)
    {
        $res = [
            'ok' => true,
            // 'result'    => $result,
        ];
        if ($result) {
            $res['result'] = $result;
        }

        if ($msg) {
            $res['message'] = $msg;
        }

        if ($additional) {
            $res = array_merge($res, $additional);
        }

        return response()->json($res, 200);
    }

    public function handleError($error, $errorMsg = [], $code = 401)
    {
        $res = [
            'ok' => false,
            'message' => $error,
        ];
        if (!empty($errorMsg)) {
            $res['result'] = $errorMsg;
        }
        return response()->json($res, $code);
    }
}
