<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Good\Entities\Attribute;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create(Attribute::getTableName(), function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('key')->nullable();
            $table->string('group_name')->nullable();
            $table->foreignIdFor(Attribute::class, 'parent_id')->nullable()->constrained(Attribute::getTableName())->cascadeOnUpdate();
            //$table->enum('type', collect(Attribute::types)->pluck('value')->toArray());
            $table->string('type');
            $table->boolean('showing')->default(true);
            $table->tinyInteger('sort')->default(0);
            $table->boolean('required')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists(Attribute::getTableName());
    }

};
