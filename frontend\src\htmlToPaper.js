function addStyles(win, styles) {
    for (let index = 0; index < styles.length; index++) {
        let link = win.document.createElement("link");
        link.setAttribute("rel", "stylesheet");
        link.setAttribute("type", "text/css");
        link.setAttribute("href", styles[index]);
        win.document.getElementsByTagName("head")[0].appendChild(link);
    }
}
function addStyle(win, style_code) {
    let style = win.document.createElement("style");
    style.innerText = style_code;
    win.document.getElementsByTagName("head")[0].appendChild(style);
}

const VueHtmlToPaper = (el, localOptions = {}, cb = () => true) => {
    localOptions.autoPrint = localOptions.autoPrint == undefined ? true : localOptions.autoPrint;
    let defaultName = "_blank",
        defaultSpecs = ["width=" + screen.availWidth, "height=" + screen.availHeight, "fullscreen=yes", "titlebar=yes", "scrollbars=yes"],
        defaultReplace = true,
        defaultStyles = ["/"],
        defaultStyle = "";
    let {
        name = defaultName,
        specs = defaultSpecs,
        replace = defaultReplace,
        styles = defaultStyles,
        style = defaultStyle,
    } = {};

    // If has localOptions
    // TODO: improve logic
    if (!!localOptions) {
        if (localOptions.name) name = localOptions.name;
        if (localOptions.specs) specs = localOptions.specs;
        if (localOptions.replace) replace = localOptions.replace;
        if (localOptions.styles) styles = localOptions.styles;
        if (localOptions.style) style = localOptions.style;
    }

    specs = !!specs.length ? specs.join(",") : "";

    const element = window.document.getElementById(el);
    // console.log(element)
    // if (!element) {
    //     alert(`Element to print #${el} not found!`);
    //     return;
    // }

    const url = "";
    const win = window.open(url, name, specs, replace);
    let forWrite = '';
    //win.document.write("<html><head>");
    forWrite += "<html><head>";
    for (let style of document.head.querySelectorAll(
        '[rel="stylesheet"],style'
    )) {
        forWrite += style.outerHTML;
    }

    forWrite += '</head><body dir="rtl">';
    forWrite +=  element ? element.innerHTML : el;
    forWrite += '</body></html>';
    win.document.write(forWrite);

    addStyles(win, styles);
    addStyle(
        win,
        `.print-hidden {
          display: none;
      }`
    );
    addStyle(win, style);
    win.document.close();
    if (localOptions.autoPrint)
        win.addEventListener(
            "load",
            () => {
                win.focus();
                win.print();
                // win.close();
            },
            false
        );
    return true;
};

export default VueHtmlToPaper;
