<?php

namespace Database\Seeders;

use App\Models\City;
use App\Models\Province;
use Illuminate\Database\Seeder;

class CitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $provinces = json_decode(file_get_contents(__DIR__ . '\provinces.json'), true);
        foreach ($provinces as $province) {
            $model = Province::create(['name' => $province['name'], 'slug' => $province['slug']]);
            foreach ($province['cities'] as $city) {
                City::create(['province_id' => $model['id'], 'name' => $city['name'], 'slug' => $city['slug']]);
            }

        }
    }
}
