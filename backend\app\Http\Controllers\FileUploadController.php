<?php

namespace App\Http\Controllers;

use App\Models\File;
use Hash;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class FileUploadController extends Controller
{
    public function fileUpload(Request $req)
    {
        $req->validate([
            'file' => 'required'
        ]);
   
        $fileModel = new File;
        if ($req->file()) {
            $filePath = '/' . Storage::disk('public')->putFile('uploads', $req->file('file'));

            $fileModel->name = $req->file('file')->hashName();

            $fileModel->file_path = $filePath;

            $fileModel->save();

            return  response()->json(['id' => $fileModel->id, 'path' => $fileModel->file_path]);
        }
    }
}
