<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Good\Entities\Attribute;
use Modules\Good\Entities\Good;
use Modules\Good\Entities\Group;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create($this->table(), function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Good::class)->nullable()->constrained(Good::getTableName());
            $table->foreignIdFor(Group::class)->nullable()->constrained(Group::getTableName());
            $table->json('if');
            $table->json('then');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists($this->table());
    }

    public function table()
    {
        $prefix = config('good.prefix');
        return ($prefix ? $prefix . '__' : '') . 'attribute_conditions';
    }
};
