<?php

namespace Modules\Good\Http\Controllers;

use App\Http\Controllers\API\BaseController;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Good\Entities\Category;
use Modules\Good\Entities\Good;
use Modules\Good\Http\Requests\GoodRequest;

class CategoryGoodController extends BaseController
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index(Category $category)
    {
        return JsonResource::collection(Good::query()->get())->additional([
            'category' => $category,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Category $category, GoodRequest $request)
    {
        $data = Good::create(array_merge($request->all(), [
            'category_id' => $category->id,
        ]));
        return $this->handleResponse($data, trans('request.done'));
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(Category $category, Good $good)
    {
        return $this->handleResponse($good);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(GoodRequest $request, Category $category, Good $good)
    {
        $good->update(array_merge($request->all(), [
            'category_id' => $category->id,
        ]));
        return $this->handleResponse($good, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy(Category $category, Good $good)
    {
        $good->delete();
    }
}
