<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Validation\Rule;

class BaseRepositoryControllerw extends BaseController
{
    protected $model = null;

    public function __construct()
    {
        if (!$this->model) {
            throw new \Exception('Model not defined in controller.');
        }
    }

    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return JsonResource::collection($this->model::query()->filter()->jpaginate());
    }

    public function show($id): JsonResponse
    {
        $item = $this->model::find($id);
        return $item ? $this->handleResponse($item) : $this->handleError($item, trans('request.not_found'), 404);

    }

    public function storeData(Request $request): JsonResponse
    {
        // در متد پایه قوانین اعتبارسنجی نداریم، باید در کلاس فرزند تعریف شود
        $item = $this->model::create($request->all());
        return $this->handleResponse($item, trans('request.done'));

    }

    public function update(Request $request, $id)
    {
        // در متد پایه قوانین اعتبارسنجی نداریم، باید در کلاس فرزند تعریف شود
        $item = $this->model->update($id, $request->all());
        return $item ? $this->handleResponse($item, trans('request.done')) : $this->handleError($item, trans('request.not_found'), 404);

    }

    public function destroy($id): JsonResponse
    {
        $deleted = $this->model::delete($id);
        return $deleted ? $this->handleResponse(trans('Deleted successfully')) : $this->handleError(trans('request.not_found'), 404);
    }

    public function delete(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => ['integer', Rule::exists($this->model, 'id')],
        ], [
            //'ids.*.exists' => 'کاربری با شناسه :input یافت نشد یا امکان حذف ندارد.',
        ]);

        // دریافت شناسه‌ها از ورودی
        $ids = $validated['ids'];

        // حذف مدل‌ها با شناسه‌های مشخص شده
        try {
            $this->model::destroy($ids);
        } catch (\Exception $e) {
            // در صورت بروز خطا، می‌توانید یک پاسخ مناسب ارسال کنید
            return $this->handleError($e, 'امکان حذف وجود ندارد', 500);
        }

        // ارسال پاسخ موفقیت‌آمیز
        return $this->handleResponse(null, trans('request.done'));
        $deleted = $this->model::delete($id);
        return $deleted ? response()->json(['message' => 'Deleted successfully']) : response()->json(['message' => 'Not found'], 404);
    }

    public function create()
    {
        return $this->handleResponse([
            'formOptions' => [
            ],
        ]);
    }

    public function edit($id)
    {
        $model = $this->model::find($id);
        return $this->handleResponse([
            'form' => $model,
            'formOptions' => [
            ],
        ]);
    }

    public function search()
    {
        $data = $this->model::query()->get();
        return $this->handleResponse($data);
    }
}
