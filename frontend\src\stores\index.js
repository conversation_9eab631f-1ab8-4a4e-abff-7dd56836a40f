// Export stores based on current domain
const currentHost = typeof window !== 'undefined' ? window.location.host : '';
const panelDomain = import.meta.env.VITE_PANEL_DOMAIN;
const crmDomain = import.meta.env.VITE_CRM_DOMAIN;

if (currentHost === panelDomain || currentHost === 'localhost:5173' || currentHost.includes('localhost')) {
    // Panel stores
    export * from './panel';
} else if (currentHost === crmDomain) {
    // CRM stores
    export * from './crm';
} else {
    // Default to panel stores for development
    export * from './panel';
}
