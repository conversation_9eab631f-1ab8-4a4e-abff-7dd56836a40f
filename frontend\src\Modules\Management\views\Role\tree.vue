<template>
    <q-tree :nodes="nodes" label-key="label" node-key="id" tick-strategy="strict" v-model:expanded="expanded"
        v-model:ticked="tickedNodes" />
</template>
<script setup>
import { defineProps, ref, watch } from 'vue';

const props = defineProps({
    nodes: Array,
    ticked: Array,
})
const emit = defineEmits(['update:ticked'])




const groupByNestedKey = (items) => {

    const createNode = (key, parent = null) => ({
        name: key,
        children: [],
        ...(parent ? { parent } : {}),
    });

    const root = createNode(null); // گره ریشه
    const nodesMap = new Map();
    items.forEach(item => {
        const keys = item.name.split('.');
        let currentParent = root;
        let path = ''; // مسیر کامل برای هر گره

        keys.forEach((key, index) => {
            path = path ? `${path}.${key}` : key; // ساخت مسیر کامل
            if (!nodesMap.has(path)) {
                const newNode = createNode(key, currentParent);
                nodesMap.set(path, newNode);
                currentParent.children.push(newNode);
            }
            currentParent = nodesMap.get(path);
        });

        // اضافه کردن داده نهایی به آخرین گره
        currentParent.data = item;
    });
    return root.children;
}
const tickedNodes = ref(props.ticked ?? []);
const expanded = ref(props.ticked ?? [])

const nodes = ref(
    groupByNestedKey(props.nodes ?? [])
        .map(mapTreeNodes)
)


watch(() => props.ticked, newTick => {
    tickedNodes.value = newTick
    expanded.value = newTick
})

watch(() => props.nodes, newNodes => {
    // console.error('new nodes' , newNodes)
    nodes.value = groupByNestedKey(newNodes ?? [])
        .map(mapTreeNodes)
    // expanded.value = newTick
})






// مپ داده‌ها به فرمت درختی برای q-tree
function mapTreeNodes(node) {
    //console.error(node.data.noTick)

    return {
        label: node.data?.label ?? node.name,
        name: node.name,
        id: node.data?.id ?? node.name,
        selectable: true,
        children: (node.children || []).map(mapTreeNodes),
        parent: node.parent,
        noTick: node.data.noTick,
        icon: node.data.icon,
        // disabled: node.data.disabled,
        data: node.data // داده اضافی برای شناسایی دقیق‌تر
    };
}


watch(() => props.nodes, (newNodes) => {
    nodes.value = groupByNestedKey(newNodes ?? []).map(mapTreeNodes);
})
watch(() => tickedNodes.value, (newVal, oldVal) => {
    const check = newVal.filter(item => !oldVal.includes(item))[0] ?? null;
    const uncheck = oldVal.filter(item => !newVal.includes(item))[0] ?? null;

    if (check) {
        updateCheckNestedParent(nodes.value.findNested('id', check))
    }

    if (uncheck) {
        updateUncheckNestedChild(nodes.value.findNested('id', uncheck))
    }
    emit('update:ticked', tickedNodes.value)
})

function updateCheckNestedParent(item) {
    const id = item.parent?.data?.id
    if (id && !tickedNodes.value.find(f => f == id)) {
        tickedNodes.value.push(id)
        updateCheckNestedParent(item.parent)
    }
    //console.log(item)

}
function updateUncheckNestedChild(item) {
    item.children.forEach(element => {
        const id = element?.data?.id
        if (id) {
            const find = tickedNodes.value.findIndex(f => f == id)
            if (find >= 0) tickedNodes.value.splice(find, 1)
        }
        updateUncheckNestedChild(element)

    });

    // console.log(item)

}

</script>