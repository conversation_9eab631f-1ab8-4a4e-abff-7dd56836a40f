<?php

namespace Modules\Production\Entities;

use Modules\Good\Entities\Attribute;

class InstructionItemCondition extends BModel
{
    protected $fillable = [
        'instruction_item_id',
        'attribute_id',
        'condition',
        'items',
    ];
    protected $casts = [
        'items' => 'json',
        'condition' => 'boolean'
    ];

    protected $appends = ['attribute_key'];


    public function getAttributeKeyAttribute()
    {
        return $this->attribute?->key;
    }

    public function attribute()
    {
        return $this->belongsTo(Attribute::class);
    }
}
