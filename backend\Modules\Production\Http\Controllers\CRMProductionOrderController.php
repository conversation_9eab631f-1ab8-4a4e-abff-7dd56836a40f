<?php

namespace Modules\Production\Http\Controllers;

use App\Http\Controllers\API\BaseController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Modules\Good\Entities\Attribute;
use Modules\Production\Entities\ProductionOrder;

class CRMProductionOrderController extends BaseController
{
    public function index()
    {
        $user = Auth::user();
        return JsonResource::collection(ProductionOrder::query()->where('party_id', $user->id)->filter()->jpaginate());
    }
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            //'delivery_date' => 'required',
            //'party_id' => 'required',
            'customer_name' => 'required',
            'items' => 'required',
        ], [
            'items.required' => 'اقلام سفارش الزامی است!',
        ]);
        $data = $request->all();
        $data['status'] = ProductionOrder::DRAFT;
        $data['is_created_by_customer'] = true;
        $data['party_id'] = auth()->user()->id;
        $crmProductionOrder = ProductionOrder::create($data);
        $items = collect($data['items'])->map(function ($item) {
            return [...$item, 'good_id' => $item['good']['id'], 'total_price' => $item['count'] * $item['price']];
        })->toArray();
        $crmProductionOrder->items()->sync($items);
        $crmProductionOrder->items = $crmProductionOrder->items()->with('good')->get();

        $this->changeStatus($crmProductionOrder, $crmProductionOrder->status);

        return $this->handleResponse($crmProductionOrder, trans('request.done'));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(ProductionOrder $crmProductionOrder)
    {

        $crmProductionOrder->items = $crmProductionOrder->items()->with('good.attributes')->get();
        return $this->handleResponse([
            'form' => $crmProductionOrder,
            'formOptions' => [
                'attributes' => Attribute::query()->with('items')->get(),
            ],
        ]);
    }
    public function create()
    {
        return $this->handleResponse([
            'formOptions' => [
                'attributes' => Attribute::query()->with('items')->get(),
            ],
        ]);
    }
    // متد update با قوانین اعتبارسنجی مشخص
    public function update(Request $request, ProductionOrder $crmProductionOrder): JsonResponse
    {
        $request->validate([
            //'delivery_date' => 'required',
            //'party_id' => 'required',
            'items' => 'required',
        ], [
            'items.required' => 'اقلام سفارش الزامی است!',
        ]);
        $data = $request->all();
        //dd(auth()->user());
        $data['party_id'] = auth()->user()->id;
        $data['is_created_by_customer'] = true;
        unset($data['status']);
        $crmProductionOrder->update($data);
        $items = collect($data['items'])->map(function ($item) {
            return [...$item, 'good_id' => $item['good']['id'], 'total_price' => $item['count'] * $item['price']];
        })->toArray();

        $crmProductionOrder->items()->sync($items);
        $crmProductionOrder->items = $crmProductionOrder->items()->with('good')->get();

        $this->changeStatus($crmProductionOrder, $crmProductionOrder->status);

        return $this->handleResponse([
            'form' => $crmProductionOrder,
        ]);
        // اگر اعتبارسنجی موفق بود، فراخوانی متد update از کلاس پایه
        //return parent::update($request, $id);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $ids
     * @return \Illuminate\Http\Response
     */
    public function delete(Request $request)
    {
        // اعتبارسنجی ورودی برای اطمینان از اینکه ورودی آرایه‌ای از شناسه‌هاست
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => ['integer', Rule::exists(ProductionOrder::class, 'id')],
        ], [
            'ids.*.exists' => 'کاربری با شناسه :input یافت نشد یا امکان حذف ندارد.',
        ]);

        // دریافت شناسه‌ها از ورودی
        $ids = $validated['ids'];

        // حذف مدل‌ها با شناسه‌های مشخص شده
        try {
            ProductionOrder::destroy($ids);
        } catch (\Exception $e) {
            // در صورت بروز خطا، می‌توانید یک پاسخ مناسب ارسال کنید
            return $this->handleError($e, 'امکان حذف وجود ندارد', 500);
        }

        // ارسال پاسخ موفقیت‌آمیز
        return $this->handleResponse(null, trans('request.done')); // response()->json(['message' => 'Records deleted successfully']);
    }

    public function sendToProduction(ProductionOrder $crmProductionOrder)
    {
        $crmProductionOrder->update(['status' => ProductionOrder::SEND_DRAFT]);

        $this->changeStatus($crmProductionOrder, $crmProductionOrder->status);
        // $crmProductionOrder->checklists()->create([
        //     'status' => $crmProductionOrder->status,
        //     'updated_at' => Carbon::now(),
        //     'username' => auth()->user()->full_name,
        // ]);
        return $this->handleResponse(null, trans('request.done'));
    }
    private function changeStatus($crmProductionOrder, $status)
    {
        if ($current = $crmProductionOrder->checklists()->where('status', $status)->first()) {
            $current->update([
                'status' => $status,
                'updated_at' => Carbon::now(),
                'username' => auth()->user()->full_name,
            ]);
        } else {
            $crmProductionOrder->checklists()->create([
                'status' => $status,
                'updated_at' => Carbon::now(),
                'username' => auth()->user()->full_name,
            ]);
        }
    }


    public function print(ProductionOrder $productionOrder)
    {
        $attributes = Attribute::query()->with(['items', 'parent.items'])->get()->map(function ($m) {
            if ($m['items'] && $m->parent?->items) {
                $m['items'] = $m['items']->merge($m->parent?->items);
            }
            return array_merge($m->toArray(), [
                "items" => $m['items']->toArray(),
            ]);
        });

        $productionOrder->items = $productionOrder->items()->with('good')->get()->map(function ($m) use ($attributes) {
            $getLabelAttributes = $m->getLabelAttributes($attributes);
            $m['attributes_label'] = collect($getLabelAttributes)->pluck('label', 'attribute_key');
            $m['attributes_label_column'] = $getLabelAttributes;

            return $m;
        });
        $productionOrder['diff_delivery_date'] = !($productionOrder->delivery_date && $productionOrder->submit_date) ? 0 : verta()->parse($productionOrder->submit_date)->diffDays(verta()->parse($productionOrder->delivery_date));
        return $this->handleResponse($productionOrder, null, [
            'statuses' => collect(ProductionOrder::$statuses)->map(function ($m) use ($productionOrder) {
                $checklist = $productionOrder->checklists->where('status', $m['value'])->first();
                if ($checklist) {
                    $m['is_done'] = $m['value'] == $productionOrder['status'];
                    $m['updated_at'] = $checklist['updated_at'];
                }

                return $m;
            }),
            'attributes' => $attributes,
            'station_labels' => '',
            'templates' => [],
        ]);
    }
}
