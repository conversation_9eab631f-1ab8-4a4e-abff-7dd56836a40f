// Simple test for multi-domain setup
const fs = require('fs');
const path = require('path');

console.log('🚀 Testing Multi-Domain Frontend Setup...\n');

// Check required files
const requiredFiles = [
    'src/config/domains.js',
    'src/config/themes.js',
    'src/composables/useDomain.js',
    'src/views/panel/DashboardView.vue',
    'src/views/crm/DashboardView.vue',
    'src/views/shared/LoginView.vue'
];

let allGood = true;

requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - Missing!`);
        allGood = false;
    }
});

// Check package.json
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
if (packageJson.scripts['dev:panel'] && packageJson.scripts['dev:crm']) {
    console.log('✅ Package.json scripts configured');
} else {
    console.log('❌ Package.json scripts missing');
    allGood = false;
}

// Check .env
if (fs.existsSync('.env')) {
    const envContent = fs.readFileSync('.env', 'utf8');
    if (envContent.includes('VITE_PANEL_DOMAIN') && envContent.includes('VITE_CRM_DOMAIN')) {
        console.log('✅ Environment variables configured');
    } else {
        console.log('❌ Environment variables missing');
        allGood = false;
    }
} else {
    console.log('❌ .env file missing');
    allGood = false;
}

if (allGood) {
    console.log('\n🎉 All checks passed! Multi-Domain Frontend is ready!');
    console.log('\n📖 Usage:');
    console.log('Panel: npm run dev:panel (http://panel.erp.test:3000)');
    console.log('CRM: npm run dev:crm (http://crm.erp.test:3001)');
} else {
    console.log('\n❌ Some issues found. Please check the setup.');
}
