<template>
    <div class="table-container">
      <div class="table-header">
        <div 
          v-for="(col, index) in columns" 
          :key="index" 
          class="header-cell"
          :style="{ width: columnWidth + 'px' }"
        >
          {{ col }}
        </div>
      </div>
      <div class="table-body">
        <div 
          v-for="(row, rowIndex) in rows" 
          :key="rowIndex" 
          class="table-row"
        >
          <div 
            v-for="(col, colIndex) in columns" 
            :key="colIndex" 
            class="body-cell"
            :style="{ width: columnWidth + 'px' }"
          >
            {{ row[col] }}
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    props: {
      columns: {
        type: Array,
        required: true
      },
      rows: {
        type: Array,
        required: true
      },
      columnWidth: {
        type: Number,
        default: 120
      }
    }
  }
  </script>
  
  <style scoped>
  .table-container {
    overflow-x: auto;
    border: 1px solid #ddd;
  }
  
  .table-header, .table-row {
    display: flex;
    min-width: fit-content;
  }
  
  .header-cell, .body-cell {
    padding: 8px;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    white-space: nowrap;
  }
  
  .header-cell {
    background-color: #f8f9fa;
    font-weight: bold;
  }
  </style>