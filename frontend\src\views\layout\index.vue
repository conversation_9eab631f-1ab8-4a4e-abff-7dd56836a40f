<template>
    <q-layout view="lHr lpr lfr">
        <q-header class="sm:p-4 bg-transparent">
            <div class="bg-white text-gray-700 sm:rounded-md shadow overflow-auto">
                <q-toolbar>
                    <q-btn flat @click="toggleDrawer" round dense icon="menu" />
                    <q-toolbar-title><img src="@/assets/logo.svg" class="h-10" /></q-toolbar-title>
                    <div class="q-gutter-sm row items-center no-wrap">
                        <ThemeSwitcher simple />
                        <q-avatar round>
                            <img src="https://cdn.quasar.dev/img/avatar1.jpg">
                            <q-menu class="no-shadow card_style" transition-show="jump-down" transition-hide="jump-up"
                                fit>
                                <q-list style="min-width: 200px;" class="shadow-none">
                                    <UserProfile :user="authStore.user" />
                                    <q-separator />
                                    <q-item clickable v-close-popup :to="{ name: 'profile' }">
                                        <IconItem icon="person" label="پروفایل" />
                                    </q-item>
                                    <q-separator />
                                    <q-item clickable v-close-popup @click="authStore.logout()">
                                        <IconItem icon="logout" label="خروج" />
                                    </q-item>
                                </q-list>
                            </q-menu>
                        </q-avatar>
                    </div>
                </q-toolbar>

                <q-toolbar v-if="$route.meta.toolbar !== false" class="border-t-2 toolbar-custom">
                    <q-icon flat round dense :name="publicStore.icon ?? 'assignment'" size="xs" class="mr-2" />
                    <div class="text-md font-bold">{{ publicStore.titleWindow ?? '' }}</div>
                    <q-space />
                    <ToolbarActions />
                </q-toolbar>
            </div>
        </q-header>

        <q-drawer v-model="drawer" show-if-above :width="290" :breakpoint="1100" class="shadow">
            <q-scroll-area class="h-full p-3">
                <q-list>
                    <TreeItem v-for="(route, index) in filteredRoutes" :key="index" v-bind="route"
                        :current-route="currentRouteName" />
                </q-list>
            </q-scroll-area>
        </q-drawer>

        <q-page-container>
            <q-page class="bg-transparent sm:mx-4" id="body-page" :style-fn="myTweak">
                <slot :key="publicStore.componentKey" />
            </q-page>
        </q-page-container>
    </q-layout>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useAuthStore, usePublicStore } from '@/stores';
import { useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import TreeItem from './components/TreeItem.vue';
import UserProfile from './components/UserProfile.vue';
import IconItem from './components/IconItem.vue';
import ToolbarActions from './components/ToolbarActions.vue';
import ThemeSwitcher from '@/components/ThemeSwitcher.vue';
import { getUrlAssets } from '@/helpers';

const $q = useQuasar();
const drawer = ref(false);
const authStore = useAuthStore();
const publicStore = usePublicStore();
const route = useRoute();
const currentRouteName = computed(() => route.name);
const routes = ref([]);

authStore.routes().then(res => {
    console.log(res.map(mapRoute))
    //routes.value = res.filter(f => !(typeof f?.meta?.disable === 'function' ? f.meta.disable({ user: authStore.user }) : f.meta?.disable ?? false));
    routes.value = res.filter(routeMenuFilter).map(m => {
        if (m.children) m.children = m.children.filter(routeMenuFilter)
        if (m.children && m.children.length === 1 && m.children[0].path == '') {
            m = { ...m.children[0], path: m.path }
        }
        return m;
    })
});

const mapRoute = function (route) {
    if (route.children) route.children = route.children.filter(routeMenuFilter).map(mapRoute)
    if (route.children && route.children.length === 1 && route.children[0].path == '') {
        route = { ...route.children[0], path: route.path }
    }
    return route;
}

const routeMenuFilter = function (f) {
    return !f.hidden && !(typeof f?.meta?.disable === 'function' ? f.meta.disable({ user: authStore.user }) : f.meta?.disable ?? false)
}



const filteredRoutes = computed(() => routes.value);

function toggleDrawer() {
    drawer.value = !drawer.value;
}

const myTweak = function (offset) {
    // "offset" is a Number (pixels) that refers to the total
    // height of header + footer that occupies on screen,
    // based on the QLayout "view" prop configuration

    // this is actually what the default style-fn does in Quasar
    return { '--parent-height': offset ? `calc(100vh - ${offset}px ${(offset > 85 ? '- 1rem' : '')})` : '100vh', maxHeight: offset ? `calc(100vh - ${offset}px ${(offset > 85 ? '- 1rem' : '')})` : '100vh' }
}
</script>

<style scoped>
.toolbar-custom {
    min-height: 35px;
    padding-top: 3px;
    padding-bottom: 3px;
}
</style>