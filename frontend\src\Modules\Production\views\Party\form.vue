<template>
    <j-form-data url="production/party" :form="form">
        <template v-slot="{ form, formOptions }">
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <j-input v-model="form.full_name" label="نام و نام خانوادگی" error-field="full_name" required />
                <j-input v-model="form.display_name" label="نام نمایشی" required />
                <j-input v-model="form.mobile_number" label="شماره موبایل" mask="###########" hint="09#########"
                    required />
            </div>
            <j-input v-model="form.address" label="آدرس" type="textarea" />

        </template>
    </j-form-data>
</template>

<script setup>
import { ref } from 'vue';
const form = ref({ roles: [], permissions: [] })
</script>