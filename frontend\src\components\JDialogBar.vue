<template>
    <j-dialog ref="ref_dialog" :maximized="maximized || $q.screen.xs" backdrop-filter="blur(4px)">
        <q-card :style="dialogStyle">
            <q-bar class="space-x-2 sticky top-0 z-10 border-b h-10 bg-gray-50">
                <q-btn dense flat icon="close" @click="close">
                    <q-tooltip>بستن</q-tooltip>
                </q-btn>
                <span class="mr-1" v-if="$slots.title">
                    <slot name="title" />
                </span>
                <q-space />
                <slot name="bar" />
                <j-btn v-if="!$q.screen.xs" flat dense size="md" :icon="maximized ? 'fullscreen_exit' : 'fullscreen'"
                    title="تمام عرض" @click="maximize" />

                <!-- <q-btn dense flat icon="refresh" @click="++key">
                    <q-tooltip>رفرش</q-tooltip>
                </q-btn> -->
            </q-bar>
            <div :class="contentClass">
                <q-pull-to-refresh disable @refresh="(done) => { ++key; done(); }">
                    <slot :key="key" v-bind="{ maximized: maximized || $q.screen.xs }" />
                </q-pull-to-refresh>
            </div>

            <!-- <div v-if="$slots.footer" class="space-x-5 sticky bottom-0 z-10">
                <slot name="footer" />
            </div> -->
        </q-card>
    </j-dialog>
</template>

<script>
import { ref } from 'vue';

export default {
    props: {
        contentClass: {
            type: String,
            default: ''
        },
        close: Function,
        dialogStyle: {
            type: String,
            default: 'max-width: 100%;'
        }
    },
    setup(props) {
        const ref_dialog = ref(null)

        const maximized = ref(false)

        return {
            key: ref(1),
            hide() {
                //console.log('dialog hide')
                ref_dialog.value.hide()
            },
            show() {
                //console.log('dialog hide')
                ref_dialog.value.show()
            },
            close() {
                if (props.close && typeof props.close == 'function')
                    props.close(ref_dialog.value.hide)
                else
                    ref_dialog.value.hide()
            },
            ref_dialog,
            maximized,
            maximize() {
                maximized.value = !maximized.value;
            }
        }
    },
}
</script>