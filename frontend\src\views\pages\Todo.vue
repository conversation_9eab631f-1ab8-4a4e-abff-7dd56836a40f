<template>
  <div>
    <h2>Todo List 1</h2>
    <div v-if="isLoading">Loading...</div>
    <div v-else-if="error">{{ error }}</div>
    <ul v-else>
      <li v-for="todo in todos" :key="todo.id">
        {{ todo.title }}
        <button @click="deleteTodo(todo.id)">Delete</button>
      </li>
    </ul>
  </div>
</template>

<script>
import { useTodoStore } from '@/stores/todo.store';
import { storeToRefs } from 'pinia';

export default {
  setup() {
    const todoStore1 = useTodoStore(); // نمونه اول
    const { todos, isLoading, error } = storeToRefs(todoStore1);

    todoStore1.fetchTodos();

    return {
      todos,
      isLoading,
      error,
      deleteTodo: todoStore1.deleteTodo,
    };
  },
};
</script>
