<template>
    <j-input v-model="form.name" label="نام" error-field="name" dense />
    <j-select-remote v-model="form.group_id" url="/good/group/search" label="گروه" dense />
    <template v-for="attribute, key in attributes" :key="key">
        <template v-if="form.group_id && group[attribute.id] && attribute.type == 'SELECT'" class="p-2">
            <j-select v-if="attribute.type == 'SELECT'" v-model="form.attributes[attribute.id]" dense
                :options="attribute.items" option-label="name" option-value="id" use-input hide-bottom-space
                :label="attribute.name" :disable="is_edit" multiple />
        </template>
    </template>
    <!-- <pre>{{ attributes }}</pre> -->


    <!-- <j-table-simple :columns="columns" row-key="name">
        <template #dialog="{ formOption, form }">
            <table-form :form="form" class="p-5" :formOption="formOption" />
        </template>
    </j-table-simple> -->
</template>
<script>
// import TableForm from "./form2.vue";

import { api } from '@/boot/axios';
import { ref } from '@vue/reactivity';
import { onMounted, watch } from '@vue/runtime-core';

export default {
    props: {
        form: {
            type: Object,
            default: () => { }
        },

    },
    setup(props, { emit }) {
        const attributes = ref([])
        const group = ref({})
        const form = ref(props.form ?? {})

        if (!form.value.attributes) {
            form.value.attributes = {}
        }

        api.get('/good/attribute').then(res => {
            attributes.value = res.data
        })

        watch(() => form.value.group_id, (val) => {
            if (val) {
                api.get(`/good/group/${val}`).then(res => {
                    group.value = res.data.attributes;
                })
            } else {
                group.value = {}
            }
        })

        watch(() => form.value, (val) => {
            emit('update:form', form.value)
        }, { deep: true })
        // const select = (val) => {

        // }
        onMounted(() => {
            const val = form.value.group_id;
            if (val) {
                api.get(`/good/group/${val}`).then(res => {
                    group.value = res.data.attributes;
                })
            } else {
                group.value = {}
            }
        })
        return {
            // select,
            attributes,
            group,
            form,
        }
        //     return {
        //         columns: [
        //             {
        //                 feild: 'name',
        //                 label: 'نام'
        //             },
        //             {
        //                 feild: 'station_name',
        //                 label: 'ایستگاه'
        //             }
        //         ]
        //     }
    },
    // components: { TableForm }
};
</script>