<?php

// use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
 */

// Route::get('/test', function () {
//     Auth::loginUsingId(1);

//     LogBatch::startBatch();
//     $author = Attribute::create(['name' => 'color']);
//     $book = Product::create(['name' => 'miz', 'attribute_id' => $author->id]);
//     $book->update(['name' => 'A Scanner Darkly']);
//     $author->delete();
//     LogBatch::endBatch();

//     return Product::all();
// });

// Backend is now API-only
// Frontend is served separately

// All routes moved to API
