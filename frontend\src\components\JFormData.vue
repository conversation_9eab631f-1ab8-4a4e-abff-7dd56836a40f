<template>
    <q-card bordered flat :square="$q.screen.xs" :style="!maximized ? 'width:100%; max-width:1024px; margin: 0 auto':''">
        <q-card-actions class="flex bg-primary text-white sticky z-10 top-0">
            <!-- <span class="text-primary font-black">
                <template v-if="form?.id">
                    <j-icon name="edit" size="xs" />
                    ویرایش شناسه {{ form?.id }}
                </template>
<template v-else>
                    <j-icon name="add" size="xs" />
                    ایجاد
                </template>
</span>

<q-space /> -->
            <j-btn flat dense size="md" label="برگشت" icon="arrow_forward" @click="publicStore.back()" />
            <j-btn flat dense size="md" type="submit" label="ذخیره" icon="save" @click="validate" />
            <q-space />
            <j-btn v-if="!$q.screen.xs" flat dense size="md" :icon="maximized ? 'fullscreen_exit':'fullscreen'" title="تمام عرض" @click="maximize" />
        </q-card-actions>
        <q-separator />
        <q-card-section style="overflow-y: auto;
    max-height: calc(var(--parent-height) - 47px);">
            <j-form ref="myForm" @submit="submit" class="space-y-2">
                <slot v-bind="{ form, formOptions }" />

            </j-form>
        </q-card-section>

    </q-card>
</template>
<script>
import { formApi } from "@/helpers";
import { useRoute } from "vue-router";
import { ref } from 'vue'
import { onMounted } from "vue";
import { nextTick } from "vue";
import { usePublicStore } from "@/stores/public.store";
export default {
    props: {
        form: {
            type: Object,
            default: () => { }
        },
        hasCreateRoute: {
            type: Boolean,
            default: false,
        },
        JSelectform: {
            type: Object,
            default: () => { }
        },
        formOption: {
            type: Object,
            default: () => { }
        },
        form: {
            type: Object,
            default: () => { }
        },
        url: String,
        load: Function,
    },
    setup(props, context) {
        const myForm = ref(null)
        const route = useRoute()
        const publicStore = usePublicStore()
        //console.log('url', props.url)
        const { form, formOptions, submit } = formApi({ id: route?.params?.id, uri: props.url, props, context })
        //console.log('formOptions', formOptions)
        const validate = () => {
            myForm.value.submit()
        }
        onMounted(() => {
            nextTick(() => {
                console.log('onMounted jFormData')
            })
        })
        const maximized = ref(false)
        return {
            publicStore,
            myForm,
            form,
            formOptions,
            submit,
            validate,
            maximized,
            maximize() {
                maximized.value = !maximized.value;
            }
        };
    },
};
</script>