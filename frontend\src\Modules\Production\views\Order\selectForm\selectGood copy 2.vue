<template>
  <!-- {{ conditions }} -->
  <!-- <pre v-if="good" dir="ltr">{{ good.default_attribute }}</pre> -->
  <!-- <pre dir="ltr">hidden_attribute_items{{ hidden_attribute_items }}</pre> -->
  <!-- <pre dir="ltr">hidden_attributes{{ hidden_attributes }}</pre> -->
  <j-dialog-bar>
    <!-- <pre dir="ltr"> {{ form }} </pre> -->
    <!-- {{ hidden_attributes }} -->

    <j-form @submit="submit" class="w-full" ref="ref_form">
      <div class="sticky gap-2 py-1 pb-3 px-3 flex top-8 z-10 w-full left-0 right-0 bg-white">
        <!-- {{  form  }} -->
        <select-product ref="ref_good" v-model:value="form.good" url="/good/good/search" hide-bottom-space autofocus
          @update:model-value="(res) => selectGood(res, true)" label="نام کالا" :params="{ is_active: true }" size="sm"
          search-local sort-by="name" :disable="is_edit" class="flex-1" required />

      </div>
      <div class="p-3">
        <div class="grid grid-cols-1 gap-8">            
              
              <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <template v-for="(attribute, key) in good_attributes.filter(
                  (f) =>
                    !hidden_attributes[f.key] &&
                    (f.pivot?.showing ?? f.showing)
                )" :key="key">
                  <j-select v-if="['SELECT_IMAGE','SELECT'].includes(attribute.type)" v-model="form.attributes[attribute.key]" :bg-color="good.default_attribute &&
                    good?.default_attribute[attribute.key] !== undefined &&
                    good.default_attribute[attribute.key] !==
                    form.attributes[attribute.key]
                    ? 'amber-2'
                    : ''
                    " :options="attribute.items.filter(
                      (f) =>
                        !hidden_attribute_items[attribute.key] ||
                        (hidden_attribute_items[attribute.key] &&
                          hidden_attribute_items[attribute.key].includes(f.key))
                    )
                      " option-label="name" option-value="key" search-local hide-bottom-space :label="attribute.name"
                    outlined :required="attribute.pivot.required ?? attribute.required"
                    @update:model-value="(val) => change(attribute.key, val)" />
                  <!-- <j-select-image v-if="attribute.type == 'SELECT_IMAGE'" v-model:value="form.attributes[attribute.key]"
                    :image-key="(row) => row.data.image" :bg-color="good.default_attribute &&
                      good?.default_attribute[attribute.key] !== undefined &&
                      good.default_attribute[attribute.key] !==
                      form.attributes[attribute.key]
                      ? 'amber-2'
                      : ''
                      " :options="attribute.items.filter((f) => !f.hidden)" option-label="name" option-value="key"
                    search-local hide-bottom-space :label="attribute.name" outlined
                    :required="attribute.pivot.required ?? attribute.required"
                    @update:model-value="(val) => change(attribute.key, val)" /> -->
                  <j-input v-else-if="attribute.type == 'INPUT'" v-model="form.attributes[attribute.key]" 
                    hide-bottom-space :label="attribute.name" outlined
                    :required="attribute.pivot.required ?? attribute.required" :disable="(is_edit && false) ||
                      disable_attributes.includes(attribute.key)
                      " @update:model-value="(val) => change(attribute.key, val)" :bg-color="good.default_attribute &&
                        good?.default_attribute[attribute.key] !== undefined &&
                        good.default_attribute[attribute.key] !==
                        form.attributes[attribute.key]
                        ? 'amber-2'
                        : ''
                        " />
                  <j-input v-else-if="attribute.type == 'NUMBER'" v-model="form.attributes[attribute.key]" 
                    hide-bottom-space :label="attribute.name" type="number" step="0.1" min="0" outlined
                    :required="attribute.pivot.required ?? attribute.required" :disable="(is_edit && false) ||
                      disable_attributes.includes(attribute.key)
                      " @update:model-value="(val) => change(attribute.key, val)" :bg-color="good.default_attribute &&
                        good?.default_attribute[attribute.key] !== undefined &&
                        good.default_attribute[attribute.key] !==
                        form.attributes[attribute.key]
                        ? 'amber-2'
                        : ''
                        " />
                  <j-toggle v-else-if="attribute.type == 'SWITCH'" v-model="form.attributes[attribute.key]" 
                    hide-bottom-space :label="attribute.name" outlined
                    :required="attribute.pivot.required ?? attribute.required" :disable="(is_edit && false) ||
                      disable_attributes.includes(attribute.key)
                      " @update:model-value="(val) => change(attribute.key, val)" :class="good.default_attribute &&
                        good?.default_attribute[attribute.key] !== undefined &&
                        good.default_attribute[attribute.key] !==
                        form.attributes[attribute.key]
                        ? 'bg-amber-2'
                        : ''
                        " />
                  <j-upload v-else-if="attribute.type == 'FILE'" v-model:value="form.attributes[attribute.key]"
                    :label="attribute.name" auto-upload accept="image/*" url="/api/upload-file" field-name="file"
                    class="col-span-full" style="width: 100%" flat bordered />
                </template>
              </div>
        </div>
        <br />
        <j-input v-model="form.description" label="توضیحات کالا" type="textarea" class="col-span-full" outlined />
      </div>
      <div class=" sticky bottom-0 left-0 right-0 p-2 text-center z-10 bg-white border-t">
        <!-- <price-good class="w-48" v-model:price="form.price" v-model:price_details="form.price_details"
          v-bind="{ form, good, good_attributes }" /> -->

        <j-btn size="md" :color="is_edit ? 'secondary' : 'primary'" type="submit" class="text-white"
          :label="is_edit ? 'ویرایش' : 'افزودن'" icon="add_shopping_cart" @shortkey="ref_form.submit()"
          v-shortkey="['ctrl', 'space']" />
      </div>
    </j-form>
  </j-dialog-bar>
  <!-- <pre dir="ltr">{{ form.attributes }}</pre> -->
  <!-- <pre dir="ltr">{{ form.price_details }}</pre> -->
</template>

<script>
import { api } from "@/boot/axios";
import { checkPermission } from "@/helpers";
import { onMounted, ref, watch } from "vue";
import { conditionCompute } from "./index";
import SelectProduct from "./SelectProduct.vue";
import {
  dakheliItems,
  parvazItems,
} from "@/Modules/Production/computeProductionFormula/rokoob";
import {
  doCenterLayerThickness,
  doDoorThickness,
  doZevardamageh,
} from "@/Modules/Production/computeProductionFormula/door";
import { doTypeDoor } from "@/Modules/Production/computeProductionFormula";
import {
  doMokammelWidth,
  doMokammelCount,
  doMokammelPerLength,
  doThicknessEdgeOfFrame,
  doWidthFloor,
} from "@/Modules/Production/computeProductionFormula/frame";
import PriceGood from "./priceGood.vue";
import { checkRole } from "@/helpers";
export default {
  components: { SelectProduct, PriceGood },
  setup(props, context) {
    const good = ref({});
    const is_edit = ref(false);
    const form = ref({
      attributes: {},
      good: null,
      count: 1,
    });
    const ref_good = ref(null);
    const ref_form = ref(null);

    const conditions = ref([]);

    const onEdit = (value) => {
      console.log('onEdit')
      is_edit.value = true;
      good.value = {};
      selectGood(value.good);
      form.value = JSON.parse(JSON.stringify(value));
      //ref_good.value.focus();
    };

    const onAdd = (value) => {
      //console.log('onAdd', value)

      is_edit.value = false;
      good.value = {};
      form.value = {
        attributes: {},
        count: 1,
        good: null,
      }
      //selectGood(value.good_id);
      //form.value = JSON.parse(JSON.stringify(value));
      //ref_good.value.focus();
    };

    const onCopy = (value) => {
      const temp = JSON.parse(JSON.stringify(value));
      delete temp.id;
      onEdit(temp);
      is_edit.value = false;
      //ref_good.value.focus();
    };
    const good_attributes = ref([]);
    const disable_attributes = ref([]);
    const hidden_attributes = ref({});
    // const { conditionProccess, disable_attributes, hidden_attributes, good_attributes } = conditionCompute({ form, good })
    const computeCondition = (attribute_id = null) => {
      //     disable_attributes.value = []
      //     hidden_attributes.value = []
      good_attributes.value = JSON.parse(JSON.stringify(good.value.attributes));
      console.log('computeCondition', good_attributes.value)
      //     //console.log('================================================')

      //     conditionProccess(conditions.value)

      //     // conditionProccess(conditions.value.filter(f => !f.good_id && !f.group_id && f.if.filter(
      //     //     (ff) =>
      //     //         !attribute_id ? true : ff.attribute_id == attribute_id
      //     // ).length > 0))
      //     // conditionProccess(conditions.value.filter(f => f.group_id == good.value.group_id && f.if.filter(
      //     //     (ff) =>
      //     //         !attribute_id ? true : ff.attribute_id == attribute_id
      //     // ).length > 0))
      //     // conditionProccess(conditions.value.filter(f => f.good_id == good.value.id && f.if.filter(
      //     //     (ff) =>
      //     //         !attribute_id ? true : ff.attribute_id == attribute_id
      //     // ).length > 0))
    };

    const selectGood = (value, default_attribute = false) => {
      console.log('selectGood')
      form.value.attributes = {};
      hidden_attributes.value = {};
      if (!value) {
        good.value = {};
        return;
      }
      api.get(`good/good/${value.id}/selectForOrder`).then((res) => {

        good.value = res.result;

        computeCondition();
        // defaultAttribute();
      });
    };

    let old_attributes = {};
    watch(
      () => form.value.attributes,
      (newVal, oldValue) => {
        if (!good.value.attributes) return;
        Object.keys(old_attributes).forEach((old_attr) => {
          if (form.value.attributes[old_attr] !== old_attributes[old_attr]) {
            computeCondition(old_attr);
          }
        });
        old_attributes = Object.assign({}, newVal);

        //
      },
      {
        deep: true,
      }
    );

    const reset = () => {
      is_edit.value = false;
      form.value = {
        attributes: {},
        count: 1,
      };
      good.value = {};
      //ref_good.value.focus();
    };

    const groupAttributes = [
      {
        key: "byDoor",
        conditions: (good) => good && ["door"].includes(good?.group?.key),
        label: "همراه با",
      },
      {
        key: "frame",
        conditions: (good) =>
          good &&
          (form.value.attributes["hasFrame"] ||
            ["frame"].includes(good?.group?.key)),
        label: "چهارچوب",
      },
      {
        key: "rokoob",
        conditions: (good) => {
          return (
            good &&
            (form.value.attributes["hasRokoob"] ||
              ["rokoob"].includes(good?.group?.key))
          );
        },
        label: "روکوب",
      },
      {
        key: "door",
        conditions: (good) => good && ["door"].includes(good?.group?.key),
        label: (good) => good?.group?.name,
      },

      {
        key: "maghta",
        conditions: (good) => good && ["maghta"].includes(good?.group?.key),
        label: (good) => good?.group?.name,
      },
      {
        key: "zehvar",
        conditions: (good) => {
          return (
            good &&
            ["zehvar"].includes(good?.group?.key)
          );
        },
        label: "زهوار",
      },
      {
        key: "garniz",
        conditions: (good) => {
          console.log('garniz')
          return (
            good &&
            ["garniz"].includes(good?.group?.key)
          );
        },
        label: "قرنیز",
      },
      {
        key: "",
        conditions: (good) =>
          good && ["other", null].includes(good?.group?.key),
        label: (good) => good?.group?.name,
      },
      {
        key: "public",
        label: "سایر",
      },
      {
        conditions: () => checkRole(['admin', 'sell_person']),
        key: "production",
        label: "تولید",
      },
    ];


    const defaultAttribute = () => {
      groupAttributes.forEach((groupAttribute) => {
        if (
          groupAttribute.conditions == undefined ||
          (groupAttribute.conditions && groupAttribute.conditions(good.value))
        ) {
          good_attributes.value
            .forEach((f) => {
              if (f.key == 'hasBarjestegi' && !good.value.default_attribute[f.key]) {
                // hidden_attributes.value.hasBarjestegi = true;

              }
              if (
                good.value.default_attribute[f.key] !== undefined &&
                form.value.attributes[f.key] === undefined
              ) {
                form.value.attributes[f.key] =
                  good.value.default_attribute[f.key];
              }
            });
        }
      });

    };

    const defaultAttributeSecond = () => {
      if (form.value.attributes["hasFrame"])
        form.value.attributes["typeMaterialFrame"] =
          form.value.attributes["typeMaterialDoor"];
      if (form.value.attributes["hasRokoob"])
        form.value.attributes["typeMaterialRokoob"] =
          form.value.attributes["typeMaterialDoor"];
    };

    const hidden_attribute_items = ref({});
    const oldFormAttribute = ref({});


    watch(
      () => form.value.attributes,
      (newVal) => {
        Object.keys(newVal).forEach((key) => {

          if (oldFormAttribute.value[key] + '' == newVal[key] + '') return;
          let group_key = "";

          switch (key) {
            case "hasFrame":
              group_key = "frame";
              break;
            case "hasRokoob":
              group_key = "rokoob";
              break;
          }
          if (group_key) {
            if (newVal[key]) {
              good_attributes.value
                .filter((f) => f.group_name && f.group_name.includes(group_key))
                .forEach((f) => {
                  if (
                    good.value?.default_attribute &&
                    good.value?.default_attribute[f.key]
                  ) {
                    form.value.attributes[f.key] =
                      good.value.default_attribute[f.key];
                  }
                });
            } else {
              good_attributes.value
                .filter((f) => f.group_name && f.group_name == group_key)
                .forEach((f) => {
                  delete form.value.attributes[f.key];
                });
            }
            //  defaultAttributeSecond();
          }

   

          if (form.value.attributes["hasEdgeOfDoor"]) {
            delete hidden_attributes.value.alignEdgeOfDoor;
          } else {
            delete form.value.attributes.alignEdgeOfDoor;
            hidden_attributes.value.alignEdgeOfDoor = true;
          }

          if (form.value.attributes["countZevardamageh"]) {
            delete hidden_attributes.value.typeZehvar;
          } else {
            delete form.value.attributes.typeZehvar;
            hidden_attributes.value.typeZehvar = true;
          }

          if (form.value.attributes["hasParvaz"]) {

            delete hidden_attributes.value.typeMaterialRokoob;
            delete hidden_attributes.value.typeParvaz;
            delete hidden_attributes.value.countParvaz;
            delete hidden_attributes.value.widthParvaz;
            delete hidden_attributes.value.perLengthParvaz;

          } else if (form.value.attributes["hasParvaz"] === false) {

            delete form.value.attributes.typeMaterialRokoob;
            delete form.value.attributes.typeParvaz;
            delete form.value.attributes.countParvaz;
            delete form.value.attributes.widthParvaz;
            delete form.value.attributes.perLengthParvaz;

            hidden_attributes.value.typeMaterialRokoob = true;
            hidden_attributes.value.typeParvaz = true;
            hidden_attributes.value.countParvaz = true;
            hidden_attributes.value.widthParvaz = true;
            hidden_attributes.value.perLengthParvaz = true;
          }


          if (form.value.attributes["hasBarjestegi"]) {
            hidden_attributes.value[65] = false;
            hidden_attributes.value[66] = false;
            hidden_attributes.value[67] = false;
            hidden_attributes.value[68] = false;
            hidden_attributes.value[69] = false;
            hidden_attributes.value[70] = false;
            hidden_attributes.value[71] = false;
            hidden_attributes.value[75] = false;
            hidden_attributes.value[76] = false;
            hidden_attributes.value[77] = false;
          } else {
            delete form.value.attributes[65];
            delete form.value.attributes[66];
            delete form.value.attributes[67];
            delete form.value.attributes[68];
            delete form.value.attributes[69];
            delete form.value.attributes[70];
            delete form.value.attributes[71];
            delete form.value.attributes[75];
            delete form.value.attributes[76];
            delete form.value.attributes[77];

            hidden_attributes.value[65] = true;
            hidden_attributes.value[66] = true;
            hidden_attributes.value[67] = true;
            hidden_attributes.value[68] = true;
            hidden_attributes.value[69] = true;
            hidden_attributes.value[70] = true;
            hidden_attributes.value[71] = true;
            hidden_attributes.value[75] = true;
            hidden_attributes.value[76] = true;
            hidden_attributes.value[77] = true;
          }

          if (
            (form.value.attributes["hasFrame"] ||
              ["frame"].includes(good.value?.group?.key)) &&
            form.value.attributes["coverFrame"]
          ) {
            if (
              [
                17, 18, 20, 22, 24, 25, 27, 28, 33, 37, 38, 39, 45, 46, 48, 50,
                51, 53, 56, 58, 59, 61, 64, 87, 93, 192,
              ].includes(form.value.attributes["coverFrame"] * 1)
            )
              form.value.attributes["colorNavardarzgir"] = "111";
            if (
              [34, 52, 54, 55, 60, 92, 94, 95, 193].includes(
                form.value.attributes["coverFrame"] * 1
              )
            )
              form.value.attributes["colorNavardarzgir"] = "112";
            if (
              [
                19, 21, 26, 29, 30, 31, 35, 36, 40, 42, 43, 44, 47, 49, 57, 62,
                63, 66, 67, 68, 69, 70, 71, 73, 74, 75, 76, 77, 78, 79, 80, 81,
                82, 83, 84, 85, 86, 89, 90, 91, 96,
              ].includes(form.value.attributes["coverFrame"] * 1)
            )
              form.value.attributes["colorNavardarzgir"] = "113";
          }

          if (form.value.attributes["typeDakheli"] == "french") {
            form.value.attributes["widthDakheli"] = 9.5;
          }
          if (form.value.attributes["typeDakheli"] == "mexic") {
            form.value.attributes["widthDakheli"] = 10.5;
          }

          if (form.value.attributes["typeParvaz"] == "french") {
            form.value.attributes["widthParvaz"] = 7;
          }
          if (form.value.attributes["typeParvaz"] == "mexic") {
            form.value.attributes["widthParvaz"] = 10.5;
          }

          if (form.value.attributes["typeMaterialFrame"]) {
            form.value.attributes["typeMaterialRokoob"] =
              form.value.attributes["typeMaterialFrame"];
          }
          // console.log('good',form.value.attributes);
          // فیدار فوم
          if (good.value.group_id == 16 && ['4', '5'].includes(form.value.attributes["sheetCNCThickness"]))
            form.value.attributes["typeMaterialDoor"] = "mdf";

          hidden_attribute_items.value = {};

          parvazItems.forEach((parvaz) => {
            if (
              form.value.attributes["typeParvaz"] == parvaz.typeParvaz &&
              parvaz.typeMaterialRokoob.includes(
                form.value.attributes["typeMaterialRokoob"]
              )
            ) {
              const find = good_attributes.value.findIndex(
                (f) => f.key == "perLengthParvaz"
              );
              if (find >= 0) {
                hidden_attribute_items.value[good_attributes.value[find].key] =
                  good_attributes.value[find].items
                    .filter((f) => parvaz.includes.includes(f.name * 1))
                    .map((m) => m.key);
                if (
                  form.value.attributes["perLengthParvaz"] &&
                  good_attributes.value[find].items.filter(
                    (ff) =>
                      hidden_attribute_items.value[
                        good_attributes.value[find].key
                      ].includes(ff.key) &&
                      ff.key == form.value.attributes["perLengthParvaz"]
                  ).length == 0
                ) {
                  delete form.value.attributes["perLengthParvaz"];
                }
              }
            }
          });

          dakheliItems.forEach((dakheli) => {
            if (
              form.value.attributes["typeDakheli"] == dakheli.typeDakheli &&
              dakheli.typeMaterialRokoob.includes(
                form.value.attributes["typeMaterialRokoob"]
              )
            ) {
              const find = good_attributes.value.findIndex(
                (f) => f.key == "perLengthDakheli"
              );
              if (find >= 0) {
                hidden_attribute_items.value[good_attributes.value[find].key] =
                  good_attributes.value[find].items
                    .filter((f) => dakheli.includes.includes(f.name * 1))
                    .map((m) => m.key);
                if (
                  form.value.attributes["perLengthDakheli"] &&
                  good_attributes.value[find].items.filter(
                    (ff) =>
                      hidden_attribute_items.value[
                        good_attributes.value[find].key
                      ].includes(ff.key) &&
                      ff.key == form.value.attributes["perLengthDakheli"]
                  ).length == 0
                ) {
                  delete form.value.attributes["perLengthDakheli"];
                }
              }
            }
          });


          if (form.value.attributes["coverDoor"] == 'two_color' && good.value.attributes.findIndex(f => f.key == "coverFrontDoor") >= 0) {

            delete hidden_attributes.value["coverFrontDoor"]
            delete hidden_attributes.value["coverBackDoor"]

          } else {
            hidden_attributes.value["coverFrontDoor"] = true;
            hidden_attributes.value["coverBackDoor"] = true;

            // delete form.value.attributes["coverFrontDoor"]
            // delete form.value.attributes["coverBackDoor"]
          }

          if (form.value.attributes["coverDoor"] == 'two_color' && good.value.attributes.findIndex(f => f.key == "coverMiddleSheet") >= 0) {

            delete hidden_attributes.value["coverMiddleSheet"]
            delete hidden_attributes.value["coverBaghalBazoo"]

          } else {
            hidden_attributes.value['coverMiddleSheet'] = true;
            hidden_attributes.value['coverBaghalBazoo'] = true;

            //delete form.value.attributes["coverMiddleSheet"]
            //delete form.value.attributes["coverBaghalBazoo"]
          }






          const typeDoor = doTypeDoor(good.value.group_id);
          const hasEdgeOfDoor = form.value.attributes["hasEdgeOfDoor"];
          const hasAbzar = form.value.attributes["hasAbzar"];
          const typeMaterialDoor = form.value.attributes["typeMaterialDoor"];
          const sheetCNCThickness = form.value.attributes["sheetCNCThickness"];
          const goodId = good.value.id;

          const doorThickness = doDoorThickness({
            typeDoor,
            hasEdgeOfDoor,
            goodId,
            hasAbzar,
            typeMaterialDoor,
            sheetCNCThickness,
          });

          if (doorThickness)
            form.value.attributes["doorThickness"] = doorThickness + "";
          if (doorThickness && form.value.attributes["hasFrame"])
            form.value.attributes["thicknessEdgeOfFrame"] =
              doThicknessEdgeOfFrame({ doorThickness });


          const centerLayerThickness = doCenterLayerThickness({
            typeMaterialDoor,
            doorThickness,
            typeDoor,
            good_id: form.value.good_id
          });
          if (centerLayerThickness)
            form.value.attributes["centerLayerThickness"] =
              centerLayerThickness.label;

          if (doorThickness) form.value.attributes["baghalBazooThickness"] = (doorThickness * 10 - form.value.attributes["centerLayerThickness"]) / 2
          if (form.value.attributes["widthFloor"]) {
            delete form.value.attributes.mokammelWidth;
            delete form.value.attributes.mokammelCount;
            delete form.value.attributes.mokammelPerLength;
            delete form.value.attributes.hasMokammel;
            hidden_attributes.value.mokammelWidth = true;

            const wallThickness = form.value.attributes["wallThickness"];
            const widthFloor = form.value.attributes["widthFloor"];
            if (widthFloor) {
              const mokammelWidth = doMokammelWidth({ wallThickness, widthFloor })
              if (mokammelWidth > 0) {
                form.value.attributes.mokammelWidth = mokammelWidth;
                form.value.attributes.mokammelCount = doMokammelCount({ frameWidth: form.value.attributes["frameWidth"] });
                form.value.attributes.mokammelPerLength = doMokammelPerLength({ frameHeight: form.value.attributes["frameHeight"] });
                form.value.attributes.hasMokammel = true;
                hidden_attributes.value.mokammelWidth = false;
              }


            }

          }
        });

        oldFormAttribute.value = Object.assign({}, newVal);
      },
      {
        deep: true,
      }
    );
    return {
      checkPermission,
      selectGood,
      submit: () => {
        context.emit(
          "confirm",
          {
            ...form.value,
            good: form.value.good.pick(['id', 'name']),
            //count: 1,
          },
          is_edit.value
        );
        reset();
      },
      good,
      form,
      ref_good,
      onEdit,
      onAdd,
      onCopy,
      is_edit,
      ref_form,
      good_attributes,
      hidden_attributes,
      disable_attributes,
      hidden_attribute_items,
      change(attribute_key, newVal) {

        if (attribute_key == 'hasFrame'){
            if (newVal){

            } else {}
        }

        if (attribute_key == "widthDakheli" && newVal) {
          if (form.value.attributes["widthDakheli"] > 20)
            form.value.attributes["widthDakheli"] = 20
        }
        if (attribute_key == "widthParvaz" && newVal) {
          if (form.value.attributes["widthParvaz"] > 10.5)
            form.value.attributes["widthParvaz"] = 10.5
        }

        if (attribute_key == "hasFrame" && newVal) {
          if (form.value.attributes["hasRokoob"])
            form.value.attributes["hasRokoob"] = false;
          form.value.attributes["hasEdgeOfDoor"] = false;
        }

        if (attribute_key == "hasFrame" && !newVal) {
          form.value.attributes["hasEdgeOfDoor"] = true;
          form.value.attributes["alignEdgeOfDoor"] = 'threeSide';
        }

        if (attribute_key == "hasRokoob" && newVal) {
          if (form.value.attributes["hasFrame"])
            form.value.attributes["hasFrame"] = false;
        }

        if (attribute_key == "hasEdgeOfDoor" && newVal) {
          if (form.value.attributes["hasFrame"])
            form.value.attributes["hasFrame"] = false;
          form.value.attributes["alignEdgeOfDoor"] = 'threeSide';

        }

        if (attribute_key == "typeDakheli") {
          if (form.value.attributes["typeDakheli"] == "none_standard") {
            form.value.attributes["widthDakheli"] = '';
          }
        }

        if (attribute_key == "typeParvaz") {
          if (form.value.attributes["typeParvaz"] == "none_standard") {
            form.value.attributes["widthParvaz"] = '';
          }
        }

        if (form.value.attributes["hasFrame"]) {
          if (["hasThreshold", "frameHeight"].includes(attribute_key)) {
            form.value.attributes["doorHeight"] =
              form.value.attributes["frameHeight"] * 1 -
              7 +
              (form.value.attributes["hasThreshold"] ? 0 : 2);
          }
          if (attribute_key == "frameWidth") {
            form.value.attributes["doorWidth"] =
              form.value.attributes["frameWidth"] * 1 - 7;
          }
          // if (attribute_key == "typeMaterialDoor") {
          //   form.value.attributes["typeMaterialFrame"] =
          //     form.value.attributes["typeMaterialDoor"];
          // }

          if (attribute_key == 'doorLengeh' || attribute_key == 'hasEdgeOfDoor') {

            form.value.attributes["countZevardamageh"] = doZevardamageh({ doorLengeh: form.value.attributes["doorLengeh"], hasEdgeOfDoor: form.value.attributes["hasEdgeOfDoor"], })
          }

          if (attribute_key == "pvcColor" && newVal != 'two_color') {
            if (good.value.attributes.findIndex(f => f.key == "coverFrontDoor") >= 0) {
              form.value.attributes["coverFrontDoor"] = newVal;
              form.value.attributes["coverBackDoor"] = newVal;
            }

            if (good.value.attributes.findIndex(f => f.key == "coverMiddleSheet") >= 0) {
              form.value.attributes["coverMiddleSheet"] = newVal;
              form.value.attributes["coverBaghalBazoo"] = newVal;
            }
            if (form.value.attributes["hasFrame"]) {
              form.value.attributes["coverFrame"] = newVal;

            }
          }


          if (attribute_key == 'doorLengeh' || attribute_key == 'hasEdgeOfDoor') {
            form.value.attributes["countZevardamageh"] = doZevardamageh({ doorLengeh: form.value.attributes["doorLengeh"], hasEdgeOfDoor: form.value.attributes["hasEdgeOfDoor"], })
          }

          if (attribute_key == "coverBaghalBazoo" && form.value.attributes["hasFrame"]) {
            form.value.attributes["coverFrame"] = newVal;
          }
          if (attribute_key == "coverFrontDoor" && form.value.attributes["hasFrame"]) {
            form.value.attributes["coverFrame"] = newVal;
          }

          if (attribute_key == "coverBaghalBazoo" && form.value.attributes["hasRokoob"]) {
            form.value.attributes["coverRokoob"] = newVal;
          }
          if (attribute_key == "coverFrontDoor" && form.value.attributes["hasRokoob"]) {
            form.value.attributes["coverRokoob"] = newVal;
          }



          // if (form.value.attributes["hasRokoob"] && attribute_key == "typeMaterialDoor")
          //   form.value.attributes["typeMaterialRokoob"] = form.value.attributes["typeMaterialDoor"];



          if (!good.value.attributes) return;
          computeCondition();
        }

        if (form.value.attributes["hasFrame"] || form.value.good_id == 183) {
          if (attribute_key == "wallThickness") {
            const wallThickness = form.value.attributes["wallThickness"];
            const widthFloor = doWidthFloor({ wallThickness });
            if (widthFloor) {
              form.value.attributes["widthFloor"] = widthFloor.label;
            }
          }
        }
      },
      groupAttributes,
      checkRole,
    };
  },
};
</script>