import { ref, reactive, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { TABLE_SETTINGS } from '@/constants';

/**
 * Composable for table functionality with pagination, sorting, and filtering
 */
export function useTable(options = {}) {
    const route = useRoute();
    const router = useRouter();
    
    const {
        defaultPageSize = TABLE_SETTINGS.DEFAULT_PAGE_SIZE,
        defaultSortBy = null,
        defaultDescending = false,
        persistFilters = true
    } = options;
    
    // Table state
    const loading = ref(false);
    const rows = ref([]);
    const selected = ref([]);
    
    // Pagination
    const pagination = reactive({
        page: parseInt(route.query.page) || 1,
        rowsPerPage: parseInt(route.query.per_page) || defaultPageSize,
        rowsNumber: 0,
        sortBy: route.query.sort_by || defaultSortBy,
        descending: route.query.descending === 'true' || defaultDescending
    });
    
    // Filters
    const filters = ref({});
    
    // Initialize filters from URL query
    const initializeFilters = (filterDefinitions = {}) => {
        Object.keys(filterDefinitions).forEach(key => {
            if (route.query[key]) {
                try {
                    filters.value[key] = {
                        ...filterDefinitions[key],
                        value: JSON.parse(route.query[key])
                    };
                } catch {
                    filters.value[key] = {
                        ...filterDefinitions[key],
                        value: route.query[key]
                    };
                }
            } else {
                filters.value[key] = {
                    ...filterDefinitions[key],
                    value: filterDefinitions[key].defaultValue || null
                };
            }
        });
    };
    
    // Watch for filter changes and update URL
    watch(
        () => filters.value,
        (newFilters) => {
            if (!persistFilters) return;
            
            const query = { ...route.query };
            
            Object.keys(newFilters).forEach(key => {
                const filter = newFilters[key];
                if (filter.value !== null && filter.value !== undefined && filter.value !== '') {
                    if (typeof filter.value === 'object') {
                        query[key] = JSON.stringify(filter.value);
                    } else {
                        query[key] = filter.value;
                    }
                } else {
                    delete query[key];
                }
            });
            
            // Reset to first page when filters change
            query.page = 1;
            pagination.page = 1;
            
            router.replace({ path: route.path, query });
        },
        { deep: true }
    );
    
    // Watch for pagination changes and update URL
    watch(
        () => pagination,
        (newPagination) => {
            if (!persistFilters) return;
            
            const query = { ...route.query };
            query.page = newPagination.page;
            query.per_page = newPagination.rowsPerPage;
            
            if (newPagination.sortBy) {
                query.sort_by = newPagination.sortBy;
                query.descending = newPagination.descending;
            } else {
                delete query.sort_by;
                delete query.descending;
            }
            
            router.replace({ path: route.path, query });
        },
        { deep: true }
    );
    
    // Computed properties
    const hasFilters = computed(() => {
        return Object.values(filters.value).some(filter => 
            filter.value !== null && 
            filter.value !== undefined && 
            filter.value !== ''
        );
    });
    
    const activeFiltersCount = computed(() => {
        return Object.values(filters.value).filter(filter => 
            filter.value !== null && 
            filter.value !== undefined && 
            filter.value !== ''
        ).length;
    });
    
    const hasSelection = computed(() => {
        return selected.value.length > 0;
    });
    
    const isAllSelected = computed(() => {
        return rows.value.length > 0 && selected.value.length === rows.value.length;
    });
    
    const isSomeSelected = computed(() => {
        return selected.value.length > 0 && selected.value.length < rows.value.length;
    });
    
    // Methods
    const clearFilters = () => {
        Object.keys(filters.value).forEach(key => {
            filters.value[key].value = filters.value[key].defaultValue || null;
        });
    };
    
    const clearSelection = () => {
        selected.value = [];
    };
    
    const selectAll = () => {
        selected.value = [...rows.value];
    };
    
    const toggleSelectAll = () => {
        if (isAllSelected.value) {
            clearSelection();
        } else {
            selectAll();
        }
    };
    
    const onRequest = async (props, fetchFunction) => {
        const { page, rowsPerPage, sortBy, descending } = props.pagination;
        
        loading.value = true;
        
        try {
            // Build query parameters
            const params = {
                page,
                per_page: rowsPerPage
            };
            
            if (sortBy) {
                params.sort_by = sortBy;
                params.order = descending ? 'desc' : 'asc';
            }
            
            // Add filter parameters
            Object.keys(filters.value).forEach(key => {
                const filter = filters.value[key];
                if (filter.value !== null && filter.value !== undefined && filter.value !== '') {
                    params[key] = filter.value;
                }
            });
            
            const response = await fetchFunction(params);
            
            // Update rows and pagination
            rows.value = response.data || response;
            
            if (response.meta) {
                pagination.page = response.meta.current_page;
                pagination.rowsPerPage = response.meta.per_page;
                pagination.rowsNumber = response.meta.total;
            }
            
            pagination.sortBy = sortBy;
            pagination.descending = descending;
            
            return response;
        } finally {
            loading.value = false;
        }
    };
    
    const refresh = (fetchFunction) => {
        return onRequest({ pagination }, fetchFunction);
    };
    
    return {
        loading,
        rows,
        selected,
        pagination,
        filters,
        hasFilters,
        activeFiltersCount,
        hasSelection,
        isAllSelected,
        isSomeSelected,
        initializeFilters,
        clearFilters,
        clearSelection,
        selectAll,
        toggleSelectAll,
        onRequest,
        refresh
    };
}
