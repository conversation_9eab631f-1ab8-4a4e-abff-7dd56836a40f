<template>
    <div class="q-gutter-sm">
        <!-- <j-table-bar :columns="columns" v-model:rows="data" :row-key="row => row.order.id" @onFilter="onFilter">
          <template #select_bar="{ selected }">
              <template v-if="selected">
                  <j-btn dense icon="task_alt" color="primary" @click="showChecklist(selected)"
                      @shortkey="showChecklist(selected)" v-shortkey="['insert']" />
                  <j-dialog-bar v-model="showDialog">
                      <Checklist v-model:data="selected.items"
                          :attributes="attribute_station.length > 0 ? attribute_station : attributes" :url="url"
                          @afterSave="afterSave" style="width:100%" />
                  </j-dialog-bar>
              </template>
          </template>
      </j-table-bar> -->
        <!-- <Checklist v-model:data="selected.items"
              :attributes="attribute_station.length > 0 ? attribute_station : attributes" :url="url"
              @afterSave="afterSave" style="width:100%" /> -->

        <!-- <j-table-data :url="'/production/station/' + $route.params.id + '/production_checklist'" readonly
          :row-key="row => row.order.id" :columns="columns" dense>
          <template #dialog="props">
              <Checklist v-model:data="props.row.items"
                  :attributes="attribute_station.length > 0 ? attribute_station : attributes" :url="url"
                  @afterSave="afterSave" style="width:100%" />
          </template>
          <template #select_bar="{ selected }">
              <j-btn dense icon="task_alt" color="primary" @click="showChecklist(selected)"
                  @shortkey="showChecklist(selected)" v-shortkey="['insert']" />
          </template>
      </j-table-data> -->
        <j-table :columns="columns" :rows="data" :row-key="row => row.order.code" class="even-highlight-2" :grid="false"
            :rows-per-page-options="[0]" :pagination="{ rowsPerPage: 0 }" @onFilter="onFilter">

            <template v-slot:body="props">
                <q-tr :props="props">
                    <q-td v-for="col in props.cols" :key="col.name" :props="props">
                        <template v-if="col.name == 'expand'">
                            <q-btn size="sm" flat dense @click="props.expand = !props.expand"
                                :icon="props.expand ? 'remove' : 'add'" />
                            <!-- <j-btn dense icon="task_alt" color="primary" @click="showChecklist(props.row)" /> -->

                        </template>
                        <template v-else>
                            {{ col.value }}
                        </template>
                    </q-td>
                </q-tr>
                <q-tr v-show="props.expand" :props="props">
                    <q-td colspan="5" style="padding:0">
                        <!-- <Checklist v-model:data="props.row.items"
                          :attributes="attribute_station.length > 0 ? attribute_station : attributes" :url="url"
                          @afterSave="afterSave" style="width:100%" /> -->
                        <div class="bg-gray-400 p-1 grid gap-1">

                            <template v-for="instruction_item in props.row.items"
                                :key="instruction_item.station_work_name">
                                <!-- <q-scroll-area style="min-height: 200px;height: 100%; width: 100%;"> -->
                                <div class="overflow-x-auto">
                                    <Checklist :data="instruction_item" :attributes="attributes" :url="url"
                                        @afterSave="afterSave" style="width:100%" />
                                </div>
                                <!-- </q-scroll-area> -->

                            </template>
                        </div>

                    </q-td>
                </q-tr>
            </template>

            <template v-slot:item="props">
                <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3 grid-style-transition border-b-2">
                    <div :class="props.selected ? 'bg-grey-2' : ''">
                        <q-card-section v-if="props.selected !== undefind">
                            <q-checkbox dense v-model="props.selected"
                                :label="props.cols.filter(f => f.name == 'name').map(m => m.value).join('')" />
                        </q-card-section>
                        <q-separator />
                        <q-list dense>
                            <q-item v-for="col in props.cols.filter(col => col.name !== 'desc')" :key="col.name">
                                <q-item-section>
                                    <template v-if="col.name == 'expand'">
                                        <q-btn size="sm" flat dense @click="props.expand = !props.expand"
                                            :icon="props.expand ? 'remove' : 'add'" />
                                    </template>
                                    <q-item-label v-else>{{ col.label }}</q-item-label>
                                </q-item-section>
                                <q-item-section side>
                                    <q-item-label caption>{{ col.value }}</q-item-label>
                                </q-item-section>
                            </q-item>
                        </q-list>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3 grid-style-transition border-b-2"
                    v-show="props.expand">
                    <!-- {{ Object.values(props.row.items.group('instruction_item_id')).length }}
                  <template
                      v-for="instruction_item, index in Object.values(props.row.items.group('instruction_item_id'))"
                      :key="index">
                      <Checklist :data="instruction_item"
                          :attributes="attribute_station.length > 0 ? attribute_station : attributes" :url="url"
                          @afterSave="afterSave" style="width:100%" />
                  </template> -->
                    <template v-for="instruction_item in props.row.items" :key="instruction_item.station_work_name">
                        <Checklist :data="instruction_item" :attributes="attributes" :url="url" @afterSave="afterSave"
                            style="width:100%" />
                    </template>
                </div>
            </template>

        </j-table>
        <!-- <j-table-data :url="`/production/station/${$route.params.id}/production_checklist`" :columns="columns" @loaded="loadedData">
      <template #dialog="props">
        <table-form v-bind="props" :instruction-id="$route.params.id" />
      </template>
      <template #body-cell-parents="props">
        <q-td :props="props">
          <q-chip v-for="parent, index in props.row.parents" :key="index" square outline text-color="primary" dense>{{
              parent.name
          }}</q-chip>
        </q-td>
      </template>
    </j-table-data> -->
    </div>
</template>

<script>
import { usePublicStore } from "@/stores";
import { api } from "@/boot/axios";
import TableForm from "./form.vue";
import { useRoute } from 'vue-router';
import { ref } from "vue";
import Checklist from "./checklist.vue";
import { printTemplate } from "../productionOrder/prints";

export default {
    setup() {
        const route = useRoute();
        const url = '/production/station/' + route.params.id + '/production_checklist';

        const columns = [
            {
                name: 'expand',
                required: true,
                style: 'width: 30px',

            },
            {
                name: 'id',
                required: true,
                label: 'کد سفارش',
                field: row => row.order.code,
                sortable: true,
                style: 'width: 50px',
                filter: {
                    type: 'FilterInput',
                    relation: 'productionOrderItem.productionOrder',
                },
            },
            {
                name: 'full_name',
                required: true,
                label: 'نمایندگی',
                field: row => row.order.party_name,
                sortable: true,
                filter: {
                    type: 'FilterInput',
                    relation: 'productionOrderItem.productionOrder.party',
                },
            },
            {
                name: 'customer_name',
                required: true,
                label: 'مشتری',
                field: row => row.order.customer_name,
                sortable: true,
                filter: {
                    type: 'FilterInput',
                    relation: 'productionOrderItem.productionOrder',
                },
            },
            {
                name: 'delivery_date',
                required: true,
                label: 'تاریخ سفارش',

                field: row => row.order.delivery_date,
                sortable: true,
                style: 'width: 50px',
                filter: 'FilterDate'
            },

        ]
        const data = ref([])
        const attributes = ref([])
        const attribute_station = ref([])
        const paramFilter = ref({})
        const getData = () => {
            api.get(url, {
                params: {
                    filters: paramFilter.value,
                }
            }).then(res => {
                data.value = res.data.map(m => {
                    return {
                        ...m, items: Object.values(m.items
                            //.map(m => ({ ...m, key: m.instruction_item.name })).group('key')
                        )
                    };
                });
                // attribute_station.value = res.attributes;
                publicStore.titleWindow = res.station.name
            })
        }
        api.get('good/attribute').then(res => {
            attributes.value = res.data
            getData()
        })

        const publicStore = usePublicStore();


        const showDialog = ref(false)
        return {
            showDialog,
            attribute_station,
            attributes,
            columns,
            data,
            url,
            afterSave() {
                getData()
                // api.get(url).then(res => {
                //     data.value = res.result.checklists;
                //     // publicStore.titleWindow = res.result.station.name
                // })
            },
            showChecklist(row) {
                showDialog.value = true;
            },
            printTemplate,
            onFilter(filters) {

                const oldParamFilter = Object.assign({}, paramFilter.value);
                paramFilter.value = Object.assign({}, filters);
                Object.keys(paramFilter.value).filter((f) => {
                    if (!paramFilter.value[f].value) delete paramFilter.value[f];
                });
                if (
                    !(
                        Object.keys(paramFilter.value).length == 0 &&
                        Object.keys(oldParamFilter) == 0
                    )
                )

                    getData()


            }
        };
    },
    components: { TableForm, Checklist },
};
</script>
