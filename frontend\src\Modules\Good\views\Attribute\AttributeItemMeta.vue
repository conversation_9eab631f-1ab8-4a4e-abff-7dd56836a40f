<template>
    <j-btn color="secondary" dense icon="view_list" flat title="متا" @click="openDialog" />
    <j-dialog-bar v-model="visible">
        <template #bar>
            <j-btn flat icon="save" dense @click="save">
                <q-tooltip>ذخیره</q-tooltip>
            </j-btn>
        </template>
        <good-meta v-model:value="data" />
    </j-dialog-bar>
</template>
<script setup>
import { api } from '@/boot/axios';
import { ref } from 'vue'
import GoodMeta from '../Good/GoodMeta.vue';

const props = defineProps(['item'])
const data = ref({})
const visible = ref(false)
const openDialog = () => {
    visible.value = true
    api.get('/good/attributeItem/' + props.item.id + '/meta').then(res => {
        data.value = res.result;
    })
}
const save = () => {
    api.post('/good/attributeItem/' + props.item.id + '/meta', {
        metas: data.value,
    }).then(res => {
        data.value = res.result;
    })
}
</script>