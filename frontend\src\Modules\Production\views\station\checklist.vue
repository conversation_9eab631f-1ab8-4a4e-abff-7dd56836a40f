<template>
    <!-- <div class="flex"> -->
    <!-- <j-btn color="blue" class="mr-1"><span class="w-5">{{ $props.data.station_work_name }}</span></j-btn> -->
    <j-table :columns="columns" :rows="data" dense flat hide-bottom separator="cell" :rows-per-page-options="[0]"
        :grid="false">
        
        <template #body-cell-station_work_name="props">
            <q-td :props="props">
                <q-chip square color="primary" text-color="white" clickable dense>{{
                    props.value
                }}</q-chip>
            </q-td>
        </template>
        <template #body-cell-action="props">
            <q-td :props="props">
                <!-- <q-btn-toggle v-model="status" dense toggle-color="primary" flat :options="[
                  {label: '...', value: 'one'},
                  {label: 'تمام', value: 'two'},
                  {label: 'خراب', value: 'three'}
                ]" /> -->
                <q-toggle v-model="props.row.status" dense toggle-indeterminate checked-icon="check" color="green"
                    unchecked-icon="clear" true-value="DONE" false-value="REPAIRING" indeterminate-value="PROCESSING" />
            </q-td>
        </template>
        <template #body-cell-58="props">
            <q-td :props="props">
                <j-icon name="image" size="md" color="primary" @click="showImage(props.value)" />
            </q-td>
        </template>
        <template #header-cell-action="props">
            <q-th :props="props">
                <!-- <q-btn-toggle v-model="status" dense toggle-color="primary" flat :options="[
                  {label: '...', value: 'one'},
                  {label: 'تمام', value: 'two'},
                  {label: 'خراب', value: 'three'}
                ]" /> -->
                <div class="grid gap-3 text-center">
                    <j-toggle v-model="status_all" dense toggle-indeterminate checked-icon="check" color="primary"
                        keep-color unchecked-icon="clear" true-value="DONE" false-value="REPAIRING"
                        indeterminate-value="PROCESSING" />

                    <j-btn icon="save" flat dense color="primary" @click="save" />
                </div>
            </q-th>
        </template>
        <template #item-action="props">

            <q-toggle v-model="props.row.status" dense toggle-indeterminate checked-icon="check" color="green"
                unchecked-icon="clear" true-value="DONE" false-value="REPAIRING" indeterminate-value="PROCESSING" />
        </template>
    </j-table>
    <!-- </div> -->
    <j-dialog v-model="isShowImage" maximized>
        <q-card>
            <q-card-section class="row items-center q-pb-none">
                <div class="text-h6">نمایش طرح</div>
                <q-space />
                <q-btn icon="close" flat round dense v-close-popup />
            </q-card-section>

            <q-card-section>
                <img :src="'/' + urlImage" class="rounded-md h-screen m-auto" />
            </q-card-section>
        </q-card>
    </j-dialog>

</template>
<script>
import { useQuasar } from 'quasar';
import { computed, ref, watch } from 'vue'
import { api } from '@/boot/axios';

export default {
    props: {
        attributes: Array,
        data: Array,
        url: String,
    },
    setup(props, { emit }) {
        const $q = useQuasar()
        const columns = computed(() => {

            const attribute_columns = props.data.attribute_columns.filter(f => props.data.production_order_items.map(m => Object.keys(m.attributes)).flat().unique().indexOf(f + '') !== -1);

            return [...[
                {
                    name: 'index',
                    label: '#',
                    field: 'index',
                    headerClasses: 'w-10'
                },

                {
                    name: "action",
                    label: "عملیات",
                    align: "center"
                },
                {
                    name: "station_work_name",
                    label: "کار",
                    field: () => props.data.station_work_name,
                },
                {
                    name: "good",
                    label: "کالا/خدمات",
                    field: 'good_name',
                },
            ],
            ...props.attributes.sort(function (a, b) {
                if (a.pivot) {
                    if (a.pivot.sort < b.pivot.sort) return -1;
                    if (a.pivot.sort > b.pivot.sort) return 1;
                } else {
                    if (a.sort < b.sort) return -1;
                    if (a.sort > b.sort) return 1;
                }

                return 0;
            })
                .filter(f => attribute_columns.includes(f.id))
                .map(m => {

                    return {
                        name: m.id,
                        label: m.name,
                        vertical: true,
                        align: "center",
                        field: row => {
                            switch (m.type) {
                                case 'SELECT':
                                case 'SELECT_IMAGE':
                                    const find = m.items.findIndex(ff => ff.id + '' == row.attributes[m.id])
                                    return find >= 0 ? m.items[find].name : '';
                                case 'SWITCH':
                                    return row.attributes[m.id] ? 'دارد' : ''
                                case 'FILE':
                                    return row.attributes[m.id]
                                case 'NUMBER':
                                case 'INPUT':
                                    return row.attributes[m.id] ?? ''
                            }
                        },
                    }
                })
            ];

        })
        const status_all = ref(false)
        const status = ref({})
        watch(() => status_all.value, function (newVal) {
            props.data.production_order_items.map(m => {
                m.status = newVal;
            })
        })
        const isShowImage = ref(false)
        const urlImage = ref('')
        return {
            columns,
            data: computed(() => props.data.production_order_items.map((m, i) => {
                m.index = i + 1
                return m
            })),
            isShowImage,
            urlImage,
            showImage(value) {
                urlImage.value = value
                isShowImage.value = true
            },

            status,
            status_all,
            save() {
                $q.dialog({
                    title: 'ذخیره',
                    message: 'آیا مطمئن هستید ذخیره شود؟',
                    cancel: true,
                    persistent: true
                }).onOk(() => {
                    api.put(props.url, {
                        data: props.data.production_order_items.map(m => ({ id: m.id, status: m.status ?? 'PROCESSING' }))
                    }).then(() => {
                        emit('afterSave')
                    })
                })
            },
        }
    },
}
</script>
<style>
.q-toggle__inner--falsy {
    color: #ff4242;
}

.q-toggle__inner--falsy .q-toggle__thumb:after {
    background-color: currentColor;
}

.q-toggle__inner--falsy .q-toggle__thumb .q-icon {
    color: #fff;
    opacity: 1;
}


</style>