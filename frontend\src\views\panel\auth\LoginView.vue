<template>
  <div id="q-app">
    <q-layout view="lHh Lpr fff">
      <q-page-container>
        <q-page :style="pageStyle"
          class="row justify-center items-center dark:bg-gray-800 dark:bg-blend-luminosity q-pa-lg">
          <div class="column shadow-4 bg-white/90 rounded-xl overflow-auto">


            <q-card class="flex md:flex-row flex-col">
              <div class="image-container flex-1 flex items-center justify-center p-8 bg-gray-50 dark:bg-gray-800">
                <img src="@/assets/login-background.svg" class="w-3/4 h-auto">
              </div>
              <div class="form-container flex-1 p-6">
                <img src="@/assets/logo.svg" class="w-24 h-auto mx-auto">
                <q-card-section>
                  <j-form class="q-px-sm" @submit="submitForm">
                    <j-input v-for="(field, index) in inputFields" :key="index" clearable v-model="field.value"
                      :type="field.type" lazy-rules :label="field.label" required :square="false">
                      <template v-slot:prepend>
                        <q-icon :name="field.icon" />
                      </template>
                    </j-input>

                    <j-btn unelevated :loading="loading" size="md" color="blue" icon="login" type="submit"
                      class="full-width text-white mt-4" label="ورود" />
                  </j-form>

                </q-card-section>
              </div>
            </q-card>


            <!-- <q-card class="shadow-24 rounded-md bg-white/90">  
              <q-card-section>  
                
                <j-form class="q-px-sm" @submit="submitForm">  
                  <j-input  
                    v-for="(field, index) in inputFields"  
                    :key="index"  
                    square  
                    clearable  
                    v-model="field.value"  
                    :type="field.type"  
                    lazy-rules  
                    :label="field.label"  
                    required  
                    dense  
                  >  
                    <template v-slot:prepend>  
                      <q-icon :name="field.icon" />  
                    </template>  
                  </j-input>  
                  <j-btn  
                    unelevated  
                    :loading="loading"  
                    size="md"  
                    color="secondary"  
                    push  
                    type="submit"  
                    class="full-width text-white mt-4"  
                    label="ورود"  
                  />  
                </j-form>  
              </q-card-section>  
            </q-card>   -->
          </div>
        </q-page>
      </q-page-container>
    </q-layout>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useAuthStore } from "@/stores";
import bg from '@/assets/background-login.svg';

const loading = ref(false);
const inputFields = ref([
  { label: "نام کاربری", type: "text", icon: "person", value: ref("") },
  { label: "کلمه عبور", type: "password", icon: "lock", value: ref("") }
]);

const pageStyle = computed(() => ({
  "background-image": `url(${bg})`,
  "background-size": 'cover',
  "background-position": 'center',
  "background-repeat": 'no-repeat'
}));

const submitForm = async () => {
  loading.value = true;
  const authStore = useAuthStore();
  await authStore.login({
    username: inputFields.value[0].value,
    password: inputFields.value[1].value
  });
  loading.value = false;
};  
</script>