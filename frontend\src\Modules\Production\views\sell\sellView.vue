<template>
  <j-table-data-crud :url="url" :columns="columns" />
  <!-- <div class="q-gutter-sm">
    <j-table-data :url="url" :columns="columns" @afterSubmit="afterSubmit"
      :can-be-add="checkPermission('production_order_create') || checkRole(['admin', 'customer_level_1'])"
      :can-be-edit="(selected) => (selected.status == 'DRAFT' && checkRole('customer_level_1')) || (checkPermission('production_order_edit_force') || (['PREORDER', 'SEND_DRAFT'].includes(selected.status) && (checkRole('admin') || checkPermission('production_order_edit'))))"
      :can-be-delete="(selected) => (selected.status == 'DRAFT' && checkRole('customer_level_1')) || ((['PREORDER', 'SEND_DRAFT'].includes(selected.status) && checkPermission('production_order_delete')) || checkRole('admin'))">
      <template #select_bar="{ selected, url, callback }">
        <checklist :selected="selected" :callback="callback" />
        <print-order ref="print_order_ref" :selected="selected" :callback="callback" />
        <tracking-production :selected="selected" :callback="callback" />


      </template>
      <template #body-cell-is_created_by_customer="props">
        <q-td key="is_created_by_customer" :props="props" :class="props.row.is_created_by_customer ? 'bg-amber-3' : ''">
          {{ props.value }}
        </q-td>
      </template>
      <template #dialog="{ load, formOption, form }">
        <table-form :form="form" @afterSubmit="load" :formOption="formOption" />
      </template>
    </j-table-data>
  </div> -->
</template>

<script>
import TableForm from "./form.vue";
import Checklist from "./checklist.vue";
import PrintOrder from "./printOrder.vue";
import TrackingProduction from "./trackingProduction.vue";
import { checkPermission, checkRole } from '@/helpers'
import { useAuthStore } from "@/stores";
import { useQuasar } from "quasar";
import { api } from "@/boot/axios";
import { ref } from 'vue'
export default {
  components: { TableForm, Checklist, PrintOrder, TrackingProduction },

  setup() {
    console.log('sell view')
    const authStore = useAuthStore();
    const print_order_ref = ref(null)
    const columns = [
      {
        name: 'code',
        required: true,
        label: 'شماره فاکتور',
        field: 'code',
        sortable: true,
        style: 'width: 50px',
        filter: 'FilterInput',
        checkbox_label: true,
      },
      authStore.user.is_customer ? null : {
        name: 'is_created_by_customer',
        required: true,
        label: 'نوع سفارش',
        field: row => row.is_created_by_customer ? 'آنلاین' : 'دستی',
        sortable: true,
        style: 'width: 50px',
        filter: 'FilterSelect',
        filterOption: [
          {
            value: true,
            label: 'آنلاین'
          },
          {
            value: false,
            label: 'دستی'
          },
        ]
      },
      {
        name: 'status',
        required: true,
        label: 'وضعیت',
        field: row => authStore.user.is_customer ? row.label_status_customer : row.label_status,
        sortable: true,
        style: 'width: 80px',
        filter: 'FilterSelect',
        filterOption: 'statuses'
      },
      authStore.user.is_customer ? null : {
        name: 'display_name',
        required: true,
        label: 'نمایندگی',
        field: 'party_name',
        sortable: true,
        filter: {
          type: 'FilterInput',
          relation: 'party',
        },
      },
      {
        name: 'customer_name',
        required: true,
        label: 'مشتری',
        field: 'customer_name',
        sortable: true,
        filter: 'FilterInput',
      },

      {
        name: 'total_count',
        required: true,
        label: 'تعداد کل',
        field: 'total_count',
        style: 'width: 70px',
      },
      {
        name: 'total_price',
        required: true,
        label: 'قیمت کل',
        permissions: 'show price order',
        field: row => String.currencyFormat(row.total_price),
        style: 'width: 90px',
      },
      {
        name: 'created_at',
        required: true,
        label: 'تاریخ ثبت',
        field: 'created_at',
        sortable: true,
        style: 'width: 50px',
      },
      {
        name: 'submit_date',
        required: true,
        label: 'تاریخ شروع',
        field: 'submit_date',
        sortable: true,
        style: 'width: 50px',
        filter: 'FilterDate'
      },
      {
        name: 'delivery_date',
        required: true,
        label: 'تاریخ سفارش',
        field: 'delivery_date',
        sortable: true,
        style: 'width: 50px',
        filter: 'FilterDate'
      },
      authStore.user.is_customer ? null : {
        name: 'full_name',
        label: 'کاربر',
        field: 'user_name',
        sortable: true,
        filter: {
          type: 'FilterInput',
          relation: 'user',
        },
      }
    ].filter(f => f);
    const url = '/production/production_order';

    const $q = useQuasar();

    const afterSubmit = () => {
      if (authStore.user.is_customer) {
        // $q.dialog({
        //   title: "ارسال به کارخانه جهت صدور فاکتور",
        //   cancel: true,
        // })
        //   .onOk(okAfterSubmit)
        //print_order_ref.value.showDialogPrint()
      }
    }
    const okAfterSubmit = () => {
      print_order_ref.value.showDialogPrint()
    }
    return {
      afterSubmit,
      print_order_ref,
      url,
      columns,
      checkPermission,
      checkRole,
      authStore,
      // async changeStatus(item, status, callback = () => { }) {
      //   $q.dialog({
      //     title: "مطمئن هستید؟",
      //     cancel: true,
      //   })
      //     .onOk(async () => {
      //       await api.post(url + '/' + item.id + '/changeStatus', { status })
      //       callback();
      //     })
      // }
    };
  },
};
</script>
