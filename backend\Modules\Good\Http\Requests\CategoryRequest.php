<?php

namespace Modules\Good\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CategoryRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required',
            'type' => 'required',
            'group_id' => 'required',
        ];
    }
    public function messages()
    {
        return [
            'name.required' => trans('validation.is_required'),
            'type.required' => trans('validation.is_required'),
            'group_id.required' => trans('validation.is_required'),
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
