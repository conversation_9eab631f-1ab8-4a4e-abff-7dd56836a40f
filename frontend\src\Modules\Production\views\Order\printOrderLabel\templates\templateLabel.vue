<template>
    <section>
        <div style="width:10.16cm;padding:10px">
            <table class="asd w-full text-center">
                <tbody>




                    <tr >
                        <td rowspan="2">
                            <div style="position: relative;"><img :src="logo_src" /></div>
                        </td>

                        <td class="td-title text-xs text-left h-1" style="border-bottom: 0;">
                            تاریخ سفارش
                        </td>
                        <td rowspan="2">
                            <div v-html="qrcode_svg" style="height: 50px;display: flex;"></div>
                        </td>
                    </tr>
                    <tr>

                        <td style="width: 4cm;border-top: 0;">
                            <strong style="font-size:16px"> {{
                                delivery_date
                            }}</strong>
                        </td>
                    </tr>

                    <tr class="tr-title text-xs text-left h-1">

                        <td>
                            مدل
                        </td>

                        <td>
                            کد نماینده
                        </td>
                        <td>
                            نام مشتری
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong style="font-size:16px">{{ model }}</strong>
                        </td>
                        <td>
                            <strong style="font-size:16px">{{ party_name }}</strong>
                        </td>

                        <td>
                            <strong style="font-size:16px">{{ customer_name }}</strong>
                        </td>
                    </tr>
                    <tr class="tr-title text-xs text-left h-1">

                        <td>
                            ابعاد
                        </td>

                        <td>
                            ضخامت ورق
                        </td>
                        <td>
                            نوع جنس
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong style="font-size:16px">{{ width }} x {{ height }}</strong>
                        </td>
                        <td>
                            <strong style="font-size:16px">{{ thickness }}</strong>
                        </td>

                        <td>
                            <strong style="font-size:16px">{{ type }}</strong>
                        </td>
                    </tr>
                    <tr class="tr-title text-xs text-left h-1">
                        <td>
                            جهت درب
                        </td>
                        <td>
                            قابلبه
                        </td>
                        <td>
                            روکش
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong style="font-size:16px">
                                {{ align }}
                            </strong>
                        </td>
                        <td>
                            <strong style="font-size:16px">
                                {{ gablabeh == "دارد" ? "با قابلبه" : "بدون قابلبه" }}
                            </strong>
                        </td>

                        <td>
                            <strong style="font-size: 16px;">{{ color }}</strong>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3" style="font-size: 12px;line-height: 12px;text-align: right;">
                            توضیحات: <b>{{ description }}</b>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </section>
</template>
<script setup>
console.log('sssssssssssssssss')
import { defineProps, ref } from 'vue'
import QRCode from "qrcode";
import { onMounted } from 'vue';
const props = defineProps({
    logo_src: String,
    code: String,
    party_name: String,
    customer_name: String,
    delivery_date: String,
    item: Object
})
const {
    logo_src,
    //code,
    party_name,
    customer_name,
    delivery_date,
    order_description,
    item
} = props;
const serial = 'code';
const code = '';
const qrcode = '';

const color = item.attributes?.pvcColor !== 'two_color' || !item.attributes_label?.coverFrontDoor ? item.attributes_label?.pvcColor : ["<b>نما: </b>" + item.attributes_label?.coverFrontDoor, "<b>پشت: </b>" + item.attributes_label?.coverBackDoor].join('<br>');

const model = item.good.name;
const height = item.attributes.doorHeight;
const width = item.attributes.doorWidth;
const type = item.attributes_label.typeMaterialDoor;
const align = item.attributes_label.doorAlign;
const lock = item.attributes_label.hasLockPlace;
const type_label = item.attributes_label.typeEdge;
const thickness_wall = item.attributes_label.doorThickness;
const gablabeh = item.attributes_label.hasEdgeOfDoor;
const align_gablabeh =
    item.attributes.alignEdgeOfDoor !== "threeSide" &&
        item.attributes.hasEdgeOfDoor
        ? item.attributes_label.alignEdgeOfDoor
        : undefined;

const barjestegi = item.attributes_label.hasBarjestegi;
const vahed = item.attributes.vahed;
const thickness =
    item.attributes_label.centerLayerThickness ||
    item.attributes_label.sheetCNCThickness;
const description = [
    item.attributes.doorLengeh == 1
        ? ""
        : item.attributes_label.doorLengeh,
    order_description ?? "",
    item.description ?? "",
]
    .filter((f) => f)
    .join(" - ")



let customer = [];
if (party_name) customer.push(party_name);
if (customer_name) customer.push(customer_name);
if (vahed) customer.push(vahed);


const createQrCode = async () => {
    qrcode_svg.value = await QRCode.toString(`${item.id}`, {
        type: "svg",
        margin: 0,
    })
};
const qrcode_svg = ref(null);
onMounted(createQrCode);


</script>