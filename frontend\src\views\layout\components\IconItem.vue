<template>
    <q-item-section side>
      <q-avatar rounded style="font-size: 35px;">
        <q-icon :name="icon" />
      </q-avatar>
    </q-item-section>
    <q-item-section>
      {{ label }}
    </q-item-section>
  </template>
  
  <script setup>
  import { defineProps } from 'vue';
  
  const props = defineProps({
    icon: {
      type: String,
      required: true,
    },
    label: {
      type: String,
      required: true,
    },
  });
  </script>
  