<?php

use App\Models\User;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\BuyAndSell\Entities\Order;
use Modules\BuyAndSell\Entities\Party;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create($this->table(), function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('code')->unique()->nullable();
            $table->String('type');
            $table->String('status');
            $table->foreignIdFor(Party::class)->constrained(Party::getTableName());
            $table->string('customer_name');
            $table->text('description')->default('')->nullable();
            // $table->foreignIdFor(User::class)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists($this->table());
    }

    public function table(){
        $prefix = config('buyandsell.prefix');
        return ($prefix ? $prefix .'__' : '').'orders';
    }
};
