import door from "./door";
import frame from "./frame";
import rokoob from "./rokoob";
export function doTypeDoor(group_id) {
    if ([8, 18].includes(group_id)) {
        return "RAPINGI";
    } else if ([16, 20].includes(group_id)) {
        return "CNC";
    } else if ([24].includes(group_id)) {
        return "KOMODI";
    }
    else if ([19].includes(group_id)) {
        return "MAGHTA";
    }
}
export function computeProductionOrderItem(items) {
    return items.map((item) => {
        if (item.good?.group?.key == "door") {
            item.typeDoor = doTypeDoor(item.good?.group_id);


            Object.assign(
                item.attributes_label,
                door(
                    { goodId: item.good_id, typeDoor: item.typeDoor },
                    item.attributes,
                    item.count
                )
            );
            if (item.attributes.hasFrame)
                Object.assign(
                    item.attributes_label,
                    frame(item.attributes, item.count)
                );

            if (item.attributes.hasRokoob)
                Object.assign(
                    item.attributes_label,
                    rokoob(item.attributes, item.count)
                );
        }
        if (item.good?.group?.key == "frame") {
            Object.assign(item.attributes_label, frame(item.attributes, item.count));
        }
        if (item.good?.group?.key == "rokoob") {
            Object.assign(item.attributes_label, rokoob(item.attributes, item.count));

        }

        // console.log(item)

        return item;
    });
}
export function computeProductionOrder(item, good) {
    item = JSON.parse(JSON.stringify(item))
    // console.log(item, good)
    if (good?.group?.key == "door") {
        item.typeDoor = doTypeDoor(good?.group_id);

 
        Object.assign(
            item.attributes,
            door(
                { goodId: item.good_id, typeDoor: item.typeDoor },
                item.attributes,
                item.count
            )
        );
        if (item.attributes.hasFrame)
            Object.assign(
                item.attributes,
                frame(item.attributes, item.count)
            );

        if (item.attributes.hasRokoob)
            Object.assign(
                item.attributes,
                rokoob(item.attributes, item.count)
            );
    }
    if (good?.group?.key == "frame") {
        Object.assign(item.attributes, frame(item.attributes, item.count));
    }
    if (good?.group?.key == "rokoob") {
        Object.assign(item.attributes, rokoob(item.attributes, item.count));

    }
    return item;

}

export function cmFormat(number) {
    number = Math.ceil(number);
    return `<span dir="ltr">${number.toString().replace(/\B(?=(\d{2})+(?!\d))/g, ",") + (number < 100 ? ' cm' : '')}</span>`;
    let res = [];
    if (Math.floor(number / 100))
        res.push(
            Math.floor(number / 100)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
        );
    res.push(number % 100);
    return res.join(",");
}