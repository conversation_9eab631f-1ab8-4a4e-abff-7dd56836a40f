import { <PERSON>View, UserView } from './views';
import { iconImage } from '@/helpers'
import { RoleForm, UserForm, PartyOnlineView, PartyOnlineForm } from './views/index';

export default {
    path: '/management',
    name: 'management',
    meta: {
        title: 'تنظیمات',
        icon: iconImage('industry'),
        //permissions: 'menu_management',
        //role: 'admin'
    },
    children: [
        {
            path: 'users',
            name: 'users',
            // component: UserView,
            meta: {
                title: 'کاربران',
                icon: iconImage('users'),
            },
            children: [
                {
                    path: '',
                    name: 'users.index',
                    component: UserView,
                    meta: {
                        title: 'کاربران',
                        icon: iconImage('users'),
                        permissions: 'users',

                    },
                },
                {
                    path: 'create',
                    name: 'users.create',
                    component: UserForm,
                    hidden: true,
                    meta: {
                        title: 'ایجاد کاربر',
                        icon: 'add',
                        permissions: 'users.create',

                    },
                },
                {
                    path: ':id/edit',
                    name: 'users.edit',
                    component: UserForm,
                    props: true,
                    hidden: true,
                    meta: {
                        title: 'ویرایش کاربر',
                        icon: 'edit',
                        permissions: 'users.edit',

                    },
                },

            ],

        },

        {
            path: 'parties_online',
            name: 'parties_online',
            //component: PartyOnlineView,
            meta: {
                title: 'نمایندگان آنلاین',
                icon: iconImage('userOnline'),
            },
            children: [
                {
                    path: '',
                    name: 'parties_online.index',
                    component: PartyOnlineView,
                    meta: {
                        title: 'نمایندگان آنلاین',
                        icon: iconImage('userOnline'),
                        permissions: 'parties_online',

                    },
                },
                {
                    path: ':id/edit',
                    name: 'parties_online.edit',
                    component: PartyOnlineForm,
                    props: true,
                    hidden: true,
                    meta: {
                        title: 'ویرایش نماینده آنلاین',
                        icon: 'edit',
                        permissions: 'parties_online.edit',

                    },
                },
            ],

        },
        {
            path: 'parties_online/:id/edit',
            name: 'parties_online.edit',
            component: PartyOnlineForm,
            props: true,
            hidden: true,
            meta: {
                title: 'ویرایش نماینده آنلاین',
                icon: 'edit',
                permissions: 'parties_online.edit',

            },
        },
        {
            path: 'roles',
            name: 'roles',
            //component: RoleView,
            meta: {
                title: 'سمت های کاربری',
                icon: iconImage('privacy'),
                permissions: 'roles',
            },
            children: [
                {
                    path: '',
                    name: 'roles.index',
                    component: RoleView,
                    meta: {
                        title: 'سمت های کاربری',
                        icon: iconImage('privacy'),
                        // permissions: 'manage_role',
                    },
                },
                {
                    path: 'create',
                    name: 'roles.create',
                    component: RoleForm,
                    hidden: true,
                    meta: {
                        title: 'ایجاد سمت',
                        icon: 'add',
                        permissions: 'roles.create',
                    },
                },
                {
                    path: ':id/edit',
                    name: 'roles.edit',
                    component: RoleForm,
                    props: true,
                    hidden: true,
                    meta: {
                        title: 'ویرایش سمت',
                        icon: 'edit',
                        permissions: 'roles.edit',
                    },
                },
            ],
        },

    ],
};


