<?php

use Illuminate\Support\Facades\Route;
use Modules\Good\Http\Controllers\AttributeController;
use Modules\Good\Http\Controllers\AttributeItemController;
use Modules\Good\Http\Controllers\AttributeItemMetaController;
use Modules\Good\Http\Controllers\CategoryController;
use Modules\Good\Http\Controllers\CategoryGoodController;
use Modules\Good\Http\Controllers\ConditionController;
use Modules\Good\Http\Controllers\GoodController;
use Modules\Good\Http\Controllers\GroupController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->group(function () {
    //Route::apiResource('category', CategoryController::class);
    Route::japiResource('good', GoodController::class);//->middleware(['permission:good_read']);
    Route::get('good/{good}/selectForOrder', [GoodController::class, 'selectForOrder']);
    //Route::apiResource('category.good', CategoryGoodController::class);
    Route::get('group/attributes', [GroupController::class, 'attributes'])->middleware(['permission:good_read']);

    Route::apiResource('group', GroupController::class)->middleware(['permission:good_read']);
    Route::japiResource('attribute', AttributeController::class);
    Route::apiResource('condition', ConditionController::class);
    Route::apiResource('attribute.item', AttributeItemController::class);
    Route::apiResource('attributeItem/{attributeItem}/meta', AttributeItemMetaController::class);
});
