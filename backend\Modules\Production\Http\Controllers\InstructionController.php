<?php

namespace Modules\Production\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\API\BaseController;
use App\Repositories\ModelRepositoryInterface;
use Modules\Production\Entities\Instruction;
use stdClass;

class InstructionController extends BaseController
{
    public function __construct(ModelRepositoryInterface $repository)
    {
        $repository->model = Instruction::class;
        $this->repository = $repository;
    }
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return $this->repository->getAll();
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        $model = Instruction::create($request->all());
        // $model->attributes()->sync(!$request->has('attributes') ? [] : collect($request->input('attributes'))->map(function ($m) {
        //     return ['options' => $m];
        // }));

        // $attributes = $model->attributes()->get();
        // $model->attributes = count($attributes) > 0 ? $attributes->groupBy('id')->map(function ($m) {
        //     return $m[0]->pivot->options;
        // }) : new stdClass();
        return $this->handleResponse($model, trans('request.done'));
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(Instruction $instruction)
    {
        // $attributes = $instruction->attributes()->get();

        // $instruction->attributes = count($attributes) > 0 ? $attributes->groupBy('id')->map(function ($m) {
        //     return $m[0]->pivot->options;
        // }) : new stdClass();
        return $this->repository->get($instruction);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, Instruction $instruction)
    {
         $instruction->update($request->all());
        // $instruction->attributes()->sync(!$request->has('attributes') ? [] : collect($request->input('attributes'))->map(function ($m) {
        //     return ['options' => $m];
        // }));

        // $attributes = $instruction->attributes()->get();
        // $instruction->attributes = count($attributes) > 0 ? $attributes->groupBy('id')->map(function ($m) {
        //     return $m[0]->pivot->options;
        // }) : new stdClass();
        return $this->handleResponse($instruction, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy(Instruction $instruction)
    {
        return $this->repository->delete($instruction);
    }

    public function search()
    {
        $query = Instruction::query();
        return $this->handleResponse($query->when(request()->input('group_id'), function ($query, $group_id) {
            $query->where('group_id', $group_id);
        })->get());

        
    }
}
