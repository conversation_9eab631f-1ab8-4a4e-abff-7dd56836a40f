<template>
  <q-table ref="tableRef" :grid="grid && $q.screen.xs" :grid-header="grid" :columns="columns.filter(f => checkPermission(f.permissions))"
    :visible-columns="grid && $q.screen.xs ? columns.map(m => m.name) : columns.filter(f => !f.hide_table).map(m => m.name)"
    :loading="requestStore.loading" :flat="$q.screen.xs"  card-container-class="gap-5">
    <template v-for="(_, slot) in $slots" v-slot:[slot]="props">
      <slot :name="slot" v-bind="props" />
    </template> 

    <template v-slot:body="props">
      <q-tr :props="props">
        <q-td v-if="props.selected !== undefined">
          <j-checkbox v-model="props.selected" v-bind="props" />
          <q-btn v-if="$slots.expand" size="sm" outline :color="props.expand ? 'red' : 'primary'" dense
            @click="props.expand = !props.expand" :icon="props.expand ? 'expand_less' : 'expand_more'" class="ml-3" />
        </q-td>
        <template v-for="col in props.cols" :key="col.name" :props="props">
          <slot v-if="$slots['body-cell-' + col.name]" :name="'body-cell-' + col.name"
            v-bind="{ ...props, value: col.value, col }" />
          <q-td v-else :props="{ ...props, value: col.value, col }">
            {{ col.value }}
          </q-td>
        </template>
      </q-tr>
      <q-tr v-if="$slots.expand" v-show="props.expand" :props="props">
        <slot name="expand" v-bind="props" />
      </q-tr>

    </template>


    <template v-slot:header-cell="props">
      <q-th :props="props">
        <span :class="{ 'text-vertical': props.col.vertical }">
          {{ props.col.label }}
        </span>
      </q-th>
    </template>
    <template v-slot:item="props">
      <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
        <div :class="props.selected ? 'bg-cyan-100' : ''" class="border  border-slate-300 rounded-md">
          <q-card-section v-if="props.selected !== undefind" @click="props.selected = !props.selected">
            <q-checkbox dense v-model="props.selected" :label="props.cols
              .filter((f) => f.checkbox_label)
              .map((m) => m.value)
              .join(' - ')
            " />
          </q-card-section>
          <q-separator />
          <q-list dense class="[&>*:nth-child(even)]:bg-gray-800 [&>*:nth-child(even)]:bg-opacity-10">
            <q-expansion-item v-if="props.cols.filter((col) => col.name !== 'desc' && col.summary).length > 0"
              group="somegroup" header-style="padding:0;display:grid" expand-icon-class="items-center">
              <template v-slot:header>
                <q-list dense class="w-full [&>*:nth-child(even)]:bg-gray-800 [&>*:nth-child(even)]:bg-opacity-10">

                  <q-item v-for="col, index in props.cols.filter((col) => col.name !== 'desc' && col.summary)"
                    :key="col.name">
                    <q-item-section>
                      <q-item-label>{{ col.label }}</q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <slot v-if="$slots[`item-${col.name}`]" :name="`item-${col.name}`" v-bind="props" />
                      <q-item-label v-else>{{ col.value }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
                <q-space />
              </template>
              <q-list dense class="w-full [&>*:nth-child(even)]:bg-gray-800 [&>*:nth-child(even)]:bg-opacity-10">

                <q-item v-for="col, index in props.cols.filter((col) => col.name !== 'desc' && !col.summary)"
                  :key="col.name" dense>
                  <q-item-section>
                    <q-item-label>{{ col.label }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <slot v-if="$slots[`item-${col.name}`]" :name="`item-${col.name}`" v-bind="props" />
                    <q-item-label v-else class="text-md">{{ col.value }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
              <slot v-if="$slots.expand" name="expand_grid" v-bind="props" />
            </q-expansion-item>
            <template v-else>
              <q-item v-for="col in props.cols.filter((col) => col.name !== 'desc')" :key="col.name">
                <q-item-section>
                  <q-item-label>{{ col.label }}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <slot v-if="$slots[`item-${col.name}`]" :name="`item-${col.name}`" v-bind="props" />
                  <q-item-label v-else class="text-md">{{ col.value }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>

          </q-list>
        </div>
      </div>
    </template>
    <template v-if="grid && $q.screen.xs && filter_columns.length > 0" #header>
      <j-btn flat dense icon="tune" label="فیلترها" title="فیلتر" class="m-3"
        @click="showFilter = true">
        <q-badge v-if="Object.values(filters).map(f => f.value).filter(f => f && f.length > 0).length > 0" color="red"
          rounded floating />
      </j-btn>
      <q-separator />
      <j-dialog v-model="showFilter">
        <q-card class="p-5">
          <template v-for="(column, index) in filter_columns" :key="index">
            <div v-if="column.filter" class="col-6 text-center border p-3">
              <component v-if="typeof column.filter == 'object'" :is="column.filter.type"
                v-model:value="filters[column.column].value" :label="column.label">
                {{ column.label }}
              </component>
              <component v-else-if="typeof column.filter == 'string'" :is="column.filter"
                v-model:value="filters[column.column].value" :label="column.label" :filter-option="column.filterOption">
                {{ column.label }}
              </component>
            </div>
          </template>
        </q-card>
      </j-dialog>
    </template>
    <template v-else-if="grid && $q.screen.xs" #header></template>
    <template v-for="(column, index) in columns.filter((f) => f.hasImage)" #[`body-cell-${column.name}`]="props"
      :key="index">
      <q-td>
        <image-by-text :src="column.image(props.row)" :text="props.value" />
      </q-td>
    </template>

    <template v-for="(column, index) in filter_columns" #[`header-cell-${column.column}`]="props" :key="index">
      <q-th :props="typeof props !== 'array'
        ? undefined
        : props.map((m) => {
          m.col.sortable = false;
          return m;
        })
      ">
        <component v-if="typeof column.filter == 'object'" :is="column.filter.type"
          v-model:value="filters[column.column].value" :label="column.label" :filter-option="column.filterOption"
          v-model:size="column.filterSize">
          <div :class="{ 'text-vertical': props.col.vertical }" class="mx-auto">
            {{ props.col.label }}
          </div>
        </component>
        <component v-else-if="typeof column.filter == 'string'" :is="column.filter"
          v-model:value="filters[column.column].value" :label="column.label" :filter-option="column.filterOption"
          v-model:size="column.filterSize">
          {{ props.col.label }}
        </component>
      </q-th>
    </template>
  </q-table>
</template>
<script>
import { computed, ref, watch } from "vue";
import { useRequestStore } from "@/stores";
import { useRoute, useRouter } from "vue-router";
import ImageByText from "@/Modules/Production/views/productionOrder/components/ImageByText.vue";
import { checkPermission } from "@/helpers";

export default {
  components: { ImageByText },
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    filters: Object,
    formOption: Array,
    grid: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, context) {
    const requestStore = useRequestStore();
    const route = useRoute();
    const router = useRouter();

    const filters = ref(props.filters ?? {});
    watch(
      () => filters.value,
      (newVal) => {
        let new_query = JSON.parse(JSON.stringify(route.query));
        Object.keys(newVal).forEach((f) => {
          if (newVal[f].value) {
            if (typeof newVal[f].value == "object")
              new_query[f] = JSON.stringify(newVal[f].value);
            else new_query[f] = newVal[f].value;
          } else {
            delete new_query[f]
          }
        });
        router.replace({ path: route.path, query: new_query });
        context.emit("update:filters", newVal);
        context.emit("filters:update", newVal);
        context.emit("onFilter", newVal);
      },
      { deep: true }
    );

    const filter_columns = computed(() => {
      return (
        props.columns
          .filter((f) => f.filter)
          .map((m) => {
            if (typeof route.query[m.name] == 'string' && JSON.valid(route.query[m.name]))
              route.query[m.name] = JSON.parse(route.query[m.name]);
            if (filters.value[m.name]) return { ...m, column: m.name };

            if (typeof m.filter == "object") {
              filters.value[m.name] = {
                ...m.filter,
                value: route.query[m.name] ?? undefined,
              };
            } else {
              filters.value[m.name] = {
                type: m.filter,
                value: route.query[m.name] ?? undefined,
              };
            }
            if (typeof m.filterOption == "string")
              filters.value[m.name].filterOption =
                props.formOption[m.filterOption];
            return { ...m, column: m.name };
          }) ?? []
      );
    });
    const tableRef = ref(null)

    return {
      tableRef,
      filter_columns,
      filters,
      requestStore,
      showFilter: ref(false),
      checkPermission,
    };
  },
};
</script>