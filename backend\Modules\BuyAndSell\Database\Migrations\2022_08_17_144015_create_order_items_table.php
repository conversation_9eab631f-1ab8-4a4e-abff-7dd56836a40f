<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\BuyAndSell\Entities\Order;
use Modules\Good\Entities\Good;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create($this->table(), function (Blueprint $table) {
            $table->id();
            $table->string('code');
            $table->foreignIdFor(Order::class)->constrained(Order::getTableName());
            $table->foreignIdFor(Good::class)->constrained(Good::getTableName());
            $table->json('attributes')->nullable();
            $table->unsignedInteger('count');
            $table->unsignedDecimal('price', 13);
            $table->unsignedDecimal('total_price', 13);
            $table->json('price_details')->nullable();
            $table->text('description')->default('')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists($this->table());
    }

    public function table()
    {
        $prefix = config('buyandsell.prefix');
        return ($prefix ? $prefix . '__' : '') . 'order_items';
    }
};
