/** @type {import('tailwindcss').Config} */

const defaultTheme = require("tailwindcss/defaultTheme");
const colors = require("tailwindcss/colors");

module.exports = {
    content: ["./src/**/*.vue", "./src/**/*.js", "./index.html"],
    darkMode: ["class", ".body--dark"],
    theme: {
        screens: {
            xs: "475px",
            //...defaultTheme.screens,
            sm: '600px',
            md: '1024px',
            lg: '1440px',
            xl: '1920px',
            '2xl': '2048px',
            'max-xs': { 'max': '475px' },
            'max-sm': { 'max': '600px' },
            'max-md': { 'max': '1024px' },
            'max-lg': { 'max': '1440px' },
            'max-xl': { 'max': '1920px' },
            'max-2xl': { 'max': '2048px' },
        },
        fontSize: {
            ...defaultTheme.fontSize,
            "2xs": "0.4rem",
        },
        colors: {
            ...colors,
            page: {
                DEFAULT: "#f3f4f6",
                dark: "#0e2338",
            },
            white: "#ffffff",
        },
        extend: {
            screens: {
                print: { raw: "print" },
            },
            maxHeight: {
                'inherit': 'inherit',
            },
        },
    },
};
