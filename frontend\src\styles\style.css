body {
    font-family:
        Vazirmatn FD,
        sans-serif !important;
}
/* 
.q-field--float .q-field__label {
    font-size: 15px;
    font-weight: 900;
}

.q-field__label {
    color: rgba(0, 0, 0, 0.8);
}



.q-field__bottom {
    padding-top: 4px;
} */

.q-field__label {
    top: 13px;
}
.q-item__section--avatar {
    min-width: 0 !important;
}
[dir="rtl"] .q-table .text-right {
    text-align: right;
}

[dir="rtl"] .q-table .text-left {
    text-align: left;
}

.q-date__calendar-item > div,
.q-date__calendar-item button {
    width: 100%;
    border-radius: 5px;
}

/* .q-textarea.q-field--labeled .q-field__control-container {
    padding-top: 14px !important;
} */
/* 
.q-field--auto-height.q-field--labeled .q-field__control-container {
    padding-top: 0 !important;
}

.q-field--labeled .q-field__native,
.q-field--labeled .q-field__prefix,
.q-field--labeled .q-field__suffix {
    padding-bottom: 0 !important;
} */
/* 
.q-field--labeled.q-field--dense .q-field__native,
.q-field--labeled.q-field--dense .q-field__prefix,
.q-field--labeled.q-field--dense .q-field__suffix,
.q-field--auto-height.q-field--dense.q-field--labeled .q-field__control-container {
    padding-top: 0 !important;
}

.q-field--float .q-field__label {
    transform: translateY(-26px) scale(0.75) !important;
    background-color: white;
    z-index: 1;
    padding: 0 4px !important;
}

.q-field--dense.q-field--float .q-field__label {
    transform: translateY(-18px) scale(0.75) !important;
}

form .q-field {
    margin-top: 8px;
} */

.even-highlight-2 > div > table > tbody > tr:nth-of-type(4n + 3),
.even-highlight tr:nth-of-type(2n),
.odd-highlight tr:nth-of-type(2n + 1) {
    background: #e0e0e0;
}

/* tr.selected td:after {
    background-color: rgb(0 208 255 / 15%) !important;
} */
/* 
.q-field__control {
    background: #f3f3f3;
}

.q-table__container {
    background: white;
} */

/* .q-table__grid-content.row>div:nth-of-type(2n) {
    background: #f5f5f5;
} */
/* 
.highlight .q-field__control {
    background-color: #d2a81921;
}

.interal-border tr:first-of-type td {
    border-top: 0;
}

.interal-border tr:last-of-type td {
    border-bottom: 0;
}

.interal-border tr td:last-of-type {
    border-right: 0;
}

.interal-border tr td:first-of-type {
    border-left: 0;
} */



section,
.page-break {
    page-break-after: always;
    break-after: page;
}

.page-break-2n:nth-of-type(2n) {
    page-break-after: always;
    break-after: page;
}

.j-table {
    font-size: 13px;
}

.j-table td:not(.no-border),
.j-table th:not(.no-border) {
    border: 1px solid black;
    padding: 2px;
}

.no-border-all,
.no-border-all td,
.no-border-all th {
    border: none !important;
}

.tr-title *, .td-title {
    height: 0px;
}

.asd {
    line-height: 22px;
    font-size: 15px;
}

table.asd td {
    border: 0.3px solid black;
    padding: 2px;
    line-height: 20px;
}

td.highlight {
    background: black;
    color: white;
}

tr.highlight {
    background: #dddddd;
}

.tr-title + tr td {
    border-top: 0;
    line-height: 18px;
    padding: 0;
}

tr.tr-title td,.td-title {
    text-align: left;
    font-size: 10px;
    border-bottom: 0;
    line-height: 10px;
}

.tr-title + tr {
    height: 20%;
}

.td-value,
.td-value * {
    font-size: 12px;
    text-align: center !important;
}

.verticalTableHeader {
    text-align: center;
    white-space: nowrap;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
}

.verticalTableHeader div {
    margin: 0 -100%;
    display: inline-block;
}

.verticalTableHeader div:before {
    content: "";
    width: 0;
    padding-top: 110%;
    /* takes width as reference, + 10% for faking some extra padding */
    display: inline-block;
    vertical-align: middle;
}

.verticalTable {
    text-align: center;
    table-layout: fixed;
}

body.desktop .q-toggle--dense:not(.disabled):focus .q-toggle__thumb:before,
body.desktop .q-toggle--dense:not(.disabled):hover .q-toggle__thumb:before {
    opacity: 0.4;
}

body.desktop .q-focusable:focus > .q-focus-helper,
body.desktop .q-manual-focusable--focused > .q-focus-helper,
body.desktop .q-hoverable:hover > .q-focus-helper {
    box-shadow:
        0 3px 5px -1px rgb(0 0 0 / 20%),
        0 5px 8px rgb(0 0 0 / 14%),
        0 1px 14px rgb(0 0 0 / 12%);
}

.q-btn--actionable.q-btn--standard:active:before,
.q-btn--actionable.q-btn--standard:focus:before,
.q-btn--actionable.q-btn--standard.q-btn--active:before,
.q-btn--actionable.q-btn--standard.q-btn--focus:before {
    box-shadow:
        0 3px 5px -1px rgb(0 0 0 / 20%),
        0 5px 8px rgb(0 0 0 / 14%),
        0 1px 14px rgb(0 0 0 / 12%);
}

.q-table tbody td {
    font-size: 14px !important;
}
/* 
tr.selected td {
    background: #c2f3ff;
} */

.q-table th {
    font-size: 14px !important;
}

.hidden-arrow input[type="number"]::-webkit-inner-spin-button,
.hidden-arrow input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.important.h-6 div {
    height: 24px !important;
}

.important.h-8 div {
    height: 2rem !important;
}

.text-vertical {
    writing-mode: vertical-rl;
    transform: rotate(-180deg);
}

.p-td-0 td {
    padding: 0 !important;
}

/* 



.is-loading::before {
    background: linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%);
    background-size: 200% 100%;
    animation: 1.5s shine linear infinite;
}

@keyframes shine {
    to {
        background-position-x: -200%;
    }
} */

.table-out-none-border tr:nth-of-type(1) td,
.table-out-none-border tr:nth-of-type(1) th {
    border-top: 0 !important;
}

.table-out-none-border tr:nth-last-of-type(1) td,
.table-out-none-border tr:nth-last-of-type(1) th {
    border-bottom: 0 !important;
}

.table-out-none-border td:nth-of-type(1),
.table-out-none-border th:nth-of-type(1) {
    border-left: 0 !important;
}

.table-out-none-border td:nth-last-of-type(1),
.table-out-none-border th:nth-last-of-type(1) {
    border-right: 0 !important;
}

.q-field span {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.table-border-radius {
    border: 2px solid;
    border-radius: 10px;
    overflow: hidden;
}

.table-border-radius th,
.table-border-radius td {
    border-bottom: 0 !important;
}

.table-border-radius tr:first-child th {
    border-top: 0;
}

.table-border-radius tr:last-child td {
    border-bottom: 0;
}

.table-border-radius td:first-child,
.table-border-radius th:first-child {
    border-left: 0;
}

.table-border-radius td:last-child,
.table-border-radius th:last-child {
    border-right: 0;
}

/* width */
.custom-scrollbar::-webkit-scrollbar,
.custom-scrollbar ::-webkit-scrollbar {
    width: 30px;
    height: 30px;
}

/* Track */
.custom-scrollbar::-webkit-scrollbar-track,
.custom-scrollbar ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border: 1px solid #c1c1c1;
    box-shadow: inset 0 0 20px #c1c1c1;
}

/* Handle */
.custom-scrollbar::-webkit-scrollbar-thumb,
.custom-scrollbar ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
}

/* Handle on hover */
.custom-scrollbar::-webkit-scrollbar-thumb:hover,
.custom-scrollbar ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.box-shadow-center {
    box-shadow:
        0 0px 5px 0 rgb(0 0 0 / 0.1),
        0 0px 2px -1px rgb(0 0 0 / 0.1);
}

[dir="rtl"] .q-badge--floating {
    right: -3px;
    left: auto !important;
}

[dir="ltr"] .q-badge--floating {
    right: auto !important;
    left: -3px;
}

legend {
    font-size: 15px;
    padding: 0 0.5rem;
    font-weight: bolder;
}

/* .q-table thead {
    background-color: #e0e0e0;
} */

.card_style {
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.12);
}

.no-shadow,
.shadow-0 {
    box-shadow: none !important;
}

.disabled,
.disabled *,
[disabled],
[disabled] * {
    opacity: 0.6;
}
.navigation-item {
    border-radius: 5px;
    min-height: 44px !important;
}

.dark_active,
.tab-active {
    background: linear-gradient(
        72.47deg,
        #88e9ff 22.16%,
        #d0f6ff 76.47%
    ) !important;
    /* box-shadow:0 2px 6px #0000002e; */
    color: #009fc6 !important;
    font-weight: bold;
}

/* body .q-field--dense .q-field__control,
body .q-field--dense .q-field__marginal {
    height: 25px;
}

[dir="rtl"] .q-table--dense .q-table th:first-child,
[dir="rtl"] .q-table--dense .q-table td:first-child {
    padding-right: 8px;
} */

/* [dir="rtl"] .q-table--vertical-separator td,
[dir="rtl"] .q-table--vertical-separator th,
[dir="rtl"] .q-table--cell-separator td,
[dir="rtl"] .q-table--cell-separator th {
    border-left-width: 1px;
    border-right-width: 0;
} */

/* .q-table--dense .q-table thead tr,
.q-table--dense .q-table tbody tr,
.q-table--dense .q-table tbody td {
    height: 35px;
} */

#body-page > * {
    max-height: inherit;
    overflow-y: auto;
}
.my-sticky-header-table {
    /* height or max-height is important */
    max-height: inherit;
}

.my-sticky-header-table thead tr th {
    position: sticky;
    z-index: 1;
    top: 0;
    background: #fff;
}

.my-sticky-header-table tbody tr:first-child td {
    position: sticky;
    z-index: 1;
    top: 31px;
    background: #fff;
}

.my-sticky-header-table td:first-child {
    z-index: 1;
}

.my-sticky-header-table td:first-child,
.my-sticky-header-table th:first-child {
    position: sticky;
    left: 0;
}

/* this is when the loading indicator appears */
.my-sticky-header-table.q-table--loading thead tr:last-child th {
    /* height of all previous header rows */
    top: 48px;
}

/* prevent scrolling behind sticky top row on focus */
.my-sticky-header-table tbody {
    /* height of all previous header rows */
    scroll-margin-top: 48px;
}

.my-sticky-header-column-table {
    /* height or max-height is important */
    max-height: inherit;

    /* specifying max-width so the example can
      highlight the sticky column on any browser window */
    max-width: inherit;
}

.my-sticky-header-column-table td:first-child {
    /* bg color is important for td; just specify one */
    background-color: #fff;
}

.my-sticky-header-column-table tr th {
    position: sticky;
    /* higher than z-index for td below */
    z-index: 2;
    /* bg color is important; just specify one */
    background: #fff;
}

/* this will be the loading indicator */
.my-sticky-header-column-table thead tr:last-child th {
    /* height of all previous header rows */
    top: 48px;
    /* highest z-index */
    z-index: 3;
}

.my-sticky-header-column-table thead tr:first-child th {
    top: 0;
    z-index: 1;
}

.my-sticky-header-column-table tr:first-child th:first-child {
    /* highest z-index */
    z-index: 3;
}

.my-sticky-header-column-table td:first-child {
    z-index: 1;
}

.my-sticky-header-column-table td:first-child,
.my-sticky-header-column-table th:first-child {
    position: sticky;
    left: 0;
}

/* prevent scrolling behind sticky top row on focus */
.my-sticky-header-column-table tbody {
    /* height of all previous header rows */
    scroll-margin-top: 48px;
}

.topRowTable .q-table__top {
    background: var(--q-primary);
    color: white;
    min-height: 44px;
}
@media print {
    .print-hidden {
        display: none;
    }

    .q-table--dense .q-table th,
    .q-table--dense .q-table td {
        padding: 0 5px !important;
    }
    .q-table--dense .q-table thead tr,
    .q-table--dense .q-table tbody tr,
    .q-table--dense .q-table tbody td {
        height: 20px;
    }

    .q-table tbody td, .q-table th {
        font-size: 12px !important;
    }
    
}

.q-table{
    border-collapse: collapse;
}


::-webkit-scrollbar {
    height: 14px;
    width: 14px;
    background: transparent;
    z-index: 12;
    overflow: visible
}

::-webkit-scrollbar-thumb {
    width: 10px;
    background-color: #c5c5c5;
    border-radius: 10px;
    z-index: 12;
    border: 4px solid rgba(0,0,0,0);
    background-clip: padding-box;
    -webkit-transition: background-color .28s ease-in-out;
    transition: background-color .28s ease-in-out;
    margin: 4px;
    min-height: 32px;
    min-width: 32px
}

::-webkit-scrollbar-thumb:hover {
    background-color: #00b4ff;
    cursor:default
}

.j-whitespace-normal th, .j-whitespace-normal td {
    white-space: normal !important;
}