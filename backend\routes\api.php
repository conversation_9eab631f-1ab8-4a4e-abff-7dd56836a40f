<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\CustomerAuthController;
use App\Http\Controllers\API\GuaranteeAuthController;
use App\Http\Controllers\API\MembershipController;
use App\Http\Controllers\API\MembershipRequestController;
use App\Http\Controllers\API\ProvinceController;
use App\Http\Controllers\API\RoleController;
use App\Http\Controllers\API\UserController;
use App\Http\Controllers\ChangeAttributeController;
use App\Http\Controllers\FileUploadController;
use App\Http\Controllers\GuaranteeController;
use Illuminate\Support\Facades\Storage;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware(['auth:sanctum'])->group(function () {

    Route::get('/user', [UserController::class, 'show_current']);
    Route::put('/user', [UserController::class, 'update_current']);
    Route::japiResource('/users', UserController::class);//->middleware(['permission:menu_management']);

    Route::japiResource('/roles', RoleController::class);//->middleware(['permission:menu_management']);

    Route::apiResource('/provinces', ProvinceController::class);
    Route::apiResource('/memberships', MembershipController::class);
    Route::get('/membership-request', [MembershipRequestController::class, 'get']);
    Route::post('/membership-request', [MembershipRequestController::class, 'request']);
    //Route::apiResource('/membership-request', MembershipRequestController::class);

    Route::post('upload-file', [FileUploadController::class, 'fileUpload'])->name('fileUpload');

});


//Route::post('customer_login', [CustomerAuthController::class, 'login']);





Route::post('uploader', function (Request $request) {

    if ($request->hasFile('image')) {
        $image      = $request->file('image');
        $fileName   = time() . '.' . $image->getClientOriginalExtension();

        $img = Image::make($image->getRealPath());
        $img->resize(120, 120, function ($constraint) {
            $constraint->aspectRatio();
        });

        $img->stream(); // <-- Key point

        //dd();
        Storage::disk('local')->put('images/1/smalls' . '/' . $fileName, $img, 'public');
    }
});
// Route::post('register', [AuthController::class, 'register']);

Route::get('changeAttribute', [ChangeAttributeController::class, 'index']);


Route::group(['domain' => env('VITE_CRM_DOMAIN')], function () {
   // Route::post('sendVerifyCode', [CustomerAuthController::class, 'sendVerifyCode']);
    Route::post('login', [CustomerAuthController::class, 'login']);
});
Route::post('login', [AuthController::class, 'login']);
