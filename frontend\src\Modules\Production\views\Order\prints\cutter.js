import { cmFormat, getField, getField<PERSON>y<PERSON><PERSON> } from ".";

export default function (data, attributes, template) {
    const goods = Object.values(
        data.items
            .filter((f) => [8, 16, 17, 18, 20, 24].includes(f.good.group_id))
            .map((m) => ({
                ...m,
                key:
                    [m.attributes.typeMaterialDoor,
                    m.attributes.sheetCNCThickness,
                    m.attributes.centerLayerThickness,
                    m.attributes.hasEdgeOfDoor,
                    m.attributes.alignEdgeOfDoor,
                    m.good.group_id].join('_'),
                width: m.attributes.doorWidth * 1,
                height: m.attributes.doorHeight * 1,
                label:
                    (m.attributes.typeMaterialDoor == "mdf" ? "ام دی اف" : "فومیزه") +
                    " " +
                    (m.attributes_label.centerLayerThickness ??
                        m.attributes_label.sheetCNCThickness) +
                    " میل",
            }))
            .group("key")
    );

    const columns = [
        {
            label: "پشت درپشت",
            field: (row) => {
                return `<strong>${row?.attributes_label?.poshtdarposhtSize?.height}</strong><span style="margin: 5px;color: red;">x</span><strong>${row?.attributes_label?.poshtdarposhtSize?.width}</strong>`;
                console.log(row.attributes_label)
                let width = row.attributes.doorWidth * 1;
                let height = row.attributes.doorHeight * 1;
                if (row.attributes.hasEdgeOfDoor) {
                    switch (row.attributes.alignEdgeOfDoor) {
                        case "threeSide":
                            width += 3;
                            height += 2;
                            break;
                        case "fourSide":
                            width += 3;
                            height += 3;
                            break;
                        case "onlyTopSide":
                            width += 1.5;
                            height += 2;
                            break;
                        case "onlyLolaSide":
                            width += 2;
                            height += 1.5;
                            break;
                        case "onlyLockSide":
                            width += 2;
                            height += 1.5;
                            break;
                        case "onlyTopAndLolaSide":
                            width += 2;
                            height += 2;
                            break;
                        case "onlyTopAndLockSide":
                            width += 2;
                            height += 2;
                            break;
                        case "onlyLockAndLolaSide":
                            width += 3;
                            height += 1.5;
                            break;
                    }

                    if (row.attributes.doorLengeh * 1) {
                        switch (row.attributes.doorLengeh * 1) {
                            case 1.5:
                            case 2:
                                width += 2;
                                break;
                            case 3:
                                width += 4;
                                break;
                            case 4:
                                width += 6;
                                break;
                        }
                    }
                } else {
                    let per = 1.5;
                    if ([16, 20].includes(row.good.group_id)) {
                        height += 1;
                        per = 1;
                    } else height += 1.5;

                    if (row.attributes.doorLengeh * 1) {
                        switch (row.attributes.doorLengeh * 1) {
                            case 1:
                                width += per;
                                break;
                            case 1.5:
                            case 2:
                                width += 2 * per;
                                break;
                            case 3:
                                width += 3 * per;
                                break;
                            case 4:
                                width += 4 * per;
                                break;
                        }
                    }
                }

                return `<strong>${height}</strong><span style="margin: 5px;color: red;">x</span><strong>${width}</strong>`;
            },
            width: "100px",
        },

        {
            label: "توضیحات",
            field: (row) => {
                let res = "";
                const lengeh =
                    row.attributes.doorLengeh == 1
                        ? false
                        : row.attributes_label.doorLengeh;
                if (lengeh) res += lengeh;
                return res + ' ' + (row.description ?? '');
            },
        },

        {
            label: "تعداد",
            field: (row) => {
                return row.count > 1 ? row.count : "";
            },
            width: "70px",
        },
        {
            label: "قابلبه",
            field: (row) => {
                const gablabe = row.attributes_label.alignEdgeOfDoor;
                return row.attributes.hasEdgeOfDoor
                    ? row.attributes.alignEdgeOfDoor !== 'threeSide'
                        ? gablabe
                        : "دارد"
                    : "";
            },
            width: "70px",
        },
        {
            label: "اندازه",
            field: (row) => {
                return `<strong>${row.attributes.doorHeight}</strong><span style="margin: 5px;color: red;">x</span><strong>${row.attributes.doorWidth}</strong>`;
            },
            width: "100px",
        },
    ];

    /*const columns = [
        {
            field: (row) => row.label,
            label: "",
            hiddenable: true,
        },
        ...Object.keys(group).map((color_id) => {
            const find = attributes[
                attributes.findIndex((f) => f.id == 3)
            ].items.findIndex((f) => f.id + "" == color_id + "");
            if (find >= 0) {
                return {
                    // field: (row) => row.label,
                    label: attributes[attributes.findIndex((f) => f.id == 3)]
                        .items[find].name,
                    field: (row) => row.field({ color_id }, group[color_id]),
                };
            }
            return "";
        }),
    ];*/

    const rows_data = goods.map((good) => {
        let res = ``;

        res += `<table class="j-table w-full text-center odd-highlight" style="margin-top:5px">
                    <tr>
                    ${columns
                .map((column) => {
                    return `<th>${column.label}</th>`;
                })
                .join("")}
                        <th rowspan="50" width="130px" style="font-size:14px">${good[0].good.group.name +
            "<br>" +
            '<span style="font-size:16px">' +
            good[0].label +
            "</span>" +
            "<br>" +
            "<span>" +
            good.reduce((a, b) => a + b.count, 0) +
            " عدد</span>"
            }</th>
                    </tr>
                    
                    ${Object.values(
                good
                    .map((m) => ({
                        ...m,
                        key:
                            m.height +
                            "_" +
                            m.width +
                            "_" +
                            m.attributes.hasEdgeOfDoor +
                            "_" +
                            m.attributes.alignEdgeOfDoor,
                    }))
                    .group("key")
            )
                .map((m) => ({
                    ...m[0],
                    count: m.reduce((a, b) => a + b.count, 0),
                }))
                .sortDesc("width", "height")
                .map((row) => {
                    return (
                        `<tr>` +
                        columns
                            .map((column) => {
                                return `<td${column.width
                                    ? ` width="${column.width}"`
                                    : ""
                                    }>${column.field(row)}</td>`;
                            })
                            .join("") +
                        `</tr>`
                    );
                })
                .join("")}
                    
                </table>`;

        return {
            content: `<tr>
            <td colspan="10" class="no-border">${res}</td>
        </tr>`,
            key: good[0].good.group,
        };
    });
    // const rows_data = rows
    //     .map((row) => {
    //         if (
    //             columns.filter((f) => !(f.hiddenable || f.field(row))).length ==
    //             0
    //         )
    //             return `<tr>${columns
    //                 .map((column) => {
    //                     return "<td>" + column.field(row) + "</td>";
    //                 })
    //                 .join("")}</tr>`;
    //     })
    //     .join("");

    return rows_data
        .map((row) => {
            let res = "";

            res += `
    
            <table class="j-table w-full text-center mb-3">
                
               
                <thead>
                            <tr>
                                <td class="no-border p-0">
                                    <table class="j-table w-full text-center odd-highlight">
                                        <tr class="h-8">
        
                                            <td class="no-border w-1/3 bg-white">
                                                <div class="text-left text-sm">
                                                    <b>نام نمایندگی: </b> ${data.party_name
                }
                                                </div>
                                                <div class="text-left text-sm">
                                                    <b>نام مشتری: </b> ${data.customer_name
                }
                                                </div>
                                            </td>
                                            <td class="no-border w-1/3 bg-white">
                                                <img src="/images/logo-factor.png" class="h-10 m-auto" />
                                                <h6 style="text-align: center;font-weight: bolder;">برگه تولید ${template.station.name
                }</h6>
                                            </td>
                                            <td class="no-border w-1/3 bg-white text-right">
                                                <table class="ml-auto mr-0 text-sm">
                                                    <tr>
                                                        <th class="no-border bg-white">شناسه سفارش:</th>
                                                        <td class="no-border bg-white">${data.code
                }</td>
        
                                                    </tr>
                                                    <tr>
                                                        <th class="no-border bg-white">تاریخ سفارش:</th>
        
                                                        <td class="no-border bg-white">${data.submit_date
                }</td>
                                                    </tr>
                                                    <tr>
                                                        <th class="no-border bg-white">تاریخ سفارش:</th>
        
                                                        <td class="no-border bg-white">${data.delivery_date
                }</td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
        
        
                                    </table>
                                </td>
                            </tr>
                           
        
                        </thead>
        
                <tbody>
                <tr>
                    <td colspan="10" class="no-border">
                    ${row.content}
                    </td>
                </tr>
                
                ${data.description
                    ? `
                <tr>
                    <th colspan="10">توضیحات</th>
                    </tr>
                <tr>
                    <td colspan="10">${data.description}</td>
                </tr>`
                    : ""
                }
                </tbody>
            </table>
        `;
            return res;
        })
        .join("");

    //     return `

    //     <table class="j-table w-full text-center mb-3">
    //         <thead style="background:white">
    //             <tr><th colspan="10" class="no-border"><h6 style="text-align: center;font-weight: bolder;">برگه تولید ${
    //                 template.station.name
    //             }</h6></th></tr>
    //             <tr class="highlight">
    //                 <th>شناسه سفارش</th>
    //                 <th>نام نمایندگی</th>
    //                 <th>نام مشتری</th>
    //                 <th>تاریخ سفارش</th>
    //                 <th>تاریخ سفارش</th>
    //             </tr>
    //             <tr>
    //                 <td>${data.code}</td>
    //                 <td>${data.party_name}</td>
    //                 <td>${data.customer_name}</td>
    //                 <td>${data.submit_date}</td>
    //                 <td>${data.delivery_date}</td>
    //             </tr>
    //             ${
    //                 data.description
    //                     ? `
    //             <tr>
    //                 <th colspan="10">توضیحات</th>
    //                 </tr>
    //             <tr>
    //                 <td colspan="10">${data.description}</td>
    //             </tr>`
    //                     : ""
    //             }

    //         </thead>

    //         <tbody>
    //         ${rows_data}

    //         </tbody>
    //     </table>

    //     `;
}
