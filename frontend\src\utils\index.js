import { DATE_FORMATS } from '@/constants';
import moment from 'moment-jalaali';

/**
 * Format currency with Persian digits and separators
 */
export const formatCurrency = (amount, currency = 'ریال') => {
    if (amount === null || amount === undefined) return '-';

    const formatted = new Intl.NumberFormat('fa-IR').format(amount);
    return currency ? `${formatted} ${currency}` : formatted;
};

/**
 * Format phone number for better display
 */
export const formatPhoneNumber = (phone) => {
    if (!phone) return '-';

    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, '');

    // Format Iranian mobile numbers (09xxxxxxxxx)
    if (cleaned.length === 11 && cleaned.startsWith('09')) {
        return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;
    }

    // Format Iranian landline numbers (021xxxxxxxx)
    if (cleaned.length === 11 && (cleaned.startsWith('021') || cleaned.startsWith('026'))) {
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 7)} ${cleaned.slice(7)}`;
    }

    // Return original if doesn't match patterns
    return phone;
};

/**
 * Format file size in human readable format
 */
export const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 بایت';

    const k = 1024;
    const sizes = ['بایت', 'کیلوبایت', 'مگابایت', 'گیگابایت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Format date using Jalali calendar
 */
export const formatDate = (date, format = DATE_FORMATS.JALALI_SHORT) => {
    if (!date) return '-';
    return moment(date).format(format);
};

/**
 * Get relative time in Persian
 */
export const getRelativeTime = (date) => {
    if (!date) return '-';

    const now = moment();
    const target = moment(date);
    const diff = now.diff(target, 'minutes');

    if (diff < 1) return 'همین الان';
    if (diff < 60) return `${diff} دقیقه پیش`;
    if (diff < 1440) return `${Math.floor(diff / 60)} ساعت پیش`;
    if (diff < 43200) return `${Math.floor(diff / 1440)} روز پیش`;

    return formatDate(date);
};

/**
 * Debounce function
 */
export const debounce = (func, wait, immediate = false) => {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
};

/**
 * Throttle function
 */
export const throttle = (func, limit) => {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
};

/**
 * Deep clone object
 */
export const deepClone = (obj) => {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
};

/**
 * Generate unique ID
 */
export const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

/**
 * Check if value is empty
 */
export const isEmpty = (value) => {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string') return value.trim() === '';
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === 'object') return Object.keys(value).length === 0;
    return false;
};

/**
 * Capitalize first letter
 */
export const capitalize = (str) => {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * Convert Persian/Arabic digits to English
 */
export const toEnglishDigits = (str) => {
    if (!str) return str;

    const persianDigits = '۰۱۲۳۴۵۶۷۸۹';
    const arabicDigits = '٠١٢٣٤٥٦٧٨٩';
    const englishDigits = '0123456789';

    let result = str.toString();

    for (let i = 0; i < 10; i++) {
        result = result.replace(new RegExp(persianDigits[i], 'g'), englishDigits[i]);
        result = result.replace(new RegExp(arabicDigits[i], 'g'), englishDigits[i]);
    }

    return result;
};

/**
 * Convert English digits to Persian
 */
export const toPersianDigits = (str) => {
    if (!str) return str;

    const englishDigits = '0123456789';
    const persianDigits = '۰۱۲۳۴۵۶۷۸۹';

    let result = str.toString();

    for (let i = 0; i < 10; i++) {
        result = result.replace(new RegExp(englishDigits[i], 'g'), persianDigits[i]);
    }

    return result;
};

/**
 * Validate Iranian national code
 */
export const validateNationalCode = (code) => {
    if (!code || code.length !== 10) return false;

    const check = parseInt(code[9]);
    let sum = 0;

    for (let i = 0; i < 9; i++) {
        sum += parseInt(code[i]) * (10 - i);
    }

    const remainder = sum % 11;

    if (remainder < 2) {
        return check === remainder;
    } else {
        return check === 11 - remainder;
    }
};

/**
 * Download file from blob
 */
export const downloadFile = (blob, filename) => {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
};

/**
 * Copy text to clipboard
 */
export const copyToClipboard = async (text) => {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (err) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        return true;
    }
};

/**
 * Get file extension from filename
 */
export const getFileExtension = (filename) => {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
};

/**
 * Check if file type is allowed
 */
export const isFileTypeAllowed = (file, allowedTypes) => {
    return allowedTypes.includes(file.type);
};

/**
 * Scroll to element
 */
export const scrollToElement = (element, behavior = 'smooth') => {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }

    if (element) {
        element.scrollIntoView({ behavior });
    }
};
