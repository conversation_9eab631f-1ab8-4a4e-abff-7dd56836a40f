<template>
    <cutter v-if="['cutMiddleCNCSheet', 'cutMiddleSheet'].includes(name)" v-bind="{ data, attributes, name }" />
    <slitter v-if="[
    //'pvcMiddleSheet','pvcMiddleCNCSheet',
    'pvcSlitterSilicon', 'pvcSlitterRapood', 'pvcSlitterDorfak'].includes(name)"
        v-bind="{ data, attributes, name }" />
</template>
<script>
import cutter from './cutter/index.vue';
import slitter from './slitter/index.vue';
export default {
    components: {
        cutter,
        slitter,
    },
    props: {
        name: String,
        data: {
            type: Array,
            default: [],
        },
        attributes: {
            type: Array,
            default: [],
        },
    },
    setup() {

    }
}
</script>