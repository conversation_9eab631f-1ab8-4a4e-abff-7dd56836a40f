<template>
    <j-select ref="_ref" v-model="data" :options="sortBy ? options.sortBy(sortBy) : options" option-label="name"
        @filter="filterFn" use-input :input-debounce="searchLocal ? 0 : 1000">
        <template v-for="_, slot in $slots" v-slot:[slot]="props" :key="slot">
            <slot :name="slot" v-bind="props" :key="slot" />
        </template>
        <template v-slot:before>
            <j-image-viewer v-if="options.findIndex(f => f.id == data) >= 0"
                :src="options[options.findIndex(f => f.id == data)].image_src" />
        </template>
        <template v-slot:option="scope">
            <q-item v-bind="scope.itemProps">
                <!-- <q-item-section avatar>
                    <j-image-viewer :src="scope.opt.image_src" />
                </q-item-section> -->
                <q-item-section>
                    <q-item-label>{{ scope.opt.name }}</q-item-label>
                </q-item-section>
            </q-item>
        </template>
        <template #after>
            <q-btn v-if="$slots.addNew" round dense flat icon="add" @click="add = true" />
            <slot name="after" />
            <j-dialog-bar v-model="add" @hide="getData">
                <slot v-if="$slots.addNew" name="addNew" />
            </j-dialog-bar>
        </template>
    </j-select>
</template>
<script>
import { api } from '@/boot/axios';
import { ref, watch } from 'vue';

export default {
    props: {
        value: String | Number,
        url: String,
        searchLocal: {
            type: Boolean,
            default: false,
        },
        field: {
            type: String,
            default: 'name'
        },
        sortBy: {
            type: String,
        },
        params: {
            type: Object,
            default: () => { }
        },

    },
    setup(props, { emit }) {
        const options = ref([])
        const orginal = ref([])
        const data = ref(props.value ?? '')
        watch(() => props.value, (newVal) => {
            data.value = newVal;
        })
        watch(() => data.value, (newVal) => {
            //console.log(4444444444444, newVal)
            emit('update:value', newVal)
        })
        api.get(props.url, { params: props.params }).then(res => {
            orginal.value = res.result.map(m => { return { ...m, value: m.id } });
            options.value = res.result.map(m => { return { ...m, value: m.id } });
        })
        const filterFn = (val, update, abort) => {
            update(() => {

                if (!props.searchLocal && val !== '')
                    api.get(props.url, { params: { ...props.params, [props.field]: val } }).then(res => {
                        options.value = res.result.map(m => { return { ...m, value: m.id } });
                    })
                else
                    options.value = orginal.value.filter(v => v[props.field].toLowerCase().indexOf(val.toLowerCase()) > -1)

            })
        }

        const _ref = ref(null)
        return {
            options,
            filterFn,
            add: ref(false),
            data,
            _ref,
            ref_instance: _ref,
            focus: () => _ref.value.focus()

        }
    },
}
</script>