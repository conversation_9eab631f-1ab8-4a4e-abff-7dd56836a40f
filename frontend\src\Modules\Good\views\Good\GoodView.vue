<template>
  <j-table-data-crud url="/good/good" :columns="columns" routeName="products" :permissions="permissions">

    <template v-slot:body-cell-image="props">
      <q-td :props="props">
       <j-image-viewer :src="props.value" class="w-24" fullHeight />
      </q-td>
    </template>
  </j-table-data-crud>
</template>

<script setup>

const permissions = {
  create: 'products.create',
  edit: 'products.edit',
  delete: 'products.delete',
}
const columns = [
  {
    name: 'name',
    required: true,
    label: 'نام',
    field: 'name',
    sortable: true,
    filterType: 'text'
  },
  // {
  //   name: 'type',
  //   required: true,
  //   label: 'نوع',
  //   field: 'label_type',
  //   sortable: true,
  //   filterType: 'select',
  //   filterOptions: [
  //     {
  //       label: 'محصول',
  //       value: 'PRODUCT',
  //     },
  //   ]
  // },
  {
    name: 'group',
    required: true,
    label: 'گروه',
    field: 'label_group',
    sortable: true,
  },
  {
    name: 'image',
    label: 'عکس',
    field: 'image_src',
  },
]


</script>
