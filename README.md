# پروژه ERP ایران پارس

این پروژه به دو بخش جداگانه تقسیم شده است:

## ساختار پروژه

```
├── backend/          # Laravel API Backend
├── frontend/         # Vue.js Frontend
└── README.md
```

## Backend (Laravel API)

### نصب و راه‌اندازی

```bash
cd backend
composer install
cp .env.example .env
php artisan key:generate
php artisan migrate
php artisan serve --port=8000
```

Backend روی پورت 8000 اجرا می‌شود: `http://localhost:8000`

### ویژگی‌های Backend

- Laravel 12
- API-only structure
- Laravel Sanctum برای احراز هویت
- Laravel Modules برای ساختار ماژولار
- CORS تنظیم شده برای frontend

## Frontend (Vue.js + Quasar)

### نصب و راه‌اندازی

```bash
cd frontend
npm install
npm run dev
```

Frontend روی پورت 5173 اجرا می‌شود: `http://localhost:5173`

### ویژگی‌های Frontend

- Vue.js 3
- Quasar Framework
- Pinia برای مدیریت state
- Axios برای ارتباط با API
- Vue Router برای routing
- RTL Support

## تنظیمات

### Backend Environment Variables

فایل `.env` در پوشه backend:
```
APP_URL=http://localhost:8000
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=raasa
```

### Frontend Environment Variables

فایل `.env` در پوشه frontend:
```
VITE_API_BASE_URL=http://localhost:8000/api
VITE_APP_NAME="پنل ایران پارس"
```

## اجرای پروژه

### حالت توسعه (Development)

برای اجرای همزمان backend و frontend در حالت توسعه:

```bash
# Terminal 1 - Backend (پورت 8000)
cd backend
php artisan serve --host=api.erp.test --port=8000

# Terminal 2 - Frontend Panel (پورت 3000)
cd frontend
npm run dev:panel

# Terminal 3 - Frontend CRM (پورت 3001) - اختیاری
cd frontend
npm run dev:crm
```

## Multi-Domain Frontend

فرانت‌اند برای دو دامنه جداگانه طراحی شده است:

- **Panel**: `panel.erp.test:3000` - پنل مدیریت اصلی
- **CRM**: `crm.erp.test:3001` - سیستم مدیریت ارتباط با مشتری

### تنظیم DNS محلی

برای توسعه محلی، دامنه‌ها را در فایل hosts اضافه کنید:

**Windows**: `C:\Windows\System32\drivers\etc\hosts`
**Linux/Mac**: `/etc/hosts`

```
127.0.0.1 panel.erp.test
127.0.0.1 crm.erp.test
127.0.0.1 api.erp.test
```

### دستورات اجرا

```bash
# اجرای پنل
npm run dev:panel

# اجرای CRM
npm run dev:crm

# ساخت هر دو
npm run build:prod
```

**آدرس‌ها در حالت توسعه:**
- Backend API: `http://api.erp.test:8000`
- Frontend Panel: `http://panel.erp.test:3000`

### حالت پروداکشن (Production)

برای اجرای در حالت پروداکشن:

```bash
# Backend (پورت 80)
cd backend
cp .env.production .env
php artisan serve --host=api.erp.test --port=80

# Frontend (پورت 80)
cd frontend
npm run build:prod
npm run preview:prod
```

**آدرس‌ها در حالت پروداکشن:**
- Backend API: `http://api.erp.test`
- Frontend Panel: `http://panel.erp.test`

## ماژول‌ها

- **Good**: مدیریت کالاها و گروه‌ها
- **Production**: مدیریت تولید
- **BuyAndSell**: خرید و فروش
- **Inventory**: انبارداری

## توسعه

برای توسعه پروژه:

1. Backend: فایل‌های PHP در پوشه `backend/`
2. Frontend: فایل‌های Vue در پوشه `frontend/src/`
3. API endpoints: `backend/routes/api.php` و ماژول‌ها

## نکات مهم

- Backend فقط API ارائه می‌دهد
- Frontend به صورت SPA اجرا می‌شود
- CORS برای ارتباط بین frontend و backend تنظیم شده
- احراز هویت با Laravel Sanctum
