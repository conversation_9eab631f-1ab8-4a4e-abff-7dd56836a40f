<template>
  <q-layout view="lHr lpr lfr">
    <q-header class="md:p-4 bg-transparent">
      <div class="bg-white text-gray-700 md:rounded-md shadow overflow-auto">
        <q-toolbar>
          <q-btn flat @click="drawer = !drawer" round dense icon="menu" />
          <q-toolbar-title>

          </q-toolbar-title>
          <div class="q-gutter-sm row items-center no-wrap">

            <q-avatar round>
              <img src="https://cdn.quasar.dev/img/avatar1.jpg">
              <q-menu class="no-shadow card_style" transition-show="jump-down" transition-hide="jump-up" fit>

                <q-list style="min-width: 200px;" class="shadow-none">

                  <q-item clickable>
                    <q-item-section avatar>
                      <q-avatar>
                        <img src="https://cdn.quasar.dev/img/avatar1.jpg">
                      </q-avatar>
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ authStore?.user?.full_name ?? 'جواد کاظمی' }}</q-item-label>
                      <q-item-label caption>{{ authStore?.user?.role_name ?? 'ادمین' }}</q-item-label>
                    </q-item-section>
                  </q-item>


                  <q-separator />
                  <q-item clickable v-close-popup :to="{ name: 'profile' }">
                    <q-item-section side>
                      <q-avatar rounded style="font-size: 35px;">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                          aria-hidden="true" role="img" width="1em" height="1em" viewBox="0 0 24 24"
                          class="iconify iconify--tabler">
                          <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                            stroke-width="2"
                            d="M8 7a4 4 0 1 0 8 0a4 4 0 0 0-8 0M6 21v-2a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v2">
                          </path>
                        </svg>
                      </q-avatar>

                    </q-item-section>
                    <q-item-section>پروفایل</q-item-section>

                  </q-item>
                  <q-separator />

                  <q-item clickable v-close-popup @click="authStore.logout()">
                    <q-item-section side>
                      <q-avatar rounded style="font-size: 35px;" icon="logout">

                      </q-avatar>

                    </q-item-section>
                    <q-item-section>خروج</q-item-section>

                  </q-item>
                </q-list>

              </q-menu>
            </q-avatar>

          </div>



        </q-toolbar>

        <q-toolbar v-if="$route.meta.toolbar !== false" class="border-t-2"
          style="min-height: 35px;padding-top: 3px;padding-bottom: 3px;">

          <j-icon flat round dense :name="publicStore.icon ?? 'o_assignment'" size="xs" class="mr-2" />
          <div class="text-md font-bold">{{ publicStore.titleWindow ?? '' }}</div>
          <q-space />
          <div class="flex gap-2">
            <component v-if="publicStore.toolbar_component" :is="publicStore.toolbar_component"></component>
            <q-btn flat round dense icon="refresh" @click="publicStore.reloadViewRouter()" title="shift + alt + r"
              @shortkey="publicStore.reloadViewRouter()" size="sm" v-shortkey="['alt', 'shift', 'r']">
              <q-tooltip>
                بارگذاری مجدد
              </q-tooltip>
            </q-btn>
            <q-btn flat round dense icon="arrow_forward" size="sm" @click="publicStore.forward()"
              :disable="!publicStore.canGoForward()">
              <q-tooltip>
                جلو
              </q-tooltip>
            </q-btn>
            <q-btn flat round dense icon="arrow_back" size="sm" @click="publicStore.back()"
              :disable="!publicStore.canGoBack()">
              <q-tooltip>
                برگشت
              </q-tooltip>
            </q-btn>
          </div>


        </q-toolbar>
      </div>

    </q-header>

    <q-drawer v-model="drawer" show-if-above :width="200" :breakpoint="1100" class="shadow">

      <q-scroll-area class="fit">
        <q-list dense class="text-gray-300">
          <template v-for="(route, index) in routes" :key="index">
            <TreeItem v-bind="route" :current-route="currentRouteName" />
          </template>
        </q-list>
      </q-scroll-area>
    </q-drawer>

    <q-page-container>

      <div :class="$q.screen.xs ? '' : 'p-5'">
        <!-- <q-pull-to-refresh disable @refresh="(done) => { publicStore.reloadViewRouter(); done() }"> -->
        <slot :key="publicStore.componentKey" />
        <!-- </q-pull-to-refresh> -->
      </div>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { getUrlAssets } from "@/helpers";
import JIcon from "@/components/JIcon.vue";
// import { routes } from "@/helpers";
import { ref } from "vue";
import TreeItem from "./components/TreeItem.vue";
import { useAuthStore, usePublicStore } from "@/stores";
import { useQuasar } from 'quasar'
import { useRoute } from 'vue-router'
import { computed } from 'vue'

const $q = useQuasar()
const routes = ref([])
const authStore = useAuthStore();
const publicStore = usePublicStore();
const drawer = ref(false);
const logo = getUrlAssets("logo.svg");
const nightMode = ref(false)
const route = useRoute()
const currentRouteName = computed(() => route.name)
authStore.routes().then(res => {
  routes.value = res.filter(f => !(typeof f?.meta?.disable == 'function' ? f?.meta?.disable({ user: authStore.user }) : f?.meta?.disable ?? false))
})

</script>