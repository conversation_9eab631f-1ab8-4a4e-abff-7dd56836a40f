<?php

namespace Modules\Production\Http\Controllers;

use App\Http\Controllers\API\BaseController;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Response;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Validation\Rule;
use Modules\Production\Entities\Party;
use Modules\Production\Http\Requests\PartyRequest;

class PartyController extends BaseController implements HasMiddleware
{

    protected $model = Party::class;

    public static function middleware(): array
    {
        return [
            new Middleware('permission:parties', only: ['index']),
            new Middleware('permission:parties.create', only: ['create', 'store']),
            new Middleware('permission:parties.edit', only: ['show', 'update']),
            new Middleware('permission:parties.delete', only: ['delete', 'destroy']),
            //new Middleware('subscribed', except: ['store']),
        ];
    }

    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return JsonResource::collection(Party::query()->filter()->jpaginate(null, 'id', 'true'));
        // return $this->repository->getAll();
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(PartyRequest $request)
    {
        $data = Party::create($request->all());
        return $this->handleResponse($data, trans('request.done'));
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(Party $party)
    {
        return $this->handleResponse([
            'form' => $party,
            'formOptions' => [

            ],
        ]);
    }

    public function create()
    {
        return $this->handleResponse([
            'formOptions' => [],
        ]);
    }

    public function edit(Party $party)
    {
        return $this->handleResponse([
            'form' => $party,
            'formOptions' => [],
        ]);
    }
    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(PartyRequest $request, Party $party)
    {
        $request->validate([
            'full_name' => 'required',
            'mobile_number' => ['required', Rule::unique(Party::class, 'mobile_number')->ignore($party)],
            'confirm_password' => 'required_with:password|same:password',
        ], [
            'mobile_number.unique' => 'قبلا انتخاب شده است',
        ]);
        $input = $request->all();
        if ($request->has('password')) {
            $input['password'] = bcrypt($input['password']);
        }
        $party->update($input);
        return $this->handleResponse($party, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy(Party $party)
    {
        return $this->handleResponse($party->delete(), trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $ids
     * @return \Illuminate\Http\Response
     */
    public function delete(Request $request)
    {
        // اعتبارسنجی ورودی برای اطمینان از اینکه ورودی آرایه‌ای از شناسه‌هاست
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => ['integer', Rule::exists(Party::class, 'id')],
        ], [
            'ids.*.exists' => 'نماینده با شناسه :input یافت نشد یا امکان حذف ندارد.',
        ]);

        // دریافت شناسه‌ها از ورودی
        $ids = $validated['ids'];

        // حذف مدل‌ها با شناسه‌های مشخص شده
        try {
            Party::destroy($ids);
        } catch (\Exception $e) {
            // در صورت بروز خطا، می‌توانید یک پاسخ مناسب ارسال کنید
            return $this->handleError($e, 'امکان حذف وجود ندارد', 500);
        }

        // ارسال پاسخ موفقیت‌آمیز
        return $this->handleResponse(null, trans('request.done')); // response()->json(['message' => 'Records deleted successfully']);
    }

    public function search()
    {
        $data = Party::query()->get();
        return $this->handleResponse($data);
    }
}
