<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\API\BaseController as BaseController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Modules\Production\Entities\Party;

class CustomerAuthController extends BaseController
{

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'mobile_number' => ['required'],
            'password' => ['required'],
        ]);
        $auth = Party::where('mobile_number', $credentials['mobile_number'])->where('enable', true)->first();

        if ($auth && Hash::check($credentials['password'], $auth->password)) {

            //if (Auth::guard('crm')->attempt(array_merge($credentials, ['enable' => true]))) {
            //$auth = Auth::guard('crm')->user();
            $auth->tokens()->delete();
            $success['token'] = $auth->createToken('LaravelSanctumAuth', ['*'], now()->addMinutes(60 * 24))->plainTextToken;
            $success['name'] = $auth->name;
            return $this->handleResponse($success, trans('auth.success'));
        } else {
            return $this->handleError(trans('auth.failed'), ['error' => trans('auth.failed')]);
        }
    }

    // public function loginByPhone(Request $request)
    // {
    //     $credentials = $request->validate([
    //         'mobile' => ['required', 'regex:/^(\+98|0)?9\d{9}$/', 'min:10'],
    //         'password' => ['required', 'numeric'],
    //     ]);

    //     $user = Party::where('phone_number', $request->input('mobile'))->first();
    //     $token = $user->smsTokens()->latest()->pluck('code')->first();

    //     if ($token == $request->input('password')) {
    //         auth()->guard('customer')->login($user);
    //         $auth = Auth::guard('customer')->user();
    //         $auth->tokens()->delete();
    //         $auth->smsTokens()->delete();
    //         $success['token'] =  $auth->createToken('LaravelSanctumAuth')->plainTextToken;

    //         $success['name'] =  $auth->name;

    //         return $this->handleResponse($success, trans('auth.success'));
    //     } else {
    //         return $this->handleError(trans('auth.failed_code'));
    //     }
    // }

    // public function sendVerifyCode(Request $request)
    // {
    //     $credentials = $request->validate([
    //         'mobile' => ['required', 'regex:/^(\+98|0)?9\d{9}$/', 'min:10'],
    //     ]);

    //     $user = Party::firstOrCreate(
    //         [
    //             'phone_number' => $request->input('mobile'),
    //         ]
    //         // , [
    //         //     'full_name' => $request->input('mobile'),
    //         // ]
    //     );

    //     // $token = SmsToken::create([
    //     //     'user_id' => $user->id
    //     // ]);
    //     $token = $user->smsTokens()->create();
    //     $sendCode = $token->sendCode($user->phone_number);
    //     if ($sendCode['ok']) {
    //         return response()->json($sendCode);
    //     }
    //     $token->delete();
    //     return response()->json($sendCode, 400);
    // }

    // public function register(Request $request)
    // {
    //     $validator = Validator::make($request->all(), [
    //         'name' => 'required',
    //         'username' => 'required',
    //         'password' => 'required',
    //         'confirm_password' => 'required|same:password',
    //     ]);

    //     if ($validator->fails()) {
    //         return $this->handleError($validator->errors());
    //     }

    //     $input = $request->all();
    //     $input['password'] = bcrypt($input['password']);
    //     $user = User::create($input);
    //     $success['token'] =  $user->createToken('LaravelSanctumAuth')->plainTextToken;
    //     $success['name'] =  $user->name;

    //     return $this->handleResponse($success, 'User successfully registered!');
    // }
}
