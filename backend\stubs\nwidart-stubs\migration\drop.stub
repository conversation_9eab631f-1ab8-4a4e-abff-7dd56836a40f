<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists($this->table());
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::create($this->table(), function (Blueprint $table) {
            $table->bigIncrements('id');
$FIELDS$
            $table->timestamps();
        });
    }

    public function table(){
        $prefix = config('$LOWER_NAME$.prefix');
        return ($prefix ? $prefix .'___' : '').'$TABLE$';
    }
};
