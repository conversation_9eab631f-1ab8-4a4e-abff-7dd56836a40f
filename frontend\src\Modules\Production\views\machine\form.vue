<template>
    <j-input v-model="form.name" label="نام دستگاه" error-field="name" dense />
    <!-- <div class="grid xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
        <q-checkbox v-for="item, j in additional.attributes" v-model="form.attributes" :label="item.name" :val="item.id"
            :key="j" />
    </div> -->
    <j-table-data :columns="columns" v-model:list="form.problems" row-key="name">
        <template #dialog="{ load, formOption, form }">
            <j-input v-model="form.name" label="نام" dense />
        </template>
    </j-table-data>
</template>
<script>

export default {
    props: {
        form: {
            type: Object,
            default: () => { problems: [] }
        },
        
    },
    setup() {
        const columns = [
            {
                name: 'name',
                required: true,
                label: 'نام خطا',
                field: 'name',
            },
        ]

        return {
            columns
        }
    }
};
</script>