<?php

namespace Modules\Good\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\API\BaseController;
use App\Repositories\ModelRepositoryInterface;
use Modules\Good\Entities\Category;

class CategoryController extends BaseController
{
    public function __construct(ModelRepositoryInterface $repository)
    {
        $repository->model = Category::class;
        $this->repository = $repository;
    }
    
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return $this->repository->getAll([
            'formOption' => [
                'types' => Category::types,
            ],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store()
    {
        return $this->repository->create();
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(Category $category)
    {
        return $this->repository->get($category);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, Category $category)
    {
        return $this->repository->update($category);
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy(Category $category)
    {
        return $this->repository->delete($category);
    }

}
