<template>
    <q-list bordered separator dense>
        <!-- <pre>{{ options }}</pre> -->
        <template v-for="attribute, i in attributes.sort((a,b) => options[a.id]?.sort - options[b.id]?.sort)" :key="i">
            <q-expansion-item group="somegroup" header-class="text-primary">
                <template #header>
                    <q-item-section avatar>
                        <q-checkbox v-model="selected" :label="attribute.name" :val="attribute.id" />
                    </q-item-section>
                    <q-space />
                    <j-input v-if="options[attribute.id]" v-model="options[attribute.id].sort" />

                    <j-select v-if="attribute.type == 'SELECT'" v-model="default_attribute[attribute.key]"
                        :options="attribute.items" option-label="name" option-value="key" search-local hide-bottom-space
                        :label="attribute.name" clearable />
                    <j-select-image v-if="attribute.type == 'SELECT_IMAGE'" v-model="default_attribute[attribute.key]"
                        :options="attribute.items" option-label="name" option-value="key" search-local hide-bottom-space
                        :label="attribute.name" clearable />
                    <j-input v-else-if="attribute.type == 'INPUT'" v-model="default_attribute[attribute.key]"
                        hide-bottom-space :label="attribute.name" clearable />
                    <j-input v-else-if="attribute.type == 'NUMBER'" v-model="default_attribute[attribute.key]"
                        hide-bottom-space :label="attribute.name" type="number" step="0.01" min="0" clearable />
                    <j-toggle v-else-if="attribute.type == 'SWITCH'" v-model="default_attribute[attribute.key]"
                        hide-bottom-space :label="attribute.name" clearable />

                </template>
                <q-card>
                    <!-- <q-card-section>
                        <j-select v-if="attribute.type == 'SELECT'" v-model="default_attribute[attribute.key]" dense
                            :options="attribute.items" option-label="name" option-value="key" search-local
                            hide-bottom-space :label="attribute.name" clearable />
                        <j-select-image v-if="attribute.type == 'SELECT_IMAGE'"
                            v-model="default_attribute[attribute.key]" dense :options="attribute.items"
                            option-label="name" option-value="key" search-local hide-bottom-space
                            :label="attribute.name" clearable />
                        <j-input v-else-if="attribute.type == 'INPUT'" v-model="default_attribute[attribute.key]" dense
                            hide-bottom-space :label="attribute.name" clearable />
                        <j-input v-else-if="attribute.type == 'NUMBER'" v-model="default_attribute[attribute.key]" dense
                            hide-bottom-space :label="attribute.name" type="number" step="0.01" min="0" clearable />
                        <j-toggle v-else-if="attribute.type == 'SWITCH'" v-model="default_attribute[attribute.key]"
                            dense hide-bottom-space :label="attribute.name" clearable />
                    </q-card-section> -->
                    <template v-if="selected.findIndex(f => f == attribute.id) >= 0 && attribute.items.length">
                        <q-separator />
                        <q-card-section>
                            <div class="grid md:grid-cols-4">
                                <q-checkbox v-for="item, j in attribute.items" v-model="options[attribute.id].options"
                                    :label="item.name" :val="item.id" :key="j" />
                            </div>
                        </q-card-section>
                    </template>
                </q-card>
            </q-expansion-item>
        </template>
    </q-list>
</template>
<script>
import { ref, watch } from 'vue'
export default {
    props: {
        value: {
            type: Object,
            default: () => { }
        },
        default_attribute: {
            type: Object,
            default: () => { }
        },
        attributes: {
            type: Array,
            default: () => []
        },
    },
    setup(props, { emit }) {
        const selected = ref([])
        const default_attribute = ref(props.default_attribute ?? {})
        const options = ref(props.value ?? {})

        selected.value = Object.keys(props.value).map(m => m * 1);


        watch(() => props.default_attribute, newVal => {
            default_attribute.value = newVal;
        })

        watch(() => default_attribute.value, newVal => {
            emit('update:default_attribute', newVal)
        }, { deep: true })

        watch(() => options.value, (value) => {
            emit('update:value', value)
        }, { deep: true })
        watch(() => selected.value, () => {
            selected.value.forEach(k => {
                if (!options.value[k] || !options.value[k].options)
                    options.value[k] = { options: [] }
            })
            Object.keys(options.value).forEach(k => {
                if (selected.value.findIndex(f => f == k) < 0)
                    delete options.value[k]
            })
        })
        return {
            selected,
            default_attribute,
            options,
        }
    },
}
</script>