<template>
    <j-form-data url="users" :form="form" hasCreateRoute>
        <template v-slot="{ form, formOptions }">
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <j-input v-model="form.full_name" required label="نام و نام خانوادگی" error-field="full_name"
                    autofocus />
                <j-input v-model="form.username" required label="نام کاربری" error-field="username" />
                <j-input v-model="form.password" :required="!form?.id" :type="isPwd ? 'password' : 'text'"
                    label="کلمه عبور جدید" error-field="password">
                    <template v-slot:append>
                        <q-icon :name="isPwd ? 'visibility_off' : 'visibility'" class="cursor-pointer"
                            @click="isPwd = !isPwd" />
                    </template>
                </j-input>
                <j-input v-model="form.confirm_password" :type="isPwd ? 'password' : 'text'" label="تکرار کلمه
                عبور جدید" error-field="confirm_password">
                    <template v-slot:append>
                        <q-icon :name="isPwd ? 'visibility_off' : 'visibility'" class="cursor-pointer"
                            @click="isPwd = !isPwd" />
                    </template>
                </j-input>
            </div>

            <q-item-label header>سمت</q-item-label>

            <div class="grid gap-2 sm:grid-cols-5">
                <q-checkbox v-for="option, index in formOptions.roles" v-model="form.roles" :val="option.id"
                    :label="option.label" :key="index" />
            </div>
            <q-item-label header>دسترسی</q-item-label>

            <tree :nodes="getFilterPermissions({ form, formOptions })" v-model:ticked="form.permissions" />

            <!-- <q-card flat bordered>
                <q-tabs v-model="tab" inline-label align="left" class="text-primary">
                    <q-tab name="roles" icon="admin_panel_settings" label="سمت ها" />
                    <q-tab name="permissions" icon="key" label="دسترسی ها" />
                </q-tabs>
                <q-separator />

                <q-tab-panels v-model="tab" animated swipeable vertical transition-prev="jump-up"
                    transition-next="jump-up">
                    <q-tab-panel name="roles">
                        
                    </q-tab-panel>

                    <q-tab-panel name="permissions">

                        <tree :nodes="getFilterPermissions({ form, formOptions })" v-model:ticked="form.permissions" />
                       
                    </q-tab-panel>
                </q-tab-panels>
            </q-card> -->
        </template>




    </j-form-data>

</template>

<script setup>
import { ref } from 'vue';
import tree from '../Role/tree.vue';



const form = ref({ roles: [], permissions: [] })


const isPwd = ref(false)
const tab = ref('roles')
const getFilterPermissions = ({ form, formOptions }) => {
    return JSON.parse(JSON.stringify(formOptions.permissions ?? [])).map(permission => {
        const roles = formOptions.roles.filter(f => form.roles && form.roles.includes(f.id));
        if (roles.length > 0 && roles.map(m => m.permissions.map(mm => mm.id)).flat().unique().includes(permission.id)) {
            permission.noTick = true;
            permission.icon = 'done';
        }
        return permission;

    })
}
</script>