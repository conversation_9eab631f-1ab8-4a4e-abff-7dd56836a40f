import { defineAsyncComponent } from 'vue';

// بارگذاری تمامی فایل‌های Vue داخل یک پوشه
const components = import.meta.glob('./*.vue');

export default function registerComponent(app) {
  Object.entries(components).forEach(([path, component]) => {
    // استخراج نام کامپوننت از مسیر فایل
    const name = path.match(/\.\/(.*)\.vue$/)[1];
    app.component(name, defineAsyncComponent(component));
  });
}
