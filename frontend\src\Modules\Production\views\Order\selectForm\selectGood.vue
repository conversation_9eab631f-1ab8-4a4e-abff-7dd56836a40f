<template>
  <!-- {{ conditions }} -->
  <!-- <pre v-if="good" dir="ltr">{{ good.default_attribute }}</pre> -->
  <!-- <pre dir="ltr">hidden_attribute_items{{ hidden_attribute_items }}</pre> -->
  <!-- <pre dir="ltr">hidden_attributes{{ hidden_attributes }}</pre> -->
  <j-dialog-bar dialogStyle="width:100%;max-width: 1024px;">
    <!-- <pre dir="ltr"> {{ form.attributes }} </pre> -->

    <j-form @submit="submit" class="w-full" ref="ref_form">
      <div class="sticky gap-2 py-1 pb-3 px-3 flex top-8 z-10 w-full left-0 right-0 bg-white">
        <!-- {{  form  }} -->
        <select-product ref="ref_good" v-model:value="form.good" url="/good/good/search" hide-bottom-space autofocus
          @update:model-value="(res) => selectGood(res, true)" label="نام کالا" :params="{ is_active: true }" size="sm"
          search-local sort-by="name" :disable="is_edit" class="flex-1" required />
      </div>
      <div class="p-3">
        <div class="grid grid-cols-1 gap-4 divide-y">
          <template v-for="good_attribute in Object.values(
            good_attributes.group('group_name')
          )">
            <div v-if="
              good_attribute.filter(
                (f) =>
                  !hidden_attributes[f.key] && (f.pivot?.showing ?? f.showing)
              ).length > 0
            " class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 pt-5">
              <template v-for="(attribute, key) in good_attribute.filter(
                (f) =>
                  !hidden_attributes[f.key] && (f.pivot?.showing ?? f.showing)
              )" :key="key">
                <j-select v-if="['SELECT'].includes(attribute.type)" v-model="form.attributes[attribute.key]" :bg-color="good.default_attribute &&
                  good?.default_attribute[attribute.key] !== null &&
                  good?.default_attribute[attribute.key] !== undefined &&
                  good.default_attribute[attribute.key] !==
                  form.attributes[attribute.key]
                  ? 'amber-2'
                  : ''
                  " :options="attribute.items.filter(
                    (f) =>
                      !hidden_attribute_items[attribute.key] ||
                      (hidden_attribute_items[attribute.key] &&
                        hidden_attribute_items[attribute.key].includes(f.key))
                  )
                    " option-label="name" option-value="key" search-local hide-bottom-space :label="attribute.name"
                  outlined :required="attribute.pivot.required ?? attribute.required"
                  @update:model-value="(val) => change(attribute.key, val)" />
                <j-select-image v-if="attribute.type == 'SELECT_IMAGE'" v-model:value="form.attributes[attribute.key]"
                  :image-key="(row) => row.data.image" :bg-color="good.default_attribute &&
                    good?.default_attribute[attribute.key] !== undefined &&
                    good.default_attribute[attribute.key] !==
                    form.attributes[attribute.key]
                    ? 'amber-2'
                    : ''
                    " :options="attribute.items.filter((f) => !f.hidden)" option-label="name" option-value="key"
                  search-local hide-bottom-space :label="attribute.name" outlined
                  :required="attribute.pivot.required ?? attribute.required"
                  @update:model-value="(val) => change(attribute.key, val)" />
                <j-input v-else-if="attribute.type == 'INPUT'" v-model="form.attributes[attribute.key]"
                  hide-bottom-space :label="attribute.name" outlined
                  :required="attribute.pivot.required ?? attribute.required" :disable="(is_edit && false) ||
                    disable_attributes.includes(attribute.key)
                    " @update:model-value="(val) => change(attribute.key, val)" :bg-color="good.default_attribute &&
                      good?.default_attribute[attribute.key] !== null &&
                      good?.default_attribute[attribute.key] !== undefined &&
                      good.default_attribute[attribute.key] !==
                      form.attributes[attribute.key]
                      ? 'amber-2'
                      : ''
                      " />
                <j-input v-else-if="attribute.type == 'NUMBER'" v-model="form.attributes[attribute.key]"
                  hide-bottom-space :label="attribute.name" type="number" step="0.1" min="0" outlined
                  :required="attribute.pivot.required ?? attribute.required" :disable="(is_edit && false) ||
                    disable_attributes.includes(attribute.key)
                    " @update:model-value="(val) => change(attribute.key, val)" :bg-color="good.default_attribute &&
                      good?.default_attribute[attribute.key] !== null &&
                      good?.default_attribute[attribute.key] !== undefined &&
                      good.default_attribute[attribute.key] !==
                      form.attributes[attribute.key]
                      ? 'amber-2'
                      : ''
                      " />
                <j-toggle v-else-if="attribute.type == 'SWITCH'" v-model="form.attributes[attribute.key]"
                  hide-bottom-space :label="attribute.name" outlined
                  :required="attribute.pivot.required ?? attribute.required" :disable="(is_edit && false) ||
                    disable_attributes.includes(attribute.key)
                    " @update:model-value="(val) => change(attribute.key, val)" :class="good.default_attribute &&
                      good?.default_attribute[attribute.key] !== null &&
                      good?.default_attribute[attribute.key] !== undefined &&
                      good.default_attribute[attribute.key] !==
                      form.attributes[attribute.key]
                      ? 'bg-amber-2'
                      : ''
                      " />

                <j-toggle v-else-if="attribute.type == 'SWITCH'" v-model="form.attributes[attribute.key]"
                  hide-bottom-space :label="attribute.name" outlined
                  :required="attribute.pivot.required ?? attribute.required" :disable="(is_edit && false) ||
                    disable_attributes.includes(attribute.key)
                    " @update:model-value="(val) => change(attribute.key, val)" :class="good.default_attribute &&
                      good?.default_attribute[attribute.key] !== null &&
                      good?.default_attribute[attribute.key] !== undefined &&
                      good.default_attribute[attribute.key] !==
                      form.attributes[attribute.key]
                      ? 'bg-amber-2'
                      : ''
                      " />
                <j-upload v-else-if="attribute.type == 'FILE'" v-model:value="form.attributes[attribute.key]"
                  :label="attribute.name" auto-upload accept="image/*" url="/api/upload-file" field-name="file"
                  class="w-full"  flat bordered />
              </template>
            </div>
          </template>
        </div>
        <br />
        <j-input v-model="form.description" label="توضیحات کالا" type="textarea" class="col-span-full" outlined />
      </div>
      <div class="sticky bottom-0 left-0 right-0 p-2 text-center z-10 bg-gray-50 dark:bg-gray-800 border-t">
        <!-- <price-good class="w-48" v-model:price="form.price" v-model:price_details="form.price_details"
          v-bind="{ form, good, good_attributes }" /> -->

        <j-btn size="md" :color="is_edit ? 'secondary' : 'primary'" type="submit" class="text-white"
          :label="is_edit ? 'ویرایش' : 'افزودن'" icon="add_shopping_cart" @shortkey="ref_form.submit()"
          v-shortkey="['ctrl', 'space']" />
      </div>
    </j-form>
  </j-dialog-bar>
  <!-- <pre dir="ltr">{{ form.attributes }}</pre> -->
  <!-- <pre dir="ltr">{{ form.price_details }}</pre> -->
</template>

<script>
import { api } from "@/boot/axios";
import { checkPermission } from "@/helpers";
import { ref, watch } from "vue";
import SelectProduct from "./SelectProduct.vue";

import PriceGood from "./priceGood.vue";
import { checkRole } from "@/helpers";
export default {
  components: { SelectProduct, PriceGood },
  setup(props, context) {
    const good = ref({});
    const is_edit = ref(false);
    const form = ref({
      attributes: {},
      good: null,
      count: 1,
    });
    const ref_good = ref(null);
    const ref_form = ref(null);

    const onEdit = (value) => {
      console.log("onEdit");
      is_edit.value = true;
      good.value = {};
      selectGood(value.good);
      form.value = JSON.parse(JSON.stringify(value));
    };

    const onAdd = () => {
      console.log('onAdd')
      reset()
      // ref_good.value.focus();
    };

    const onCopy = (value) => {
      const temp = JSON.parse(JSON.stringify(value));
      delete temp.id;
      onEdit(temp);
      is_edit.value = false;
    };
    const good_attributes = ref([]);
    const disable_attributes = ref([]);
    const hidden_attributes = ref({});


    const selectGood = (value, default_attribute = false) => {
      console.log("selectGood");
      form.value.attributes = {};
      hidden_attributes.value = {};
      if (!value) {
        good.value = {};
        return;
      }
      api.get(`good/good/${value.id}/selectForOrder`).then((res) => {
        good.value = res.result;

        good_attributes.value = JSON.parse(JSON.stringify(good.value.attributes));
        defaultAttribute();
        changeAttributes();

      });
    };

    const reset = () => {
      //console.error('reset form')
      //console.debug('reset form')
      is_edit.value = false;
      form.value = {
        attributes: {},
        count: 1,
      };
      good.value = {};
      good_attributes.value = [];
      //ref_good.value.focus();
    };

    const groupAttributes = [
      {
        key: "byDoor",
        conditions: (good) => good && ["door"].includes(good?.group?.key),
        label: "همراه با",
      },
      {
        key: "frame",
        conditions: (good) =>
          good &&
          (form.value.attributes["hasFrame"] ||
            ["frame"].includes(good?.group?.key)),
        label: "چهارچوب",
      },
      {
        key: "rokoob",
        conditions: (good) => {
          return (
            good &&
            (form.value.attributes["hasRokoob"] ||
              ["rokoob"].includes(good?.group?.key))
          );
        },
        label: "روکوب",
      },
      {
        key: "door",
        conditions: (good) => good && ["door"].includes(good?.group?.key),
        label: (good) => good?.group?.name,
      },

      {
        key: "maghta",
        conditions: (good) => good && ["maghta"].includes(good?.group?.key),
        label: (good) => good?.group?.name,
      },
      {
        key: "zehvar",
        conditions: (good) => {
          return good && ["zehvar"].includes(good?.group?.key);
        },
        label: "زهوار",
      },
      {
        key: "garniz",
        conditions: (good) => {
          console.log("garniz");
          return good && ["garniz"].includes(good?.group?.key);
        },
        label: "قرنیز",
      },
      {
        key: "",
        conditions: (good) =>
          good && ["other", null].includes(good?.group?.key),
        label: (good) => good?.group?.name,
      },
      {
        key: "public",
        label: "سایر",
      },
      {
        conditions: () => checkRole(["admin", "sell_person"]),
        key: "production",
        label: "تولید",
      },
    ];



    const hidden_attribute_items = ref({});

    const defaultAttribute = () => {
      good_attributes.value
        .forEach((e) => {
          //if (newVal && !hidden_attributes.value[e.key]) {
          if (good.value.default_attribute[e.key])
            form.value.attributes[e.key] =
              good.value.default_attribute[e.key];
          // }

        });
    }
    const changeAttributes = function () {
      //console.error("change", good_attributes.value);

      if (good_attributes.value.find(f => f.key == 'hasFrame')) {
        good_attributes.value
          .filter(
            (f) =>
              f.group_name.split(",").includes("frame") && f.key != "hasFrame"
          )
          .forEach((e) => {
            hidden_attributes.value[e.key] = !form.value.attributes["hasFrame"];

            if (!form.value.attributes["hasFrame"]) {
              delete form.value.attributes[e.key];
            }
          });
      }

      if (good_attributes.value.find(f => f.key == 'hasRokoob')) {
        good_attributes.value
          .filter(
            (f) =>
              f.group_name.split(",").includes("rokoob") && f.key != "hasRokoob"
          )
          .forEach((e) => {
            hidden_attributes.value[e.key] = !form.value.attributes["hasRokoob"];

            if (!form.value.attributes["hasRokoob"]) {
              delete form.value.attributes[e.key];
            }
          });
      }

      hidden_attributes.value["widthRabet"] =
        !form.value.attributes["hasRabet"];
      hidden_attributes.value["countRabet"] =
        !form.value.attributes["hasRabet"];
      if (!form.value.attributes["hasRabet"]) {
        delete form.value.attributes["widthRabet"];
        delete form.value.attributes["countRabet"];
      }


      if (form.value.attributes["hasCustomModelDoor"]) {
        console.log("333333333333333")
        hidden_attributes.value["modelDoorCNC"] = true;
        hidden_attributes.value["ModelDoorRapingi"] = true;
        delete hidden_attributes.value["customModelDoor"]
        delete form.value.attributes["modelDoorCNC"];
        delete form.value.attributes["ModelDoorRapingi"];
      } else {
        hidden_attributes.value["customModelDoor"] = true;
        delete hidden_attributes.value["modelDoorCNC"]
        delete hidden_attributes.value["ModelDoorRapingi"]
        delete form.value.attributes["customModelDoor"];
      }


      if (form.value.attributes["hasFrame"]) {
        let thicknessEdgeOfFrame = null;
        switch (form.value.attributes["sheetCNCThickness"]) {
          case "12":
            if (form.value.attributes["hasEdgeOfDoor"])
              thicknessEdgeOfFrame = "4.7";
            else thicknessEdgeOfFrame = "5.5";
            break;
          case "5":
          case "8":
            if (form.value.attributes["hasEdgeOfDoor"])
              thicknessEdgeOfFrame = "4";
            else thicknessEdgeOfFrame = "4.7";
        }
        if (thicknessEdgeOfFrame) form.value.attributes["thicknessEdgeOfFrame"] = thicknessEdgeOfFrame;
      }

      if (["5"].includes(form.value.attributes["sheetCNCThickness"]))
        form.value.attributes["typeMaterialDoor"] = "mdf";


    };

    const change = function (attribute_key, newVal) {
      if (form.value.attributes["hasFrame"]) {

        if (["hasThreshold", "frameHeight"].includes(attribute_key)) {
          form.value.attributes["doorHeight"] =
            form.value.attributes["frameHeight"] * 1 -
            4.5 -
            (form.value.attributes["hasThreshold"] ? 2.5 : 0);
        }
        if (attribute_key == "frameWidth") {
          form.value.attributes["doorWidth"] =
            form.value.attributes["frameWidth"] * 1 - 7.2;
        }

        if (attribute_key == "doorWidth") {
          form.value.attributes["frameWidth"] =
            form.value.attributes["doorWidth"] * 1 + 7.2;
        }
      }

      if (attribute_key == "hasRokoob") {
        if (newVal && form.value.attributes["hasFrame"]) {
          form.value.attributes["hasFrame"] = false;
        }
        good_attributes.value
          .filter(
            (f) =>
              f.group_name.split(",").includes("rokoob") && f.key != "hasRokoob"
          )
          .forEach((e) => {
            hidden_attributes.value[e.key] =
              !form.value.attributes["hasRokoob"];
            if (newVal && !hidden_attributes.value[e.key]) {
              form.value.attributes[e.key] =
                good.value.default_attribute[e.key];
            }
            if (!form.value.attributes["hasRokoob"]) {
              delete form.value.attributes[e.key];
            }
          });
      }

      if (attribute_key == "hasFrame") {
        if (newVal && form.value.attributes["hasRokoob"]) {
          form.value.attributes["hasRokoob"] = false;
        }
        good_attributes.value
          .filter(
            (f) =>
              f.group_name.split(",").includes("frame") && f.key != "hasFrame"
          )
          .forEach((e) => {
            hidden_attributes.value[e.key] = !form.value.attributes["hasFrame"];
            if (newVal && !hidden_attributes.value[e.key]) {
              form.value.attributes[e.key] =
                good.value.default_attribute[e.key];
            }
            if (!form.value.attributes["hasFrame"]) {
              delete form.value.attributes[e.key];
            }
          });
      }
      if (attribute_key == "typeMaterialFrame") {
        form.value.attributes["typeMaterialDoor"] = form.value.attributes["typeMaterialFrame"]
      }

    };

    watch(
      () => form.value.attributes,
      (newVal) => {
        Object.keys(newVal).forEach((key) => {
          changeAttributes();
        });
      },
      {
        deep: true,
      }
    );

    const submit = () => {
      context.emit(
        "confirm",
        {
          ...form.value,
          good: form.value.good.pick(["id", "name"]),
          //count: 1,
        },
        is_edit.value
      );
      reset();
    }

    return {
      checkPermission,
      selectGood,
      submit,
      good,
      form,
      ref_good,
      onEdit,
      onAdd,
      onCopy,
      is_edit,
      ref_form,
      good_attributes,
      hidden_attributes,
      disable_attributes,
      hidden_attribute_items,
      change,
      groupAttributes,
      checkRole,
    };
  },
};
</script>