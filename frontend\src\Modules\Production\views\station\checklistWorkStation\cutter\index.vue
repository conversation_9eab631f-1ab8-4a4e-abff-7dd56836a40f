<template>
    <q-tabs v-model="tab" dense active-color="white" active-bg-color="primary" indicator-color="primary" align="justify"
        narrow-indicator>
        <q-tab v-for="good, index in rows_data" :name="good.key" :label="good.label" :key="index" class="rounded-xl" />
    </q-tabs>
    <q-tab-panels v-model="tab" animated>
        <q-tab-panel v-for="rows, index in rows_data" :name="rows.key" :key="index" style="padding: 0"
            class="shadow-md">
            <div class="grid grid-cols-1 gap-10 w-full mt-3">
                <item v-for="good, index4 in rows.items" :key="index4" v-bind="{ good }"
                    @afterSave="$emit('afterSave')" />
            </div>
        </q-tab-panel>
    </q-tab-panels>
</template>

<script>
import { computed, ref } from 'vue';
import item from './item.vue';
export default {
    components: { item },
    props: {
        data: {
            type: Array,
            default: () => []
        },
    },
    setup(props) {
        const rows_data = computed(() => {
            return Object.values(
                props.data
                    .map((m) => ({
                        ...m,
                        key: [
                            m.attributes.typeMaterialDoor,
                            m.attributes.centerLayerThickness,
                            m.attributes.sheetCNCThickness,
                            // m.group_id,
                        ].join("_"),
                        width: m.attributes.doorWidth,
                        height: m.attributes.doorHeight,
                        label: ([16, 20].includes(m.group_id) ? 'ورق درب CNC ' : 'ورق وسط رپینگی ') +
                            (m.attributes.typeMaterialDoor == "mdf"
                                ? "ام دی اف"
                                : "فومیزه") +
                            " " +
                            (m.attributes_label?.centerLayerThickness ?? m.attributes_label?.sheetCNCThickness) + ' میل',
                    }))
                    .group("key")).map(m => {
                        return {
                            key: m[0].key,
                            label: m[0].label,
                            items: Object.values(m.map(mm => ({ ...mm, key: [mm.attributes.hasEdgeOfDoor, mm.attributes.alignEdgeOfDoor].join('_') })).group('key')).map(mm => ({
                                key: mm[0].key,
                                label: mm[0].label + ' -  قابلبه ' + (mm[0].attributes.alignEdgeOfDoor == "threeSide"
                                    ? "دارد"
                                    : mm[0].attributes_label?.alignEdgeOfDoor ?? "ندارد"),
                                items: mm
                            }))
                        }
                    })

        })


        const tab = ref('')
        return {
            rows_data,
            tab,
        }
    }
}
</script>