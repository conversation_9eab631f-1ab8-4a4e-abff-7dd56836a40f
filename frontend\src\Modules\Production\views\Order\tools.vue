<template>
    <div class="flex gap-3">
        <j-btn v-if="checkPermission('production_order.create')" flat dense size="md" icon="add"
            :to="{ name: 'production_order.create' }" title="جدید" :label="$q.screen.lt.sm ? '' : 'جدید'" />
        <template v-if="props.selected?.length > 0">
            <j-btn
                v-if="props.selected?.length == 1 && (checkRole('admin') || (checkPermission('production_order.edit') && props.selected[0].user_id == authStore.user.id) || (checkPermission('production_order.status-PREORDER') && !props.selected[0].user_id))"
                flat dense size="md" icon="edit" title="ویرایش" :label="$q.screen.lt.sm ? '' : 'ویرایش'"
                :to="{ name: 'production_order.edit', params: { id: props.selected[0].id } }" />
            <j-btn
                v-if="checkRole('admin') || (checkPermission('production_order.delete') && props.selected.filter(f => f.user_id !== authStore.user.id).length == 0)"
                flat dense size="md" icon="delete" title="حذف" :label="$q.screen.lt.sm ? '' : 'حذف'"
                @click="doDelete" />
            <j-btn v-if="props.selected?.length == 1 && checkPermission('production_order.change-status')" flat dense
                size="md" icon="published_with_changes" title="تغییر وضعیت"
                :label="$q.screen.lt.sm ? '' : 'تغییر وضعیت'" @click="doChangeStatus" />
            <j-btn v-if="props.selected?.length == 1 && checkPermission('production_order.print-order')" flat dense
                size="md" icon="print" title="فاکتور" :label="$q.screen.lt.sm ? '' : 'فاکتور'" @click="doPrint" />
            <j-btn
                v-if="props.selected?.length == 1 && checkPermission('production_order.print-label') && props.selected[0].printable"
                flat dense size="md" icon="qr_code" title="لیبل" :label="$q.screen.lt.sm ? '' : 'لیبل'"
                @click="doPrintLabel" />
        </template>
    </div>
</template>
<script>
import { computed } from 'vue';
import { useTableStore } from '@/stores/table.store';
import { checkPermission, checkRole } from '@/helpers/functions';
import { useQuasar } from "quasar";
import checklist from './checklist.vue';
import printOrder from './printOrder.vue';
import { useAuthStore } from '@/stores/auth.store';
import PrintOrderLabel from './printOrderLabel/printOrderLabel.vue';
export default {
    setup() {
        const tableStore = useTableStore()
        const authStore = useAuthStore()
        const props = computed(() => tableStore.props);
        const $q = useQuasar();

        return {
            props,
            doDelete() {
                tableStore.actions.doDelete(props.value.selected)
            },
            checkPermission,
            checkRole,
            authStore,
            doChangeStatus() {
                $q.dialog({
                    component: checklist,
                    componentProps: {
                        selected: props.value.selected[0]
                    },
                })
                    .onDismiss(async () => {
                        console.log('onHide')
                        tableStore.actions.fetchData()
                    })
            },
            doPrint() {
                $q.dialog({
                    component: printOrder,
                    componentProps: {
                        selected: props.value.selected[0]
                    },
                })
                    .onDismiss(async () => {
                        console.log('onHide')
                        tableStore.actions.fetchData()
                    })
            },
            doPrintLabel() {
                $q.dialog({
                    component: PrintOrderLabel,
                    componentProps: {
                        selected: props.value.selected[0]
                    },
                })
                    .onDismiss(async () => {
                        console.log('onHide')
                        tableStore.actions.fetchData()
                    })
            },

        }
    }
}
</script>