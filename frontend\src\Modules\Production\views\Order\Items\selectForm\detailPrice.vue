<template>
    <div>
        <j-icon v-if="price_details && price_details.length > 0" size="sm" name="info" class="text-orange"
            @click="alert = true" />
        {{
            //Intl.NumberFormat().format(price_details.reduce((s, x) => s + x.price * 1, 0))
            Intl.NumberFormat().format(price)
        }} ریال
    </div>
    <q-dialog v-if="checkRole(['admin', 'sell_person'])" v-model="alert" @hide="temp = {}">
        <q-card>
            <q-card-section>
                <div class="text-h6">جزئیات قیمت</div>
            </q-card-section>
            <q-separator />
            <!-- <div v-if="price_details.reduce((s, x) => s + x.price * 1, 0) > 0" class="[&>*:nth-child(even)]:bg-gray-300"> -->
            <div class="grid gap-3 p-3">
                <template v-for="price_list, index in price_details">
                    <div class="grid grid-cols-2 p-1 gap-3">
                        <!-- <div class="text-bold grow">{{ price_list.label }}</div> -->
                        <j-input v-model="price_list.label" dense label="عنوان" hide-bottom-space
                            @update:model-value="update">
                            <template v-slot:before>
                                <j-icon name="clear" class="text-red cursor-pointer" size="sm" @click="remove(index)" />
                            </template>
                        </j-input>
                        <j-input v-model.number="price_list.price" dense label="مبلغ" hide-bottom-space dir="ltr"
                            unmasked-value reverse-fill-mask @update:model-value="update">
                            <template v-slot:prepend>
                                <span class="text-sm"> {{ Intl.NumberFormat().format(price_list.price) }} ریال</span>
                            </template>
                        </j-input>
                        <!-- <div class="text-right w-24">{{ Intl.NumberFormat().format(price_list.price) }} ریال</div> -->
                    </div>
                </template>
            </div>
            <q-separator />
            <div class="grid grid-cols-2 gap-3 p-3 bg-gray-100">
                <j-input v-model="temp.label" dense label="عنوان" hide-bottom-space>
                    <template v-slot:before>
                        <j-btn icon="add" color="primary" dense @click="add" />
                    </template>
                </j-input>
                <j-input v-model.number="temp.price" dense label="مبلغ" hide-bottom-space dir="ltr" unmasked-value
                    reverse-fill-mask>
                    <template v-slot:prepend>
                        <span class="text-sm"> {{ Intl.NumberFormat().format(temp.price) }} ریال</span>
                    </template>
                </j-input>
            </div>
            <q-separator />
            <div class="text-bold grow p-3 text-center text-lg">جمع : {{
                Intl.NumberFormat().format(price)
            }} ریال</div>
        </q-card>
    </q-dialog>

    <q-dialog v-else v-model="alert">
        <q-card class="p-3">
            <ul v-if="price_details.reduce((s, x) => s + x.price * 1, 0) > 0" class="[&>*:nth-child(even)]:bg-gray-300">
                <template v-for="price_list in price_details">
                    <li v-if="price_list.price > 0" class="flex p-1">
                        <div class="text-bold grow">{{ price_list.label }}</div>
                        <div class="text-right w-24">{{ Intl.NumberFormat().format(price_list.price) }} ریال</div>
                    </li>
                </template>

            </ul>
            <q-separator size="3px" color="black" />
            <div class="flex p-1 text-italic">
                <div class="text-bold grow">جمع</div>
                <div class="text-right w-24">{{
                    Intl.NumberFormat().format(price_details.reduce((s, x) => s + x.price * 1, 0))
                }} ریال</div>
            </div>
        </q-card>
    </q-dialog>
</template>
<script>
import { checkRole } from '@/helpers'
import { ref } from 'vue'
export default {
    props: {
        price_details: {
            type: Array,
            default: () => [],
        },
        price: {
            type: Number,
            default: 0,
        },
    },
    setup(props, { emit }) {
        const temp = ref({ price: 0 })
        return {
            checkRole,
            alert: ref(false),
            temp,
            add() {
                const tmp = [...props.price_details, Object.assign({}, temp.value)]
                temp.value = { price: 0 }
                emit('update:price_details', tmp)
                emit('update:price', tmp.reduce((s, a) => (isNaN(Number(a.price)) ? 0 : Number(a.price)) + s, 0))
                emit('change')
            },
            remove(index) {
                const tmp = props.price_details;
                tmp.splice(index, 1)
                emit('update:price_details', tmp)
                emit('update:price', tmp.reduce((s, a) => (isNaN(Number(a.price)) ? 0 : Number(a.price)) + s, 0))
                emit('change')
            },
            update() {
                const tmp = props.price_details;
                emit('update:price_details', tmp)
                emit('update:price', tmp.reduce((s, a) => (isNaN(Number(a.price)) ? 0 : Number(a.price)) + s, 0))
                emit('change')
            }
        }
    }
}
</script>