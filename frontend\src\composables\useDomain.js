/**
 * Composable for domain-specific functionality
 */

import { computed, ref } from 'vue';
import { getCurrentDomainType, DOMAIN_TYPES, getDomainConfig } from '@/config/domains';
import { getCurrentTheme } from '@/config/themes';

export function useDomain() {
    // Reactive domain type
    const domainType = ref(getCurrentDomainType());
    
    // Computed properties
    const isPanelDomain = computed(() => domainType.value === DOMAIN_TYPES.PANEL);
    const isCrmDomain = computed(() => domainType.value === DOMAIN_TYPES.CRM);
    
    // Domain configuration
    const domainConfig = computed(() => getDomainConfig());
    const theme = computed(() => getCurrentTheme());
    
    // Domain-specific features
    const features = computed(() => domainConfig.value.features || {});
    
    // Navigation items based on domain
    const navigationItems = computed(() => {
        if (isPanelDomain.value) {
            return [
                {
                    label: 'داشبورد',
                    icon: 'dashboard',
                    to: { name: 'dashboard' }
                },
                {
                    label: 'مدیریت',
                    icon: 'settings',
                    children: [
                        {
                            label: 'کاربران',
                            icon: 'people',
                            to: { name: 'users' }
                        }
                    ]
                }
            ];
        } else if (isCrmDomain.value) {
            return [
                {
                    label: 'سفارشات',
                    icon: 'shopping_cart',
                    to: { name: 'crm_production_order' }
                },
                {
                    label: 'مشتریان',
                    icon: 'people',
                    to: { name: 'customers' }
                }
            ];
        }
        return [];
    });
    
    // Page title based on domain
    const getPageTitle = (routeTitle) => {
        const domainName = domainConfig.value.name;
        return routeTitle ? `${domainName} - ${routeTitle}` : domainName;
    };
    
    // Logo path based on domain
    const logoPath = computed(() => theme.value.logo);
    
    // Primary color based on domain
    const primaryColor = computed(() => theme.value.colors.primary);
    
    // Check if feature is enabled for current domain
    const hasFeature = (featureName) => {
        return features.value[featureName] === true;
    };
    
    // Get domain-specific API endpoints
    const getApiEndpoint = (endpoint) => {
        const baseUrl = import.meta.env.VITE_API_BASE_URL;
        
        // Add domain-specific prefix if needed
        if (isCrmDomain.value && !endpoint.startsWith('crm/')) {
            return `${baseUrl}/crm/${endpoint}`;
        }
        
        return `${baseUrl}/${endpoint}`;
    };
    
    // Get domain-specific route name
    const getDomainRoute = (routeName) => {
        if (isCrmDomain.value && !routeName.startsWith('crm_')) {
            return `crm_${routeName}`;
        }
        return routeName;
    };
    
    // Update domain type (useful for testing or dynamic changes)
    const updateDomainType = () => {
        domainType.value = getCurrentDomainType();
    };
    
    return {
        // Reactive properties
        domainType,
        isPanelDomain,
        isCrmDomain,
        
        // Computed properties
        domainConfig,
        theme,
        features,
        navigationItems,
        logoPath,
        primaryColor,
        
        // Methods
        hasFeature,
        getPageTitle,
        getApiEndpoint,
        getDomainRoute,
        updateDomainType
    };
}
