export default function (
    {
        doorWidth,
        doorHeight,
        typeMaterial,
        hasThreshold,
        doorLengeh,
    },
    count = 1
) {
    const res = {
        centerLayerThickness: 0,
        baghalBazoo: {
            thickness: 0,
            // width: 0,
            height: 0,
            counts: (() => {
                let counts = { 13.5: 0, 17: 0 };

                switch (doorLengeh * 1) {
                    case verticalParvaz <= 237:
                        counts[237] += 4 * count;
                        return;
                    case verticalParvaz <= 244:
                        counts[244] += 4 * count;
                        return;
                }
                const verticalParvaz =
                    frameHeight + 3 + (hasThreshold ? 14 : 7);
                switch (true) {
                    case verticalParvaz <= 237:
                        counts[237] += 4 * count;
                        return;
                    case verticalParvaz <= 244:
                        counts[244] += 4 * count;
                        return;
                }
            })(),
        },
    };

    return res;
}
