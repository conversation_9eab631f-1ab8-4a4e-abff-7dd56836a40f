<?php

namespace Modules\Good\Http\Controllers;

use App\Http\Controllers\API\BaseController;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Modules\Good\Entities\AttributeItem;

class AttributeItemMetaController extends BaseController
{
    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(AttributeItem $attributeItem)
    {
        return $this->handleResponse($attributeItem->metas);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request, AttributeItem $attributeItem)
    {
        $attributeItem->metas()->sync($request->metas);
        return $this->handleResponse($attributeItem->metas);
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function show($id)
    {
        return view('good::show');
    }


    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        //
    }
}
