<template>
    <div v-if="form" class="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-4 gap-2 bg-white p-5 shadow-1 rounded-md mb-5">
        <j-select-remote ref="start" v-model="form.party_id" url="/buy-and-sell/party/search" label="نمایندگی"
            option-label="full_name" field="full_name" dense error-field="party_id" search-local />

        <j-input v-model="form.customer_name" label="مشتری" dense error-field="customer_name" />
        <!-- <j-select v-model="form.status" label="وضعیت" :options="formOption.statuses" dense /> -->
        <j-date-input v-model:value="form.submit_date" label="تاریخ شروع" dense />
        <j-date-input v-model:value="form.delivery_date" label="تاریخ سفارش" dense />
        <j-toggle v-model="form.has_logo" label="لوگو" dense class="col-span-full" />
        <j-input v-model="form.description" label="توضیحات" type="textarea" dense class="col-span-full" />

    </div>
    <select-item v-if="form" v-model:data="form.items" :id="form.id" />
</template>
<script>



import { usePublicStore } from "@/stores";
import { api } from "@/boot/axios";
import { useRoute } from 'vue-router';
import { ref } from 'vue';
import SelectItem from './selectForm/index.vue';

export default {

    components: { SelectItem },

    setup(props, { emit }) {
        const route = useRoute();
        const url = '/production/production_order/' + route.params.id;

        const form = ref({})
        const data = ref([])

        const getData = () => {
            api.get(url).then(res => {
                form.value = res.result;
            })
        }

        const publicStore = usePublicStore();
        publicStore.titleWindow = 'مشخصات سفارش'
        getData()

        const start = ref(null)

        return {
            start,
            form,
            data,
        };
    },
};
</script>
