<?php

namespace Modules\BuyAndSell\Http\Controllers;

use App\Http\Controllers\API\BaseController;
use App\Repositories\ModelRepositoryInterface;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Modules\BuyAndSell\Entities\Order;

class OrderController extends BaseController
{
    public function __construct(ModelRepositoryInterface $repository)
    {
        $repository->model = Order::class;
        $this->repository = $repository;
    }

    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return JsonResource::collection($this->repository->query()
            ->with(['party'])
            ->withCount(['items as total_price' => function ($query) {
                $query->select(DB::raw("SUM(total_price) as total_price"));
            }, 'items as total_count' => function ($query) {
                $query->select(DB::raw("SUM(count) as count"));
            }])
            ->orderByDesc('id')
            ->paginate(request('rowsPerPage') == 0 ? 1000 : request('rowsPerPage')))->additional([
            'formOption' => [
                'statuses' => Order::$statuses,
            ]
        ]);
        return $this->repository->getAll([
            'formOption' => [
                'statuses' => Order::$statuses,
            ]
        ], [
            'party',
            // 'user',
        ]);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        $params = $request->all();
        $params['status'] =  Order::DRAFT;
       // $params['party_id'] =  auth()->user()->party_id;
        // $params['user_id'] =  Auth::user()->id;
        $order = Order::create($params);
        // $productionOrder->code = $productionOrder->saveCode();


        $order->items()->sync(collect($request->input('items'))->map(function ($m, $k) {
            return [
                'code' => $k + 1,
                'good_id' => $m['good_id'],
                'attributes' => $m['attributes'],
                'count' => $m['count'],
                'id' => $m['id'] ?? null,
                'description' => $m['description'] ?? null,

                'price' => $m['price'],
                'total_price' =>  $m['price'] * $m['count'],
                'price_details' => $m['price_details'],
            ];
        })->toArray());
        $order->items = $order->items()->with('good')->get();

        return $this->handleResponse($order, trans('request.done'));
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(Order $sell)
    {
        if (!auth()->user()->is_super_admin && ($sell->party_id != auth()->user()->party_id))
            return $this->handleError('دسترسی غیر مجاز', [], 403);

        $sell->items = $sell->items()
            ->with('good')->orderByRaw('CONVERT(code, SIGNED)')
            ->get();
        return $this->handleResponse($sell);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, Order $sell)
    {
        if (!auth()->user()->is_super_admin && ($sell->party_id != auth()->user()->party_id))
            return $this->handleError('دسترسی غیر مجاز', [], 403);

        $params = $request->all();
       // $params['party_id'] =  auth()->user()->party_id;

        $sell->update($params);
        $sell->items()->sync(collect($request->input('items'))->map(function ($m, $k) {
            return [
                'code' => $k + 1,
                'good_id' => $m['good_id'],
                'attributes' => $m['attributes'],
                'count' => $m['count'],
                'id' => $m['id'] ?? null,
                'description' => $m['description'] ?? null,

                'price' => $m['price'],
                'total_price' =>  $m['price'] * $m['count'],
                'price_details' => $m['price_details'],
            ];
        })->toArray());
        // $this->changeStatus($productionOrder);

        $sell->items = $sell->items()->with('good')->get();
        return $this->handleResponse($sell, trans('request.done'));
    }

    public function updateStatus(Request $request, Order $sell)
    {
        if (!auth()->user()->is_super_admin && ($sell->party_id != auth()->user()->party_id))
            return $this->handleError('دسترسی غیر مجاز', [], 403);

        $sell->update(['status' => $request->input('status')]);
        return $this->handleResponse($sell, trans('request.done'));
    } /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy(Order $sell)
    {
        if (!auth()->user()->is_super_admin && ($sell->party_id != auth()->user()->party_id))
            return $this->handleError('دسترسی غیر مجاز', [], 403);

        return $this->handleResponse($sell->delete(), trans('request.done'));
    }

    public function checklists(Order $sell, $message = null)
    {
        $sell->checklists;
        return $this->handleResponse([
            'id' => $sell['id'],
            'status' => $sell['status'],
            'next_status' => $sell['next_status'],
            'previous_status' => $sell['previous_status'],
            // 'checklists' => $sell->checklists,
            'statuses' => collect(Order::$statuses)->map(function ($m) use ($sell) {
                $checklist = $sell->checklists->where('status', $m['value'])->first();
                if ($checklist) {
                    $m['is_done'] = $m['value'] == $sell['status'];
                    $m['updated_at'] = $checklist['updated_at'];
                }

                return $m;
            }),
        ], $message);
    }

    public function changeStatus(Order $sell)
    {
        $status = request('status') ?? $sell->status;
        if (!$sell->checklists()->where('status', $status)->first())
            $sell->checklists()->create([
                'status' => $status,
                'updated_at' =>  Carbon::now(),
            ]);

        $this->repository->update($sell, ['status' => $status]);

        return $this->checklists($sell, trans('request.done'));
    }
}
