<template>
  <q-checkbox :rules="rules" />
</template>
<script>
import { validationApi } from '@/helpers';

export default {
  props: {
    required: {
      type: Boolean,
      default: false,
    },
    errorField: {
      type: String,
      default: "",
    },
  },
  setup(props) {
    const { rules, requestStore } = validationApi(props)
    return {
      rules, requestStore
    }
  }
}
</script>
