<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="256" height="256" viewBox="0 0 256 256" xml:space="preserve">

<defs>
</defs>
<g style="stroke: none; stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;" transform="translate(1.4065934065934016 1.4065934065934016) scale(2.81 2.81)" >
	<path d="M 78.883 82.984 H 11.117 C 4.977 82.984 0 78.007 0 71.867 v -0.505 c 0 -6.14 4.977 -11.117 11.117 -11.117 h 67.766 c 6.14 0 11.117 4.977 11.117 11.117 v 0.505 C 90 78.007 85.023 82.984 78.883 82.984 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(221,234,237); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 33.337 60.245 h 23.326 c 5.864 -5.426 10.131 -13.535 10.131 -26.888 V 17.725 C 59.529 17.369 52.265 13.799 45 7.016 c -7.265 6.783 -14.529 10.353 -21.794 10.709 v 15.631 C 23.206 46.71 27.473 54.819 33.337 60.245 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(46,140,204); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 45.175 50.309 h -0.35 c -4.256 0 -7.707 -3.45 -7.707 -7.707 v -5.987 h 15.763 v 5.987 C 52.882 46.859 49.431 50.309 45.175 50.309 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(254,183,86); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 49.856 37.615 c -0.553 0 -1 -0.448 -1 -1 v -7.404 c 0 -2.126 -1.73 -3.856 -3.856 -3.856 c -2.126 0 -3.856 1.73 -3.856 3.856 v 7.404 c 0 0.552 -0.448 1 -1 1 s -1 -0.448 -1 -1 v -7.404 c 0 -3.229 2.627 -5.856 5.856 -5.856 s 5.856 2.627 5.856 5.856 v 7.404 C 50.856 37.167 50.409 37.615 49.856 37.615 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(254,183,86); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 45 44.1 c -0.26 0 -0.52 -0.11 -0.71 -0.3 C 44.11 43.62 44 43.36 44 43.1 c 0 -0.07 0.01 -0.14 0.02 -0.2 c 0.01 -0.06 0.03 -0.13 0.06 -0.19 c 0.02 -0.06 0.05 -0.12 0.09 -0.17 c 0.03 -0.05 0.08 -0.11 0.12 -0.15 c 0.09 -0.09 0.2 -0.17 0.33 -0.22 c 0.37 -0.15 0.81 -0.06 1.09 0.22 C 45.89 42.57 46 42.83 46 43.1 c 0 0.13 -0.02 0.26 -0.08 0.38 c -0.05 0.12 -0.12 0.23 -0.21 0.32 c -0.05 0.05 -0.1 0.09 -0.15 0.13 c -0.06 0.03 -0.12 0.06 -0.18 0.09 c -0.06 0.02 -0.12 0.04 -0.18 0.06 C 45.13 44.09 45.06 44.1 45 44.1 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(46,140,204); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 45 78.973 c -0.552 0 -1 -0.447 -1 -1 V 65.257 c 0 -0.553 0.448 -1 1 -1 s 1 0.447 1 1 v 12.716 C 46 78.525 45.552 78.973 45 78.973 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(46,140,204); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 51.358 72.614 H 38.642 c -0.552 0 -1 -0.447 -1 -1 s 0.448 -1 1 -1 h 12.717 c 0.553 0 1 0.447 1 1 S 51.911 72.614 51.358 72.614 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(46,140,204); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 40.504 77.11 c -0.256 0 -0.512 -0.098 -0.707 -0.293 c -0.391 -0.391 -0.391 -1.023 0 -1.414 l 8.992 -8.992 c 0.391 -0.391 1.023 -0.391 1.414 0 s 0.391 1.023 0 1.414 l -8.992 8.992 C 41.016 77.013 40.76 77.11 40.504 77.11 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(46,140,204); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 49.496 77.11 c -0.256 0 -0.512 -0.098 -0.707 -0.293 l -8.992 -8.992 c -0.391 -0.391 -0.391 -1.023 0 -1.414 s 1.023 -0.391 1.414 0 l 8.992 8.992 c 0.391 0.391 0.391 1.023 0 1.414 C 50.008 77.013 49.752 77.11 49.496 77.11 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(46,140,204); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 67.223 78.973 c -0.553 0 -1 -0.447 -1 -1 V 65.257 c 0 -0.553 0.447 -1 1 -1 s 1 0.447 1 1 v 12.716 C 68.223 78.525 67.775 78.973 67.223 78.973 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(46,140,204); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 73.581 72.614 H 60.864 c -0.553 0 -1 -0.447 -1 -1 s 0.447 -1 1 -1 h 12.717 c 0.553 0 1 0.447 1 1 S 74.134 72.614 73.581 72.614 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(46,140,204); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 62.727 77.11 c -0.256 0 -0.512 -0.098 -0.707 -0.293 c -0.391 -0.391 -0.391 -1.023 0 -1.414 l 8.992 -8.992 c 0.391 -0.391 1.023 -0.391 1.414 0 s 0.391 1.023 0 1.414 l -8.992 8.992 C 63.238 77.013 62.982 77.11 62.727 77.11 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(46,140,204); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 71.719 77.11 c -0.256 0 -0.512 -0.098 -0.707 -0.293 l -8.992 -8.992 c -0.391 -0.391 -0.391 -1.023 0 -1.414 s 1.023 -0.391 1.414 0 l 8.992 8.992 c 0.391 0.391 0.391 1.023 0 1.414 C 72.23 77.013 71.975 77.11 71.719 77.11 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(46,140,204); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 22.777 78.973 c -0.552 0 -1 -0.447 -1 -1 V 65.257 c 0 -0.553 0.448 -1 1 -1 s 1 0.447 1 1 v 12.716 C 23.777 78.525 23.33 78.973 22.777 78.973 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(46,140,204); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 29.135 72.614 H 16.419 c -0.552 0 -1 -0.447 -1 -1 s 0.448 -1 1 -1 h 12.716 c 0.552 0 1 0.447 1 1 S 29.688 72.614 29.135 72.614 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(46,140,204); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 18.281 77.11 c -0.256 0 -0.512 -0.098 -0.707 -0.293 c -0.391 -0.391 -0.391 -1.023 0 -1.414 l 8.992 -8.992 c 0.391 -0.391 1.023 -0.391 1.414 0 s 0.391 1.023 0 1.414 l -8.992 8.992 C 18.793 77.013 18.537 77.11 18.281 77.11 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(46,140,204); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
	<path d="M 27.273 77.11 c -0.256 0 -0.512 -0.098 -0.707 -0.293 l -8.992 -8.992 c -0.391 -0.391 -0.391 -1.023 0 -1.414 s 1.023 -0.391 1.414 0 l 8.992 8.992 c 0.391 0.391 0.391 1.023 0 1.414 C 27.785 77.013 27.529 77.11 27.273 77.11 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(46,140,204); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
</g>
</svg>