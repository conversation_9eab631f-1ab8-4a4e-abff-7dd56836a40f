<template>
  <q-toggle :rules="rules" checked-icon="check" unchecked-icon="clear" size="lg" />
</template>
<script>
import { validationApi } from '@/helpers';

export default {
  props: {
    required: {
      type: Boolean,
      default: false,
    },
    errorField: {
      type: String,
      default: "",
    },
  },
  setup(props) {
    const { rules, requestStore } = validationApi(props)
    return {
      rules, requestStore
    }
  }
}
</script>
