<?php

namespace Modules\Production\Http\Controllers;

use App\Http\Controllers\API\BaseController;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;
use Modules\Production\Entities\ProductionOrderItem;

class GuaranteeController extends BaseController
{
    public function checkBarcode(Request $request)
    {


        $validator = Validator::make($request->all(), [
            'barcode' => 'required',
        ]);

        if ($validator->fails()) {

            //pass validator errors as errors object for ajax response

           // return response()->json(['errors' => $validator->errors()], 422);
            return $this->handleError($validator->errors(),null,422);

        } else {
            $barcode = $request->input('barcode');
            $ex = explode('.', $barcode);
            $check = ProductionOrderItem::query()->whereHas('productionOrder')->where('id', $ex[0])->where('attributes', 'like', '%"typeMaterialDoor":"fomizeh"%')->where('count', '>=', isset($ex[1]) ? $ex[1] : 1)->with('productionOrder')->first();
            //return response()->json($check);
            $res = ['ok' => $check];
            if (!!$check) {
                $res['production_order_code'] = $check->productionOrder->code;
                $res['delivery_date'] = $check->productionOrder->getRawOriginal('delivery_date');
            }
            return response()->json($res);
        }
    }
}
