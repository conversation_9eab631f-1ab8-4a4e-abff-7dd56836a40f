<template>
    <j-table :columns="columns" :rows="data" separator="cell"
    virtual-scroll
style="height:300px"
            :row-key="row => row.good_id + '|' + JSON.stringify(row.attributes)" hide-pagination dense :rows-per-page-options="[0]"> 
            <template #top-left>
                لیست سفارش
            </template>
        </j-table>
</template>
<script>
import { computed, ref, watch } from 'vue';
import { api } from '@/boot/axios';

export default {
    setup(props) {
        

        const columns = computed(() => {
            const attribute_columns = data.value.map(m => Object.keys(m.attributes)).flat().unique();
            return [...[
                {
                    name: "good",
                    label: "کالا/خدمات",
                    field: row => row.good.name ?? '',
                },
                {
                    name: "count",
                    label: "تعداد",
                    field: "count",
                }
            ],
            ...attributes.value.filter(f => attribute_columns.includes(f.id + '')).map(m => {

                return {
                    name: m.id,
                    label: m.name,
                    field: row => {
                        switch (m.type) {
                            case 'SELECT':
                            case 'SELECT_IMAGE':
                                const find = m.items.findIndex(ff => ff.id + '' == row.attributes[m.id])
                                return find >= 0 ? m.items[find].name : '';
                            case 'SWITCH':
                                return row.attributes[m.id] ? 'دارد' : ''
                            case 'NUMBER':
                            case 'INPUT':
                                return row.attributes[m.id] ?? ''
                        }
                    },
                }
            })
            ];

        })
        const data = ref([])
        api.get('production/production_order_items').then(res => {
            data.value = res.data
        })
        const attributes = ref([])
        api.get('good/attribute').then(res => {
            attributes.value = res.data
        })


        return {
            columns,
            data,
            attributes,
        }

     
    
    },
}
</script>