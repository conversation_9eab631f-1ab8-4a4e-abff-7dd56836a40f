# پروژه فرانت‌اند ERP ایران پارس

## 📋 فهرست مطالب

- [معرفی](#معرفی)
- [تکنولوژی‌ها](#تکنولوژیها)
- [نصب و راه‌اندازی](#نصب-و-راهاندازی)
- [ساختار پروژه](#ساختار-پروژه)
- [اسکریپت‌ها](#اسکریپتها)
- [تنظیمات](#تنظیمات)
- [راهنمای توسعه](#راهنمای-توسعه)
- [کیفیت کد](#کیفیت-کد)

## 🚀 معرفی

این پروژه فرانت‌اند سیستم ERP ایران پارس است که با استفاده از Vue.js 3 و Quasar Framework توسعه یافته است.

### ویژگی‌های کلیدی

- ✅ رابط کاربری مدرن و ریسپانسیو
- ✅ پشتیبانی کامل از RTL و زبان فارسی
- ✅ PWA (Progressive Web App)
- ✅ ساختار ماژولار و قابل توسعه
- ✅ کامپوننت‌های قابل استفاده مجدد
- ✅ مدیریت state با Pinia
- ✅ احراز هویت و مجوزها
- ✅ جداول پیشرفته با فیلتر و صفحه‌بندی

## 🛠 تکنولوژی‌ها

- **Vue.js 3** - فریمورک اصلی
- **Quasar Framework** - کامپوننت‌ها و UI
- **Vite** - ابزار build
- **Pinia** - مدیریت state
- **Vue Router** - مسیریابی
- **Axios** - HTTP client
- **Tailwind CSS** - استایل‌دهی
- **Sass/SCSS** - پیش‌پردازنده CSS
- **Moment Jalaali** - تاریخ شمسی

## 📦 نصب و راه‌اندازی

### پیش‌نیازها

- Node.js (نسخه 18 یا بالاتر)
- npm یا yarn

### مراحل نصب

```bash
# کلون کردن پروژه
git clone <repository-url>
cd frontend

# نصب dependencies
npm install

# کپی کردن فایل environment
cp .env.example .env

# اجرای پروژه در حالت development
npm run dev
```

پروژه روی آدرس `http://panel.erp.test:3000` در دسترس خواهد بود.

## 📁 ساختار پروژه

```
src/
├── assets/          # فایل‌های استاتیک
├── boot/            # تنظیمات اولیه
├── components/      # کامپوننت‌های عمومی
├── composables/     # منطق قابل استفاده مجدد
├── constants/       # ثابت‌ها
├── directives/      # دایرکتیوهای سفارشی
├── helpers/         # توابع کمکی
├── Modules/         # ماژول‌های اصلی
├── stores/          # Pinia stores
├── styles/          # فایل‌های CSS/SCSS
├── utils/           # ابزارهای کمکی
└── views/           # صفحات اصلی
```

### ساختار ماژول‌ها

```
Modules/
├── Production/
│   ├── router.js    # مسیرهای ماژول
│   └── views/       # صفحات ماژول
├── Good/
└── Management/
```

## 🔧 اسکریپت‌ها

```bash
# توسعه
npm run dev              # اجرای development server
npm run build            # ساخت برای production
npm run preview          # پیش‌نمایش build

# کیفیت کد
npm run lint             # بررسی و اصلاح ESLint
npm run lint:check       # فقط بررسی ESLint
npm run format           # فرمت کردن کد با Prettier
npm run format:check     # بررسی فرمت کد

# تست
npm run test             # اجرای تست‌ها
npm run test:coverage    # تست با coverage

# تحلیل
npm run analyze          # تحلیل bundle size
```

## ⚙️ تنظیمات

### متغیرهای محیطی

فایل `.env` را بر اساس `.env.example` تنظیم کنید:

```env
VITE_API_BASE_URL=http://localhost:8000/api
VITE_PANEL_DOMAIN=panel.erp.test
VITE_CRM_DOMAIN=crm.erp.test
```

### تنظیمات Vite

فایل `vite.config.js` شامل تنظیمات:
- PWA
- Alias‌ها
- Plugin‌ها
- Build options

## 👨‍💻 راهنمای توسعه

### ایجاد کامپوننت جدید

```vue
<template>
  <div class="my-component">
    <!-- محتوا -->
  </div>
</template>

<script setup>
import { ref } from 'vue';

// Props
const props = defineProps({
  title: String
});

// Emits
const emit = defineEmits(['update']);

// State
const loading = ref(false);
</script>
```

### استفاده از Composables

```javascript
import { useApi, useForm } from '@/composables';

const { get, post } = useApi();
const { formData, handleSubmit } = useForm();
```

### ایجاد Store جدید

```javascript
import { defineStore } from 'pinia';

export const useMyStore = defineStore('my-store', {
  state: () => ({
    items: []
  }),
  
  actions: {
    async fetchItems() {
      // منطق دریافت داده
    }
  }
});
```

### مسیریابی

```javascript
// در فایل router ماژول
export default [
  {
    path: '/my-route',
    name: 'my-route',
    component: MyComponent,
    meta: {
      title: 'عنوان صفحه',
      permissions: 'my-permission'
    }
  }
];
```

## 🎯 کیفیت کد

### قوانین ESLint

- استفاده از Vue 3 Composition API
- Single quotes برای strings
- Semicolon در پایان statements
- 2 spaces برای indentation

### قوانین Prettier

- حداکثر 100 کاراکتر در هر خط
- Trailing commas: none
- Arrow function parens: avoid

### بهترین روش‌ها

1. **نام‌گذاری**: از نام‌های واضح و توصیفی استفاده کنید
2. **کامپوننت‌ها**: کامپوننت‌ها را کوچک و قابل استفاده مجدد نگه دارید
3. **State Management**: از Pinia برای مدیریت state استفاده کنید
4. **Performance**: از lazy loading برای route‌ها استفاده کنید
5. **Accessibility**: قوانین a11y را رعایت کنید

## 🐛 رفع مشکلات

### مشکلات رایج

1. **خطای CORS**: بررسی کنید backend CORS را تنظیم کرده باشد
2. **مشکل routing**: بررسی کنید domain در `/etc/hosts` تنظیم شده باشد
3. **خطای build**: cache را پاک کنید: `rm -rf node_modules/.vite`

### لاگ‌ها

برای دیباگ، متغیر `VITE_DEBUG=true` را تنظیم کنید.

## 📝 مشارکت

1. Fork کنید
2. Branch جدید ایجاد کنید
3. تغییرات را commit کنید
4. Pull request ارسال کنید

## 📄 مجوز

این پروژه تحت مجوز MIT منتشر شده است.
