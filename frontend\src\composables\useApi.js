import { ref, reactive } from 'vue';
import { api } from '@/boot/axios';
import { useRequestStore } from '@/stores';
import { Notify } from 'quasar';

/**
 * Composable for API operations with loading states and error handling
 */
export function useApi() {
    const requestStore = useRequestStore();
    const loading = ref(false);
    const error = ref(null);

    /**
     * Generic API call wrapper
     * @param {Function} apiCall - The API function to call
     * @param {Object} options - Options for the API call
     */
    const execute = async (apiCall, options = {}) => {
        const {
            showLoading = true,
            showSuccess = true,
            showError = true,
            successMessage = null,
            errorMessage = null
        } = options;

        try {
            if (showLoading) {
                loading.value = true;
                requestStore.loading = true;
            }
            
            error.value = null;
            const response = await apiCall();
            
            if (showSuccess && successMessage) {
                Notify.create({
                    type: 'positive',
                    message: successMessage
                });
            }
            
            return response;
        } catch (err) {
            error.value = err;
            
            if (showError) {
                const message = errorMessage || 
                    err.response?.data?.message || 
                    'خطایی رخ داده است';
                
                Notify.create({
                    type: 'negative',
                    message
                });
            }
            
            throw err;
        } finally {
            if (showLoading) {
                loading.value = false;
                requestStore.loading = false;
            }
        }
    };

    /**
     * GET request
     */
    const get = (url, config = {}) => {
        return execute(() => api.get(url, config));
    };

    /**
     * POST request
     */
    const post = (url, data = {}, config = {}) => {
        return execute(() => api.post(url, data, config));
    };

    /**
     * PUT request
     */
    const put = (url, data = {}, config = {}) => {
        return execute(() => api.put(url, data, config));
    };

    /**
     * DELETE request
     */
    const del = (url, config = {}) => {
        return execute(() => api.delete(url, config));
    };

    /**
     * PATCH request
     */
    const patch = (url, data = {}, config = {}) => {
        return execute(() => api.patch(url, data, config));
    };

    return {
        loading,
        error,
        execute,
        get,
        post,
        put,
        delete: del,
        patch
    };
}

/**
 * Composable for CRUD operations
 */
export function useCrud(baseUrl) {
    const { get, post, put, delete: del } = useApi();
    const items = ref([]);
    const item = ref(null);
    const pagination = reactive({
        page: 1,
        rowsPerPage: 15,
        rowsNumber: 0
    });

    /**
     * Fetch all items with pagination
     */
    const fetchItems = async (params = {}) => {
        const response = await get(baseUrl, { params });
        items.value = response.data || response;
        
        if (response.meta) {
            pagination.page = response.meta.current_page;
            pagination.rowsPerPage = response.meta.per_page;
            pagination.rowsNumber = response.meta.total;
        }
        
        return response;
    };

    /**
     * Fetch single item by ID
     */
    const fetchItem = async (id) => {
        const response = await get(`${baseUrl}/${id}`);
        item.value = response.data || response;
        return response;
    };

    /**
     * Create new item
     */
    const createItem = async (data) => {
        const response = await post(baseUrl, data, {
            successMessage: 'با موفقیت ایجاد شد'
        });
        
        // Add to items list if successful
        if (response.data) {
            items.value.unshift(response.data);
        }
        
        return response;
    };

    /**
     * Update existing item
     */
    const updateItem = async (id, data) => {
        const response = await put(`${baseUrl}/${id}`, data, {
            successMessage: 'با موفقیت به‌روزرسانی شد'
        });
        
        // Update in items list if successful
        if (response.data) {
            const index = items.value.findIndex(item => item.id === id);
            if (index !== -1) {
                items.value[index] = response.data;
            }
            item.value = response.data;
        }
        
        return response;
    };

    /**
     * Delete item
     */
    const deleteItem = async (id) => {
        const response = await del(`${baseUrl}/${id}`, {
            successMessage: 'با موفقیت حذف شد'
        });
        
        // Remove from items list if successful
        const index = items.value.findIndex(item => item.id === id);
        if (index !== -1) {
            items.value.splice(index, 1);
        }
        
        return response;
    };

    return {
        items,
        item,
        pagination,
        fetchItems,
        fetchItem,
        createItem,
        updateItem,
        deleteItem
    };
}
