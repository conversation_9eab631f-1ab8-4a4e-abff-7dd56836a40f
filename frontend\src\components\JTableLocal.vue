<template>
  <div class="flex" :class="!$q.screen.xs ? 'q-table__card' : 'border-b-2'" style="background: none;border-radius: 10px;">
    <div class="w-12 p-2" style="background:#2e3d50; border-radius: 0 10px 10px 0;">
      <div class="sticky top-14 flex gap-2">
        <j-btn v-if="canBeAdd && !readonly" dense icon="add" color="blue" @click="onAdd()" title="افزودن" />
        <template v-if="selected.length > 0">
          <j-btn v-if="canBeEdit(selected[0]) && !readonly" dense icon="edit" color="green" @click="onEdit()"
            title="ویرایش" />
          <j-btn v-if="canBeDelete(selected[0]) && !readonly" dense icon="delete" color="red" @click="onDelete()"
            title="حذف" />
          <slot name="select_bar" v-bind="{ callback: getAll, selected: selected[0] }" />
        </template>
      </div>
    </div>


    <j-table v-model:selected="selected" :rows="list" :columns="columns" selection="single" :row-key="rowKey ?? 'id'"
      separator="cell" v-model:filters="filters" v-model:formOption="formOption" @onFilter="onFilter"
      class="flex-auto w-12" flat @row-click="dblClick" :dense="dense" style="border-radius: 10px 0 0 10px;">

      <template v-for="_, slot in $slots" v-slot:[slot]="props" :key="slot">
        <slot :name="slot" v-bind="props" :key="slot" />
      </template>

    </j-table>
  </div>
</template>
<script>
import { tableApi } from '@/helpers';
import { ref, watch } from 'vue';

export default {
  props: {
    data: {
      type: Array,
      default: () => []
    },
    dense: {
      type: Boolean,
      default: false
    },
    columns: {
      type: Array,
      default: () => []
    },
    rowKey: String | Function,
    readonly: {
      type: Boolean,
      default: false,
    },
    canBeAdd: {
      type: Boolean,
      default: true,
    },
    canBeEdit: {
      type: Function,
      default: () => true,
    },
    canBeDelete: {
      type: Function,
      default: () => true,
    },
    selected: {
      type: Array,
      default: () => []
    }
  },
  setup(props, context) {
    const list = ref(props.data);
    const add = ref(false);
    const ref_form = ref(null);


    columns.value = props.columns;
    selected.value = [props.selected];
    watch(() => selected.value, () => {
      context.emit('update:selected', selected.value[0])
    })

    const onAdd = () => {
      if (add.value)
        return;
      add.value = true;
      // form.value = {}
      selected.value = []
    }
    const onEdit = async () => {
      form.value.id = selected.value[0].id;//Object.assign({}, selected.value[0]);
      show();
      add.value = true;
      // selected.value = []
    }


    const dblClick = (evt, row, index) => {
      selected.value[0] = row
      context.emit('update:selected', selected.value[0])
    }


    return {
      add,
      onAdd,
      onEdit,
      list,
      onDelete,
      selected,
      getAll,
      onFilter,
      formOption,
      additional,
      form,
      filters,
      url,
      form,
      submitForm,
      ref_form,
      dblClick,

    }
  },
}
</script>