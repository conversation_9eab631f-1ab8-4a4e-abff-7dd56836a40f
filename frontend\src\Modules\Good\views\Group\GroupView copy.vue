<template>
  <div class="q-gutter-sm">
    <div class="flex space-x-2 border-b-2 pb-2">
      <j-btn flat dense icon="add" color="blue" @click="onAddToChild()" label="افزودن" />
      <q-space />

      <q-input filled v-model="filter" label="جستجو" dense class="w-max">
        <template v-slot:append>
          <q-icon v-if="filter !== ''" name="clear" class="cursor-pointer" @click="resetFilter" />
        </template>
      </q-input>
    </div>

    <q-tree :nodes="list" :filter="filter" default-expand-all node-key="id" label-key="name"
      v-model:selected="selected" class="rounded-md">
      <template v-slot:default-header="prop">
        <div class="row items-center w-full">
          <div class="text-weight-bold text-primary">
            {{ prop.node.name }}
            <!-- <q-popup-edit
              v-model="prop.node.name"
              :validate="(val) => val.length > 2"
              v-slot="scope"
            >
              <q-input
                autofocus
                dense
                v-model="scope.value"
                :model-value="scope.value"
                :rules="[
                  (val) => scope.validate(val) || 'حداقل 3 حرف',
                ]"
              >
                <template v-slot:after>
                  <q-btn
                    flat
                    dense
                    color="negative"
                    icon="cancel"
                    @click.stop.prevent="scope.cancel"
                  />

                  <q-btn
                    flat
                    dense
                    color="positive"
                    icon="check_circle"
                    @click.stop.prevent="scope.set"
                    :disable="
                      scope.validate(scope.value) === false ||
                      scope.initialValue === scope.value
                    "
                  />
                </template>
              </q-input>
            </q-popup-edit> -->
          </div>
          <q-space />
          <j-btn flat dense icon="add" color="blue" @click="onAddToChild(prop.node.id)" />
          <j-btn flat dense icon="edit" color="green" @click="onEdit(prop.node)" />
          <j-btn flat dense icon="delete" color="red" @click="onDelete(prop.node.id)" />
        </div>
      </template>
    </q-tree>
    <j-dialog v-model="add">
      <q-card style="width:100%; max-width: 500px">
        <!-- <q-card-section class="row items-center border-b-2">
          <div class="text-h6">Close icon</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section> -->
        <group-form v-model:form="data" class="p-5" @afterSubmit="reloadView" />
      </q-card>
    </j-dialog>
  </div>
</template>

<script>
import { ref } from "vue";
import { api } from "@/boot/axios";
import GroupForm from "./GroupForm.vue";
import { useQuasar } from 'quasar'

export default {
  setup() {
    const list = ref([]);
    const add = ref(false);
    const data = ref({});
    const filter = ref("");
    const $q = useQuasar()

    const onAddToChild = (parent_id = null) => {
      add.value = true;
      data.value = { parent_id }
    }

    const onEdit = (value) => {
      add.value = true;
      data.value = Object.assign({}, value)
    }

    const onDelete = (id) => {
      $q.dialog({
        title: 'مطمئن هستید؟',
        // message: 'Would you like to turn on the wifi?',
        cancel: true,
        persistent: true
      }).onOk(() => {
        api.delete(`/good/group/${id}`).then((response) => {
          reloadView();
        });
      })
    }

    const reloadView = () => {
      api.get("/good/group").then((response) => {
        list.value = response.data;
      });
    }

    reloadView();
    return {
      filter,
      add,
      edit: ref(false),
      selected: ref([]),
      resetFilter() {
        filter.value = "";
        filterRef.value.focus();
      },
      list,
      data,
      reloadView,
      onAddToChild,
      onEdit,
      onDelete,
    };
  },
  components: { GroupForm },
};
</script>
