<?php

namespace Modules\Good\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Database\Eloquent\Model;
use Modules\Good\Entities\Good;
use Modules\Good\Entities\Group;

class GoodDatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        
        Model::unguard();

        $array = [
            [
                'name' => 'درب',
                'children' => [
                    ['name' => 'فومیزه']
                ]
            ]
        ];
        //Group::factory()->count(4)->has(Group::factory())->create();
        //Group::factory()->count(4)->asd()->make();

        // $this->call("OthersTableSeeder");
        Good::factory()->count(10)->create();
    }
}
