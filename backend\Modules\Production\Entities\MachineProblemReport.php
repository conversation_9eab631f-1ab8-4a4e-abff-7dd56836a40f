<?php

namespace Modules\Production\Entities;

class MachineProblemReport extends BModel
{
   protected $fillable = [
      'station_id',
      'machine_id',
      'status',
      'description',
   ];
   const REPORTED = 'REPORTED';
   const REPARING = 'REPARING';
   const DONE = 'DONE';
   public static $statuses = [
      [
         'value' => self::REPORTED,
         'label' => 'گزارش شده',
      ],
      [
         'value' => self::REPARING,
         'label' => 'درحال پیگیری',
      ],
      [
         'value' => self::DONE,
         'label' => 'انجام شده',
      ],

   ];
   protected $appends = [
      'label_status'
   ];
   public function getLabelStatusAttribute()
   {
      return collect(self::$statuses)->where('value', $this->status)->pluck('label')->first();
   }

   public function problems()
   {
      return $this->belongsToMany(MachineProblem::class, MachineProblemReportItem::class);
   }
   
   public function machine()
   {
      return $this->belongsTo(Machine::class);
   }
   public function station()
   {
      return $this->belongsTo(Station::class);
   }
   
}
