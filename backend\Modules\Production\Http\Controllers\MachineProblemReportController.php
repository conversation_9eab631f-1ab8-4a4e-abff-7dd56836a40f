<?php

namespace Modules\Production\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\API\BaseController;
use App\Repositories\ModelRepositoryInterface;
use Modules\Production\Entities\Machine;
use Modules\Production\Entities\MachineProblemReport;
use Modules\Production\Entities\Station;

class MachineProblemReportController extends BaseController
{
    public function __construct(ModelRepositoryInterface $repository)
    {
        $repository->model = MachineProblemReport::class;
        $this->repository = $repository;
    }
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index_all()
    {
        return $this->repository->getAll([
            'formOption' => [
                'statuses' => MachineProblemReport::$statuses,
            ]
        ], [
            'machine',
            'station',
            'problems',
            'createdAtActivity.causer'
        ]);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function put_all(Request $request, MachineProblemReport $machineProblemReport)
    {
        $request->validate([
            'status' => 'required',
        ]);
        return $machineProblemReport->update(['status' => $request->input('status')]);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function delete_all(MachineProblemReport $machineProblemReport)
    {
        return $this->handleResponse($machineProblemReport->delete());
    }


    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index(Station $station, Machine $machine)
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request, Station $station, Machine $machine)
    {
        $request->validate([
            'description' => 'string',
            'problems' => 'array',
        ]);
        $model = $machine->reports()->create(array_merge($request->all(), [
            'status' => MachineProblemReport::REPORTED,
            'station_id' => $station->id
        ]));
        $model->problems()->sync($request->input('problems') ?? []);
        return $this->handleResponse($model, trans('request.done'));
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy($id)
    {
        //
    }
}
