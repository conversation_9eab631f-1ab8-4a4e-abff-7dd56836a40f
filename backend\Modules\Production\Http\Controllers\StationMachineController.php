<?php

namespace Modules\Production\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\API\BaseController;
use Arr;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Good\Entities\Attribute;
use Modules\Production\Entities\Station;
use Modules\Production\Entities\Machine;

class StationMachineController extends BaseController
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index(Station $station)
    {
        return JsonResource::collection($station->machines()->with('problems')->get())->additional([
            'model' => $station,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Station $station, Request $request)
    {
        $item = Machine::create(array_merge($request->all(), [
            'station_id' => $station->id,
        ]));
        $item->problems()->sync(collect($request->input('problems') ?? [])->map(fn($m) => Arr::only($m, ['name', 'id']))->toArray());
        $item->problems;
        return $this->handleResponse($item, trans('request.done'));
    }

    /**
     * Show the specified resource.
     * @param int Station $station, Machine $machine
     * @return Response
     */
    public function show(Station $station, Machine $machine)
    {
        $machine->problems;
        return $this->handleResponse($machine);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int Station $station, Machine $machine
     * @return Response
     */
    public function update(Request $request, Station $station, Machine $machine)
    {
        $machine->update(array_merge($request->all(), [
            'station_id' => $station->id,
        ]));
        $machine->problems()->sync(collect($request->input('problems') ?? [])->map(fn($m) => Arr::only($m, ['name', 'id']))->toArray());
        $machine->problems;

        return $this->handleResponse($machine, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     * @param int Station $station, Machine $machine
     * @return Response
     */
    public function destroy(Station $station, Machine $machine)
    {
        return $this->handleResponse($machine->delete());
    }
}
