<?php

namespace App\Http\Controllers;

use App\Http\Controllers\API\BaseController;
use App\Models\Guarantee;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use Modules\Production\Entities\ProductionOrderItem;

class GuaranteeController extends BaseController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $user = Auth::user();
        return [
            'data' => Guarantee::query()->where('user_id', $user->id)->get()->map(function ($m) {
                $m['delivery_date'] = verta()->instance($m['delivery_date'])->timezone('Asia/Tehran')->format('Y/m/d');
                $m['activation_date'] = verta()->instance($m['activation_date'])->timezone('Asia/Tehran')->format('Y/m/d');
                return $m;
            }),
            'user' => array_merge($user->toArray(), [
                'sign_date' => verta()->instance($user->created_at)->format('Y/m/d'),
            ]),
        ];
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'address' => 'required',
            'barcode' => 'required|unique:guarantees',
        ], [
            'barcode.unique' => 'بارکد قبلا ثبت شده است!'
        ]);

        if ($validator->fails()) {
            //return response()->json(['errors' => $validator->errors()], 429);
            //return $this->handleError($validator->errors(), ['error' => $validator->errors()], 429);
            return response()->json(['message' => $validator->errors()], 429);
        } else {
            $barcode =  $request->input('barcode');
            $ex = explode('.', $barcode); //$request->input('barcode'));
            // if (!isset($barcode[1]))
            //     $barcode[1] = 1;
            // $barcode = implode('.', $barcode);

            if (Guarantee::query()->where('barcode', $request->input('barcode'))->count() > 0)
               // return response()->json(['errors' => ['barcode' => 'بار کد قبلا ثبت شده است!']], 429);
               return response()->json(['message' => 'بار کد قبلا ثبت شده است!'], 429);
               // return $this->handleError('بار کد قبلا ثبت شده است!', ['error' => 'بار کد قبلا ثبت شده است!'], 429);

            $check = ProductionOrderItem::query()->whereHas('productionOrder')
                ->where('id', $ex[0])->where('attributes', 'like', '%"typeMaterialDoor":"fomizeh"%')
                ->where('count', '>=', $ex[1] ?? 1)
                ->with('productionOrder')
                ->first();
            //return response()->json($check);
            //  dd($barcode,$check);

            if (!!$check) {
                //dd(Auth::user()->id, $check->productionOrder->code, $check->productionOrder->getRawOriginal('delivery_date'))
                Guarantee::query()->create([
                    'user_id' => Auth::user()->id,
                    'production_order_code' => $check->productionOrder->code,
                    'barcode' => $barcode,
                    'delivery_date' => $check->productionOrder->getRawOriginal('delivery_date'),
                    'activation_date' => now(),
                    'address' => $request->input('address'),
                ]);
                return response()->json(['message' => 'گارانتی با موفقیت ثبت شد!']);
            } else {
                return response()->json(['message' => 'بارکد اشتباه است!'], 429);
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Guarantee  $guarantee
     * @return \Illuminate\Http\Response
     */
    public function show(Guarantee $guarantee)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Guarantee  $guarantee
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Guarantee $guarantee)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Guarantee  $guarantee
     * @return \Illuminate\Http\Response
     */
    public function destroy(Guarantee $guarantee)
    {
        //
    }

    /**
     * Search the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function search()
    {
        //
    }
}
