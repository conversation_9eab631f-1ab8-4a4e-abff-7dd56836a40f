<template>
    <j-input v-model="form.name" dense error-field="name" label="نام گروه" />
    <j-toggle v-model="form.is_active" label="فعال"></j-toggle>
    <j-toggle v-model="form.is_active_customer" label="فعال مشتری"></j-toggle>
    <instruction v-if="form.attributes" v-model:data="form.instructions" :id="form.id" :attributes="form.attributes"
        :group_id="form.group_id" />
    <q-separator spaced />


    <attribute-group v-model:value="form.attributes" :attributes="additional.attributes"
        v-model:default_attribute="form.default_attribute" />
</template> 
<script>
import Instruction from './instruction.vue';
import AttributeGroup from "./AttributeGroup.vue";

export default {
    props: {
        form: {
            type: Object,
            default: () => { default_attribute: { } }
        },
        formOption: {
            type: Object,
            default: () => { }
        },
        additional: {
            type: Object,
            default: () => { }
        }
    },
    components: { AttributeGroup, Instruction }
};
</script>