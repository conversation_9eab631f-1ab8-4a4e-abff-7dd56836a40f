<template>
  <q-select ref="_ref" :rules="rules" outlined  
    :error-message="requestStore.errors[errorField] && requestStore.errors[errorField][0] ? requestStore.errors[errorField].join('') : undefined"
    :error="!!requestStore.errors[errorField]" :use-input="!!searchLocal" emit-value map-options :options="options"
    :options-dense="dense" :dense="dense" @filter="filterFn" @keyup="onFocus" behavior="menu" >
    <template v-for="_, slot in $slots" v-slot:[slot]="props" :key="slot">
      <slot :name="slot" v-bind="props" :key="slot" />
    </template>
  </q-select>
</template>
<script>
import { validationApi } from '@/helpers';
import { ref } from '@vue/reactivity';
import { watch } from 'vue';

export default {
  props: {
    required: {
      type: Boolean,
      default: false,
    },
    options: {
      type: Array,
      default: () => [],
    },
    errorField: {
      type: String,
      default: "",
    },
    dense: {
      type: Boolean,
      default: false,
    },
    searchLocal: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const { rules, requestStore } = validationApi(props)
    const _ref = ref(null)


    const options = ref(props.options ?? [])
    const orginal = ref(props.options ?? [])
    watch(() => props.options, newVal => {
      options.value = newVal;
      orginal.value = newVal;
    }, { deep: true })
    return {
      options,
      rules,
      requestStore,
      _ref,
      ref_instance: _ref,
      focus: () => _ref.value.focus(),
      onFocus: (e) => {
        if (e.keyCode === 9) {
          _ref.value.showPopup()
        }
      },
      filterFn(val, update) {
        if (!props.searchLocal) {
          update();
          return;

        }
        if (val === '') {
          update(() => {
            options.value = orginal.value;

            // here you have access to "ref" which
            // is the Vue reference of the QSelect
          })
          return
        }

        update(() => {
          const needle = val.toLowerCase()
          options.value = orginal.value.filter(v => v.name.toLowerCase().indexOf(needle) > -1)
        })
      }
    }
  },
}
</script>