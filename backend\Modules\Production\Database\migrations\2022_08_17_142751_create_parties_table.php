<?php

use App\Models\City;
use App\Models\Province;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create($this->table(), function (Blueprint $table) {
            $table->id();
            $table->string('full_name');
            $table->string('display_name')->nullable();
            //$table->string('phone_number');
            $table->text('address')->nullable();

            //$table->string('status')->default('pending');
            //$table->string('national_code')->unique()->nullable();
            $table->string('mobile_number')->unique()->nullable();
            $table->string('password')->nullable();
            $table->boolean('enable')->default(false);
            $table->foreignIdFor(Province::class)->nullable()->constrained(Province::getTableName())->cascadeOnUpdate();
            $table->foreignIdFor(City::class)->nullable()->constrained(City::getTableName())->cascadeOnUpdate();
            $table->string('image')->nullable();
            $table->json('location')->nullable();
            $table->rememberToken();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists($this->table());
    }

    public function table()
    {
        $prefix = config('production.prefix');
        return ($prefix ? $prefix . '__' : '') . 'parties';
    }
};
