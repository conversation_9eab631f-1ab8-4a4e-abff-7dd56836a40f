<?php

namespace $MODULE_NAMESPACE$\$STUDLY_NAME$\Entities;

use App\Models\UModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class BModel extends UModel
{
    use HasFactory;
    public function getTable()
    {
        return $this->table ?? (config('$LOWER_NAME$.prefix') ?config('$LOWER_NAME$.prefix') .'__' : '') . Str::snake(Str::pluralStudly(class_basename($this)));

    }
}
