<?php

namespace Modules\Good\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Good\Entities\Group;

class GroupFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = \Modules\Good\Entities\Group::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => fake()->randomElement([
                'درب',
                'چهارچوب',
            ]),
            'parent_id' => null,
        ];
    }

    public function asd()
    {
        return $this->state(function () {
            return [
                'parent_id' => Group::factory()->create()->id,
            ];
        });
    }
}
