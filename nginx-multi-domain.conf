# Nginx configuration for Multi-Domain Frontend
# Place this in your nginx sites-available directory

# Panel Domain Configuration
server {
    listen 80;
    server_name panel.erp.test;
    root /var/www/frontend/dist;
    index index.html;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Handle Vue Router (SPA)
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API proxy to backend
    location /api/ {
        proxy_pass http://api.erp.test:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# CRM Domain Configuration
server {
    listen 80;
    server_name crm.erp.test;
    root /var/www/frontend/dist;
    index index.html;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Handle Vue Router (SPA)
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API proxy to backend
    location /api/ {
        proxy_pass http://api.erp.test:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# SSL Configuration (for production)
# Uncomment and configure for HTTPS

# server {
#     listen 443 ssl http2;
#     server_name panel.erp.test;
#     
#     ssl_certificate /path/to/ssl/panel.erp.test.crt;
#     ssl_certificate_key /path/to/ssl/panel.erp.test.key;
#     
#     # Include the same configuration as above
#     root /var/www/frontend/dist;
#     index index.html;
#     
#     # ... rest of configuration
# }

# server {
#     listen 443 ssl http2;
#     server_name crm.erp.test;
#     
#     ssl_certificate /path/to/ssl/crm.erp.test.crt;
#     ssl_certificate_key /path/to/ssl/crm.erp.test.key;
#     
#     # Include the same configuration as above
#     root /var/www/frontend/dist;
#     index index.html;
#     
#     # ... rest of configuration
# }
