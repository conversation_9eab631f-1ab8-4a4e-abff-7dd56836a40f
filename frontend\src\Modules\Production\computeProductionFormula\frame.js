import { parvazItems } from "./rokoob";
export function doWidthFloor({ wallThickness }) {
    switch (true) {
        case wallThickness < 13 && wallThickness >= 9:
            return {
                value: 9,
                label: '9',
            };
        case (wallThickness <= 17 && wallThickness >= 13) ||
            (wallThickness <= 24 && wallThickness > 21):
            return {
                value: 13,
                label: '13',
            };;
        case (wallThickness <= 21 && wallThickness > 17) ||
            wallThickness > 24:
            return {
                value: 17,
                label: '17',
            };
    }
    return false;
};

export function doMokammelWidth({ wallThickness, widthFloor }) {
    wallThickness = wallThickness * 1;
    widthFloor = widthFloor * 1;
    const temp = wallThickness - widthFloor - 4;
    if (0 < temp && temp <= 7)
        return 7;
    else if (temp > 7)
        return temp;

    return 0;

};

export function doMokammelCount({ frameWidth }) {
    if (frameWidth >= 136) return 4
    else if (frameWidth >= 106) return 3;
    else return 2.5
};

export function doMokammelPerLength({ frameHeight }) {
    if (frameHeight + 3 <= 220)
        return 220;
    else
        return 244;
};

export function doThicknessEdgeOfFrame({ doorThickness }) {
    switch (doorThickness * 1) {
        case 4:
            return 4.5;
        case 4.4:
            return 4.9;
        case 4.8:
            return 5.3;
    }
    return false;
};

export default function (
    { frameHeight, frameWidth, hasThreshold, wallThickness, typeMaterialFrame, typeParvaz, widthFloor, mokammelWidth = 0, mokammelCount = 0, mokammelPerLength = 0, countParvaz = null },
    count = 1
) {
    frameHeight *= 1;
    frameWidth *= 1;
    widthFloor *= 1;
    mokammelWidth *= 1;
    mokammelCount *= 1;
    mokammelPerLength *= 1;
    //console.log(attributes);
    // const mokammelWidth = doMokammelWidth({ wallThickness, widthFloor });

    // const widthFloor = doWidthFloor({ wallThickness })?.value;

    const countPich =
        (() => {
            if (widthFloor == 9) return 2;
            return 4;
        })() *
        (hasThreshold ? 2 : 1) *
        count;

    const mokammel = {
        width: mokammelWidth,
        counts: (() => {
            let counts = {};
            if (mokammelWidth == 0) return counts;


            (() => {
                const vertical = frameHeight + 3;
                switch (true) {
                    case vertical <= 220:
                        counts[220] = 2 * count;
                        return;
                    case vertical <= 244:
                        counts[244] = 2 * count;
                        return;
                }
            })();
            (() => {

                if (frameWidth >= 136)
                    return counts[Object.keys(counts)[0]] += 2
                        * count;
                else if (frameWidth >= 106)
                    return counts[Object.keys(counts)[0]] += 1 * count;
                else return counts[Object.keys(counts)[0]] += 0.5 * count;

                // const horizontal = frameWidth + 3;
                // switch (true) {
                //     case horizontal * 2 <= 220:
                //         counts[220] += 0.5 * count;
                //         return;
                //     case horizontal * 2 <= 244:
                //         counts[244] += 0.5 * count;
                //         return;
                //     case horizontal <= 220:
                //         counts[220] += 1 * count;
                //         return;
                //     case horizontal <= 244:
                //         counts[244] += 1 * count;
                //         return;
                // }
            })();
            return counts;
        })(),
    };

    const parvazCounts = (() => {
        const parvazSizes = parvazItems
            .filter(
                (f) =>
                    f.typeMaterialRokoob.includes(typeMaterialFrame) &&
                    f.typeParvaz == typeParvaz
            )
            .map((m) => m.includes)[0];
        let counts = {};
        (() => {
            const vertical = frameHeight + 3 + (hasThreshold ? 14 : 7);
            if (!parvazSizes)
                return;
            const max = Math.max(...parvazSizes)
            if (max < vertical) {
                counts[max] = 4 * count;
                return;

            }
            for (const parvazSize of parvazSizes) {
                if (vertical <= parvazSize) {
                    counts[parvazSize] = (counts[parvazSize] ?? 0) + 4 * count;
                    return;
                }
            }
        })();
        (() => {

            // if (frameWidth >= 136)
            //     return counts[Object.keys(counts)[0]] += (hasThreshold ? 2 : 1) * 4 * count;
            if (frameWidth >= 106)
                return counts[Object.keys(counts)[0]] += (hasThreshold ? 2 : 1) * 2 * count;
            else return counts[Object.keys(counts)[0]] += (hasThreshold ? 2 : 1) * 1 * count;


            // const horizontal = frameWidth + 3 + 14;
            // for (const parvazSize of parvazSizes) {
            //     if (horizontal * 2 <= parvazSize) {
            //         counts[parvazSize] =
            //             (counts[parvazSize] ?? 0) + (hasThreshold ? 2 : 1) * 1 * count;
            //         return;
            //     }
            // }
            // for (const parvazSize of parvazSizes) {
            //     if (horizontal <= parvazSize) {
            //         counts[parvazSize] = (counts[parvazSize] ?? 0) + (hasThreshold ? 2 : 1) * 2 * count;
            //         return;
            //     }
            // }
        })();
        return counts;
    })();

    const perLengthPasarFrame = (() => {
        const frameWidthProduction = frameWidth + 3;
        switch (true) {
            case frameWidthProduction <= 94:
                return 94;
            case frameWidthProduction <= 205:
                return 205;
            case frameWidthProduction <= 215:
                return 215;
            case frameWidthProduction <= 220:
                return 220;
            case frameWidthProduction <= 225:
                return 225;
            case frameWidthProduction <= 244:
                return 244;
        }
        return 0;
    })();

    const lengthPasarSlitter = (() => {
        const frameWidthProduction = frameWidth + 3;
        switch (true) {
            case frameWidthProduction <= 94:
                return 94;
            case frameWidthProduction <= 122:
                return 122;
            case frameWidthProduction <= 205:
                return 205;
            case frameWidthProduction <= 215:
                return 215;
            case frameWidthProduction <= 220:
                return 220;
            case frameWidthProduction <= 225:
                return 225;
            case frameWidthProduction <= 244:
                return 244;
        }
        return 0;
    })();

    const countPasarFrame =
        (() => {
            if (hasThreshold) return 2;
            return 1;
        })() * count;

    const countBaoFrame = 2 * count;
    // console.log(countBaoFrame)
    const perLengthBaoFrame = (() => {
        const frameHeightProduction = frameHeight + 3;
        switch (true) {
            case frameHeightProduction <= 205:
                return 205;
            case frameHeightProduction <= 215:
                return 215;
            case frameHeightProduction <= 220:
                return 220;
            case frameHeightProduction <= 225:
                return 225;
            case frameHeightProduction <= 244:
                return 244;
        }
        return 0;
    })();

    const hasMokammel = mokammelWidth !== 0;
    const lengthNavardarzgir =
        (frameHeight * 2 + frameWidth * (hasThreshold ? 2 : 1)) * count;
    const countVasher = countPich * 2;

    const res = {
        widthFloor,
        perLengthPasarFrame,
        lengthPasarSlitter,
        countPasarFrame,
        perLengthBaoFrame,
        countBaoFrame,
        //parvazCounts,
        countParvaz: countParvaz > 0 ? countParvaz * count : (Object.values(parvazCounts).length > 0 ? Object.values(parvazCounts).reduce((s, x) => s + x, 0) : ''),
        perLengthParvaz: Object.keys(parvazCounts).length > 0 ? Object.keys(parvazCounts)[0] : '',
        hasMokammel,
        mokammelWidth,
        //mokammelCount: Object.values(mokammel.counts).length > 0 ? Object.values(mokammel.counts).reduce((s, x) => s + x, 0) : '',
        mokammelCount,
        //mokammelPerLength: Object.keys(mokammel.counts).length > 0 ? Object.keys(mokammel.counts)[0] : '',
        mokammelPerLength,
        lengthNavardarzgir,
        countPich,
        countVasher,
    };

    return res;
}
