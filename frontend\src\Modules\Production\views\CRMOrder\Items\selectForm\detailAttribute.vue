<template>
    <!-- {{ attribute_columns }} -->
    <table class="w-full">
        <thead>
            <tr>
                <th v-for="attribute in attribute_columns">
                    {{ attribute.label }}
                </th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td v-for="attribute in attribute_columns" class="text-center">
                    <template v-if="typeof attribute.field == 'string'">
                        {{ data[attribute.field] }}
                    </template>
                    <template v-else-if="typeof attribute.field == 'function'">
                        {{ attribute.field(data) }}
                    </template>
                </td>
            </tr>
        </tbody>
    </table>
</template>
<script>
import { computeProductionOrder } from '@/Modules/Production/computeProductionFormula';
import { ref, watch, computed } from 'vue'
import { attributeColumns } from '.';
export default {
    props: {
        row: {
            type: Object,
            default: () => { }
        },
        attributes: {
            type: Array,
            default: () => []
        }
    },
    setup(props) {
        const data = ref(computeProductionOrder(props.row, props.row.good) ?? {});
        watch(() => props.row, newVal => {
            data.value = computeProductionOrder(newVal, newVal.good)
            //console.log(data.value)
        })
        return {
            data,
            attribute_columns: computed(() => {
                return attributeColumns({ attributes: props.attributes, items: [data.value] })
            }),

        }
    }
}
</script>