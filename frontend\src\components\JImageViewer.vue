<template>
    <div class="flex gap-2 items-center place-content-center">
        <img v-if="src" :src="src" :class="fullHeight ? '' : 'h-7'" @click="isShowImage = true">
        <!-- <q-img v-if="src" :src="src" :class="fullHeight ? '' : 'h-10'" no-native-menu @click="isShowImage = true" /> -->

        <j-icon v-else name="image" size="md" color="primary" />
        <span v-if="title">{{ title }}</span>

        <!-- <img v-if="src" :src="src" :class="fullHeight ? '' : 'h-10'" @click="isShowImage = true"> -->
        <!-- <q-img v-if="src" :src="src" :class="fullHeight ? '' : 'h-10'" no-native-menu @click="isShowImage = true" /> -->
        <j-dialog v-if="zoom" v-model="isShowImage">
            <div class="contents p-10">
                <img :src="src" class="rounded-md m-auto h-full" />
            </div>
        </j-dialog>
    </div>
</template>
<script setup>
import { ref } from 'vue'

const props = defineProps({
    src: String,
    title: String,
    fullHeight: Boolean,
    zoom: {
        type: Boolean,
        default: true,
    },
})
const isShowImage = ref(false)
</script>
<!-- 

<template>
    <div>
        <img :src="src" class="h-10" @click="isShowImage = true">
        <j-dialog v-model="isShowImage">
            <div class="contents p-10">
                <img :src="src" class="rounded-md m-auto h-full" />
            </div>
        </j-dialog>
    </div>
</template>
<script setup>
import { ref } from 'vue'

const props = defineProps({
    src: String,
})
const isShowImage = ref(false)
</script> -->