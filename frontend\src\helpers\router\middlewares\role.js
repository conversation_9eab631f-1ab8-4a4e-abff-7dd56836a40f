import { crm_home_page as home_page } from "../index";

export default function middlewareRoles({ next, router, auth, to }) {
    console.log("middlewareRoles")
    if (!checkRole(auth, to.meta.roles))
        router.push({ name: home_page() });
    else
        next();
}

function checkRole(auth, roles) {
    if (auth.user && auth.user.is_super_admin) return true;
    if (Array.isArray(roles)) {
        let find = false;
        roles.map((m) => {
            if (auth.user.roles.includes(m)) find = true;
        });
        if (!find) return false;
    } else if (typeof roles == "string") {
        if (!auth.user.roles.includes(roles)) return false;
    }
    return true;
}