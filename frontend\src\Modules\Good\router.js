import { iconImage } from '@/helpers/functions';
import {
    GoodForm,
    GoodView,
    GroupView,
    AttributeView,
    AttributeForm,
    workInstructionView,
} from './views';

export default [
    {
        path: '/products',
        name: 'products',
        meta: {
            title: 'محصولات',
            icon: iconImage('product'),
           // permissions: pers => pers.filter(f => ['products', 'attributes'].includes(f)).length > 0,
        },
        children: [
            {
                path: 'lists',
                name: 'product_lists',
                //component: GoodView,
                meta: {
                    title: 'لیست محصولات',
                    icon: iconImage('productList'),
                },
                children: [
                    {
                        path: '',
                        name: 'products.index',
                        component: GoodView,
                        meta: {
                            title: 'لیست محصولات',
                            permissions: 'products',
                            icon: iconImage('productList'),
                        },
                    },
                    {
                        path: 'create',
                        name: 'products.create',
                        component: GoodForm,
                        hidden: true,
                        meta: {
                            title: 'ایجاد محصولات',
                            icon: 'add',
                            permissions: 'products.create',

                        },
                    },
                    {
                        path: ':id/edit',
                        name: 'products.edit',
                        component: GoodForm,
                        props: true,
                        hidden: true,
                        meta: {
                            title: 'ویرایش محصولات',
                            icon: 'edit',
                            permissions: 'products.edit',

                        },
                    }
                ]
            },
            {
                path: 'attribute',
                name: 'attribute',
                //component: AttributeView,
                meta: {
                    title: 'ویژگی ها',
                    icon: iconImage('productAttribute'),

                },
                children: [

                    {
                        path: '',
                        name: 'attributes.index',
                        component: AttributeView,
                        meta: {
                            title: 'ویژگی ها',
                            icon: iconImage('productAttribute'),
                            permissions: 'attributes',

                        },
                    },
                    {
                        path: 'create',
                        name: 'attributes.create',
                        component: AttributeForm,
                        hidden: true,
                        meta: {
                            title: 'ایجاد ویژگی',
                            icon: 'add',
                            permissions: 'attributes.create',

                        },
                    },
                    {
                        path: ':id/edit',
                        name: 'attributes.edit',
                        component: AttributeForm,
                        props: true,
                        hidden: true,
                        meta: {
                            title: 'ویرایش ویژگی',
                            icon: 'edit',
                            permissions: 'attributes.edit',

                        },
                    },

                ]
            },
        ],
    },

];
