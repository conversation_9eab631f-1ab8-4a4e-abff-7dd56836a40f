import { nextTick, onBeforeMount, onUnmounted, ref, toRaw, watch } from "vue";
import { api } from "@/boot/axios";
import { useQuasar } from "quasar";
import { useRoute, useRouter } from "vue-router";
import { useTableStore } from "@/stores/table.store";

export const tableApi = (uri, props = {}, context = null) => {
    // Quasar, Route, and Store Instances
    const $q = useQuasar();
    const route = useRoute();
    const router = useRouter();
    const tableStore = useTableStore();
    const tableRef = ref();



    // Table & Form Data
    const url = ref(uri ?? "");
    const rows = ref(props.rows ?? []);
    const rowKey = ref(props.rowKey ?? 'id');
    const columns = ref(props.columns ?? []);
    const selected = ref([]);
    const form = ref(props.form ?? {});
    const additional = ref({});
    const serverData = ref({}); // Store complete server response
    const hasPagination = ref(false);
    const dialogDelete = ref(false);
    const hasChange = ref(false);
    const loading = ref(false);

    // Filters & Pagination
    const filters = ref({});
    const filterConditions = ref({});
    const paramFilter = ref({});
    const filterOptions = ref([
        // Text filters
        { label: 'شامل شود', value: 'includes', icon: 'contains', type: 'text' },
        { label: 'شامل نشود', value: 'excludes', icon: 'not-contains', type: 'text' },
        { label: 'شروع شود با', value: 'startsWith', icon: 'starts-with', type: 'text' },
        { label: 'پایان یابد با', value: 'endsWith', icon: 'ends-with', type: 'text' },

        // Number filters
        { label: 'کوچکتر باشد از', value: 'lessThan', icon: 'less', type: 'number' },
        { label: 'کوچکتر یا مساوی باشد با', value: 'lessThanOrEqual', icon: 'less-equal', type: 'number' },
        { label: 'بزرگتر باشد از', value: 'greaterThan', icon: 'greater', type: 'number' },
        { label: 'بزرگتر یا مساوی باشد با', value: 'greaterThanOrEqual', icon: 'greater-equal', type: 'number' },
        { label: 'برابر باشد با', value: 'equals', icon: 'equals', type: 'text'},
        { label: 'برابر نباشد با', value: 'notEquals', icon: 'not-equals', type: 'text' },
        { label: 'حذف فیلتر', value: null, icon: 'default' },

        // Date filters
        { label: 'مساوی باشد', value: 'dateEquals', icon: 'equal', type: 'date' },
        { label: 'بین دو تاریخ', value: 'dateBetween', icon: 'between', type: 'date' },
        { label: 'قبل از', value: 'dateBefore', icon: 'less', type: 'date' },
        { label: 'بعد از', value: 'dateAfter', icon: 'greater', type: 'date' },
    ]);
    const pagination = ref({
        sortBy: props.pagination?.sortBy ?? route.query.sortBy ?? "id",
        descending: props.pagination?.descending ?? route.query.descending ?? null,
        page: props.pagination?.page ?? route.query.page ?? 1,
        rowsPerPage: props.pagination?.rowsPerPage ?? route.query.rowsPerPage ?? 10,
        rowsNumber: 10,
    });

    // Form Reset & Submit
    const resetForm = () => {
        form.value = {};
        hasChange.value = false;
    };

    const submitForm = async () => {
        if (url.value) {
            if (form.value && form.value[rowKey.value]) {
                await api.put(`${url.value}/${form.value[rowKey.value]}`, form.value)
                    .then((res) => {
                        form.value = res.result;
                        context.emit('afterSubmit');
                    });
            } else {
                await api.post(url.value, form.value)
                    .then((res) => {
                        form.value = res.result;
                        context.emit('afterSubmit');
                    });
            }
        } else {
            if (selected.value[0] && selected.value[0][rowKey.value]) {
                const find = rows.value.findIndex((f) => f[rowKey.value] == selected.value[0][rowKey.value]);
                if (find >= 0) rows.value.splice(find, 1, form.value);
            } else {
                rows.value.push(form.value);
            }
        }
        context.emit('update:rows', rows.value);
        context.emit('input:rows', rows.value);
    };

    // Helper function to get column name by field
    const getColumnNameByField = (field) => {
        const column = columns.value.find(col => col.field === field);
        return column ? column.name : field;
    };

    // Helper function to get column field by name
    const getColumnFieldByName = (name) => {
        const column = columns.value.find(col => col.name === name);
        return column ? column.field : name;
    };

    // Data Fetching & Filtering
    const fetchData = async (params = {}) => {
        if (!url.value) return;
        loading.value = true;



        try {
            // Build proper query parameters structure
            const queryParams = {
                page: pagination.value.page,
                sortBy: pagination.value.sortBy,
                descending: pagination.value.descending,
                rowsPerPage: pagination.value.rowsPerPage,
            };

            // Add filters in the correct format: filters[name]=value (use column name, not field)
            Object.keys(filters.value).forEach(field => {
                if (filters.value[field] && filters.value[field] !== '') {
                    const columnName = getColumnNameByField(field); // Use column name instead of field

                    if (Array.isArray(filters.value[field]) && filters.value[field].length > 0) {
                        // For select filters (arrays) - send as filters[name][0], filters[name][1], etc.
                        filters.value[field].forEach((value, index) => {
                            if (typeof value === 'object' && value.value) {
                                // If it's an object with value property
                                queryParams[`filters[${columnName}][${index}]`] = value.value;
                            } else {
                                // If it's a simple value
                                queryParams[`filters[${columnName}][${index}]`] = value;
                            }
                        });
                    } else if (typeof filters.value[field] === 'string' && filters.value[field].trim() !== '') {
                        // For text/number/date filters
                        queryParams[`filters[${columnName}]`] = filters.value[field];
                    }
                }
            });

            // Add conditions in the correct format: conditions[name]=condition (use column name, not field)
            Object.keys(filterConditions.value).forEach(field => {
                if (filterConditions.value[field]) {
                    const columnName = getColumnNameByField(field); // Use column name instead of field
                    queryParams[`conditions[${columnName}]`] = filterConditions.value[field];
                }
            });

            const response = await api.get(url.value, {
                params: queryParams,
            });

            // Store complete server response
            serverData.value = response;

            rows.value = response.data;
            pagination.value.page = response.meta.current_page;
            pagination.value.rowsPerPage = response.meta.per_page;
            pagination.value.rowsNumber = response.meta.total;
        } catch (error) {
            console.error('Error fetching data:', error);
        } finally {
            loading.value = false;
        }
        selected.value = [];
    };

    const onFilter = () => {
        paramFilter.value = { ...filters.value };
        Object.keys(paramFilter.value).forEach((key) => {
            if (!paramFilter.value[key].value) delete paramFilter.value[key];
        });
        fetchData();
    };

    // Delete Handler
    const onDelete = () => {
        if (dialogDelete.value) return;
        dialogDelete.value = true;
        $q.dialog({
            title: "مطمئن هستید؟",
            cancel: true,
        })
            .onOk(() => {
                if (url.value) {
                    api.delete(`${url.value}/${selected.value[0][rowKey.value]}`)
                        .then(() => fetchData());
                } else {
                    const find = rows.value.findIndex(f => f[rowKey.value] == selected.value[0][rowKey.value]);
                    if (find >= 0) rows.value.splice(find, 1);
                    context.emit('update:rows', rows.value);
                    context.emit('input:rows', rows.value);
                }
                dialogDelete.value = false;
            })
            .onCancel(() => {
                dialogDelete.value = false;
            });
    };

    const handleSearch = (field) => {
        console.log('handleSearch', field)

        fetchData();
        updateQueryParams();

        /*if (!filterConditions.value[field]) {
            setFilterCondition(field, 'includes'); // Change filter type to includes
        }*/
    };

    // Query Params & Pagination Update
    const updateQueryParams = () => {
        const query = {
            page: pagination.value.page,
            sortBy: pagination.value.sortBy,
            descending: pagination.value.descending,
            rowsPerPage: pagination.value.rowsPerPage,
        };

        // Add filters in the correct format: filters[name]=value (use column name, not field)
        Object.keys(filters.value).forEach(field => {
            if (filters.value[field] && filters.value[field] !== '') {
                const columnName = getColumnNameByField(field); // Use column name instead of field

                if (Array.isArray(filters.value[field]) && filters.value[field].length > 0) {
                    // For select filters (arrays) - send as filters[name][0], filters[name][1], etc.
                    filters.value[field].forEach((value, index) => {
                        if (typeof value === 'object' && value.value) {
                            // If it's an object with value property
                            query[`filters[${columnName}][${index}]`] = value.value;
                        } else {
                            // If it's a simple value
                            query[`filters[${columnName}][${index}]`] = value;
                        }
                    });
                } else if (typeof filters.value[field] === 'string' && filters.value[field].trim() !== '') {
                    query[`filters[${columnName}]`] = filters.value[field];
                }
            }
        });

        // Add conditions in the correct format: conditions[name]=condition (use column name, not field)
        Object.keys(filterConditions.value).forEach(field => {
            if (filterConditions.value[field]) {
                const columnName = getColumnNameByField(field); // Use column name instead of field
                query[`conditions[${columnName}]`] = filterConditions.value[field];
            }
        });

        router.push({ query });
    };

    const setFilterCondition = (field, value) => {
        console.log('setFilterCondition', { field, value })
        filterConditions.value[field] = value;
        if (value === null) {
            // Clear all related filter values
            filters.value[field] = '';
            filters.value[field + '_from'] = '';
            filters.value[field + '_to'] = '';
        }
        fetchData();
        updateQueryParams();
    };

    // Watchers & Lifecycle Hooks
    watch(() => props.rows, (newVal) => {
        rows.value = newVal;
    });

    watch(() => selected.value, (newVal) => {
        tableStore.props = { selected: newVal };
    });

    watch(() => columns.value, (newVal) => {
        if (newVal.filter(f => f.filter).length === 0 || Object.keys(route.query).length === 0) {
            fetchData();
        }
    });

    onBeforeMount(async () => {
        const currentQuery = router.currentRoute.value.query;

        // Parse filters from query: filters[name]=value (convert name to field)
        Object.keys(currentQuery).forEach(key => {
            const filterMatch = key.match(/^filters\[(.+?)\](?:\[(\d+)\])?$/);
            const conditionMatch = key.match(/^conditions\[(.+)\]$/);

            if (filterMatch) {
                const name = filterMatch[1]; // This is the column name from URL
                const field = getColumnFieldByName(name); // Convert name to field
                const index = filterMatch[2];
                const value = currentQuery[key];

                if (index !== undefined) {
                    // Array value (for select filters) - filters[name][0], filters[name][1], etc.
                    if (!filters.value[field]) {
                        filters.value[field] = [];
                    }
                    filters.value[field][parseInt(index)] = value;
                } else {
                    // String value (for text/number/date filters)
                    filters.value[field] = value;
                }
            } else if (conditionMatch) {
                const name = conditionMatch[1]; // This is the column name from URL
                const field = getColumnFieldByName(name); // Convert name to field
                filterConditions.value[field] = currentQuery[key];
            }
        });

        if (currentQuery.page) pagination.value.page = parseInt(currentQuery.page);
        if (currentQuery.sortBy) pagination.value.sortBy = currentQuery.sortBy;
        if (currentQuery.descending) pagination.value.descending = currentQuery.descending === 'true';
        if (currentQuery.rowsPerPage) pagination.value.rowsPerPage = parseInt(currentQuery.rowsPerPage);

        await fetchData();
    });

    onUnmounted(() => {
        tableStore.unmount();
    });



    let storedSelectedRow;
    const handleSelection = ({ rows, added, evt }) => {
        // ignore selection change from header of not from a direct click event
        if (rows.length !== 1 || evt === void 0) {
            return
        }

        const oldSelectedRow = storedSelectedRow
        const [newSelectedRow] = rows
        const { ctrlKey, shiftKey } = evt

        if (shiftKey !== true) {
            storedSelectedRow = newSelectedRow
        }

        // wait for the default selection to be performed
        nextTick(() => {
            if (shiftKey === true) {
                const tableRows = tableRef.value.filteredSortedRows
                let firstIndex = tableRows.indexOf(oldSelectedRow)
                let lastIndex = tableRows.indexOf(newSelectedRow)

                if (firstIndex < 0) {
                    firstIndex = 0
                }

                if (firstIndex > lastIndex) {
                    [firstIndex, lastIndex] = [lastIndex, firstIndex]
                }

                const rangeRows = tableRows.slice(firstIndex, lastIndex + 1)
                // we need the original row object so we can match them against the rows in range
                const selectedRows = selected.value.map(toRaw)

                selected.value = added === true
                    ? selectedRows.concat(rangeRows.filter(row => selectedRows.includes(row) === false))
                    : selectedRows.filter(row => rangeRows.includes(row) === false)
            }
            else if (ctrlKey !== true && added === true) {
                selected.value = [newSelectedRow]
            }

        })
    }

    const onRequest = (props) => {
        console.log('onRequert')
        pagination.value.sortBy = props.pagination.sortBy
        pagination.value.descending = pagination.value.sortBy ? props.pagination.descending : null
        pagination.value.rowsPerPage = props.pagination.rowsPerPage
        pagination.value.page = props.pagination.page
        updateQueryParams();
        fetchData()

    }

    tableStore.actions.fetchData = fetchData;
    tableStore.actions.doDelete = async function (value) {
        //console.log('delete')
        $q.dialog({
            title: 'حذف',
            message: 'شما در حال حذف اطلاعات انتخابی می باشید، مطمئن هستید؟',
            cancel: true,
            persistent: true
        }).onOk(() => {
            api.post(props.url + '/delete', { ids: value.map(m => m.id) }).then(res => {
                console.log('empty selected');
                selected.value = []
                fetchData();
            });
        })


    }

    return {
        url,
        rows,
        rowKey,
        columns,
        selected,
        form,
        additional,
        serverData,
        hasPagination,
        filters,
        filterConditions,
        pagination,
        filterOptions,
        tableRef,
        tableStore,
        hasChange,
        handleSearch,
        paramFilter,
        dialogDelete,
        loading,
        resetForm,
        submitForm,
        fetchData,
        onDelete,
        onFilter,
        updateQueryParams,
        setFilterCondition,
        handleSelection,
        onRequest,
    };
};
