<template>
    <div class="flex items-center flex-col justify-center h-screen max-h-inherit">
        <h1 class="font-black">403</h1>
        <div class="text-2xl">دسترسی غیر مجاز</div>
        <div class="text-lg q-mt-md">{{ domainConfig.name }}</div>
        <q-btn color="primary" icon-right="arrow_back" class="text-lg mt-5" @click="router.back()">بازگشت</q-btn>
    </div>
</template>

<script setup>
import { router } from "@/helpers";
import { useDomain } from "@/composables";

const { domainConfig } = useDomain();
</script>
