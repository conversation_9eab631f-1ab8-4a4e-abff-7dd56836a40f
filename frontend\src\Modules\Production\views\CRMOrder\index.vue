<template>
  <j-table-data-crud :columns="columns" url="production/crm_production_order" :tools="Tools">
    <template #body-cell-status="props">
      <q-td :props="props">
        <div>
          {{ props.value }}
          <q-badge v-if="!props.row.is_created_by_customer" color="orange" outlined label="کارخانه" />
        </div>

      </q-td>
    </template>

  </j-table-data-crud>
</template>
<script setup>
import Tools from './tools.vue'

const columns = [
  {
    name: 'code',
    required: true,
    label: 'شماره سفارش',
    field: 'code',
    sortable: true,
    style: 'width: 50px',
    filter: 'FilterInput',
  },
  {
    name: 'status',
    required: true,
    label: 'وضعیت سفارش',
    field: 'label_status',
    sortable: true,
    style: 'width: 80px',
    filter: 'FilterSelect',
    filterOption: 'statuses'
  },
  {
    name: 'customer_name',
    required: true,
    label: 'نام مشتری',
    field: 'customer_name',
    sortable: true,
    filter: 'FilterInput',
  },

  {
    name: 'created_at',
    required: true,
    label: 'تاریخ ثبت',
    field: 'created_at',
    sortable: true,
    style: 'width: 50px',
  },
  // {
  //   name: 'submit_date',
  //   required: true,
  //   label: 'تاریخ شروع',
  //   field: 'submit_date',
  //   sortable: true,
  //   style: 'width: 50px',
  //   filter: 'FilterDate'
  // },
  // {
  //   name: 'delivery_date',
  //   required: true,
  //   label: 'تاریخ سفارش',
  //   field: 'delivery_date',
  //   sortable: true,
  //   style: 'width: 50px',
  //   filter: 'FilterDate'
  // },

]


</script>
