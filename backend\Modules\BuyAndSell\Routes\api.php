<?php

use Mo<PERSON><PERSON>\BuyAndSell\Http\Controllers\OrderController;
use Modules\BuyAndSell\Http\Controllers\PriceListController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->group(function () {
    Route::put('sell/{sell}/updateStatus', [OrderController::class, 'updateStatus']);
    Route::apiResource('sell', OrderController::class);
    //Route::apiResource('party', PartyController::class);
    Route::apiResource('price_list', PriceListController::class);

    Route::get('sell/{sell}/checklists', [OrderController::class, 'checklists']);
    Route::post('sell/{sell}/changeStatus', [OrderController::class, 'changeStatus']);
});
