import { cmFormat, getField, getField<PERSON>y<PERSON><PERSON> } from ".";
import charchop from "./computeProductionFormula/frame";
function varagVast(items) {
    let res = {};
    let type = "";
    let type_front = "";
    let type_back = "";
    const pvc_front = items
        .map((m) => ({ ...m, key: m.attributes.typeFrontPVC })) // نوع pvc نما
        .group("key");

    const pvc_back = items
        .map((m) => ({ ...m, key: m.attributes.typeBackPVC })) // نوع pvc پشت
        .group("key");

    // درفک
    type = 195;
    if (pvc_front[type]) {
        res["درفک"] = [
            {
                height: cmFormat(
                    250 *
                        pvc_front[type].reduce((s, m) => {
                            return (
                                s +
                                m.count *
                                    (() => {
                                        switch (m.attributes.doorLengeh * 1) {
                                            case 1: // یک لنگه
                                            case 1.5: // 1.5 لنگه
                                                return 1;
                                            case 2: // 2لنگه
                                                return 2;
                                            case 3: // 3 لنگه
                                                return 3;
                                            case 4: // 4 لنگه
                                                return 4;
                                        }
                                    })()
                            );
                        }, 0)
                ),
                width: cmFormat(125),
            },
        ];
    }

    // سیلیکون
    type = "سیلیکون";
    type_front = 194;
    type_back = 198;
    if (pvc_front[type_front] || pvc_back[type_back]) {
        res[type] = [];
        //const res = [];
        const com_pvc_silicon = (f) => {
            let gablabeh_width = 1.5;
            if (f.attributes.hasEdgeOfDoor) {
                gablabeh_width = 3;
            }
            const door_width =
                f.attributes.doorWidth /
                (() => {
                    switch (f.attributes.doorLengeh * 1) {
                        case 1: // یک لنگه
                        case 1.5: // 1.5 لنگه
                            return 1;
                        case 2: // 2لنگه
                            return 2;
                        case 3: // 3 لنگه
                            return 3;
                        case 4: // 4 لنگه
                            return 4;
                    }
                })();

            let height =
                (1 * (f.attributes.doorHeight ?? 0) +
                    (f.attributes.hasEdgeOfDoor ? 2 : 1.5) -
                    (f.attributes.isTopPasar13 ? 17 + 13.5 : 17 * 2) +
                    2) *
                f.count;

            let width =
                1 * door_width +
                gablabeh_width -
                2 * (f.attributes_label[30] ?? 0) +
                2;

            // 1.5 لنگه
            if (f.attributes.doorLengeh == 1.5)
                width = 1 * door_width + gablabeh_width - 44 + 2;

            switch (f.attributes.doorLengeh * 1) {
                case 2: // 2لنگه
                    height *= 2;
                    break;
                case 3: // 3 لنگه
                    height *= 3;
                    break;
                case 4: // 4 لنگه
                    height *= 4;
                    break;
            }

            return {
                ...f,
                width,
                height,
            };
        };

        const sizes = [
            ...(pvc_front[type_front]
                ? pvc_front[type_front].map(com_pvc_silicon)
                : []),
            ...(pvc_back[type_back]
                ? pvc_back[type_back].map(com_pvc_silicon)
                : []),
        ];
        for (
            let index = Math.max(...sizes.map((m) => m.width));
            index >= Math.min(...sizes.map((m) => m.width));
            index -= 5
        ) {
            const filter = sizes.filter(
                (f) => index >= f.width && index - 5 < f.width
            );
            if (filter.length > 0) {
                const length = filter
                    // .map((m) => 2 * m.count * m.height)
                    .reduce((s, c) => s + c.height, 0);

                res[type].push({
                    width: cmFormat(Math.max(...filter.map((m) => m.width))),
                    height: cmFormat(length),
                });
            }
        }
    }

    // راپود
    type = "راپود";
    type_front = 196;
    type_back = 199;
    if (pvc_front[type_front] || pvc_back[type_back]) {
        res[type] = [];
        //const res = [];
        const com_pvc_rapood = (f) => {
            const door_width =
                f.attributes.doorWidth /
                (() => {
                    switch (f.attributes.doorLengeh * 1) {
                        case 1: // یک لنگه
                        case 1.5: // 1.5 لنگه
                            return 1;
                        case 2: // 2لنگه
                            return 2;
                        case 3: // 3 لنگه
                            return 3;
                        case 4: // 4 لنگه
                            return 4;
                    }
                })();

            let gablabeh_width = 1.5;
            if (f.attributes.hasEdgeOfDoor) {
                gablabeh_width = 3;
            }

            let width =
                (1 * (f.attributes.doorHeight ?? 0) +
                    (f.attributes.hasEdgeOfDoor ? 2 : 1.5) -
                    (f.attributes.isTopPasar13 ? 17 + 13.5 : 17 * 2) +
                    2) *
                f.count;

            let height =
                1 * door_width +
                gablabeh_width -
                2 * (f.attributes_label[30] ?? 0) +
                2;

            // 1.5 لنگه
            if (f.attributes.doorLengeh == 1.5)
                height = (1 * door_width + gablabeh_width - 44 + 2) * f.count;

            switch (f.attributes.doorLengeh * 1) {
                case 2: // 2لنگه
                    width *= 2;
                    break;
                case 3: // 3 لنگه
                    width *= 3;
                    break;
                case 4: // 4 لنگه
                    width *= 4;
                    break;
            }

            return {
                ...f,
                width,
                height,
            };
        };

        const sizes = [
            ...(pvc_front[type_front]
                ? pvc_front[type_front].map(com_pvc_rapood)
                : []),
            ...(pvc_back[type_back]
                ? pvc_back[type_back].map(com_pvc_rapood)
                : []),
        ];

        for (
            let index = Math.max(...sizes.map((m) => m.height));
            index >= Math.min(...sizes.map((m) => m.height));
            index -= 5
        ) {
            const filter = sizes.filter(
                (f) => index >= f.height && index - 5 < f.height
            );
            if (filter.length > 0) {
                const length = filter
                    // .map((m) => 2 * m.count * m.height)
                    .reduce((s, c) => s + c.width, 0);

                res[type].push({
                    width: cmFormat(length),
                    height: cmFormat(Math.max(...filter.map((m) => m.height))),
                });
            }
        }
    }
    return res;
}

function varagCNC(items) {
    let res = {};
    let type = "";
    let type_front = "";
    let type_back = "";
    const pvc_front = items
        .map((m) => ({ ...m, key: m.attributes.typeFrontPVC })) // نوع pvc نما
        .group("key");

    const pvc_back = items
        .map((m) => ({ ...m, key: m.attributes.typeBackPVC })) // نوع pvc پشت
        .group("key");

    // درفک
    type = 195;
    if (pvc_front[type]) {
        res["درفک"] = [
            {
                height: cmFormat(
                    250 *
                        pvc_front[type].reduce((s, m) => {
                            return (
                                s +
                                m.count *
                                    (() => {
                                        switch (m.attributes.doorLengeh * 1) {
                                            case 1: // یک لنگه
                                            case 1.5: // 1.5 لنگه
                                                return 1;
                                            case 2: // 2لنگه
                                                return 2;
                                            case 3: // 3 لنگه
                                                return 3;
                                            case 4: // 4 لنگه
                                                return 4;
                                        }
                                    })()
                            );
                        }, 0)
                ),
                width: cmFormat(125),
            },
        ];
    }

    // سیلیکون
    type = "سیلیکون";
    type_front = 194;
    type_back = 198;
    if (pvc_front[type_front] || pvc_back[type_back]) {
        res[type] = [];
        //const res = [];
        const com_pvc_silicon = (f) => {
            const door_width =
                f.attributes.doorWidth /
                (() => {
                    switch (f.attributes.doorLengeh * 1) {
                        case 1: // یک لنگه
                        case 1.5: // 1.5 لنگه
                            return 1;
                        case 2: // 2لنگه
                            return 2;
                        case 3: // 3 لنگه
                            return 3;
                        case 4: // 4 لنگه
                            return 4;
                    }
                })();

            let height = (1 * (f.attributes.doorHeight ?? 0) + 12) * f.count;
            let width = 1 * door_width + 9;

            switch (f.attributes.doorLengeh * 1) {
                case 2: // 2لنگه
                    height *= 2;
                    break;
                case 3: // 3 لنگه
                    height *= 3;
                    break;
                case 4: // 4 لنگه
                    height *= 4;
                    break;
            }

            return {
                ...f,
                width,
                height,
            };
        };

        const sizes = [
            ...(pvc_front[type_front]
                ? pvc_front[type_front].map(com_pvc_silicon)
                : []),
            ...(pvc_back[type_back]
                ? pvc_back[type_back].map(com_pvc_silicon)
                : []),
        ];
        for (
            let index = Math.max(...sizes.map((m) => m.width));
            index >= Math.min(...sizes.map((m) => m.width));
            index -= 5
        ) {
            const filter = sizes.filter(
                (f) => index >= f.width && index - 5 < f.width
            );
            if (filter.length > 0) {
                const length = filter
                    // .map((m) => 2 * m.count * m.height)
                    .reduce((s, c) => s + c.height, 0);

                res[type].push({
                    width: cmFormat(Math.max(...filter.map((m) => m.width))),
                    height: cmFormat(length),
                });
            }
        }
    }

    return res;
}
export default function (data, attributes, template) {
    const rows = [
        {
            label: "ورق وسط",
            style: "padding: 0",
            field: ({ color_id }, items) => {
                const res = varagVast(
                    items.filter((f) => [8, 18, 24].includes(f.good.group_id))
                );
                return `
                    <table class="j-table table-out-none-border w-full text-center">
                    ${Object.keys(res)
                        .map((m) => {
                            return `
                        <tr class="bg-transparent">
                            <td>${m}</td>
                            <td>${res[m]
                                .map((mm) => {
                                    return `
                                    <div style="direction:ltr;min-width: 70px;">
                                        <strong>${mm.height}</strong>
                                        <span style="margin: 5px;color: red;">x</span>
                                        <strong>${mm.width}</strong>
                                    </div>
                                    `;
                                })
                                .join("")}</td>
                        </tr>
                        `;
                        })
                        .join("")}
                    </table>`;
            },
        },
        {
            label: "ورق CNC",
            field: ({ color_id }, items) => {
                const res = varagCNC(
                    items.filter((f) => [16, 20].includes(f.good.group_id))
                );
                return `
                    <table class="j-table table-out-none-border w-full text-center">
                    ${Object.keys(res)
                        .map((m) => {
                            return `
                        <tr class="bg-transparent">
                            <td>${m}</td>
                            <td>${res[m]
                                .map((mm) => {
                                    return `
                                    <div style="direction:ltr;min-width: 70px;">
                                        <strong>${mm.height}</strong>
                                        <span style="margin: 5px;color: red;">x</span>
                                        <strong>${mm.width}</strong>
                                    </div>
                                    `;
                                })
                                .join("")}</td>
                        </tr>
                        `;
                        })
                        .join("")}
                    </table>`;
            },
        },
        {
            label: "بغل بازو و پاسار 13.5",
            field: ({}, items) => {
                const bagal_bazo = items
                    .filter((f) => f.attributes[32] > 0) // تعداد بغل بازو 13.5
                    .reduce(
                        (s, m) =>
                            s +
                            m.attributes[32] * m.count * m.attributes_label[31],
                        0
                    );

                const pasar = Object.values(
                    items
                        .filter((f) => f.attributes[36] > 0) // تعداد پاسار 13.5
                        .map((m) => ({ ...m, key: m.attributes[35] })) // طول شاخه پاسار
                        .group("key")
                ).map((m) => {
                    const count = m.reduce(
                        (a, b) => a + b.attributes[36] * b.count,
                        0
                    );
                    const per = Math.floor(
                        m[0].attributes_label[35] / m[0].attributes[34] // 'طول شاخه پاسار' تقسیم بر 'طول پاسار'
                    );
                    return (
                        Math.floor(count / per) * m[0].attributes_label[35] +
                        (count % per > 0 ? (count % per) * 80 : 0)
                    );
                });
                const res =
                    bagal_bazo +
                    (pasar.length > 0 ? pasar.reduce((a, b) => a + b) : 0);
                if (!res) return "";
                return `<strong>${cmFormat(
                    res
                )}</strong><span style="margin: 5px;color: red;">x</span><strong>17</strong>`;
            },
        },
        {
            label: "بغل بازو و پاسار 17",
            field: ({}, items) => {
                const temp = items
                    .filter((f) => f.attributes[33] > 0)
                    .map((m) => {
                        const a = m.attributes_label[31];

                        return m.attributes[33] * m.count * (a ?? 0);
                    });

                const bagal_bazo =
                    temp.length > 0 ? temp.reduce((a, b) => a + b) : 0;

                const pasar = Object.values(
                    items
                        .filter((f) => f.attributes[37] > 0)
                        .map((m) => ({ ...m, key: m.attributes[35] }))
                        .group("key")
                ).map((m) => {
                    const count = m.reduce(
                        (a, b) => a + b.attributes[37] * b.count,
                        0
                    );
                    const per = Math.floor(
                        m[0].attributes_label[35] / m[0].attributes_label[34]
                    );
                    return (
                        Math.floor(count / per) * m[0].attributes_label[35] +
                        (count % per > 0 ? (count % per) * 80 : 0)
                    );
                });

                const res =
                    bagal_bazo +
                    (pasar.length > 0 ? pasar.reduce((a, b) => a + b) : 0);
                if (!res) return "";

                return `<strong>${cmFormat(
                    res
                )}</strong><span style="margin: 5px;color: red;">x</span><strong>21</strong>`;
            },
        },
        {
            label: "قید 13.5",
            field: ({}, items) => {
                const temp = items
                    .filter((f) => f.attributes[33] > 0 || f.attributes[37] > 0)
                    .map((m) => m.attributes.countGheyd13)
                    .filter((f) => f > 0);

                const pasar = Object.values(
                    items
                        .filter(
                            (f) => f.attributes[33] > 0 || f.attributes[37] > 0
                        )
                        .map((m) => ({ ...m, key: m.attributes[35] }))
                        .group("key")
                ).map((m) => {
                    const count = m.reduce(
                        (a, b) => a + b.attributes[37] * b.count,
                        0
                    );
                    const per = Math.floor(
                        m[0].attributes_label[35] / m[0].attributes_label[34]
                    );
                    return (
                        Math.floor(count / per) * m[0].attributes_label[35] +
                        (count % per > 0 ? (count % per) * 80 : 0)
                    );
                });
                return temp.length == 0
                    ? ""
                    : `<strong>${cmFormat(
                          temp.reduce((a, b) => a + b) * pasar
                      )}</strong><span style="margin: 5px;color: red;">x</span><strong>18.5</strong>`;
            },
        },
        {
            label: "قید 17",
            field: ({}, items) => {
                const temp = items
                    .filter((f) => f.attributes[57] > 0)
                    .map((m) => {
                        const a = m.attributes_label[31];
                        return (a ?? 0) * m.count * m.attributes[57];
                    })
                    .filter((f) => f > 0);

                return temp.length == 0
                    ? ""
                    : `<strong>${cmFormat(
                          temp.reduce((a, b) => a + b)
                      )}</strong><span style="margin: 5px;color: red;">x</span><strong>23</strong>`;
            },
        },
        {
            label: "لبه PVC",
            field: ({}, items) => {
                const temp = items.filter((f) => 8 == f.attributes[5]);
                return temp.length == 0
                    ? ""
                    : `<strong>${cmFormat(
                          temp.reduce(
                              (a, b) =>
                                  a +
                                  b.count *
                                      (() => {
                                          switch (b.attributes.doorLengeh * 1) {
                                              case 1: // یک لنگه
                                                  return 1;
                                              case 1.5: // 1.5 لنگه
                                              case 2: // 2لنگه
                                                  return 2;
                                              case 3: // 3 لنگه
                                                  return 3;
                                              case 4: // 4 لنگه
                                                  return 4;
                                          }
                                      })(),
                              0
                          ) * 700
                      )}</strong><span style="margin: 5px;color: red;">x</span><strong>7</strong>`;
            },
        },

        {
            label: "چهارچوب کف 9",
            field: ({}, items) => {
                const temp = items
                    .filter((f) => '9' == f.attributes.widthFloor)
                    .map((m) => {
                        const res = charchop(m.attributes, m.count);
                        return (
                            res.frame.countBaoUnit * res.frame.lengthBaoUnit +
                            res.frame.countPasarUnit *
                                res.frame.lengthPasarSlitter
                        );
                    });
                return temp.length == 0
                    ? ""
                    : `<strong>${cmFormat(
                          temp.reduce((a, b) => a + b)
                      )}</strong><span style="margin: 5px;color: red;">x</span><strong>17</strong>`;
            },
        },
        {
            label: "چهارچوب کف 13",
            field: ({}, items) => {
                const temp = items
                    .filter((f) => 129 == f.attributes.widthFloor)
                    .map((m) => {
                        const res = charchop(m.attributes, m.count);
                        return (
                            res.frame.countBaoUnit * res.frame.lengthBaoUnit +
                            res.frame.countPasarUnit *
                                res.frame.lengthPasarSlitter
                        );
                    });
                return temp.length == 0
                    ? ""
                    : `<strong>${cmFormat(
                          temp.reduce((a, b) => a + b)
                      )}</strong><span style="margin: 5px;color: red;">x</span><strong>21</strong>`;
            },
        },
        {
            label: "چهارچوب کف 17",
            field: ({}, items) => {
                const temp = items
                    .filter((f) => 130 == f.attributes.widthFloor)
                    .map((m) => {
                        const res = charchop(m.attributes, m.count);
                        return (
                            res.frame.countBaoUnit * res.frame.lengthBaoUnit +
                            res.frame.countPasarUnit *
                                res.frame.lengthPasarSlitter
                        );
                    });
                return temp.length == 0
                    ? ""
                    : `<strong>${cmFormat(
                          temp.reduce((a, b) => a + b)
                      )}</strong><span style="margin: 5px;color: red;">x</span><strong>25</strong>`;
            },
        },
        {
            label: "روکوب داخلی فرانسوی",
            field: ({}, items) => {
                const temp = items.filter(
                    (f) =>
                        [200].includes(f.good_id) &&
                        f.attributes.countParvaz > 0 &&
                        f.attributes.typeRokoob == 106
                );
                return temp.length == 0
                    ? ""
                    : `<strong>${cmFormat(
                          temp.reduce(
                              (a, b) => a + b.attributes.countParvaz * b.count,
                              0
                          ) * 220
                      )}</strong><span style="margin: 5px;color: red;">x</span><strong>13.5</strong>`;

                // const temp = items
                //     .filter(
                //         (f) =>
                //             [22].includes(f.good.group_id) &&
                //             f.attributes.countParvaz > 0 &&
                //             f.attributes.typeRokoob == 106
                //     )
                //     .map((m) => {
                //         const res = charchop(m.attributes, m.count);
                //         return (
                //             res.paravaz.counts[237] * 237 +
                //             res.paravaz.counts[244] * 244
                //         );
                //     });
                // return temp.length == 0
                //     ? ""
                //     : `<strong>${cmFormat(
                //           temp.reduce((a, b) => a + b)
                //       )}</strong><span style="margin: 5px;color: red;">x</span><strong>13.5</strong>`;
            },
        },
        {
            label: "روکوب پرواز فرانسوی",
            field: ({}, items) => {
                // const temp = items.filter((f) => f.attributes.countParvaz > 0);

                // // const temp2 = items.filter(
                // //     (f) =>
                // //         [199].includes(f.good_id) &&
                // //         f.attributes.typeRokoob == 106
                // // );

                // return temp.length == 0
                //     ? ""
                //     : `<strong>${cmFormat(
                //           temp.reduce(
                //               (a, b) => a + b.attributes.countParvaz * b.count,
                //               0
                //           ) * 240
                //       )}</strong><span style="margin: 5px;color: red;">x</span><strong>13.5</strong>`;
                const temp = items
                    .filter((f) => f.attributes.countParvaz > 0)
                    .map((m) => {
                        const res = charchop(m.attributes, m.count);
                        return (
                            res.paravaz.counts[237] * 237 +
                            res.paravaz.counts[244] * 244
                        );
                    });
                return temp.length == 0
                    ? ""
                    : `<strong>${cmFormat(
                          temp.reduce((a, b) => a + b)
                      )}</strong><span style="margin: 5px;color: red;">x</span><strong>13.5</strong>`;
            },
        },
        {
            label: "روکوب داخلی مکزیکی",
            field: ({}, items) => {
                const temp = items.filter(
                    (f) => [200].includes(f.good_id) && f.attributes.typeRokoob == 107
                );
                return temp.length == 0
                    ? ""
                    : `<strong>${cmFormat(
                          temp.reduce((a, b) => a + b.count, 0) * 220
                      )}</strong><span style="margin: 5px;color: red;">x</span><strong>14</strong>`;
            },
        },
        {
            label: "روکوب پرواز مکزیکی",
            field: ({}, items) => {
                const temp = items.filter(
                    (f) => [199].includes(f.good_id) && f.attributes.typeRokoob == 107
                );
                return temp.length == 0
                    ? ""
                    : `<strong>${cmFormat(
                          temp.reduce((a, b) => a + b.count, 0) * 240
                      )}</strong><span style="margin: 5px;color: red;">x</span><strong>16</strong>`;
            },
        },
        {
            label: "مکمل",
            field: ({}, items) => {
                const temp = Object.values(
                    items
                        .filter((f) => f.attributes[47] > 0)
                        .map((m) => ({ ...m, key: m.attributes.mokammelWidth }))
                        .group("key")
                );
                return temp
                    .map(
                        (m) =>
                            `<strong>${cmFormat(
                                m.reduce(
                                    (a, b) =>
                                        a + b.attributes[47] * b.count * 220,
                                    0
                                )
                            )}</strong><span style="margin: 5px;color: red;">x</span><strong>${
                                m[0].attributes.mokammelWidth
                            }</strong>`
                    )
                    .join("<br>");
            },
        },
    ];

    const group = data.items
        .map((m) => ({ ...m, key: m.attributes.pvcColor }))
        .filter((f) => f.key !== 32)
        .group("key");

    const columns = [
        {
            field: (row) => row.label,
            label: "",
            hiddenable: true,
        },
        ...Object.keys(group)
            .map((color_id) => {
                const find = attributes[
                    attributes.findIndex((f) => f.id == 3)
                ].items.findIndex((f) => f.id + "" == color_id + "");
                if (find >= 0) {
                    return {
                        // field: (row) => row.label,
                        label: attributes[
                            attributes.findIndex((f) => f.id == 3)
                        ].items[find].name,
                        field: (row) =>
                            row.field({ color_id }, group[color_id]),
                    };
                }
                return "";
            })
            .filter((f) => f),
    ];

    const rows_data = rows
        .map((row) => {
            if (
                columns.filter((f) => !(f.hiddenable || f.field(row))).length <
                columns.length - 1
            )
                return `<tr>${columns
                    .map((column) => {
                        return `<td${
                            row.style ? ` style="${row.style}"` : ""
                        }>${column.field(row)}</td>`;
                    })
                    .join("")}</tr>`;
        })
        .join("");

    return `
    
    <table class="j-table w-full text-center mb-3">
        
       
        <thead>
                    <tr>
                        <td class="no-border p-0">
                            <table class="j-table w-full text-center odd-highlight">
                                <tr class="h-8">

                                    <td class="no-border w-1/3 bg-white">
                                        <div class="text-left text-sm">
                                            <b>نام نمایندگی: </b> ${
                                                data.party_name
                                            }
                                        </div>
                                        <div class="text-left text-sm">
                                            <b>نام مشتری: </b> ${
                                                data.customer_name
                                            }
                                        </div>
                                    </td>
                                    <td class="no-border w-1/3 bg-white">
                                        <img src="/images/logo-factor.png" class="h-10 m-auto" />
                                        <h6 style="text-align: center;font-weight: bolder;">برگه تولید ${
                                            template.station.name
                                        }</h6>
                                    </td>
                                    <td class="no-border w-1/3 bg-white text-right">
                                        <table class="ml-auto mr-0 text-sm">
                                            <tr>
                                                <th class="no-border bg-white">شناسه سفارش:</th>
                                                <td class="no-border bg-white">${
                                                    data.code
                                                }</td>

                                            </tr>
                                            <tr>
                                                <th class="no-border bg-white">تاریخ سفارش:</th>

                                                <td class="no-border bg-white">${
                                                    data.submit_date
                                                }</td>
                                            </tr>
                                            <tr>
                                                <th class="no-border bg-white">تاریخ سفارش:</th>

                                                <td class="no-border bg-white">${
                                                    data.delivery_date
                                                }</td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>


                            </table>
                        </td>
                    </tr>
                   

                </thead>

        <tbody>
        <tr>
            <td colspan="10" class="no-border">
                <table class="j-table w-full text-center odd-highlight">
                    <tr>
                        ${columns
                            .map((m) => {
                                return "<th>" + m.label + "</th>";
                            })
                            .join("")}
                    </tr>
                    ${rows_data}
                </table>
            </td>
        </tr>
        
        ${
            data.description
                ? `
        <tr>
            <th colspan="10">توضیحات</th>
            </tr>
        <tr>
            <td colspan="10">${data.description}</td>
        </tr>`
                : ""
        }
        </tbody>
    </table>
`;

    // return `
    // <h6 style="text-align: center;font-weight: bolder;">برگه تولید ${
    //     template.station.name
    // }</h6>
    // <table class="j-table w-full text-center mb-3 odd-highlight">
    //     <tr class="highlight">
    //         <th>شناسه سفارش</th>
    //         <th>نام نمایندگی</th>
    //         <th>نام مشتری</th>
    //         <th>تاریخ سفارش</th>
    //         <th>تاریخ سفارش</th>
    //     </tr>
    //     <tr>
    //         <td>${data.code}</td>
    //         <td>${data.party_name}</td>
    //         <td>${data.customer_name}</td>
    //         <td>${data.submit_date}</td>
    //         <td>${data.delivery_date}</td>
    //     </tr>

    //     ${
    //         data.description
    //             ? `
    //     <tr>
    //         <th colspan="10">توضیحات</th>
    //         </tr>
    //     <tr>
    //         <td colspan="10">${data.description}</td>
    //     </tr>`
    //             : ""
    //     }
    // </table>
    // <table class="j-table w-full text-center odd-highlight">
    //     <tr>
    //         ${columns
    //             .map((m) => {
    //                 return "<th>" + m.label + "</th>";
    //             })
    //             .join("")}
    //     </tr>
    //     ${rows_data}
    // </table>
    // `;
}

function frame({ temp, attributes }) {
    return cmFormat(
        temp.reduce((a, b) => a + b.count * b.attributes.countBaoFrame, 0) * 225 +
            temp
                .map((m) => {
                    const pasar_frame = m.attributes_label.perLengthPasarFrame;
                    return (pasar_frame ?? 0) * m.count * m.attributes.countPerPasarFrame;
                })
                .reduce((a, b) => a + b)
    );
}
