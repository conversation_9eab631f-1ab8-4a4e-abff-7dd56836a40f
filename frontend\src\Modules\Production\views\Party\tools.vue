<template>
    <div class="flex gap-3">
        <j-btn v-if="checkPermission('parties.create')" flat dense size="md" icon="add"
            :to="{ name: 'parties.create' }" title="جدید" :label="$q.screen.lt.sm ? '' : 'جدید'" />
        <template v-if="props.selected?.length > 0">
            <j-btn v-if="checkPermission('parties.edit') && props.selected?.length == 1" flat dense size="md"
                icon="edit" title="ویرایش" :label="$q.screen.lt.sm ? '' : 'ویرایش'"
                :to="{ name: 'parties.edit', params: { id: props.selected[0].id } }" />
            <j-btn v-if="checkPermission('parties.delete')" flat dense size="md" icon="delete" title="حذف"
                :label="$q.screen.lt.sm ? '' : 'حذف'" @click="doDelete" />
        </template>
    </div>
</template>
<script>
import { computed } from 'vue';
import { useTableStore } from '@/stores/table.store';
import { checkPermission } from '@/helpers/functions';

export default {
    setup() {
        const tableStore = useTableStore()
        const props = computed(() => tableStore.props);

        return {
            props,
            doDelete() {
                tableStore.actions.doDelete(props.value.selected)
            },
            checkPermission
        }
    }
}
</script>