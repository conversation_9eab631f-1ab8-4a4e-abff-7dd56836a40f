#!/usr/bin/env node

/**
 * Test script to verify build configuration
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Testing Multi-Domain Frontend Build...\n');

// Test 1: Check if required files exist
console.log('📁 Checking file structure...');
const requiredFiles = [
    'src/config/domains.js',
    'src/config/themes.js',
    'src/composables/useDomain.js',
    'src/views/panel/index.js',
    'src/views/crm/index.js',
    'src/views/shared/index.js',
    'src/stores/panel/index.js',
    'src/stores/crm/index.js',
    'src/stores/shared/index.js'
];

let allFilesExist = true;
requiredFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - Missing!`);
        allFilesExist = false;
    }
});

if (!allFilesExist) {
    console.log('\n❌ Some required files are missing. Please check the file structure.');
    process.exit(1);
}

// Test 2: Check package.json scripts
console.log('\n📦 Checking package.json scripts...');
const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
const requiredScripts = ['dev:panel', 'dev:crm', 'build:panel', 'build:crm'];

requiredScripts.forEach(script => {
    if (packageJson.scripts[script]) {
        console.log(`✅ ${script}: ${packageJson.scripts[script]}`);
    } else {
        console.log(`❌ ${script} - Missing!`);
        allFilesExist = false;
    }
});

// Test 3: Check environment variables
console.log('\n🔧 Checking environment configuration...');
const envPath = path.join(__dirname, '.env');
if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const requiredEnvVars = ['VITE_PANEL_DOMAIN', 'VITE_CRM_DOMAIN'];
    
    requiredEnvVars.forEach(envVar => {
        if (envContent.includes(envVar)) {
            console.log(`✅ ${envVar}`);
        } else {
            console.log(`❌ ${envVar} - Missing!`);
        }
    });
} else {
    console.log('❌ .env file not found!');
}

// Test 4: Try to build (commented out for safety)
console.log('\n🔨 Build test (skipped for safety)');
console.log('To test build manually, run:');
console.log('  npm run build:panel');
console.log('  npm run build:crm');

console.log('\n✅ Multi-Domain Frontend structure verification completed!');
console.log('\n📖 Next steps:');
console.log('1. Set up DNS entries in your hosts file');
console.log('2. Run: npm run dev:panel (for panel.erp.test:3000)');
console.log('3. Run: npm run dev:crm (for crm.erp.test:3001)');
console.log('4. Test both domains in your browser');
