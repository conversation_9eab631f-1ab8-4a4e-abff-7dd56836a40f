<template>
    <j-btn v-if="machines.length > 0" color="red" label="گزارش خرابی" icon="report_problem" @click="dialog = true" />
    <j-dialog-bar v-model="dialog">
        <q-tabs v-model="tab" dense active-color="white" active-bg-color="primary" indicator-color="primary" align="justify"
            narrow-indicator>
            <q-tab v-for="machine, index in machines" :name="machine.id" :label="machine.name" :key="index"
                class="rounded-xl" />
        </q-tabs>

        <q-tab-panels v-model="tab" animated class="mt-4">
            <q-tab-panel v-for="machine, index in machines" :name="machine.id" :key="index" style="padding: 0"
                class="shadow-md">
                <!-- {{ problems }} -->
                <!-- {{ form }} -->
                <j-form @submit="submit">
                    <div v-for="problem in machine.problems">
                        <q-field :model-value="form.problems" borderless hide-bottom-space>
                            <template v-slot:control>
                                <j-checkbox v-model="form.problems" :val="problem.id" :label="problem.name" />
                            </template>

                        </q-field>
                    </div>

                    <j-input type="textarea" v-model="form.description"></j-input>
                    <j-btn type="submit" label="ثبت" color="primary" class="mx-auto block" />
                </j-form>
            </q-tab-panel>
        </q-tab-panels>
    </j-dialog-bar>
</template>
<script setup>
import { api } from '@/boot/axios';
import { ref } from 'vue'
import { useRoute } from 'vue-router';

const dialog = ref(false)
const route = useRoute();
const machines = ref([])
const tab = ref('')
const form = ref({ problems: [] })
api.get(`/production/station/${route.params.id}/machines`).then(res => {
    machines.value = res.data
    tab.value = machines.value[0] ? machines.value[0].id : ''
})

const submit = () => {
    dialog.value = false;
    api.post(`/production/station/${route.params.id}/machines/${tab.value}/reports`, form.value)
    form.value = { problems: [] }
}

</script>