<template>
    <!-- <pre>{{ good }}</pre> -->
    <!-- <pre>{{ selected }}</pre> -->
    <div class="table-border-radius">

        <table class="j-table w-full text-center">
            <thead>
                <!-- <tr class="highlight">
                <th :colspan="columns.length" style="font-size: 15px;"><h5>{{ good.label }}</h5></th>
            </tr> -->
                <tr class="highlight h-10">
                    <template v-for="column, index2 in columns" :key="index2">
                        <!-- <th v-if="column.name == 'action'" :style="column.style" >
                            <div class="gap-3 grid grid-cols-1">
                                <j-toggle v-model="selectAll[good.key]" dense toggle-indeterminate checked-icon="check"
                                    color="primary" keep-color unchecked-icon="clear" true-value="DONE"
                                    false-value="REPAIRING" indeterminate-value="PROCESSING" class="mx-auto"
                                    @update:model-value="value => onToggleSelectAll(value, good)" />

                                <j-btn icon="save" flat dense color="primary" @click="save" />
                            </div>
                        </th> -->
                        <th :style="column.style">{{ column?.label }}</th>
                    </template>
                </tr>
            </thead>
            <tbody>
                <tr class="h-10">
                    <template v-for="column, index2 in columns" :key="index2">
                        <!-- <td v-if="column.name == 'action'" :style="column.style">
                            <q-toggle v-model="selected[row.id]" dense toggle-indeterminate checked-icon="check"
                                color="green" unchecked-icon="clear" true-value="DONE" false-value="REPAIRING"
                                indeterminate-value="PROCESSING" />
                        </td> -->
                        <td v-html="typeof column.field == 'function' ? column.field(good) : good[column?.field]"
                            :style="column.style" />
                    </template>
                </tr>
                <tr>
                    <td colspan="3" style="padding:0;">
                        <table class="w-full table-out-none-border text-lg">
                            <thead>
                                <tr class="highlight">
                                    <th>عرض</th>
                                    <th>طول</th>
                                    <th>شناسه سفارش</th>
                                    <th>نام نمایندگی / مشتری</th>
                                    <th>
                                        <div class="gap-3 grid grid-cols-1">
                                            <j-toggle v-model="selectAll" dense toggle-indeterminate
                                                checked-icon="check" color="primary" keep-color unchecked-icon="clear"
                                                true-value="DONE" false-value="REPAIRING"
                                                indeterminate-value="PROCESSING" class="mx-auto"
                                                @update:model-value="value => onToggleSelectAll(value, good)" />

                                            <j-btn icon="save" flat dense color="primary" @click="save" />
                                        </div>
                                    </th>
                                </tr>
                                <tr v-for="item, index_id in good.items" :key="index_id">
                                    <td v-html="item.width"></td>
                                    <td v-html="item.height"></td>
                                    <td>{{ item.production_order_code }}</td>
                                    <td>{{ item.party_name }} / {{ item.customer_name }}</td>
                                    <td>
                                        <q-toggle v-model="selected[item.id]" dense toggle-indeterminate
                                            checked-icon="check" color="green" unchecked-icon="clear" true-value="DONE"
                                            false-value="REPAIRING" indeterminate-value="PROCESSING" />
                                    </td>
                                </tr>
                            </thead>
                        </table>
                    </td>
                </tr>

            </tbody>
        </table>
    </div>
</template>
<script>
import { ref } from 'vue-demi';
import { useQuasar } from 'quasar';
import { useRoute } from 'vue-router';
import { api } from '@/boot/axios';
import { inject } from 'vue'

export default {
    props: {
        good: {
            type: Object,
            default: () => { },
        }
    },
    setup(props, { emit }) {
        const $q = useQuasar()
        const url = '/production/production_checklist';


        const columns = [

            {
                label: "عرض",
                style: "padding: 0;font-size:24px;font-weight:bolder",
                field: 'width',

            },
            {
                label: "طول",
                style: "padding: 0;font-size:24px;font-weight:bolder",
                field: 'height',
            },
            // {
            //     name: "action",
            //     style: 'width: 70px;padding:10px',
            // },

        ];


        const selected = ref({})
        const selectAll = ref(false)
        const onToggleSelectAll = (value, row) => {
            row.items.map(m => { selected.value[m.id] = value })
        }
        const { getData } = inject('afterSave')

        return {
            columns,
            selected,
            selectAll,
            onToggleSelectAll,
            save() {
                $q.dialog({
                    title: 'ذخیره',
                    message: 'آیا مطمئن هستید ذخیره شود؟',
                    cancel: true,
                    persistent: true
                }).onOk(() => {
                    api.put(url, {
                        data: selected.value
                    }).then(() => {
                        getData()
                    })
                })
            },
        }
    },
}
</script>