<template>
    <!-- {{ hidden_attributes }} -->
    <!-- {{ conditions }} -->
    <!-- <pre>{{ form.attributes }}</pre> -->
    <!-- {{ good.default_attribute }} -->
    <j-form @submit="submit" class="w-full" ref="ref_form">
        <div class="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-5 gap-3">
            <select-product ref="ref_good" v-model:value="form.good_id" url="/good/good/search" dense hide-bottom-space
                autofocus @update:model-value="res => selectGood(res, true)" label="نام کالا"
                :params="{ type: 'PRODUCT', is_active: true }" :outlined="false || true" required size="sm" search-local
                sort-by="name" :disable="is_edit" />
            <j-input v-model="form.count" dense hide-bottom-space label="تعداد" type="number" step="0.1"
                :outlined="false || true" required :min="1" class="hidden-arrow" />
            <template v-if="good && Object.keys(good).length > 0 && good_attributes">
                <template v-for="attribute, key in good_attributes" :key="key">
                    <template
                        v-if="!hidden_attributes.includes(attribute.id) && (attribute.pivot.showing !== null ? attribute.pivot.showing : attribute.showing)">

                        <!-- <template v-if="attribute.type == 'SELECT'">
                            {{ attribute.items }}
                        </template> -->
                        <j-select v-if="attribute.type == 'SELECT'" v-model="form.attributes[attribute.id]" dense
                            :bg-color="good.default_attribute[attribute.id] !== undefined && good.default_attribute[attribute.id] !== form.attributes[attribute.id] ? 'amber-2' : ''"
                            :options="attribute.items" option-label="name" option-value="id" search-local hide-bottom-space
                            :label="attribute.name" :outlined="false || true"
                            :required="attribute.pivot.required ?? attribute.required"
                            :disable="(is_edit && false) || disable_attributes.includes(attribute.id) || attribute.items.length == 1"
                            @update:model-value="val => change(attribute.id, val)" />
                        <j-select-image v-if="attribute.type == 'SELECT_IMAGE'"
                            v-model:value="form.attributes[attribute.id]" :image-key="row => row.data.image" dense
                            :bg-color="good.default_attribute[attribute.id] !== undefined && good.default_attribute[attribute.id] !== form.attributes[attribute.id] ? 'amber-2' : ''"
                            :options="attribute.items" option-label="name" option-value="id" search-local hide-bottom-space
                            :label="attribute.name" :outlined="false || true"
                            :required="attribute.pivot.required ?? attribute.required"
                            :disable="(is_edit && false) || disable_attributes.includes(attribute.id) || attribute.items.length == 1"
                            @update:model-value="val => change(attribute.id, val)" />
                        <j-input v-else-if="attribute.type == 'INPUT'" v-model="form.attributes[attribute.id]" dense
                            hide-bottom-space :label="attribute.name" :outlined="false || true"
                            :required="attribute.pivot.required ?? attribute.required"
                            :disable="(is_edit && false) || disable_attributes.includes(attribute.id)"
                            @update:model-value="val => change(attribute.id, val)"
                            :bg-color="good.default_attribute[attribute.id] !== undefined && good.default_attribute[attribute.id] !== form.attributes[attribute.id] ? 'amber-2' : ''" />
                        <j-input v-else-if="attribute.type == 'NUMBER'" v-model="form.attributes[attribute.id]" dense
                            hide-bottom-space :label="attribute.name" type="number" step="0.1" min="0"
                            :outlined="false || true" :required="attribute.pivot.required ?? attribute.required"
                            :disable="(is_edit && false) || disable_attributes.includes(attribute.id)"
                            @update:model-value="val => change(attribute.id, val)"
                            :bg-color="good.default_attribute[attribute.id] !== undefined && good.default_attribute[attribute.id] !== form.attributes[attribute.id] ? 'amber-2' : ''" />
                        <j-toggle v-else-if="attribute.type == 'SWITCH'" v-model="form.attributes[attribute.id]" dense
                            hide-bottom-space :label="attribute.name" :outlined="false || true"
                            :required="attribute.pivot.required ?? attribute.required"
                            :disable="(is_edit && false) || disable_attributes.includes(attribute.id)"
                            @update:model-value="val => change(attribute.id, val)"
                            :class="good.default_attribute[attribute.id] !== undefined && good.default_attribute[attribute.id] !== form.attributes[attribute.id] ? 'bg-amber-2' : ''" />

                    </template>
                </template>
            </template>

            <template v-if="good && Object.keys(good).length > 0 && good_attributes">
                <template v-for="attribute, key in good_attributes.filter(f => f.type == 'FILE')" :key="key">

                    <j-upload v-model:value="form.attributes[attribute.id]" :label="attribute.name" auto-upload
                        accept="image/*" url="/api/upload-file" field-name="file" class="col-span-full" flat bordered />
                </template>
            </template>
            <j-input v-model="form.description" label="توضیحات کالا" type="textarea" dense class="col-span-full" />

        </div>
        <j-btn size="md" :color="is_edit ? 'secondary' : 'primary'" type="submit" class="full-width text-white mt-4"
            :label="is_edit ? 'ویرایش' : 'افزودن'" @shortkey="ref_form.submit()" v-shortkey="['ctrl', 'space']" />
    </j-form>
</template>

<script>
import { api } from '@/boot/axios';
import { checkPermission } from '@/helpers';
import { onMounted, ref, watch } from 'vue';
import { conditionCompute } from './index'
import SelectProduct from './SelectProduct.vue';
export default {
    components: { SelectProduct },
    // props: {
    //     conditions: {
    //         type: Array,
    //         default: () => []
    //     }
    // },
    setup(props, context) {
        const good = ref({})
        const is_edit = ref(false)
        const form = ref({
            attributes: {},
            count: 1,
        })
        const ref_good = ref(null)
        const ref_form = ref(null)

        const conditions = ref([])

        const onEdit = (value) => {
            is_edit.value = true
            good.value = {}
            selectGood(value.good_id)
            form.value = JSON.parse(JSON.stringify(value));
            ref_good.value.focus()


        }

        const onCopy = (value) => {
            const temp = JSON.parse(JSON.stringify(value))
            delete temp.id
            onEdit(temp)
            is_edit.value = false
            ref_good.value.focus()


        }
        const { conditionProccess, disable_attributes, hidden_attributes, good_attributes } = conditionCompute({ form, good })
        const computeCondition = (attribute_id = null) => {
            disable_attributes.value = []
            hidden_attributes.value = []
            good_attributes.value = JSON.parse(JSON.stringify(good.value.attributes))
            //console.log('================================================')

            conditionProccess(conditions.value)

            // conditionProccess(conditions.value.filter(f => !f.good_id && !f.group_id && f.if.filter(
            //     (ff) =>
            //         !attribute_id ? true : ff.attribute_id == attribute_id
            // ).length > 0))
            // conditionProccess(conditions.value.filter(f => f.group_id == good.value.group_id && f.if.filter(
            //     (ff) =>
            //         !attribute_id ? true : ff.attribute_id == attribute_id
            // ).length > 0))
            // conditionProccess(conditions.value.filter(f => f.good_id == good.value.id && f.if.filter(
            //     (ff) =>
            //         !attribute_id ? true : ff.attribute_id == attribute_id
            // ).length > 0))
        }

        const selectGood = (value, default_attribute = false) => {

            form.value.attributes = {}
            if (!value) {
                good.value = {}
                return
            }
            api.get(`good/good/${value}/selectForOrder`).then(res => {
                res.result.attributes = res.result.attributes.sort(function (a, b) {
                    if (a.sort < b.sort) return -1;
                    if (a.sort > b.sort) return 1;
                    return 0;
                });
                good.value = res.result;
                conditions.value = Object.values(res.conditions.map(m => {
                    return { ...m, key: m.if.map(f => f.attribute_id + '_' + (f.value ?? f.between.join('|'))).sort().join('.') }
                }).group('key')).map(m => {
                    const condition_good = m.filter(f => f.good_id == good.value.id)
                    if (condition_good.length > 0)
                        return { ...condition_good[0], then: condition_good.map(m => m.then).flat() };
                    const condition_group = m.filter(f => f.group_id == good.value.group_id)
                    if (condition_group.length > 0)
                        return { ...condition_group[0], then: condition_group.map(m => m.then).flat() };

                    const condition_all = m.filter(f => !f.good_id && !f.group_id)
                    if (condition_all.length == 0)
                        return null;
                    return { ...condition_all[0], then: condition_all.map(m => m.then).flat() };
                }).filter(f => f);
                good.value.attributes.forEach(f => {
                    if (f.type == 'SWITCH')
                        form.value.attributes[f.id] = form.value.attributes[f.id] ?? false
                })
                if (default_attribute)
                    Object.keys(res.result.default_attribute).forEach(e => {
                        if (res.result.default_attribute[e]) {
                            form.value.attributes[e] = res.result.default_attribute[e]
                        }
                    })
                // good_attributes.value = good.value.attributes
                computeCondition()
            })
        }

        let old_attributes = {};
        watch(() => form.value.attributes, (newVal, oldValue) => {
            if (!good.value.attributes)
                return;
            Object.keys(old_attributes).forEach(old_attr => {
                if (form.value.attributes[old_attr] !== old_attributes[old_attr]) {
                    computeCondition(old_attr)
                }
            });
            old_attributes = Object.assign({}, newVal);

            //
        }, {
            deep: true
        })

        const reset = () => {
            is_edit.value = false;
            form.value = {
                attributes: {},
                count: 1,
            }
            good.value = {}
            ref_good.value.focus()

        }


        // onMounted(() => {
        //     setTimeout(() => {
        //         ref_good.value.focus()
        //     }, 500)
        // })

        return {
            checkPermission,
            selectGood,
            submit: () => {
                context.emit('confirm', {
                    ...Object.assign({}, form.value),
                    good: Object.assign({}, good.value),
                    //count: 1,
                }, is_edit.value)
                reset()
            },
            good,
            form,
            ref_good,
            onEdit,
            onCopy,
            is_edit,
            ref_form,
            good_attributes,
            hidden_attributes,
            disable_attributes,
            change(attribute_id, newVal) {
                //console.log(attribute_id, newVal)
                if (!good.value.attributes)
                    return;
                computeCondition()

            },
            //uploaded(files) {
            // returning a Promise
            //console.log(files)
            // return new Promise((resolve) => {
            //     // simulating a delay of 2 seconds
            //         resolve({
            //             url: '/api/upload-file'
            //         })
            // })
            //}
        }
    },
}
</script>