<?php
namespace Modules\Good\Http\Controllers;

use App\Http\Controllers\API\BaseController;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Response;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Validation\Rule;
use Modules\Good\Entities\Attribute;
use Modules\Good\Http\Requests\AttributeRequest;

class AttributeController extends BaseController implements HasMiddleware
{
    protected $model = Attribute::class;

    public static function middleware(): array
    {
        return [
            new Middleware('permission:attributes', only: ['index']),
            new Middleware('permission:attributes.create', only: ['create', 'store']),
            new Middleware('permission:attributes.edit', only: ['show', 'update']),
            new Middleware('permission:attributes.delete', only: ['delete', 'destroy']),
            //new Middleware('subscribed', except: ['store']),
        ];
    }
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return JsonResource::collection($this->model::query()->filter()->jpaginate());

        return JsonResource::collection(Attribute::query()->with(['items.conditions', 'parent.items'])->get()->map(function ($m) {

            if ($m['items'] && $m->parent?->items) {
                $m['items'] = $m['items']->merge($m->parent?->items);
            }
            return array_merge($m->toArray(), [
                "items" => $m['items']->toArray(),
            ]);
        }))->additional([
            'formOption' => [
                'types' => Attribute::types,
            ],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(AttributeRequest $request)
    {
        $items     = $request->items ?? [];
        $attribute = Attribute::create($request->all());
        if ($request->items) {
            $attribute->items()->sync(array_columns($items, ['id', 'name', 'data', 'key', 'is_active']));
        }
        return $this->handleResponse($attribute, trans('request.done'));
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(Attribute $attribute)
    {
        $attribute->items;

        return $this->handleResponse([
            'form'        => $attribute,
            'formOptions' => [
                'types' => Attribute::types,

            ],
        ]);
    }

    public function create()
    {
        return $this->handleResponse([
            'formOptions' => [
                'types' => Attribute::types,
            ],
        ]);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(AttributeRequest $request, Attribute $attribute)
    {
        $attribute->update($request->all());
        $items = $request->items ?? [];
        if ($attribute->type === Attribute::TYPE_INPUT) {
            $items = [];
        }

        $attribute->items()->sync(array_columns($items, ['id', 'name', 'data', 'key', 'is_active', 'is_active_customer']));
        $attribute->items;

        return $this->handleResponse($attribute, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy(Attribute $attribute)
    {
        $attribute->delete();
    }

    public function delete(Request $request)
    {
        // اعتبارسنجی ورودی برای اطمینان از اینکه ورودی آرایه‌ای از شناسه‌هاست
        $validated = $request->validate([
            'ids'   => 'required|array',
            'ids.*' => ['integer', Rule::exists(Attribute::class, 'id')],
        ], [
            'ids.*.exists' => 'نماینده با شناسه :input یافت نشد یا امکان حذف ندارد.',
        ]);

        // دریافت شناسه‌ها از ورودی
        $ids = $validated['ids'];

        // حذف مدل‌ها با شناسه‌های مشخص شده
        try {
            Attribute::destroy($ids);
        } catch (\Exception $e) {
            // در صورت بروز خطا، می‌توانید یک پاسخ مناسب ارسال کنید
            return $this->handleError($e, 'امکان حذف وجود ندارد', 500);
        }

                                                                   // ارسال پاسخ موفقیت‌آمیز
        return $this->handleResponse(null, trans('request.done')); // response()->json(['message' => 'Records deleted successfully']);
    }

    public function search()
    {
        $data = Attribute::query()
            ->when(request('type'), function ($q, $value) {
                $q->where('type', $value);
            })
            ->when(request('name'), function ($q, $value) {
                $q->where('name', 'like', "%$value%");
            })
            ->with('items')->get();
        return $this->handleResponse($data);
    }
}
