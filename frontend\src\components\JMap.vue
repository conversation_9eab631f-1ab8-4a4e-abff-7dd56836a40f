<template>
    <ol-map style="height:400px" class="relative w-full">
        <ol-view ref="view" :center="center" :rotation="rotation" :zoom="zoom" :projection="projection"
            @zoomChanged="zoomChanged" @centerChanged="centerChanged" @resolutionChanged="resolutionChanged"
            @rotationChanged="rotationChanged" />
        <j-icon name="location_on" class="absolute z-10 text-red" style="top:calc(50% - 50px);right:calc(50% - 30px);"
            size="60px" />
        <ol-tile-layer>
            <ol-source-osm />
        </ol-tile-layer>

        <ol-geolocation :projection="projection" @positionChanged="geoLocChange">
            <!-- <template v-slot="slotProps">
                <ol-vector-layer :zIndex="2">
                    <ol-source-vector>
                        <ol-feature ref="positionFeature">
                            <ol-geom-point :coordinates="slotProps.position"></ol-geom-point>
                            <ol-style>
                                <ol-style-icon :src="hereIcon" :scale="0.1"></ol-style-icon>
                            </ol-style>
                        </ol-feature>
                    </ol-source-vector>
                </ol-vector-layer>
            </template> -->
        </ol-geolocation>


    </ol-map>
</template>
  
<script>
import { ref,watch } from "vue";
export default {
    props: {
        modelValue: Array,
        zoom: Number,
    },
    setup(props, { emit }) {
        const center = ref(props.modelValue?.length > 0 ? props.modelValue : [51.33807541999935, 35.699711666060836]);
        const projection = ref("EPSG:4326");
        const zoom = ref(props.zoom ?? 17);
        const rotation = ref(0);
        const view = ref(null);

        watch(() => props.modelValue, (newVal) => {
            center.value = newVal ?? [51.33807541999935, 35.699711666060836];
        })

        const currentCenter = ref([0, 0])
        const currentZoom = ref(0)
        const currentResolution = ref(0)
        const currentRotation = ref(0)


        const zoomChanged = (zoom) => {
            currentZoom.value = zoom;
            emit('update:zoom', zoom)
        }
        const resolutionChanged = (resolution) => {
            currentResolution.value = resolution;
        }
        const centerChanged = (center) => {
            currentCenter.value = center;
            emit('update:modelValue', center)
        }
        const rotationChanged = (rotation) => {
            currentRotation.value = rotation;
        }
        const geoLocChange = (loc) => {
            console.log(loc);
            view.value.fit([loc[0], loc[1], loc[0], loc[1]], {
                maxZoom: zoom.value,
            });
        };

        return {
            view,
            center,
            projection,
            zoom,
            rotation,
            zoomChanged,
            resolutionChanged,
            centerChanged,
            rotationChanged,
            geoLocChange,
            currentZoom,
            currentResolution,
            currentCenter,
            currentRotation,
        };
    },
};
</script>