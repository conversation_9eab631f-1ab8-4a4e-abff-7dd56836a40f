<template>
    <div v-if="form" class="grid grid-cols-1 xs:grid-cols-2  gap-2">
        <j-select-remote v-if="checkRole(['admin', 'sell_person'])" ref="start" v-model="form.party_id"
            url="/buy-and-sell/party/search" label="نمایندگی" autofocus option-label="display_name" field="display_name"
            dense error-field="party_id" search-local />
        <j-input v-model="form.customer_name" label="مشتری" dense error-field="customer_name" />

        <j-date-input v-if="checkRole(['admin', 'sell_person'])" v-model:value="form.submit_date" label="تاریخ شروع" dense />
        <j-date-input v-if="checkRole(['admin', 'sell_person'])" v-model:value="form.delivery_date" label="تاریخ سفارش" dense
            :events="events.map(m => m.date)" :options="deliveryDateOptions" :event-color="eventColor"
            @navigation="navigation" @show="calendar()" />
        <!-- <j-toggle v-model="form.has_logo" label="لوگو" dense /> -->

    </div>
    <select-item v-if="form" v-model:data="form.items" :id="form.id" v-bind="{ onSubmit }" class="mb-5" />
    <j-input v-model="form.description" label="توضیحات سفارش" type="textarea" dense class="col-span-full" />
</template>
<script>
import { api } from '@/boot/axios';
import { checkRole } from '@/helpers';
import { onMounted, ref, watch } from 'vue';
import SelectItem from './selectForm/index.vue';
import { useAuthStore } from "@/stores";
import { useQuasar } from "quasar";

export default {
    components: { SelectItem },
    props: {
        form: {
            type: Object,
            default: () => { }
        },
        onSubmit: {
            type: Function,
        }
    },
    setup(props, { emit }) {
        const start = ref(null)
        const form = ref(Object.assign(props.form, {
            type: 'SALE'
        }))
        watch(() => props.form, (val) => {
            form.value = props.form
        }, {
            deep: true
        })
        watch(() => form.value, (val) => {
            emit('update:form', val)
        }, {
            deep: true
        })
        // onMounted(() => {
        //     setTimeout(() => {
        //         start.value.focus()
        //     }, 500)
        // })
        const events = ref([])
        const holidays = ref([])

        const calendar = (month = null) => {
            if (!month && form.value.delivery_date) {
                month = form.value.delivery_date.substr(0, 7)
            }
            api.get('/production/production_order_calendar', { params: { month } }).then(res => {
                events.value = res.delivery_dates;
                holidays.value = res.holidays;
            })
        }
        const authStore = useAuthStore();
        const $q = useQuasar();
        //console.log(authStore.user)
//         if (authStore.user.is_customer) {
//             $q.dialog({
//                 title: "توجه",
//                 //message: 'سفارشاتی که از تاریخ 1402/12/09 به بعد ثبت می شوند تحویل آنها اردیبهشت 1403 خواهد بود',
//                 message: 'قیمت اعلام شده در زمان ثبت سفارش قیمت قطعی نمی باشد بعد از شروع کار در سال ۱۴۰۳ فاکتور فروش به نرخ جدید برای شما ارسال خواهد شد.',
//                 persistent: true,
//                 ok:"تایید"
//               //  cancel: true,
//             })
// //.onOk(okAfterSubmit)
//         }

        return {
            start,
            form,
            date: ref(''),
            events,
            calendar,
            checkRole,
            navigation({ year, month }) {
                // console.log(year, month)
                calendar(year + '/' + month)
            },
            eventColor(date) {
                const find = events.value.findIndex(f => f.date == date);
                if (find >= 0) {
                    if (events.value[find].count >= 50)
                        return 'black';
                    if (events.value[find].count > 30)
                        return 'red';
                    else if (events.value[find].count > 15)
                        return 'yellow`';
                    return 'teal';
                }
            },
            deliveryDateOptions(date) {
                const find = events.value.findIndex(f => f.date == date);
                if (find >= 0) {
                    if (events.value[find].count >= 60)
                        return false;

                }
                if (holidays.value.includes(date))
                    return false
                return true;
            }
        }
    }
};
</script>