# Root level ignores
.env
.env.local
.env.*.local

# IDE and Editor files
/.fleet
/.idea
/.vscode
.phpactor.json

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Archives
*.zip
*.tar.gz
*.rar

# Backend (Laravel) specific
/backend/.phpunit.cache
/backend/node_modules
/backend/public/build
/backend/public/hot
/backend/public/storage
/backend/public/uploads
/backend/storage/*.key
/backend/vendor
/backend/.env
/backend/.env.backup
/backend/.env.production
/backend/.phpunit.result.cache
/backend/Homestead.json
/backend/Homestead.yaml
/backend/auth.json
/backend/storage/logs/*.log
/backend/storage/framework/cache/data/*
/backend/storage/framework/sessions/*
/backend/storage/framework/views/*
/backend/bootstrap/cache/*.php

# Frontend (Vue.js) specific
/frontend/node_modules
/frontend/dist
/frontend/.env
/frontend/.env.local
/frontend/.env.*.local
/frontend/coverage
/frontend/.nyc_output
/frontend/.cache
/frontend/.parcel-cache
/frontend/.vite