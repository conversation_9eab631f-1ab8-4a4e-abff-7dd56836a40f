<?php

namespace Modules\Good\Entities;

use App\Casts\ObjectCast;
use Modules\Good\Database\factories\GoodFactory;
use Modules\Production\Entities\ProductionOrderItem;
use Modules\Production\Entities\WorkInstruction;

class Good extends BModel
{

    protected static function newFactory(): GoodFactory
    {
        return GoodFactory::new();
    }

    const PRODUCT = 'PRODUCT';
    const MATERIAL = 'MATERIAL';
    const SERVICE = 'SERVICE';
    const types = [
        [
            'value' => self::PRODUCT,
            'label' => 'محصول',
        ],
        [
            'value' => self::MATERIAL,
            'label' => 'مواد اولیه',
        ],
        [
            'value' => self::SERVICE,
            'label' => 'خدمات',
        ],
    ];

    protected $fillable = [
        'name',
        'group_id',
        'type',
        'default_attribute',
        'is_active',
        'is_active_customer',
        'image_src',
    ];
    protected $casts = [
        'default_attribute' => ObjectCast::class,
        'is_active' => 'boolean',
        'is_active_customer' => 'boolean',
    ];
    protected $appends = [
        'label_type',
        'label_group',
    ];
    public function getLabelTypeAttribute()
    {
        return collect(self::types)->where('value', $this->type)->pluck('label')->first() ?? '';
    }
    public function getLabelGroupAttribute()
    {
        return $this->group->name ?? '';
    }

    public function group()
    {
        return $this->belongsTo(Group::class);
    }
    public function attributes()
    {
        //return $this->hasMany(AttributeGroup::class, 'group_id', 'group_id');
        return $this->belongsToMany(Attribute::class, AttributeGood::class)->withPivot(['options','sort'])->using(GroupPivot::class);
    }

    public function productionOrderItems()
    {
        return $this->hasMany(ProductionOrderItem::class);
    }

    // public function goodInstructions()
    // {
    //     return $this->hasMany(GoodInstruction::class); //->withPivot(['attribute_id', 'attribute_item_id']);
    // }
    // public function instructions()
    // {
    //     return $this->belongsToMany(Instruction::class, GoodInstruction::class)->withPivot(['attribute_id', 'attribute_item_id']);
    // }
    // public function instructions()
    // {
    //     return $this->belongsToMany(Instruction::class, GoodInstruction::class)->withPivot(['attribute_id', 'attribute_item_id']);
    // }

    public function workInstructions()
    {
        return $this->hasMany(WorkInstruction::class);
    }

    public function metas()
    {
       return $this->hasMany(GoodMeta::class);
    }
}
