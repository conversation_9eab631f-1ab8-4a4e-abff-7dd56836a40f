<template>
  <div class="q-gutter-sm">
    <j-table-data :url="`/production/station/${$route.params.id}/works`" :columns="columns" @loaded="loadedData">
      <template #dialog="props">
        <table-form v-bind="props" :instruction-id="$route.params.id" />
      </template>
    </j-table-data>
  </div>
</template>

<script>
import { usePublicStore } from "@/stores";
import TableForm from "./form.vue";

export default {
  setup() {

    const columns = [
      {
        name: 'name',
        required: true,
        label: 'نام',
        field: 'name',
        sortable: true,
        filter: 'FilterInput',
      },

    ]
    const publicStore = usePublicStore();
    const loadedData = (value) => {
      publicStore.titleWindow = 'کارهای ' + value.model.name
    }
    return {
      columns,
      loadedData,
    };
  },
  components: { TableForm },
};
</script>
