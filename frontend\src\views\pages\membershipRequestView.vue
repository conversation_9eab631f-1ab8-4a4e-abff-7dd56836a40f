<template>
    <div v-if="form.status == null" class="bg-white p-5 shadow rounded grid grid-cols-1 gap-y-3">
        <membership-request-form v-model:form="form" />
        <j-btn type="submit" class="sm:col-span-2" color="primary" label="ارسال درخواست" @click="submit" />
    </div>
    <div v-else-if="form.status === 'PENDING'" class="text-center text-lg flex flex-col justify-center items-center"
        style="height:calc(100vh - 150px)">
        در خواست عضویت شما با موفقیت ثبت شد پس از بررسی همکاران ما با شما تماس خواهند گرفت!
    </div>
</template>
<script setup>
import { api } from '@/boot/axios';
import { ref } from 'vue'
import membershipRequestForm from '@/views/crm/partyOnline/membershipRequestForm.vue'
const provinces = ref([]);

api.get('/provinces/search').then(res => {
    provinces.value = res.result;
})
const form = ref({})

api.get('/membership-request').then(({ result }) => {
    form.value = result;

})

const submit = () => {
    api.post('/membership-request', { ...form.value }).then(({ result }) => {
        form.value = result;
    })
}

</script>
