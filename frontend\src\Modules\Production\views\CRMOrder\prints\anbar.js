import { cmFormat } from ".";

export default function (data, attributes, template) {
    console.log('anbar')
    const goods = Object.values(
        data.items
            //.filter((f) => f.attributes.hasFrame)
            .filter(
                (f) =>
                    ['frame'].includes(f.good?.group?.key)
                    || (['door'].includes(f.good?.group?.key) && f.attributes.hasFrame)
            )
            .map((m) => ({ ...m, key: m.attributes.colorNavardarzgir }))
            .group("key")
    ).map((mm) => ({
        value:
            Math.ceil(
                mm
                    .map(
                        (m) =>
                            m.count *
                            (m.attributes.frameHeight * 2 +
                                (m.attributes.hasThreshold ? 2 : 1) * m.attributes.frameWidth +
                                5)
                    )
                    .reduce((a, b) => a + b) / 10,
                0
            ) * 10,
        label:
            mm[0].attributes_label.colorNavardarzgir +
            ` (${mm.reduce((a, b) => a + b.count, 0)})`,
    }));
    const pich = Object.values(
        data.items
            // .filter((f) => f.attributes.hasFrame)
            .filter(
                (f) =>
                    ['frame'].includes(f.good?.group?.key)
                    || (['door'].includes(f.good?.group?.key) && f.attributes.hasFrame)
            )
            .map((m) => ({ ...m, key: m.attributes.widthFloor }))
            .group("key")
    ).map((mm) =>
        mm
            .map(
                (m) =>
                    m.count *
                    (['9'].includes(m.attributes.widthFloor) ? 2 : 4) *
                    (m.attributes.hasThreshold ? 2 : 1)
            )
            .reduce((a, b) => a + b)
    );
    return `
    
    <table class="j-table w-full text-center mb-3">
        
       
        <thead>
                    <tr>
                        <td class="no-border p-0">
                            <table class="j-table w-full text-center odd-highlight">
                                <tr class="h-8">

                                    <td class="no-border w-1/3 bg-white">
                                        <div class="text-left text-sm">
                                            <b>نام نمایندگی: </b> ${
                                                data.party_name
                                            }
                                        </div>
                                        <div class="text-left text-sm">
                                            <b>نام مشتری: </b> ${
                                                data.customer_name
                                            }
                                        </div>
                                    </td>
                                    <td class="no-border w-1/3 bg-white">
                                        <img src="/images/logo-factor.png" class="h-10 m-auto" />
                                        <h6 style="text-align: center;font-weight: bolder;">برگه تولید ${
                                            template.station.name
                                        }</h6>
                                    </td>
                                    <td class="no-border w-1/3 bg-white text-right">
                                        <table class="ml-auto mr-0 text-sm">
                                            <tr>
                                                <th class="no-border bg-white">شناسه سفارش:</th>
                                                <td class="no-border bg-white">${
                                                    data.code
                                                }</td>

                                            </tr>
                                            <tr>
                                                <th class="no-border bg-white">تاریخ سفارش:</th>

                                                <td class="no-border bg-white">${
                                                    data.submit_date
                                                }</td>
                                            </tr>
                                            <tr>
                                                <th class="no-border bg-white">تاریخ تحویل:</th>

                                                <td class="no-border bg-white">${
                                                    data.delivery_date
                                                }</td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>


                            </table>
                        </td>
                    </tr>
                   

                </thead>

        <tbody>
        <tr>
            <td colspan="10" class="no-border">
            <table class="j-table w-full text-center">
                    <thead>
                        <tr>
                        ${goods
                            .map((m) => {
                                return `<th>${m.label}</th>`;
                            })
                            .join("")}
                            <th>پیچ و مهره</th>
                            <th>واشر</th>
                        </tr>
                    </thead>
                    <tbody>
                    <tr>
                        ${goods
                            .map((m) => {
                                return `<td>${cmFormat(m.value)}</td>`;
                            })
                            .join("")}
                            <td>${
                                pich.length > 0
                                    ? pich.reduce((a, b) => a + b)
                                    : ""
                            }</td>
                            <td>${
                                pich.length > 0
                                    ? 2 * pich.reduce((a, b) => a + b)
                                    : ""
                            }</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        </tbody>
    </table>
`;
}
