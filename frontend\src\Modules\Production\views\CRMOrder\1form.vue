<template>
    <j-form-data url="production/production_order">
        <template v-slot="{ form }">
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <j-select-remote ref="start" v-model="form.party_id" url="/production/party/search" label="نمایندگی"
                    autofocus option-label="full_name" field="full_name" error-field="party_id" search-local required />
                <j-input v-model="form.customer_name" label="مشتری" error-field="customer_name" />
                <!-- <j-select v-model="form.status" label="وضعیت" :options="formOption.statuses" dense /> -->
                <j-date-input v-model:value="form.delivery_date" label="تاریخ سفارش" />
                <j-input v-model="form.description" label="توضیحات سفارش" type="textarea" class="col-span-full" />
            </div>

            <items />
            <!-- <q-card flat bordered>
                <q-tabs v-model="tab" inline-label align="left" class="text-primary">
                    <q-tab name="roles" icon="admin_panel_settings" label="سمت ها" />
                    <q-tab name="permissions" icon="key" label="دسترسی ها" />
                </q-tabs>
                <q-separator />

                <q-tab-panels v-model="tab" animated swipeable vertical transition-prev="jump-up"
                    transition-next="jump-up">
                    <q-tab-panel name="roles">
                       4
                    </q-tab-panel>

                    <q-tab-panel name="permissions">
                        2
                    </q-tab-panel>
                </q-tab-panels>
            </q-card> -->
        </template>
    </j-form-data>
</template>

<script setup>
import { ref } from 'vue';
import items from './Items/index.vue'
const tab = ref(null)
</script>