import { useAuthStore } from "@/stores";


export function auth({ next, router, to }) {
    console.log('middleware auth')

    // console.log('middleware auth', from)
    const token = localStorage.getItem('token');
    if (!token && to.name != 'login') {
        return router.push({ name: 'login' });
    }
    else if (token && to.name == 'login') {
        return router.push({ name: 'dashboard' });
    }
    else return next();
}



export function permission({ next, router, to }) {
    console.log('middleware permission')

    //console.log('permission')
    //console.log(!checkPermission(to.meta.permissions))
    if (!checkPermission(to.meta.permissions)) return router.push({ name: '404' });
    else return next();
}




export function registerMiddleware(router) {
    //checkLogin(router);




}






function checkLogin(router) {
    router.beforeEach(async (to, from, next) => {
        const authStore = useAuthStore();
        const publicPages = ["/login"];
        const authRequired = !publicPages.includes(to.path);

        if (authRequired && !authStore.token) {
            next({ name: "login" });
        } else if (to.name == "login" && authStore.token) {
            next("/");
        } else if (authStore.token) {
            if (authStore.permissions == null) await authStore.getUser();
            if (!checkPermission(to.meta.permissions)) next("/403");
        }
        next();
    });
}



export function checkPermission(permissions) {
    console.log('middleware checkPermission', permissions)
    if (!permissions || Array.isArray(permissions) && permissions.length == 0)
        return true;
    const authStore = useAuthStore();
    if (authStore.user && authStore.user.is_super_admin) return true;
    //if (authStore.permissions === null) await authStore.getUser();

    if (Array.isArray(permissions)) {
        let find = false;
        permissions.map((m) => {
            if (authStore.user.permissions.includes(m)) find = true;
        });
        if (!find) return false;
    } else if (typeof permissions == "string") {
        if (!authStore.user?.permissions.includes(permissions)) return false;
    }
    else if (typeof permissions == "function") {
        if (!permissions(authStore.user.permissions)) return false;
    }
    return true;
}

export function checkRole(roles) {
    console.log('middleware checkRole')
    const authStore = useAuthStore();

    if (authStore.user && authStore.user.is_super_admin) return true;

    if (Array.isArray(roles)) {
        let find = false;
        roles.map((m) => {
            if (authStore.roles.includes(m)) find = true;
        });
        if (!find) return false;
    } else if (typeof roles == "string") {
        if (!authStore.roles.includes(roles)) return false;
    }
    return true;
}

export function filterAsyncRoutes(
    routes,
    { is_super_admin, roles, permissions }
) {
    console.log('middleware filterAsyncRoutes')
    const res = [];

    routes.forEach((route) => {
        const tmp = { ...route };
        if (canAccess({ is_super_admin, roles, permissions }, tmp)) {
            if (tmp.children) {
                tmp.children = filterAsyncRoutes(tmp.children, {
                    is_super_admin,
                    roles,
                    permissions,
                });
            }
            if (
                !tmp.component &&
                tmp.children &&
                tmp.children.filter((f) => !f.hidden).length == 0
            )
                return;
            res.push(tmp);
        }
    });

    return res;
}

function canAccess({ is_super_admin, roles, permissions }, route) {
    console.log('middleware canAccess')
    if (is_super_admin) return true;
    if (route.meta) {
        let hasRole = true;
        let hasPermission = true;
        if (route.meta.roles || route.meta.permissions) {
            hasRole = false;
            hasPermission = false;

            if (route.meta.roles && roles) {

                if (Array.isArray(route.meta.roles)) {
                    let find = false;
                    route.meta.roles.map((m) => {
                        if (roles.includes(m)) find = true;
                    });
                    if (!find) hasRole = false;

                } else if (typeof route.meta.roles == "string") {
                    if (roles.includes(route.meta.roles)) hasRole = true;
                }

            }
            hasPermission = checkPermission(route.meta.permissions)
            // if (route.meta.permissions && permissions) {

            //     if (Array.isArray(route.meta.permissions)) {
            //         let find = false;
            //         route.meta.permissions.map((m) => {
            //             if (permissions.includes(m)) find = true;
            //         });
            //         if (!find) hasPermission = false;

            //     } else if (typeof route.meta.permissions == "string") {
            //         if (permissions.includes(route.meta.permissions)) hasPermission = true;
            //     }


            // }
        }

        return hasRole || hasPermission;
    }
    return true;
}
