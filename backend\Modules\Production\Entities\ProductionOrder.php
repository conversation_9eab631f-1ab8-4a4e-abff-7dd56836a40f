<?php

namespace Modules\Production\Entities;

use App\Casts\JalaliDate;
use App\Models\User;
use Modules\Production\Entities\Party;

interface ProductionOrderStatus
{
    const DRAFT = "DRAFT";
    const SEND_DRAFT = "SEND_DRAFT";
    const PREORDER = "PREORDER";
    const SEND_PREORDER = "SEND_PREORDER";
    const CONFIRM_CUSTOMER = "CONFIRM_CUSTOMER";
    const PRODUCTION = "PRODUCTION";
    const DELIVERED = "DELIVERED";
    const CONFIRM = "CONFIRM";
    const FINISH_JOB = "FINISH_JOB";
    const FINANCIAL_APPROVAL = "FINANCIAL_APPROVAL";
    const FINANCIAL_SETTLEMENT = "FINANCIAL_SETTLEMENT";
}

class ProductionOrder extends BModel implements ProductionOrderStatus
{
    protected $fillable = [
        'party_id',
        'status',
        'customer_name',
        'description',
        'submit_date',
        'delivery_date',
        'user_id',
        'is_created_by_customer',
        'discount',
    ];

    protected $merge_casts = [
        'has_logo' => 'boolean',
        'is_created_by_customer' => 'boolean',
        'submit_date' => JalaliDate::class,
        'delivery_date' => JalaliDate::class,
    ];

    protected $appends = [
        'user_name',
        'party_name',
        'label_status',
        'label_status_customer',
        'previous_status',
        'next_status',
        'printable',
        //'code',
    ];

    public static $statuses = [
        [
            'value' => self::DRAFT,
            'label' => 'پیش نویس آنلاین',
            'label_customer' => 'پیش نویس',
            'printable' => false,
        ],
        [
            'value' => self::SEND_DRAFT,
            'label' => 'در انتظار بررسی',
            'label_customer' => 'در انتظار بررسی',
            'printable' => false,
        ],
        [
            'value' => self::PREORDER,
            'label' => 'پیش فاکتور',
            'label_customer' => 'پیش فاکتور',
            'printable' => false,
        ],
        [
            'value' => self::FINANCIAL_APPROVAL,
            'label' => 'تایید مالی',
            'label_customer' => 'تایید پیش پرداخت',
            'printable' => true,
        ],
        [
            'value' => self::PRODUCTION,
            'label' => 'خط تولید',
            'label_customer' => 'خط تولید',
            'printable' => true,
        ],
        [
            'value' => self::FINISH_JOB,
            'label' => 'اتمام کار',
            'label_customer' => 'در انتظار تسویه فاکتور',
            'printable' => true,
        ],
        [
            'value' => self::FINANCIAL_SETTLEMENT,
            'label' => 'تسویه مالی',
            'label_customer' => 'آماده تحویل',
            'printable' => true,
        ],
        [
            'value' => self::DELIVERED,
            'label' => 'حمل شده',
            'label_customer' => 'حمل شده',
            'printable' => true,
        ],
    ];



    public static function boot()
    {
        parent::boot();
        self::created(function ($model) {
            $model->code = $model->getCode();
            $model->save();
        });
    }


    public function party()
    {
        return $this->belongsTo(Party::class);
    }
    public function getPartyNameAttribute()
    {
        return $this->party->display_name ?? '';
    }
    public function getUserNameAttribute()
    {
        return $this->user ? $this->user->full_name : '';
    }
    public function getLabelStatusAttribute()
    {
        return collect(self::$statuses)->where('value', $this->status)->pluck('label')->first();
    }
    public function getLabelStatusCustomerAttribute()
    {
        return collect(self::$statuses)->where('value', $this->status)->pluck('label_customer')->first();
    }
    public function getPreviousStatusAttribute()
    {
        $status = $this->status;
        $find = collect(self::$statuses)->search(function ($item, $key) use ($status) {
            return $item['value'] == $status;
        });
        if ($find == 0) {
            return null;
        }
        return self::$statuses[$find - 1];

        // if (isset(self::$statuses[$find]['previous'])) {
        //     $find =  collect(self::$statuses)->search(fn ($item, $key) => $item['value'] == self::$statuses[$find]['previous']);
        //     return self::$statuses[$find];
        // }
        // return self::$statuses[$find - 1];
    }
    public function getNextStatusAttribute()
    {
        $status = $this->status;
        $find = collect(self::$statuses)->search(function ($item, $key) use ($status) {
            return $item['value'] == $status;
        });
        return count(self::$statuses) == $find + 1 ? null : self::$statuses[$find + 1];
    }
    public function getPrintableAttribute()
    {
        $status = $this->status;
        $find = collect(self::$statuses)->search(function ($item, $key) use ($status) {
            return $item['value'] == $status;
        });
        return count(self::$statuses) == $find + 1 ? null : self::$statuses[$find + 1]['printable'];
    }
    public function getCode()
    {
        return substr((10e3  + $this->id), 1);
        //return "PD-" . (substr($this['created_at'], 0, 4)) . substr((10e3  + $this->id), 1);
    }

    public function items()
    {
        return $this->hasMany(ProductionOrderItem::class);
    }

    public function checklists()
    {
        return $this->hasMany(ProductionOrderChecklist::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
