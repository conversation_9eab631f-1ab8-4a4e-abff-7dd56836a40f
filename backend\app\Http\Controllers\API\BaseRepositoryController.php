<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller as Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Validator;

class BaseRepositoryController extends Controller
{
    protected $repository;

    public function __construct($repository)
    {
        $this->repository = $repository;
    }

    public function index(): JsonResource
    {
        return JsonResource::collection($this->repository->query()->paginate());
    }

    public function show($id): JsonResponse
    {
        $item = $this->repository->find($id);
        return $item ? response()->json($item) : response()->json(['message' => 'Not found'], 404);
    }

    public function store(Request $request): JsonResponse
    {
        // در متد پایه قوانین اعتبارسنجی نداریم، باید در کلاس فرزند تعریف شود
        $item = $this->repository->create($request->all());
        return response()->json($item, 201);
    }

    public function update(Request $request, $id): JsonResponse
    {
        // در متد پایه قوانین اعتبارسنجی نداریم، باید در کلاس فرزند تعریف شود
        $item = $this->repository->update($id, $request->all());
        return $item ? response()->json($item) : response()->json(['message' => 'Not found'], 404);
    }

    public function destroy($id): JsonResponse
    {
        $deleted = $this->repository->delete($id);
        return $deleted ? response()->json(['message' => 'Deleted successfully']) : response()->json(['message' => 'Not found'], 404);
    }

    public function handleResponse($result = null, $msg = null, $additional = null)
    {
        $res = [
            'ok' => true,
            // 'result'    => $result,
        ];
        if ($result) {
            $res['result'] = $result;
        }

        if ($msg) {
            $res['message'] = $msg;
        }

        if ($additional) {
            $res = array_merge($res, $additional);
        }

        return response()->json($res, 200);
    }

    public function handleError($error, $errorMsg = [], $code = 404)
    {
        $res = [
            'ok' => false,
            'message' => $error,
        ];
        if (!empty($errorMsg)) {
            $res['result'] = $errorMsg;
        }
        return response()->json($res, $code);
    }
}
