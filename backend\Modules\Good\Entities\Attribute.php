<?php

namespace Modules\Good\Entities;

use App\Casts\ObjectCast;

class Attribute extends BModel
{
    protected $fillable = [
        'name',
        'type',
        'key',
        'showing',
        'required',
        'sort',
        'group_name',
        'parent_id',
    ];
    protected $casts = [
        'required' => 'boolean',
        'showing' => 'boolean',
        // 'data' => 'object',

    ];
    protected $appends = ['label_type'];
    const TYPE_SELECT = "SELECT";
    const TYPE_SELECT_IMAGE = "SELECT_IMAGE";
    const TYPE_INPUT = "INPUT";
    const TYPE_NUMBER = "NUMBER";
    const TYPE_SWITCH = "SWITCH";
    const TYPE_FILE = "FILE";
    const types = [
        [
            'value' => self::TYPE_SELECT,
            'label' => 'انتخاب',
            'has_children' => true,
        ],
        [
            'value' => self::TYPE_SELECT_IMAGE,
            'label' => 'انتخاب عکس',
            'columns' => [
                [
                    "label" => "عکس",
                    "field" => "image",
                    "name" => "image",
                    "type" => "FILE",
                ],

            ]
        ],
        [
            'value' => self::TYPE_INPUT,
            'label' => 'متن',
            'has_children' => false,
        ],
        [
            'value' => self::TYPE_NUMBER,
            'label' => 'عدد',
            'has_children' => false,
        ], [
            'value' => self::TYPE_SWITCH,
            'label' => 'تعویض',
            'has_children' => false,
        ], [
            'value' => self::TYPE_FILE,
            'label' => 'فایل',
            'has_children' => false,
        ],
    ];


    public function getLabelTypeAttribute()
    {
        return collect(self::types)->where('value', $this->type)->pluck('label')->first() ?? '';
    }

    public function items()
    {
        return $this->hasMany(AttributeItem::class)
            ->withCasts([
                'data' =>  ObjectCast::class,
            ]);
    }

    public function conditions()
    {
        return $this->hasMany(AttributeItemCondition::class);
    }

    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    public function parentItems()
    {
        return $this->items()->when($this->parent_id, function($q){
            $q->orWhere('attribute_id', $this->parent_id);
        });
    }

    public function childs()
    {
        return $this->hasMany(self::class, 'parent_id');
    }
}
