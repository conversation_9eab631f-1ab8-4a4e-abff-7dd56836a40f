<template>
    <j-form @submit="submit">
        <j-input v-model="form.name" dense error-field="name" label="نام گروه" />
        <div>
            <attribute-group v-model:value="form.attributes" />
        </div>
        <j-btn type="submit" label="ذخیره" color="primary" />
    </j-form>
</template>
<script>
import { reactive, ref } from "@vue/reactivity";
import AttributeGroup from "./AttributeGroup.vue";
import { formApi } from "@/helpers";

export default {
    props: {
        form: {
            type: Object,
            default: () => { }
        },
    },
    setup(props, context) {

        const { form, submit } = formApi('good/group', props)

        const loading = ref(false);

        return {
            form,
            submit,
            loading,
        };
    },
    components: { AttributeGroup }
};
</script>