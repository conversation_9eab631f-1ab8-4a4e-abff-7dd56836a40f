<template>
    <j-dialog-bar v-if="!loading" v-model="dialogPrint" persistent content-class="p-0">
        <template #title>فاکتور</template>
        <template #bar>
            <j-btn v-if="!['DRAFT', 'SEND_DRAFT', 'PREORDER'].includes(data.status)" flat dense icon="print"
                label="پرینت فاکتور" title="پرینت فاکتور" @click="print('content')" />
        </template>

        <template v-slot:default="{ maximized }">
            <div class="overflow-x-auto">
                <div id="content" class="p-2 overflow-auto"
                    :style="'max-height: calc(100vh - ' + (maximized ? '40px' : '88px') + ');'">

                    <table class="w-full text-center j-whitespace-normal">
                        <thead>
                            <tr>
                                <td class="no-border p-0 pb-4">
                                    <div class="flex flex-nowrap">
                                        <div class="text-xs">
                                            <div class="grid gap-1">
                                                <div class="flex flex-nowrap items-center">
                                                    <div class="text-left font-bold w-24">
                                                        شناسه سفارش:
                                                    </div>
                                                    <div class="w-40">
                                                        {{ data.code }}
                                                    </div>
                                                </div>
                                                <div class="flex flex-nowrap items-center">
                                                    <div class="text-left font-bold w-24">
                                                        نام نمایندگی:
                                                    </div>
                                                    <div class="w-40">
                                                        {{ data.party_name }}
                                                    </div>
                                                </div>
                                                <div class="flex flex-nowrap items-center">
                                                    <div class="text-left font-bold w-24">
                                                        نام مشتری:
                                                    </div>
                                                    <div class="w-40">
                                                        {{ data.customer_name }}
                                                    </div>
                                                </div>
                                                <div class="flex flex-nowrap items-center">
                                                    <div class="text-left font-bold w-24">
                                                        تاریخ سفارش:
                                                    </div>
                                                    <div class="w-40">
                                                        {{ data.created_at?.substr(0, 10) }}
                                                    </div>
                                                </div>
                                                <div class="flex flex-nowrap items-center">
                                                    <div class="text-left font-bold w-24">
                                                        تاریخ سفارش:
                                                    </div>
                                                    <div class="w-40">
                                                        {{ data.delivery_date }}
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="grow content-center min-w-24 font-bold text-2xl">
                                            فاکتور فروش
                                        </div>
                                        <div>
                                            <img src="@/assets/logo.svg" class="w-52 px-5" />
                                        </div>
                                    </div>
                                </td>
                            </tr>

                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="grid gap-5">
                                        <template
                                            v-for="item, good_id in data.items.group(m => m.good.id + '_' + m.attributes?.hasFrame ?? '')"
                                            :key="good_id">
                                            <j-table :columns="new_columns(item, good_id)" :rows="item" flat bordered
                                                separator="cell" hide-bottom row-key="code" :rows-per-page-options="[0]"
                                                dense table-header-class="bg-gray-100 dark:bg-gray-900">

                                                <template #body-cell-index="props">
                                                    <q-td :props="props">
                                                        {{ props.rowIndex + 1 }}
                                                    </q-td>
                                                </template>
                                                <template #body-cell-good="props">
                                                    <template v-if="props.rowIndex === 0">
                                                        <q-td :rowspan="item.length" :props="props">
                                                            {{ props.value }}
                                                        </q-td>
                                                    </template>
                                                </template>
                                                <template #body-cell-customModelDoor="props">
                                                    <q-td :props="props">
                                                        <j-image-viewer
                                                            :src="props.row.attributes['customModelDoor']" />
                                                    </q-td>
                                                </template>
                                                <template v-if="item && item.length > 0" #bottom-row="props">
                                                    <q-tr class="bg-gray-100 dark:bg-gray-900">
                                                        <q-td class="text-bold text-center" colspan="3">
                                                            جمع
                                                        </q-td>

                                                        <q-td class="text-center">
                                                            {{item.reduce((a, b) => a + b.count * 1, 0)}}
                                                        </q-td>

                                                        <q-td class="text-center">
                                                            {{String.currencyFormat(item.reduce((a, b) => a + b.count *
                                                                b.price, 0))}}
                                                        </q-td>

                                                        <q-td :colspan="props.cols.length - 5" />
                                                    </q-tr>
                                                </template>
                                            </j-table>
                                        </template>
                                        <q-markup-table dense flat bordered separator="cell">
                        <tbody>
                            <tr>
                                <td class="w-2/3 text-right font-bold">توضیحات</td>
                                <td>تعداد کل:</td>
                                <td>{{data.items.reduce((s, a) => a.count * 1 + s, 0)}}</td>
                            </tr>
                            <tr>
                                <td rowspan="4" class="border-r text-right align-top"
                                    style="white-space: break-spaces;">{{
                                        data.description }}</td>
                                <td>مبلغ کل:</td>
                                <td>{{String.currencyFormat(data.items.reduce((s, a) => a.total_price * 1 + s, 0))}}
                                </td>
                            </tr>
                            <tr>
                                <td>تخفیف:</td>
                                <td>{{ String.currencyFormat(data.discount) }}</td>
                            </tr>
                            <tr>
                                <td>مبلغ نهایی:</td>
                                <td>{{String.currencyFormat(data.items.reduce((s, a) => a.total_price * 1 + s, 0) -
                                    (data.discount
                                        ?? 0))}}</td>
                            </tr>
                        </tbody>
                        </q-markup-table>
                </div>
                </td>
                </tr>


                </tbody>

                </table>
            </div>
            <template v-if="authStore?.user?.is_customer && selected.status == 'DRAFT'">
                <div class="m-auto text-center">
                    <j-btn label="ارسال سفارش به کارخانه" color="primary" @click="sendDraft" />
                </div>
            </template>


            </div>

        </template>
    </j-dialog-bar>
</template>
<script>
import { computed, ref, watch } from 'vue';
import { api } from '@/boot/axios';
import { print, checkPermission, checkRole } from '@/helpers'
import { printLabel, printTemplate } from './prints'
import { useAuthStore } from '@/stores';
import ImageByText from '../productionOrder/components/ImageByText.vue';
import { computeProductionOrderItem } from '../../computeProductionFormula';
import Checklist from './checklist.vue';
import { attributeColumns } from './selectForm/index';
import { useQuasar, QSpinnerFacebook } from 'quasar';

export default {
    components: { ImageByText, Checklist },
    props: {
        selected: Object,
        callback: Function,
    },
    setup(props) {

        const data = ref({})
        const attributes = ref([])
        const doors = ref([])
        const group_doors = ref({})
        const frames = ref([])
        const others = ref([])
        const $q = useQuasar()
        $q.loading.show({
            spinner: QSpinnerFacebook,
        })


        const imageColor = row => {
            //  console.log(66666)
            const find = attributes.value.findIndex(f => f.key == "pvcColor")
            if (find >= 0) return attributes.value[find].items[attributes.value[find].items.findIndex(f => f.key == row.attributes.pvcColor)]?.data?.image;
            return ''
        }





        const isShowImage = ref(false)
        const urlImage = ref('')
        const loading = ref(false)

        watch(() => props.selected, (selected) => {
            props.selected = selected
        })

        const dialogPrint = ref(false)
        const statuses = ref([])
        const result = ref({})
        dialogPrint.value = true;
        loading.value = true;
        api.get(`/production/production_order/${props.selected.id}/print`).then(res => {
            result.value = res.result
            // res.result.items = computeProductionOrderItem(res.result.items.sortBy('code'))
            statuses.value = res.statuses
            data.value = res.result;
            doors.value = res.result.items.filter(f => ['door'].includes(f.good?.group?.key));
            group_doors.value = res.result.items.filter(f => ['door'].includes(f.good?.group?.key)).group(g => (g.attributes.hasFrame ? 'byFrame' : (g.attributes.hasRokoob ? 'byRokoob' : 'door')))
            attributes.value = res.attributes
        }).finally(() => {
            loading.value = false;
            $q.loading.hide()
        })

        const authStore = useAuthStore();

        const new_columns = (items, good_id) => {
            //console.log('items',items)
            // if (groups.value.length == 0)
            //     return [];
            return [

                // {
                //     name: "group",
                //     label: "محصول",
                //     style: 'width: 30px',
                //     field: row => row?.good?.name ?? '-'
                // },
                {
                    name: "index",
                    label: 'ردیف',
                    style: 'width: 30px',
                    //field: (row,index) => index,
                    align: 'center',
                    summary: true,
                    checkbox_label: true
                },
                // {
                //     name: "id",
                //     label: 'شناسه',
                //     style: 'width: 30px',

                //     field: row => row.id ?? '-',
                //     align: 'center',
                // },
                {
                    name: "good",
                    label: "محصول",
                    //style: 'width: 120px',

                    field: row => row.good.name ?? '',
                    align: 'center',
                    hasImage: true,
                    image: row => row.good?.image_src ?? '',
                    summary: true,
                    checkbox_label: true,
                },
                {
                    name: "price",
                    label: "فی (ریال)",
                    //style: 'width: 120px',

                    field: row => String.currencyFormat(row.price),
                    align: 'center',
                    summary: true,
                    //permissions: 'show price order',

                },
                {
                    name: "count",
                    label: "تعداد",
                    //style: 'width: 50px',

                    field: "count",
                    align: 'center',
                    summary: true

                }
                , {
                    name: "sum_price",
                    label: "جمع (ریال)",
                    //style: 'width: 120px',

                    field: row => String.currencyFormat(row.price * row.count),
                    align: 'center',
                    summary: true,
                    //permissions: 'show price order',

                },

                // ...attributeColumns({
                //     attributes: items[0].attributes, items, extra: {
                //         //hide_table: true
                //     }
                // }),
                ...attributeColumns({
                    attributes: attributes.value.filter(f => items.map(m => Object.keys(m.attributes)).flat().unique().includes(f.key)), items, extra: {
                        //hide_table: true
                    }
                }),
                {
                    name: "description",
                    label: "توضیحات",
                    field: "description",
                    align: 'center',

                }
            ];

        }


        return {
            loading,
            new_columns,
            result,
            statuses,
            checkRole,
            dialogPrint,
            authStore,
            doors,
            group_doors,
            others,
            data,
            print,
            attributes,
            printLabel,
            printTemplate,
            isShowImage,
            urlImage,
            checkPermission,
            showImage(value) {
                urlImage.value = value
                isShowImage.value = true
            },
            show() {
                //console.log('dialog hide')
                dialogPrint.value = true;
            },
            $q,
        }
    },
}
</script>
