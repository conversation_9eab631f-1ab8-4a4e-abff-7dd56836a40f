<?php
namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Modules\Production\Entities\ProductionOrder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {

        // User::factory()->create([
        //     'full_name' => 'Admin',
        //     'username' => 'admin',
        //     'is_super_admin' => true,
        //     'email' => '<EMAIL>',
        //     'password' => Hash::make('admin'),
        // ]);
        // User::factory(50)->create();

        //

        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // create permissions
        //Permission::truncate();
        //Role::truncate();
        Role::create(['name' => 'admin', 'label' => 'مدیریت']);
        Role::create(['name' => 'sell-manager', 'label' => 'مدیر فروش']);
        Role::create(['name' => 'production-manager', 'label' => 'مدیر تولید']);
        Role::create(['name' => 'sell-person', 'label' => 'کارشناس فروش']);
        Role::create(['name' => 'user', 'label' => 'کاربر']);

        // update cache to know about the newly created permissions (required if using WithoutModelEvents in seeders)
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // create roles and assign created permissions

        // this can be done as separate statements

        Permission::create(['name' => 'production_order', 'label' => 'منو سفارشات']);
        Permission::create(['name' => 'production_order.create', 'label' => 'سفارش جدید']);
        Permission::create(['name' => 'production_order.edit', 'label' => 'ویرایش سفارش']);
        Permission::create(['name' => 'production_order.delete', 'label' => 'حذف سفارش']);
        Permission::create(['name' => 'production_order.change-status', 'label' => 'تغییر وضعیت سفارش']);
        Permission::create(['name' => 'production_order.print-order', 'label' => 'چاپ سفارش']);
        Permission::create(['name' => 'production_order.print-production', 'label' => 'چاپ استعلام']);

        foreach (ProductionOrder::$statuses as $status) {
            Permission::create(['name' => 'production_order.change-status.' . $status['value'], 'label' => $status['label']]);
        }

        Permission::create(['name' => 'parties', 'label' => 'منو نمایندگان']);
        Permission::create(['name' => 'parties.create', 'label' => 'نماینده جدید']);
        Permission::create(['name' => 'parties.edit', 'label' => 'ویرایش نماینده']);
        Permission::create(['name' => 'parties.delete', 'label' => 'حذف نماینده']);

        Permission::create(['name' => 'products', 'label' => 'منو محصولات']);
        Permission::create(['name' => 'products.create', 'label' => 'محصول جدید']);
        Permission::create(['name' => 'products.edit', 'label' => 'ویرایش محصول']);
        Permission::create(['name' => 'products.delete', 'label' => 'حذف محصول']);

        Permission::create(['name' => 'attributes', 'label' => 'منو ویژگی ها']);
        Permission::create(['name' => 'attributes.create', 'label' => 'ویژگی جدید']);
        Permission::create(['name' => 'attributes.edit', 'label' => 'ویرایش ویژگی']);
        Permission::create(['name' => 'attributes.delete', 'label' => 'حذف ویژگی']);

        Permission::create(['name' => 'users', 'label' => 'منو کاربران']);
        Permission::create(['name' => 'users.create', 'label' => 'کاربر جدید']);
        Permission::create(['name' => 'users.edit', 'label' => 'ویرایش کاربر']);
        Permission::create(['name' => 'users.delete', 'label' => 'حذف کاربر']);

        Permission::create(['name' => 'parties_online', 'label' => 'منو نمایندگان آنلاین']);
        Permission::create(['name' => 'parties_online.edit', 'label' => 'ویرایش نماینده']);

        Permission::create(['name' => 'roles', 'label' => 'منو سمت های کاربری']);
        Permission::create(['name' => 'roles.create', 'label' => 'سمت جدید']);
        Permission::create(['name' => 'roles.edit', 'label' => 'ویرایش سمت']);
        Permission::create(['name' => 'roles.delete', 'label' => 'حذف سمت']);
        // $this->call(ProductionDatabaseSeeder::class);
        // $this->call(GoodDatabaseSeeder::class);
        // $this->call(CitySeeder::class);
    }
}
