<?php

namespace Modules\Production\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\API\BaseController;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Good\Entities\Attribute;
use Modules\Production\Entities\Station;
use Modules\Production\Entities\StationWork;

class StationWorkController extends BaseController
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index(Station $station)
    {
        return JsonResource::collection($station->works()->get())->additional([
            'model' => $station,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Station $station, Request $request)
    {
        $item = StationWork::create(array_merge($request->all(), [
            'station_id' => $station->id,
        ]));
        $item->attributes()->sync($request->input('attributes') ?? []);
        $item->attributes = $item->attributes()->pluck('id');
        return $this->handleResponse($item, trans('request.done'));
    }

    /**
     * Show the specified resource.
     * @param int Station $station, StationWork $work
     * @return Response
     */
    public function show(Station $station, StationWork $work)
    {
        $work->attributes = $work->attributes()->pluck('id');
        return $this->handleResponse($work, null, [
            'additional' => ['attributes' => Attribute::all()]
        ]);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int Station $station, StationWork $work
     * @return Response
     */
    public function update(Request $request, Station $station, StationWork $work)
    {
        $work->update(array_merge($request->all(), [
            'station_id' => $station->id,
        ]));
        $work->attributes()->sync($request->input('attributes') ?? []);
        $work->attributes = $work->attributes()->pluck('id');

        return $this->handleResponse($work, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     * @param int Station $station, StationWork $work
     * @return Response
     */
    public function destroy(Station $station, StationWork $work)
    {
        return $this->handleResponse($work->delete());
    }
}
