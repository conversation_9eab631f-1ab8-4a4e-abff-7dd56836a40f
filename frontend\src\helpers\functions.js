export { checkPermission, checkRole } from "./router/middleware";
export const getUrlAssets = function (uri = "") {
    return new URL("../assets/" + uri, import.meta.url).href;
};
import svgs from '@/assets/svg-icons';
export const iconImage = (src) => {
    //const svgs = await loadSvgs();
    return `img:${svgs[src]}`

}
JSON.valid = (s) => {
    try {
        JSON.parse(s);
        return true;
    } catch (e) {
        return false;
    }
    // return s
    //     ? /^[\],:{}\s]*$/.test(
    //           s
    //               .replace(/\\["\\\/bfnrtu]/g, "@")
    //               .replace(
    //                   /"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,
    //                   "]"
    //               )
    //               .replace(/(?:^|:|,)(?:\s*\[)+/g, "")
    //       )
    //     : false;
};

JSON.parseForce = (s) => {
    return JSON.valid(s) ? JSON.parse(s) : undefined;
};

Array.prototype.findNested = function (key, value) {
    return this.reduce((found, item) => {
        if (found) return found;

        if (item[key] == value) return item;
        else if (item.children && item.children.length > 0) {
            return item.children.findNested(key, value);
        } else return null;
    }, null);
};

Array.prototype.unique = function () {
    // console.log(this, key, value);
    return this.filter((value, index, self) => {
        return self.indexOf(value) === index;
    });
};

Array.prototype.toggle = function (...items) {
    var arr = this;
    for (let el of items) {
        var len = arr.length;
        for (var i = 0; i < arr.length; i++)
            if (arr[i] === el) arr.splice(i--, 1);
        if (arr.length === len) arr.push(el);
    }
    return arr;
};

Array.prototype.attach = function (...items) {
    var arr = this;
    for (let el of items) {
        if (arr.indexOf(el) === -1) {
            arr.push(el);
        }
    }
    return arr;
};

Array.prototype.dettach = function (...items) {
    var arr = this;
    for (let el of items) {
        var len = arr.length;
        for (var i = 0; i < arr.length; i++)
            if (arr[i] === el) arr.splice(i--, 1);
        // if (arr.length === len) arr.push(el);
    }
    return arr;
};

Array.prototype.group = function (key) {
    return this.reduce(function (rv, x) {
        const val = typeof key == "function" ? key(x) : x[key];
        (rv[val] = rv[val] || []).push(x);
        return rv;
    }, {});
};

Array.prototype.chunk = function (groupsize) {
    var sets = [], chunks, i = 0;
    chunks = Math.ceil(this.length / groupsize);

    while (i < chunks) {
        sets[i] = this.splice(0, groupsize);
        i++;
    }
    return sets;
};

Array.prototype.sortBy = function (key = null) {
    const collator = new Intl.Collator(undefined, {
        numeric: true,
        sensitivity: "base",
    });
    return this.sort(function (a, b) {
        return collator.compare(key ? a[key] : a, key ? b[key] : b);
    });
};

Array.prototype.sortByDesc = function (key = null) {
    const collator = new Intl.Collator(undefined, {
        numeric: true,
        sensitivity: "base",
    });
    return this.sort(function (a, b) {
        return collator.compare(key ? b[key] : b, key ? a[key] : a);
    });
};

Array.prototype.sortAsc = function (key = "sort", key2 = null) {
    return this.sort(function (a, b) {
        return a[key] - b[key] || (key2 ? a[key2] - b[key2] : 1);
    });
};
Array.prototype.sortDesc = function (key = "sort", key2 = null) {
    return this.sort(function (a, b) {
        return b[key] - a[key] || (key2 ? b[key2] - a[key2] : 1);
    });
};

Object.diff = function () {
    var i, l, leftChain, rightChain;

    function compare2Objects(x, y) {
        var p;

        // remember that NaN === NaN returns false
        // and isNaN(undefined) returns true
        if (
            isNaN(x) &&
            isNaN(y) &&
            typeof x === "number" &&
            typeof y === "number"
        ) {
            return true;
        }

        // Compare primitives and functions.
        // Check if both arguments link to the same object.
        // Especially useful on the step where we compare prototypes
        if (x === y) {
            return true;
        }

        // Works in case when functions are created in constructor.
        // Comparing dates is a common scenario. Another built-ins?
        // We can even handle functions passed across iframes
        if (
            (typeof x === "function" && typeof y === "function") ||
            (x instanceof Date && y instanceof Date) ||
            (x instanceof RegExp && y instanceof RegExp) ||
            (x instanceof String && y instanceof String) ||
            (x instanceof Number && y instanceof Number)
        ) {
            return x.toString() === y.toString();
        }

        // At last checking prototypes as good as we can
        if (!(x instanceof Object && y instanceof Object)) {
            return false;
        }

        if (x.isPrototypeOf(y) || y.isPrototypeOf(x)) {
            return false;
        }

        if (x.constructor !== y.constructor) {
            return false;
        }

        if (x.prototype !== y.prototype) {
            return false;
        }

        // Check for infinitive linking loops
        if (leftChain.indexOf(x) > -1 || rightChain.indexOf(y) > -1) {
            return false;
        }

        // Quick checking of one object being a subset of another.
        // todo: cache the structure of arguments[0] for performance
        for (p in y) {
            if (y.hasOwnProperty(p) !== x.hasOwnProperty(p)) {
                return false;
            } else if (typeof y[p] !== typeof x[p]) {
                return false;
            }
        }

        for (p in x) {
            if (y.hasOwnProperty(p) !== x.hasOwnProperty(p)) {
                return false;
            } else if (typeof y[p] !== typeof x[p]) {
                return false;
            }

            switch (typeof x[p]) {
                case "object":
                case "function":
                    leftChain.push(x);
                    rightChain.push(y);

                    if (!compare2Objects(x[p], y[p])) {
                        return false;
                    }

                    leftChain.pop();
                    rightChain.pop();
                    break;

                default:
                    if (x[p] !== y[p]) {
                        return false;
                    }
                    break;
            }
        }

        return true;
    }

    if (arguments.length < 1) {
        return true; //Die silently? Don't know how to handle such case, please help...
        // throw "Need two or more arguments to compare";
    }

    for (i = 1, l = arguments.length; i < l; i++) {
        leftChain = []; //Todo: this can be cached
        rightChain = [];

        if (!compare2Objects(arguments[0], arguments[i])) {
            return false;
        }
    }

    return true;
};


String.currencyFormat = (price) => {
    return Intl.NumberFormat().format(price);// + ' ریال'
}




Object.defineProperty(Object.prototype, 'pick', {
    value: function (keys) {
        // اگر keys خالی، null یا undefined بود، شیء اصلی را برگردان
        if (!Array.isArray(keys) || keys.length === 0) {
            return this;
        }

        return keys.reduce((result, key) => {
            if (this.hasOwnProperty(key)) {
                result[key] = this[key];
            }
            return result;
        }, {});
    },
    enumerable: false, // this is actually the default
});