<template>
    <j-dialog-bar v-if="!loading" v-model="dialogPrint" persistent content-class="p-0">
        <template #title>پرینت لیبل</template>

        <template v-slot:default="{ maximized }">
            <templateLabel v-bind="dataLabel" />
        </template>
    </j-dialog-bar>
</template>
<script>
import { ref, watch } from 'vue';
import { api } from '@/boot/axios';
import { useQuasar, QSpinnerFacebook } from 'quasar';
import templateLabel from './templates/templateLabel.vue'
import logo_src from '@/assets/logo.svg';

export default {
    components: { templateLabel },
    props: {
        selected: Object,
        callback: Function,
    },
    setup(props) {

        const data = ref({})
        const attributes = ref([])
        const dataLabel = ref({})
        const $q = useQuasar()
        $q.loading.show({
            spinner: QSpinnerFacebook,
        })
        

        const loading = ref(false)

        watch(() => props.selected, (selected) => {
            props.selected = selected
        })

        const dialogPrint = ref(false)
        const result = ref({})
        dialogPrint.value = true;
        loading.value = true;
        api.get(`/production/production_order/${props.selected.id}/print`).then(res => {
            result.value = res.result
            // res.result.items = computeProductionOrderItem(res.result.items.sortBy('code'))
            data.value = res.result;
            attributes.value = res.attributes

            dataLabel.value = {
                logo_src,
                code: data.value.code,
                party_name: data.value.party_name,
                customer_name: data.value.customer_name,
                delivery_date: data.value.delivery_date,
                order_description: data.value.description,
                item: data.value.items[0]
            }
        }).finally(() => {
            loading.value = false;
            $q.loading.hide()
        })





        return {
            loading,
            data,
            dataLabel,
            attributes,
            dialogPrint,
            $q,
            show() {
                dialogPrint.value = true;
            },
        }
    },
}
</script>
