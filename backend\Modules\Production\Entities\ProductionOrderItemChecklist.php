<?php

namespace Modules\Production\Entities;

interface ProductionOrderItemChecklistStatus
{
    const PROCESSING = 'PROCESSING';
    const DONE = 'DONE';
    const REPAIRING = 'REPAIRING';
}
class ProductionOrderItemChecklist extends BModel implements ProductionOrderItemChecklistStatus
{
    protected $fillable = [
        'production_order_item_id',
        'instruction_item_id',
        'status',
        'code',
    ];

    const type = [
        [
            'value' => self::PROCESSING,
            'label' => 'در حال انجام',
        ], [
            'value' => self::DONE,
            'label' => 'انجام شده',
        ], [
            'value' => self::REPAIRING,
            'label' => 'در حال تعمیر',
        ],
    ];

    public function instructionItem()
    {
        return $this->belongsTo(InstructionItem::class);
    }
    public function productionOrderItem()
    {
        return $this->belongsTo(ProductionOrderItem::class);
    }
}
