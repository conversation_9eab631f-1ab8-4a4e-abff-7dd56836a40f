<template>
    <q-dialog ref="ref_dialog">
        <template v-for="_, slot in $slots" v-slot:[slot]="props" :key="slot">
            <slot :name="slot" v-bind="props" :key="slot" />
        </template>
    </q-dialog>
</template>
<script>
import { ref } from 'vue';


export default {
    setup() {
        const ref_dialog = ref(null)

        return {
            ref_dialog,
            hide() {
                //console.log('dialog hide')
                ref_dialog.value.hide()
            },
            show() {
                //console.log('dialog hide')
                ref_dialog.value.show()
            },
        }
    }
}

</script>
