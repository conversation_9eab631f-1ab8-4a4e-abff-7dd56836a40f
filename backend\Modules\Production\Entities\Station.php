<?php

namespace Modules\Production\Entities;

use Modules\Good\Entities\Attribute;

class Station extends BModel
{
   protected $fillable = [
      'name',
   ];

   public function attributes()
   {
      return $this->belongsToMany(Attribute::class, AttributeStation::class)->withPivot(['sort']);
   }

   public function instructions()
   {
      return $this->belongsToMany(Instruction::class, InstructionItem::class);
   }

   public function instructionItems()
   {
      return $this->hasMany(InstructionItem::class);
   }

   public function works()
   {
      return $this->hasMany(StationWork::class);
   }

   public function machines()
   {
      return $this->belongsToMany(Machine::class, StationMachine::class);
   }

   public function instructionItemChecklists()
   {
      return $this->hasManyThrough(ProductionOrderItemChecklist::class, InstructionItem::class);
   }
   public function productionOrderItemWorkInstruction()
   {
      return $this->hasManyThrough(ProductionOrderItemWorkInstruction::class, WorkInstruction::class);
   }

   // public function productionOrderItem(){
   //    return $this->
   // }
}
