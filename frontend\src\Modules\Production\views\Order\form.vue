<template>
    <j-form-data url="production/production_order" hasCreateRoute :form="{discount:0}">
        <template v-slot="{ form, formOptions }">
            <div v-if="form" class="grid grid-cols-1 md:grid-cols-3 gap-2">
                <j-select-remote ref="start" v-model="form.party_id"
                    url="/production/party/search" label="نمایندگی" autofocus option-label="display_name"
                    field="display_name" error-field="party_id" search-local />
                <j-input v-model="form.customer_name" label="مشتری" error-field="customer_name" />
                <j-date-input v-model:value="form.delivery_date"
                    label="تاریخ سفارش" />

            </div>
            <select-item v-if="form" v-model:data="form.items" :id="form.id" v-bind="{ formOptions }" />


            <div class="grid grid-cols-3 gap-5">
                <div class="col-span-2">
                    <j-input v-model="form.description" label="توضیحات سفارش" type="textarea" class="col-span-full"
                        outlined />
                </div>
                <div class="flex gap-1 content-start" v-if="form.items">
                    <div class="grid grid-cols-2 w-full">
                        <span>تعداد کل:</span>
                        <span>{{ form.items.reduce((s, a) => a.count * 1 + s, 0) }}</span>
                    </div>
                    <div class="grid grid-cols-2 w-full">
                        <span>جمع کل:</span>
                        <span>{{ String.currencyFormat(form.items.reduce((s, a) => (a.price * a.count) + s, 0)) }}</span>
                    </div>
                    <div class="grid grid-cols-2 w-full">
                        <span>تخفیف:</span>
                        <span>

                        <j-input-number v-model.number="form.discount" :max="form.items.reduce((s, a) => (a.price * a.count) + s, 0)" dense filled />
                        </span>
                    </div>
                    <div class="grid grid-cols-2 w-full">
                        <span>مبلغ نهایی کل:</span>
                        <span>
                            {{ String.currencyFormat(form.items.reduce((s, a) => (a.price * a.count) + s, 0) - (form.discount ?? 0)) }}
                        </span>
                    </div>
                </div>
            </div>

        </template>
    </j-form-data>
</template>
<script setup>
import SelectItem from './selectForm/index.vue';
const maxDiscount = (items)=> items.reduce((s, a) => (a.price * a.count) + s, 0)
</script>