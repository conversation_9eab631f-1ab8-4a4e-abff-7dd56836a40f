<template>
    <table class="j-table w-full text-center odd-highlight">
        <thead>
            <tr>
                <th :colspan="columns.length" style="font-size: 15px;">{{ good.label }}</th>
            </tr>
            <tr>
                <template v-for="column, index2 in columns" :key="index2">
                    <th v-if="column.name == 'action'" :style="column.style">
                        <div class="gap-3 grid grid-cols-1">
                            <j-toggle v-model="selectAll[good.key]" dense toggle-indeterminate checked-icon="check"
                                color="primary" keep-color unchecked-icon="clear" true-value="DONE"
                                false-value="REPAIRING" indeterminate-value="PROCESSING" class="mx-auto"
                                @update:model-value="value => onToggleSelectAll(value, good)" />

                            <j-btn icon="save" flat dense color="primary" @click="save" />
                        </div>
                    </th>
                    <th v-else>{{ column?.label }}</th>
                </template>
            </tr>
        </thead>
        <tbody>
            <tr v-for="row, index3 in good.items.sortDesc('width', 'height')" :key="index3">
                <template v-for="column, index2 in columns" :key="index2">
                    <td v-if="column.name == 'action'" :style="column.style">
                        <q-toggle v-model="selected[row.id]" dense toggle-indeterminate checked-icon="check"
                            color="green" unchecked-icon="clear" true-value="DONE" false-value="REPAIRING"
                            indeterminate-value="PROCESSING" />
                    </td>
                    <td v-else v-html="column?.field ? column.field(row) : ''" />
                </template>
            </tr>
        </tbody>
    </table>
</template>
<script>
import { ref } from 'vue-demi';
import { useQuasar } from 'quasar';
import { useRoute } from 'vue-router';
import { api } from '@/boot/axios';
import { inject } from 'vue'

export default {
    props: {
        good: {
            type: Object,
            default: () => { },
        }
    },
    setup(props, { emit }) {
        const $q = useQuasar()
        const route = useRoute();
        const url = '/production/production_checklist';

        const columns = [
            {
                label: "پشت درپشت",
                field: (row) => {
                    return `<strong>${row.attributes_label.poshtdarposhtSize?.width ?? row.attributes.doorWidth}</strong><span style="margin: 5px;color: red;">x</span><strong>${row.attributes_label.poshtdarposhtSize?.height ?? row.attributes.doorHeight}</strong>`;
                },
                style: 'width: 100px;padding:10px',
            },
            {
                label: "توضیحات",
                field: (row) => {
                    console.log(row.description)
                    return (row.attributes.doorLengeh && row.attributes.doorLengeh != 1
                        ? row.attributes_label?.doorLengeh
                        : "") + (row.description ?? '');

                },
            },
            {
                label: "قابلبه",
                field: (row) => {
                    return row.attributes.alignEdgeOfDoor == "threeSide"
                        ? "دارد"
                        : row.attributes_label?.alignEdgeOfDoor ?? "";
                },
                style: 'width: 70px;padding:10px',
            },
            {
                label: "اندازه",
                field: (row) => {
                    return `<strong>${row.attributes.doorWidth}</strong><span style="margin: 5px;color: red;">x</span><strong>${row.attributes.doorHeight}</strong>`;
                },
                style: 'width: 100px;padding:10px',
            },
            {
                label: "نمایندگی / مشتری",
                field: (row) => {
                    return `<strong>${row.party_name} / ${row.customer_name}</strong>`;
                },
                style: 'width: 200px;padding:10px',
            },
            {
                label: "شماره لیبل",
                field: (row) => {
                    return `<strong>${row.production_order_item_id}</strong>`;
                },
                style: 'width: 60px;padding:10px',
            },
            {
                label: "شماره سفارش",
                field: (row) => {
                    return `<strong>${row.production_order_code}</strong>`;
                },
                style: 'width: 100px;padding:10px',
            },
            {
                name: "action",
                style: 'width: 70px;padding:10px',
            },
        ];
        const selected = ref({})
        const selectAll = ref({})
        const onToggleSelectAll = (value, row) => {
            row.items.map(m => { selected.value[m.id] = value })
        }
        const { getData } = inject('afterSave')

        return {
            columns,
            selected,
            selectAll,
            onToggleSelectAll,
            save() {
                $q.dialog({
                    title: 'ذخیره',
                    message: 'آیا مطمئن هستید ذخیره شود؟',
                    cancel: true,
                    persistent: true
                }).onOk(() => {
                    api.put(url, {
                        data: selected.value
                    }).then(() => {
                        getData()
                    })
                })
            },
        }
    },
}
</script>