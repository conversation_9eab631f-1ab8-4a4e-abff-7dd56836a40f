<?php

namespace Modules\BuyAndSell\Entities;

use Modules\Good\Entities\Good;

class OrderItem extends BModel
{
    protected $fillable = [
        'code',
        'order_id',
        'good_id',
        'attributes',
        'count',
        'price',
        'total_price',
        'price_details',
        'description',
    ];
    protected $casts = [
        'attributes' => 'object',
        'price_details' => 'array',
    ];


    public function good()
    {
       return $this->belongsTo(Good::class);
    }
 
    public function order()
    {
       return $this->belongsTo(Order::class);
    }
 
    public function getLabelAttributes($attributes)
    {
       $res = [];
       foreach ($this->getAttribute('attributes') as $atttribute_id => $order_attribute) {
          foreach ($attributes as  $attribute) {
             if ($atttribute_id == $attribute['key']) {
                $item_id = null;
                $item_key = null;
                $value = null;
                switch ($attribute['type']) {
                   case 'SELECT':
                   case 'SELECT_IMAGE':
                      $item_id = $order_attribute;
                      foreach ($attribute['items'] as $item) {
                         if ($item['key'] == $order_attribute) {
                            $value = $item['name'];
                            $item_key =$item['key'];
 
                            break;
                         }
                      }
                      break;
                   case 'SWITCH':
                      $value = $order_attribute ? 'دارد' : '';
                      break;
                   case 'NUMBER':
                      $value = $order_attribute * 1;
                      break;
                   case 'FILE':
                   case 'INPUT':
                      $value = $order_attribute;
                }
 
                $res[$attribute['key']] = [
                   'attribute_id' => $attribute['id'],
                   'attribute_name' => $attribute['name'],
                   'attribute_key' => $attribute['key'],
                   'attribute_type' => $attribute['type'],
                   'item_id' => $item_id,
                   'item_key' => $item_key,
                   'value' => $order_attribute,
                   'label' => $value,
                ];
                break;
             }
          }
       }
       return $res;
 
    }
}
