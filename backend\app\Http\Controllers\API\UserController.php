<?php

namespace App\Http\Controllers\API;

use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Modules\Production\Entities\Party;
use Spatie\Permission\Models\Permission;

class UserController extends BaseController implements HasMiddleware
{
    public static function middleware(): array
    {
        return [
            new Middleware('permission:users', only: ['index']),
            new Middleware('permission:users.create', only: ['create','store']),
            new Middleware('permission:users.edit', only: ['show','update']),
            new Middleware('permission:users.delete', only: ['delete','destroy']),
            //new Middleware('subscribed', except: ['store']),
        ];
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

        // if (!request()->user()->hasPermissionTo('manage_users','api')) {
        //     return $this->handleError('You do not have permission to edit this post.', '', 403);
        // }

        return JsonResource::collection(User::query()->filter()->jpaginate())->additional([
            // 'formOption' => [
            //     'permissions' => Permission::get(['id', 'label']),
            //     'roles' => Role::with('permissions')->get(['id', 'label']),
            // ],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'full_name' => 'required',
            'username' => 'required',
            'password' => 'required',
            'confirm_password' => 'required|same:password',
        ]);
        $input = $request->all();
        $input['password'] = bcrypt($input['password']);
        $model = User::create($input);
        if ($request->has('permissions')) {
            $model->permissions()->sync($request->permissions);
        }

        if ($request->has('roles')) {
            $model->roles()->sync($request->roles);
        }

        $model->permissions = $model->permissions()->get(['id'])->pluck('id');
        $model->roles = $model->roles()->get(['id'])->pluck('id');

        return $this->handleResponse($model, trans('request.done'));
    }

    public function create()
    {
        return $this->handleResponse([
            'formOptions' => [
                'permissions' => Permission::get(['id', 'label','name']),
                'roles' => Role::with('permissions')->get(['id', 'label']),
            ],
        ]);
    }



    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(User $user)
    {
        $user->permissions = $user->permissions()->get(['id'])->pluck('id');
        $user->roles = $user->roles()->get(['id'])->pluck('id');
        return $this->handleResponse([
            'form' => $user,
            'formOptions' => [
                'permissions' => Permission::get(['id', 'label','name']),
                'roles' => Role::with('permissions')->get(['id', 'label']),
            ],
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'full_name' => 'required',
            'username' => 'required',
            'confirm_password' => 'required_with:password|same:password',
        ]);

        $input = $request->all();
        if ($request->has('password')) {
            $input['password'] = bcrypt($input['password']);
        }

        $user->update($input);
        $user->permissions()->sync($request->permissions);
        $user->roles()->sync($request->roles);
        // $user->stations()->sync($request->stations);
        $user->permissions = $user->permissions()->get(['id'])->pluck('id');
        $user->roles = $user->roles()->get(['id'])->pluck('id');
        // $user->stations = $user->stations()->get(['id'])->pluck('id');

        return $this->handleResponse($user, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(User $user)
    {
        $user->delete();
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $ids
     * @return \Illuminate\Http\Response
     */
    public function delete(Request $request)
    {
        // اعتبارسنجی ورودی برای اطمینان از اینکه ورودی آرایه‌ای از شناسه‌هاست
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => ['integer', Rule::exists(User::class, 'id')->where('is_super_admin', 0)],
        ], [
            'ids.*.exists' => 'کاربری با شناسه :input یافت نشد یا امکان حذف ندارد.',
        ]);

        // دریافت شناسه‌ها از ورودی
        $ids = $validated['ids'];

        // حذف مدل‌ها با شناسه‌های مشخص شده
        try {
            User::destroy($ids);
        } catch (\Exception $e) {
            // در صورت بروز خطا، می‌توانید یک پاسخ مناسب ارسال کنید
            return $this->handleError($e, 'امکان حذف وجود ندارد', 500);
        }

        // ارسال پاسخ موفقیت‌آمیز
        return $this->handleResponse(null, trans('request.done')); // response()->json(['message' => 'Records deleted successfully']);
    }

    /**
     * Search the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function search()
    {
        //
    }

    public function show_current(Request $request)
    {

        $user = $request->user();
        $roles = $user->roles()->with('permissions')->get();
        $user->roles = $roles->pluck('name');
        // $user->is_membership = $user->status == Party::CONFIRM;
        // $user->is_customer = $user::class == Party::class;
        $user->role = $user->is_super_admin ? 'super_admin' : ($user::class == Party::class ? 'party' : 'user');
        $user->role_name = $user->is_super_admin ? 'ادمین' : ($user::class == Party::class ? 'نماینده' : 'کاربر');
        $user->permissions = array_merge($roles->pluck('permissions')->flatten()->pluck('name')->toArray(), $user->permissions()->get()->pluck('name')->toArray());
        return $user;
    }

    public function update_current(Request $request)
    {
        $request->validate([
            'confirm_password' => 'required_with:password|same:password',
        ]);
        $user = $request->user();
        $user->update($request->all());

        if ($request->has('password')) {
            $user->password = Hash::make($request->input('password'));
            $user->save();
        }
        return response()->json(['message' => 'با موفقیت انجام شد!']);
    }
}
