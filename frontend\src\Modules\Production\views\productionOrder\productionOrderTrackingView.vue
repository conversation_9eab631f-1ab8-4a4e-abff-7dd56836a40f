<template>
    <j-table :rows="data" :columns="columns" :grid="false" hide-bottom separator="cell" :rows-per-page-options="[0]">

        <template v-slot:body="props">

            <q-tr :props="props">
                <q-td v-for="col in props.cols" :key="col.name" :props="props">
                    <template v-if="col.name == 'expand'">
                        <q-btn size="sm" flat dense @click="props.expand = !props.expand"
                            :icon="props.expand ? 'remove' : 'add'" />
                        <!-- <j-btn dense icon="task_alt" color="primary" @click="showChecklist(props.row)" /> -->

                    </template>
                    <template v-else>
                        {{ col.value }}
                    </template>
                </q-td>
            </q-tr>
            <q-tr v-show="props.expand" :props="props">
                <q-td colspan="5" style="padding:0">
                    <!-- <Checklist v-model:data="props.row.items"
                          :attributes="attribute_station.length > 0 ? attribute_station : attributes" :url="url"
                          @afterSave="afterSave" style="width:100%" /> -->
                    <div class="bg-gray-400 p-1 grid gap-1">
                        <j-table :rows="props.row.checklists" :columns="rowColumns" dense flat hide-bottom
                            class="overflow-x-auto" separator="cell" :rows-per-page-options="[0]" :grid="false"
                            :row-key="row => row[0].code">


                            <template v-slot:body="props">

                                <q-tr :props="props">

                                    <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                        <template v-if="col.name == 'expand'">
                                            <q-btn size="sm" flat dense @click="props.expand = !props.expand"
                                                :icon="props.expand ? 'remove' : 'manage_search'" />
                                        </template>
                                        <template v-else-if="col.name == 'last_processing_station'">

                                            <q-chip v-for="station_work, index in col.value" :key="index" square flat
                                                text-color="white" color="primary" dense>{{
                                                    station_work
                                                }}</q-chip>
                                        </template>
                                        <template v-else>
                                            {{ col.value }}
                                        </template>
                                    </q-td>
                                </q-tr>
                                <q-tr v-show="props.expand" :props="props">
                                    <q-td colspan="5" style="padding:0">
                                        <div class="bg-gray-600 p-1 grid gap-1 overflow-x-auto">

                                            <j-table :rows="props.row" :columns="checkListColumns" dense flat
                                                :grid="false" hide-bottom separator="cell" :rows-per-page-options="[0]">


                                                <template v-slot:body="props">
                                                    <q-tr :props="props"
                                                        :class="{ 'bg-green-3': props.row.status == 'DONE' }">
                                                        <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                                            <template v-if="col.name == 'status'">
                                                                <j-icon :name="col.value" size="1.5em" />
                                                            </template>
                                                            <template v-else-if="col.name == 'action'">
                                                                <j-btn :icon="col.value" dense
                                                                    :color="props.row.status == 'DONE' ? 'red' : 'primary'"
                                                                    @click="changeStatus(props.row.id, props.row.status == 'DONE' ? 'PROCESSING' : 'DONE')" />
                                                            </template>
                                                            <template v-else>
                                                                {{ col.value }}
                                                            </template>
                                                        </q-td>
                                                    </q-tr></template>


                                            </j-table>
                                        </div>

                                    </q-td>
                                </q-tr>
                            </template>
                        </j-table>


                    </div>

                </q-td>
            </q-tr>
        </template>
    </j-table>
</template>
<script setup>

import { usePublicStore } from "@/stores";
import { api } from "@/boot/axios";
import { useRoute } from 'vue-router';
import { ref } from 'vue';
import { useQuasar } from "quasar";
import { checkPermission } from "@/helpers";

const route = useRoute();
const url = '/production/production_order/' + route.params.id + '/tracking';

const data = ref([])
const publicStore = usePublicStore();
const $q = useQuasar();

const changeStatus = (id, status) => {
    $q.dialog({
        title: 'ذخیره',
        message: 'آیا مطمئن هستید ذخیره شود؟',
        cancel: true,
        persistent: true
    }).onOk(() => {
        api.put('/production/production_checklist', {
            data: { [id]: status }
        }).then(() => {
            getData()
        })
    })
}

const getData = () => {
    api.get(url).then(res => {
        data.value = res.track;
        publicStore.titleWindow = 'رهگیری تولید سفارش ' + res.result.code
    })
}
getData()

const columns = [
    {
        name: 'expand',
        required: true,
        style: 'width: 30px',

    },
    {
        name: 'code',
        label: '#',
        field: 'code',
        style: 'width: 50px',
    },
    {
        name: 'id',
        label: 'سریال',
        field: 'id',
        style: 'width: 50px',
    }, {
        name: 'good',
        label: 'محصول',
        field: row => row.good.name,
    },
]
const rowColumns = [
    {
        name: 'expand',
        required: true,
        style: 'width: 30px',

    },
    {
        name: 'code',
        label: '#',
        field: row => row[0].code,
        style: 'width: 50px',
    },
    {
        name: 'last_processing_station',
        label: 'آخرین ایستگاه ها باز',
        field: row => row.filter(f => f.status == 'PROCESSING').map(m => m.station_work_name),
    },
]
let checkListColumns = [

    {
        label: 'کار',
        name: 'station_work_name',
        field: 'station_work_name',
    },
    {
        label: 'وضعیت',
        name: 'status',
        field: row => {
            switch (row.status) {
                case 'PROCESSING':
                    return 'pending_actions';
                case 'DONE':
                    return 'task_alt';
                case 'REPAIRING':
                    return 'build';
            }
        },
    },
    {
        label: 'تاریخ ایجاد',
        name: 'created_at',
        field: 'created_at',
    },
    {
        label: 'تاریخ ویرایش',
        name: 'updated_at',
        field: 'updated_at',
    },


]
if (checkPermission('changeStatusOnTracking')) {
    checkListColumns.push(
        {
            label: 'عملیات',
            name: 'action',
            field: row => {
                switch (row.status) {
                    case 'PROCESSING':
                        return 'pending_actions';
                    case 'DONE':
                        return 'task_alt';
                    case 'REPAIRING':
                        return 'build';
                }
            },
        },
    )
}
</script>
