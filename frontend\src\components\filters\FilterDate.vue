<template>
    <j-btn v-if="date.date_of" icon="cancel" dense flat size="xs" color="red" @click="date.date_of = null" />
    <j-btn icon="event" dense flat size="xs" :color="date.date_of ? 'red' : ''" :title="date.date_of">
        <!-- <q-popup-proxy ref="date_of" cover transition-show="scale" transition-hide="scale"> -->
        <q-menu ref="date_of" anchor="bottom end" self="top end">

            <j-date v-model="date.date_of" />
        </q-menu>
        <!-- </q-popup-proxy> -->
    </j-btn>
    <slot />
    <j-btn icon="event" dense flat size="xs" :color="date.date_to ? 'red' : ''" :title="date.date_to">

        <!-- <q-popup-proxy ref="date_to" cover transition-show="scale" transition-hide="scale"> -->
        <q-menu ref="date_to" anchor="bottom end" self="top end">

            <j-date v-model="date.date_to" />
        </q-menu>
        <!-- </q-popup-proxy> -->
    </j-btn>
    <j-btn v-if="date.date_to" icon="cancel" dense flat size="xs" color="red" @click="date.date_to = null" />

</template>
<script>
import { ref, watch } from 'vue'

export default {

    props: {
        value: {
            type: Array,
            default: () => { }
        },
    },
    setup(props, context) {
        const date_of = ref(null)
        const date_to = ref(null)
        const date = ref(props.value ?? {})
        watch(() => props.value, (newValue) => {
            date.value = newValue
        })
        watch(() => date.value.date_of + date.value.date_to, (newValue) => {
            // console.log(333333)
            date_of.value.hide()
            date_to.value.hide()
            context.emit('input', date.value)
            context.emit('update:input', date.value)
            context.emit('update:value', date.value)
        })
        return {
            date,
            date_of,
            date_to,
        }
    }
}
</script>