import { useAuthStore } from '@/stores/auth.store';
import { nextTick } from 'vue';

export default {
  beforeMount (el, binding) {
    
    nextTick(() => {
      const authStore = useAuthStore();
      const requiredPermission = binding.value;
      if (!authStore.hasPermission(requiredPermission)) {
        // حذف المان از DOM
        if (el.parentNode) {
          el.parentNode.removeChild(el);
        }
      }
    })
  }
};