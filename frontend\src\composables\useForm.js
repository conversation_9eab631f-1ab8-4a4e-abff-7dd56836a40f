import { ref, reactive, computed, watch } from 'vue';
import { useRequestStore } from '@/stores';
import { VALIDATION_MESSAGES } from '@/constants';

/**
 * Composable for form handling with validation
 */
export function useForm(initialData = {}, validationRules = {}) {
    const requestStore = useRequestStore();
    
    // Form data
    const formData = reactive({ ...initialData });
    const originalData = ref({ ...initialData });
    
    // Form state
    const isSubmitting = ref(false);
    const isDirty = ref(false);
    const errors = ref({});
    
    // Watch for changes to mark form as dirty
    watch(
        () => formData,
        () => {
            isDirty.value = !Object.diff(formData, originalData.value);
        },
        { deep: true }
    );
    
    // Computed properties
    const isValid = computed(() => {
        return Object.keys(errors.value).length === 0;
    });
    
    const hasErrors = computed(() => {
        return Object.keys(errors.value).length > 0;
    });
    
    /**
     * Validate a single field
     */
    const validateField = (fieldName, value) => {
        const rules = validationRules[fieldName];
        if (!rules) return true;
        
        for (const rule of rules) {
            const result = rule(value, formData);
            if (result !== true) {
                setFieldError(fieldName, result);
                return false;
            }
        }
        
        clearFieldError(fieldName);
        return true;
    };
    
    /**
     * Validate all fields
     */
    const validateForm = () => {
        let isFormValid = true;
        errors.value = {};
        
        for (const [fieldName, value] of Object.entries(formData)) {
            if (!validateField(fieldName, value)) {
                isFormValid = false;
            }
        }
        
        return isFormValid;
    };
    
    /**
     * Set error for a specific field
     */
    const setFieldError = (fieldName, message) => {
        errors.value[fieldName] = message;
    };
    
    /**
     * Clear error for a specific field
     */
    const clearFieldError = (fieldName) => {
        delete errors.value[fieldName];
    };
    
    /**
     * Clear all errors
     */
    const clearErrors = () => {
        errors.value = {};
        requestStore.errors = {};
    };
    
    /**
     * Reset form to initial state
     */
    const resetForm = () => {
        Object.assign(formData, originalData.value);
        clearErrors();
        isDirty.value = false;
    };
    
    /**
     * Update form data
     */
    const updateFormData = (newData) => {
        Object.assign(formData, newData);
    };
    
    /**
     * Set original data (useful after successful save)
     */
    const setOriginalData = (data) => {
        originalData.value = { ...data };
        isDirty.value = false;
    };
    
    /**
     * Handle form submission
     */
    const handleSubmit = async (submitFn, options = {}) => {
        const { validate = true, resetOnSuccess = false } = options;
        
        if (validate && !validateForm()) {
            return false;
        }
        
        try {
            isSubmitting.value = true;
            const result = await submitFn(formData);
            
            if (resetOnSuccess) {
                resetForm();
            } else {
                setOriginalData(formData);
            }
            
            return result;
        } catch (error) {
            // Handle validation errors from server
            if (error.response?.data?.errors) {
                requestStore.errors = error.response.data.errors;
            }
            throw error;
        } finally {
            isSubmitting.value = false;
        }
    };
    
    return {
        formData,
        originalData,
        isSubmitting,
        isDirty,
        errors,
        isValid,
        hasErrors,
        validateField,
        validateForm,
        setFieldError,
        clearFieldError,
        clearErrors,
        resetForm,
        updateFormData,
        setOriginalData,
        handleSubmit
    };
}

/**
 * Common validation rules
 */
export const validationRules = {
    required: (message = VALIDATION_MESSAGES.REQUIRED) => (value) => {
        return !!value || message;
    },
    
    email: (message = VALIDATION_MESSAGES.EMAIL) => (value) => {
        if (!value) return true;
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailPattern.test(value) || message;
    },
    
    minLength: (min, message = VALIDATION_MESSAGES.MIN_LENGTH) => (value) => {
        if (!value) return true;
        return value.length >= min || message.replace('{min}', min);
    },
    
    maxLength: (max, message = VALIDATION_MESSAGES.MAX_LENGTH) => (value) => {
        if (!value) return true;
        return value.length <= max || message.replace('{max}', max);
    },
    
    numeric: (message = VALIDATION_MESSAGES.NUMERIC) => (value) => {
        if (!value) return true;
        return !isNaN(value) || message;
    },
    
    phone: (message = VALIDATION_MESSAGES.PHONE) => (value) => {
        if (!value) return true;
        const phonePattern = /^(\+98|0)?9\d{9}$/;
        return phonePattern.test(value) || message;
    },
    
    confirmed: (confirmField, message = VALIDATION_MESSAGES.PASSWORD_MISMATCH) => (value, formData) => {
        return value === formData[confirmField] || message;
    }
};
