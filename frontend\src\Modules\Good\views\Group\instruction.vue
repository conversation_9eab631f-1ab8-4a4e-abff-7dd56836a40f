<template>
    <j-form @submit="submit" class="w-full p-2 border-2 rounded-md bg-gray-100">
        <div class="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-4 gap-3">
            <j-select v-model="form.instruction_id" dense hide-bottom-space label="دستورالعمل" :options="instruction"
                option-label="name" option-value="id" :outlined="false" required />
            <j-select v-if="form.instruction_id" v-model="form.attribute_id" dense hide-bottom-space label="ویژگی"
                :outlined="false" :options="attribute_items.filter(f => attributes[f.id] && f.type == 'SELECT')"
                option-label="name" option-value="id" @update:model-value="form.attribute_item_id = null" />
            <j-select v-if="form.attribute_id" v-model="form.attribute_item_id" dense hide-bottom-space label="مقدار"
                :outlined="false"
                :options="attribute_items[attribute_items.findIndex(f => f.id == form.attribute_id)].items"
                option-label="name" option-value="id" />

        </div>
        <j-btn size="md" :color="'primary'" type="submit" class="full-width text-white mt-4" :label="'افزودن'" />
    </j-form>
    <div class="flex q-table__card mt-5">
        <div class="flex flex-col border-r-gray-200 border-r-2 w-10">
            <template v-if="selected && selected.length > 0">
                <j-btn flat dense icon="delete" color="red" @click="onDelete()" />
            </template>
        </div>
        <j-table v-model:selected="selected" flat :columns="columns" :rows="data" separator="cell"
            :row-key="row => row.good_id + '|' + row.attribute_id + '|' + row.attribute_item_id" selection="single"
            :rows-per-page-options="[0]" dense class="flex-auto w-12">
        </j-table>
    </div>
</template>

<script>
import { api } from '@/boot/axios';
import { useQuasar } from 'quasar';
import { ref, watch } from 'vue';

export default {
    props: {
        data: Array,
        attributes: Object,
        group_id: Number,
    },
    setup(props, { emit }) {
        const selected = ref([])
        const $q = useQuasar();

        // const selected_attributes = ref({})
        const attribute_items = ref([])
        const instruction = ref([])
        api.get('good/attribute').then(res => {
            attribute_items.value = res.data
        })
        api.get('/production/instruction/search').then(res => {
            instruction.value = res.result
        })

        const data = ref(props.data ?? [])
        watch(() => props.data, (newVal) => {
            data.value = newVal
        })


        const good = ref({})
        const form = ref({})

        const onDelete = () => {
            $q.dialog({
                title: "مطمئن هستید؟",
                cancel: true,
                persistent: true,
            }).onOk(() => {
                const value = selected.value[0];
                const find = data.value.findIndex(f => f.good_id == value.good_id && f.attribute_id == value.attribute_id && f.attribute_item_id == value.attribute_item_id)

                if (find >= 0)
                    data.value.splice(find, 1)
                selected.value = []
            });
        }


        const reset = () => {
            form.value = {}
            good.value = {}

        }

        const columns = [
            {
                name: "instruction",
                label: "دستورالعمل",
                field: row => instruction.value.findIndex(f => f.id == row.instruction_id) == -1 ? '' : instruction.value[instruction.value.findIndex(f => f.id == row.instruction_id)].name,
            },
            {
                name: "attribute",
                label: "ویژگی",
                field: row => attribute_items.value.findIndex(f => f.id == row.attribute_id) == -1 ? '' : attribute_items.value[attribute_items.value.findIndex(f => f.id == row.attribute_id)].name,
            },
            {
                name: "attribute_item",
                label: "مقدار",
                field: row => {
                    const find = attribute_items.value.findIndex(f => f.id == row.attribute_id);
                    if (find >= 0) {
                        const find2 = attribute_items.value[find].items.findIndex(f => f.id == row.attribute_item_id);
                        if (find2 >= 0) {
                            return attribute_items.value[find].items[find2].name
                        }
                    }
                    return ''
                },
            },


        ];


        return {
            submit: () => {
                const find = data.value.findIndex(f => f.attribute_id == form.value.attribute_id && f.attribute_item_id == form.value.attribute_item_id)
                if (find >= 0)
                    Object.assign(data.value[find], form.value)
                else
                    data.value.push(form.value)
                emit('update:data', data.value)
                reset()
            },
            instruction,
            columns,
            data,
            good,
            form,
            onDelete,
            attribute_items,
            selected,
        }
    },
}
</script>