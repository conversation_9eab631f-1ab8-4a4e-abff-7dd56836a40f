<?php
namespace Modules\Good\Http\Controllers;

use App\Http\Controllers\API\BaseController;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Response;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Validation\Rule;
use Modules\Good\Entities\Attribute;
use Modules\Good\Entities\AttributeCondition;
use Modules\Good\Entities\Good;
use Modules\Good\Http\Requests\GoodRequest;
use stdClass;

class GoodController extends BaseController implements HasMiddleware
{
    protected $model = Good::class;

    public static function middleware(): array
    {
        return [
            new Middleware('permission:products', only: ['index']),
            new Middleware('permission:products.create', only: ['create', 'store']),
            new Middleware('permission:products.edit', only: ['show', 'update']),
            new Middleware('permission:products.delete', only: ['delete', 'destroy']),
            //new Middleware('subscribed', except: ['store']),
        ];
    }

    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return JsonResource::collection($this->model::query()->filter()->jpaginate());
        // return $this->repository->getAll();
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(GoodRequest $request)
    {
        $data = $this->model::create($request->all());

        $metas = request('metas') ?? [];
        $data->metas()->sync(collect($metas)->filter(function ($f) {
            return $f['key'] && $f['value'];
        })->toArray());

        $attributes       = $data->attributes()->get();
        $data->attributes = count($attributes) > 0 ? $attributes->groupBy('id') : new stdClass();

        return $this->handleResponse($data, trans('request.done'));
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(Good $good)
    {
        $good->metas;
        $attributes       = $good->attributes()->get();
        $good->attributes = count($attributes) > 0 ? $attributes->groupBy('id')->map(function ($m) {
            return [
                "options" => $m[0]->pivot->options,
                "sort"    => $m[0]->pivot->sort,
            ];
        }) : new stdClass();
        return $this->handleResponse([
            'form'        => $good,
            'formOptions' => [
                'types'      => Good::types,
                'attributes' => Attribute::query()->with('items')->get(),
            ],
        ]);
    }

    public function create()
    {
        return $this->handleResponse([
            'formOptions' => [
                'types' => Good::types,
            ],
        ]);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(GoodRequest $request, Good $good)
    {
        $params = $request->all();
        $good->update($params);

        $metas = request('metas') ?? [];
        $good->metas()->sync(collect($metas)->filter(function ($f) {
            return $f['key'] && $f['value'];
        })->toArray());

        $good->metas;

        $attributes = request('attributes') ?? [];
        $good->attributes()->sync($attributes);

        $attributes       = $good->attributes()->get();
        $good->attributes = count($attributes) > 0 ? $attributes->groupBy('id')->map(function ($m) {
            return [
                "options" => $m[0]->pivot->options,
                // "showing" => $m[0]->pivot->showing,
                // "required" => $m[0]->pivot->required,
                "sort"    => $m[0]->pivot->sort,
            ];
        }) : new stdClass();

        return $this->handleResponse($good, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy(Good $good)
    {
        return $this->handleResponse($good->delete(), trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $ids
     * @return \Illuminate\Http\Response
     */
    public function delete(Request $request)
    {
        // اعتبارسنجی ورودی برای اطمینان از اینکه ورودی آرایه‌ای از شناسه‌هاست
        $validated = $request->validate([
            'ids'   => 'required|array',
            'ids.*' => ['integer', Rule::exists(Good::class, 'id')],
        ], [
            'ids.*.exists' => 'نماینده با شناسه :input یافت نشد یا امکان حذف ندارد.',
        ]);

        // دریافت شناسه‌ها از ورودی
        $ids = $validated['ids'];

        // حذف مدل‌ها با شناسه‌های مشخص شده
        try {
            Good::destroy($ids);
        } catch (\Exception $e) {
            // در صورت بروز خطا، می‌توانید یک پاسخ مناسب ارسال کنید
            return $this->handleError($e, 'امکان حذف وجود ندارد', 500);
        }

                                                                   // ارسال پاسخ موفقیت‌آمیز
        return $this->handleResponse(null, trans('request.done')); // response()->json(['message' => 'Records deleted successfully']);
    }

    public function search()
    {

        $data = Good::query()->where('is_active', true)->when(auth()->guard('crm')->check(), function ($q) {
            $q->where('is_active_customer', true);
        })->get();
        return $this->handleResponse($data);

    }

    public function selectForOrder(Good $good)
    {
        if (! $good->is_active || (auth()->guard('crm')->check() && ! $good->is_active_customer)) {
            return $this->handleError('محصول وجود ندارد', null, 404);
        }

        $good_attributes = collect($good->attributes()
                ->with([
                    //'conditions', 'items.conditions',
                    'items.metas',
                    //'parent.items.conditions',
                    'parent.items.metas'])
                ->get()->toArray())
            ->map(function ($m) {
                if (count($m['pivot']['options']) > 0) {
                    $m['items'] = collect($m['items'])->filter(function ($f) use ($m) {
                        return in_array($f['id'], $m['pivot']['options']);
                    })->values();
                }
                if ($m['pivot']['sort'] !== null) {
                    $m['sort'] = $m['pivot']['sort'];
                }

                return $m;
            })->sortBy('sort')->values();

        $good->attributes  = $good_attributes;
        $default_attribute = $good->default_attribute;

        $good['default_attribute'] = $default_attribute;
        $good->metas               = $good->metas()->where('key', 'price')->get();
        return $this->handleResponse($good, null, [
            'conditions' => AttributeCondition::all(),
        ]);
    }

    // /**
    //  * Display a listing of the resource.
    //  * @return Response
    //  */
    // public function index()
    // {
    //     return $this->repository->getAll([
    //         'formOption' => [
    //             'types' => Good::types,
    //         ],
    //     ], [
    //         'group',
    //     ]);
    //     // $query = Good::query();
    //     // return JsonResource::collection($query->paginate())->additional([
    //     //     'formOption' => [
    //     //         'types' => Good::types,
    //     //     ],
    //     // ]);
    // }

    // /**
    //  * Store a newly created resource in storage.
    //  * @param Request $request
    //  * @return Response
    //  */
    // public function store(GoodRequest $request)
    // {
    //     // $instructions = request('instructions') ?? [];
    //     $model = Good::create(request()->all());

    //     $metas = request('metas') ?? [];
    //     $model->metas()->sync(collect($metas)->filter(function ($f) {
    //         return $f['key'] && $f['value'];
    //     })->toArray());

    //     // $model->instructions()->sync($instructions);
    //     // $model->instructions;
    //     return $this->handleResponse($model, trans('request.done'));
    // }

    // /**
    //  * Show the specified resource.
    //  * @param int $id
    //  * @return Response
    //  */
    // public function show(Good $good)
    // {
    //     //$good->attributes; // = $good->attributes()->get();
    //     //$good->instructions = $good->goodInstructions()->get(); //->pluck('pivot');
    //     $attributes = $good->attributes()->get();
    //     $good->metas;
    //     $good->attributes = count($attributes) > 0 ? $attributes->groupBy('id')->map(function ($m) {
    //         return [
    //             "options" => $m[0]->pivot->options,
    //             //"showing" => $m[0]->pivot->showing,
    //             //"required" => $m[0]->pivot->required,
    //             //"sort" => $m[0]->pivot->sort,
    //         ];
    //     }) : new stdClass();

    //     //$group_attributes = $good->group()->with('attributes.items')->get()->pluck('attributes')->first();
    //     $group_attributes = $good->group?->attributes()->with(['items', 'parent.items'])->get();

    //      $visible_attributes = !$group_attributes ? [] : $group_attributes->map(function ($m) {
    //         $items = collect($m['items'])->merge($m->parent?->items);
    //         if (isset($m['pivot']['options']) && count($m['pivot']['options']) > 0) {

    //             $items = $items->filter(function ($f) use ($m) {
    //                 return in_array($f['id'], $m['pivot']['options']);
    //             })->values();

    //             // dd(
    //             //     $m['items']->toArray(),
    //             //     $m['pivot']['options'],
    //             //     $m['items']->toArray(),
    //             //     $m->parent?->items->toArray(),
    //             //     $items->toArray()
    //             // );
    //         }

    //         return array_merge($m->toArray(), [
    //             "items" => $items->toArray(),
    //         ]);
    //     });

    //     // $good->instructions;
    //     //$good->instructions = $good->goodInstructions()->get(); //->pluck('pivot');

    //     return $this->handleResponse($good, null, [
    //         'additional' => [
    //             'attributes' => $visible_attributes,
    //             //'instructions' =>  Instruction::get(),
    //         ],
    //     ]);

    //     $good->metas;

    //     $group_attributes = $good->group->attributes()->with(['items', 'parent.items'])->get();

    //     $good_attributes = $good->attributes()->with(['items', 'parent.items'])->get();
    //     $visible_attributes = $group_attributes->filter(function ($m) use ($good_attributes) {
    //         return $good_attributes->where('id', $m->id)->count() == 0;
    //     })->merge($good_attributes->filter(function ($m) use ($group_attributes) {
    //         return $group_attributes->where('id', $m->id)->count() == 0 || ($m->pivot->enable && $group_attributes->where('id', $m->id)->count() > 0);
    //     }))->map(function ($m) {
    //         $items = collect($m['items'])->merge($m->parent?->items);
    //         if (isset($m['pivot']['options']) && count($m['pivot']['options']) > 0) {
    //             $items = $m['items']->filter(function ($f) use ($m) {
    //                 return in_array($f['id'], $m['pivot']['options']);
    //             })->values();
    //         }

    //         return array_merge($m->toArray(), [
    //             "items" => collect($m['pivot']['options'])->filter(function ($f) {
    //                 return is_array($f);
    //             })->merge($items),
    //         ]);
    //     });

    //     return $this->handleResponse($good, null, [
    //         'additional' => [
    //             'attributes' => $visible_attributes,
    //         ],
    //     ]);
    // }

    // /**
    //  * Update the specified resource in storage.
    //  * @param Request $request
    //  * @param int $id
    //  * @return Response
    //  */
    // public function update(GoodRequest $request, Good $good)
    // {
    //     $params = $request->all();
    //     $group_attributes = $good->group?->attributes()->pluck('key')->toArray();
    //     $params['default_attribute'] = collect($params['default_attribute'])->filter(function ($m, $k) use ($group_attributes) {
    //         return $m != null && in_array($k, $group_attributes);
    //     })->toArray();
    //     $good->update($params);

    //     $attributes = request('attributes') ?? [];
    //     $metas = request('metas') ?? [];
    //     //$instructions = request('instructions') ?? [];
    //     $good->attributes()->sync($attributes);
    //     $good->metas()->sync(collect($metas)->filter(function ($f) {
    //         return $f['key'] && $f['value'];
    //     })->toArray());
    //     //$good->instructions()->sync($instructions);
    //     //$good->instructions;
    //     $attributes = $good->attributes()->get();
    //     $good->attributes = count($attributes) > 0 ? $attributes->groupBy('id')->map(function ($m) {
    //         return [
    //             "options" => $m[0]->pivot->options,
    //             // "showing" => $m[0]->pivot->showing,
    //             // "required" => $m[0]->pivot->required,
    //             // "sort" => $m[0]->pivot->sort,
    //         ];
    //     }) : new stdClass();

    //     // $good->goodInstructions()->sync(collect($request->input('instructions'))->map(function ($m) use ($good) {
    //     //     $res = [
    //     //         'good_id' => $good['id'],
    //     //         'attribute_id' => $m['attribute_id'] ?? null,
    //     //         'attribute_item_id' => $m['attribute_item_id'] ?? null,
    //     //         'instruction_id' => $m['instruction_id'],
    //     //     ];
    //     //     if (isset($m['id']))
    //     //         $res['id'] = $m['id'];
    //     //     return $res;
    //     // })->toArray());
    //     // $good->instructions = $good->goodInstructions()->get(); //->pluck('pivot');
    //     $good->metas;

    //     return $this->handleResponse($good, trans('request.done'));
    // }

    // /**
    //  * Remove the specified resource from storage.
    //  * @param int $id
    //  * @return Response
    //  */
    // public function destroy(Good $good)
    // {
    //     return $this->repository->delete($good);
    // }

    // public function search()
    // {
    //     return $this->repository->search([
    //         'name' => [
    //             'type' => 'FilterInput',
    //             'value' => request('name'),
    //         ],
    //         'type' => [
    //             'type' => 'FilterInput',
    //             'value' => request('type'),
    //         ],
    //         'is_active' => [
    //             'type' => 'FilterSwitch',
    //             'value' => request('is_active'),
    //         ],
    //     ], 'name');
    // }

}
