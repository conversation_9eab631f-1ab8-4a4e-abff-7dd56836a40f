<template>
    <q-table v-model:selected="selected" :rows="items" :columns="[
        { field: 'name', name: 'name', label: 'نام' },
        { field: 'key', name: 'key', label: 'کد' },
        ...columns.map(m => ({ ...m, field: row => row.data[m.field] })),
        { name: 'action', label: 'عملیات' },
    ]" selection="single" row-key="name" hide-pagination :rows-per-page-options="[0]">
        <template v-slot:top>
            <j-btn flat icon="add" color="blue" @click="onAdd()" label="افزودن" />
            <q-space />
            <template v-if="selected.length > 0">
                <j-btn flat icon="delete" color="red" @click="onDelete()" />
                <!-- <j-btn flat dense icon="edit" color="green" @click="onEdit()" /> -->
            </template>
        </template>
        <template #body-cell-action="props">
            <q-td :props="props">
                <j-toggle v-model="props.row.is_active" label="فعال" />
                <j-toggle v-model="props.row.is_active_customer" label="فعال مشتری" />
                <attribute-item-meta v-if="props.row.id" :item="props.row" />
            </q-td>
        </template>
        <template #body-cell-image="props">
            <q-td :props="props">
                <j-image-viewer :src="props.value" :zoom="false" />
                <q-popup-edit v-model="props.row.data.image" buttons v-slot="scope" @update:model-value="updateData">
                    <j-upload v-model:value="scope.value" auto-upload accept="image/*" url="/api/upload-file"
                        field-name="file" class="col-span-full" flat bordered />
                </q-popup-edit>
            </q-td>
        </template>
        <template #body-cell-name="props">
            <q-td :props="props">
                {{ props.row.name }}
                <q-popup-edit v-model="props.row.name" buttons v-slot="scope" @update:model-value="updateData">
                    <q-input v-model="scope.value" autofocus @keyup.enter="scope.set" />
                </q-popup-edit>
            </q-td>
        </template>
        <template #body-cell-key="props">
            <q-td :props="props">
                {{ props.row.key }}
                <q-popup-edit v-model="props.row.key" buttons v-slot="scope" @update:model-value="updateData">
                    <q-input v-model="scope.value" autofocus @keyup.enter="scope.set" />
                </q-popup-edit>
            </q-td>
        </template>
    </q-table>
    <j-dialog v-model="persistent">
        <q-card class="bg-white p-5">
            <j-input v-model="form.name" label="نام" />
            <j-input v-model="form.key" label="کد" />
            <template v-for="column, index in columns" :key="index">
                <j-input v-model="form.data[column.field]" v-if="column.type == 'INPUT'" :label="column.label" dense />
                <j-upload v-model:value="form.data[column.field]" v-if="column.type == 'FILE'" :label="column.label"
                    auto-upload accept="image/*" url="/api/upload-file" field-name="file" class="col-span-full" flat
                    bordered />
            </template>
            <j-btn label="ثبت" class="bg-primary mt-3 text-white w-full" @click="saveForm()" />
        </q-card>

    </j-dialog>
</template>
<script>
import { useQuasar } from 'quasar'
import { ref } from 'vue'
import AttributeItemMeta from './AttributeItemMeta.vue'
export default {
    components: {
        AttributeItemMeta,
    },
    props: {
        value: {
            type: Array,
            default: () => []
        },
        columns: {
            type: Array,
            default: () => []
        },
    },
    setup(props, { emit }) {

        const persistent = ref(false);
        const $q = useQuasar();
        const selected = ref([])
        const form = ref({ data: {} })
        const items = ref(props.value ?? [])
        const onAdd = () => {
            form.value = { data: {} }
            persistent.value = true
        }

        // const edit = ref(false)

        // const onEdit = () => {
        //     form.value = selected.value[0]
        //     edit.value = true;
        //     persistent.value = true
        // }

        const onDelete = () => {
            $q.dialog({
                title: 'مطمئن هستید؟',
                cancel: true,
                persistent: true
            }).onOk(() => {
                const find = items.value.findIndex(f => f.name == selected.value[0].name)
                items.value.splice(find, 1)
                selected.value = []
            })
        }
        return {
            persistent,
            form,
            items,
            selected,
            onAdd,
            // onEdit,
            onDelete,
            saveForm() {
                items.value.push(form.value)
                persistent.value = false;
                emit('update:value', items.value)
            },
            updateData() {
                persistent.value = false;
                emit('update:value', items.value)

            }
        }
    },
}
</script>