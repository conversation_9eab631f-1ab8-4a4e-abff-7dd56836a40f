export default function (
    {
        frameHeight,
        frameWidth,
        typeMaterial,
        hasThreshold,
        widthParvaz,
        typeRookob,
        wallThickness,
        colorNavardarzgir,
    },
    count = 1
) {
    //console.log(attributes);
    const mokammelWidth = (() => {
        switch (true) {
            case wallThickness <= 28 && wallThickness > 21:
                return 7;
            case wallThickness > 28:
                return wallThickness - 28 + 7;
        }
        return 0;
    })();
    const widthFloor = (() => {
        switch (true) {
            case wallThickness < 13 && wallThickness >= 9:
                return 9;
            case (wallThickness <= 17 && wallThickness >= 13) ||
                (wallThickness <= 24 && wallThickness > 21):
                return 13;
            case (wallThickness <= 21 && wallThickness > 17) ||
                wallThickness > 24:
                return 17;
        }
        return 0;
    })();

    const countPich =
        (() => {
            if (widthFloor == 9) return 2;
            return 4;
        })() *
        (hasThreshold ? 2 : 1) *
        count;
    const res = {
        frame: {
            height: frameHeight,
            width: frameWidth,
            wallThickness,
            widthFloor,
            lengthPasarUnit: (() => {
                const frameWidthProduction = frameWidth + 3;
                switch (true) {
                    case frameWidthProduction <= 94:
                        return 94;
                    case frameWidthProduction <= 205:
                        return 205;
                    case frameWidthProduction <= 215:
                        return 215;
                    case frameWidthProduction <= 220:
                        return 220;
                    case frameWidthProduction <= 225:
                        return 225;
                    case frameWidthProduction <= 244:
                        return 244;
                }
                return 0;
            })(),
            lengthPasarSlitter: (() => {
                const frameWidthProduction = frameWidth + 3;
                switch (true) {
                    case frameWidthProduction <= 94:
                        return 94;
                    case frameWidthProduction <= 122:
                        return 122;
                    case frameWidthProduction <= 205:
                        return 205;
                    case frameWidthProduction <= 215:
                        return 215;
                    case frameWidthProduction <= 220:
                        return 220;
                    case frameWidthProduction <= 225:
                        return 225;
                    case frameWidthProduction <= 244:
                        return 244;
                }
                return 0;
            })(),
            countPasarUnit:
                (() => {
                    if (hasThreshold) return 2;
                    return 1;
                })() * count,
            lengthBaoUnit: (() => {
                const frameHeightProduction = frameHeight + 3;
                switch (true) {
                    case frameHeightProduction <= 205:
                        return 205;
                    case frameHeightProduction <= 215:
                        return 215;
                    case frameHeightProduction <= 220:
                        return 220;
                    case frameHeightProduction <= 225:
                        return 225;
                    case frameHeightProduction <= 244:
                        return 244;
                }
                return 0;
            })(),
            countBaoUnit: 2 * count,
            typeMaterial,
            hasThreshold,
            hasMokammel: mokammelWidth !== 0,
        },
        paravaz: {
            width: widthParvaz,
            counts: (() => {
                let counts = { 237: 0, 244: 0 };
                (() => {
                    const verticalParvaz =
                        frameHeight + 3 + (hasThreshold ? 14 : 7);
                    switch (true) {
                        case verticalParvaz <= 237:
                            counts[237] += 4 * count;
                            return;
                        case verticalParvaz <= 244:
                            counts[244] += 4 * count;
                            return;
                    }
                })();
                (() => {
                    const horizontalParvaz = frameWidth + 3 + 14;
                    switch (true) {
                        case horizontalParvaz * 2 <= 237:
                            counts[237] += (hasThreshold ? 2 : 1) * count;
                            return;
                        case horizontalParvaz * 2 <= 244:
                            counts[244] += (hasThreshold ? 2 : 1) * count;
                            return;
                        case horizontalParvaz <= 237:
                            counts[237] += (hasThreshold ? 2 : 1) * 2 * count;
                            return;
                        case horizontalParvaz <= 244:
                            counts[244] += (hasThreshold ? 2 : 1) * 2 * count;
                            return;
                    }
                })();
                return counts;
            })(),
            typeMaterial,
            typeRookob,
        },
        mokammel: {
            width: mokammelWidth,
            counts: (() => {
                let counts = { 220: 0, 244: 0 };
                if (mokammelWidth !== 0) return counts;
                (() => {
                    const vertical = frameHeight + 3;
                    switch (true) {
                        case vertical <= 220:
                            counts[220] += 2 * count;
                            return;
                        case vertical <= 244:
                            counts[244] += 2 * count;
                            return;
                    }
                })();
                (() => {
                    const horizontal = frameWidth + 3;
                    switch (true) {
                        case horizontal * 2 <= 220:
                            counts[220] += 0.5 * count;
                            return;
                        case horizontal * 2 <= 244:
                            counts[244] += 0.5 * count;
                            return;
                        case horizontal <= 220:
                            counts[220] += 1 * count;
                            return;
                        case horizontal <= 244:
                            counts[244] += 1 * count;
                            return;
                    }
                })();
                return counts;
            })(),
        },
        navardarzgir: {
            color: colorNavardarzgir,
            length:
                (frameHeight * 2 + frameWidth * (hasThreshold ? 2 : 1)) * count,
        },
        countPich,
        countVasher: countPich * 2,
    };

    return res;
}
