<?php

namespace Modules\Inventory\Entities;

use App\Models\UModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class BModel extends UModel
{
    use HasFactory;
    public function getTable()
    {
        return $this->table ?? (config('inventory.prefix') ?config('inventory.prefix') .'__' : '') . Str::snake(Str::pluralStudly(class_basename($this)));

    }
}
