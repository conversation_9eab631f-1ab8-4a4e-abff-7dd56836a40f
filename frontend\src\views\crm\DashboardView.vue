<template>
  <div class="dashboard-crm">
    <div class="row q-gutter-md">
      <div class="col-12">
        <q-card class="my-card">
          <q-card-section>
            <div class="text-h6">داشبورد CRM</div>
            <div class="text-subtitle2">سیستم مدیریت ارتباط با مشتری</div>
          </q-card-section>
          
          <q-card-section class="q-pt-none">
            <div class="text-body1">
              خوش آمدید به سیستم CRM ایران پارس
            </div>
          </q-card-section>
        </q-card>
      </div>
      
      <div class="col-md-6 col-12">
        <q-card class="my-card">
          <q-card-section>
            <div class="text-h6">آمار فروش</div>
          </q-card-section>
          
          <q-card-section class="q-pt-none">
            <div class="row q-gutter-md">
              <div class="col">
                <q-chip color="primary" text-color="white" icon="shopping_cart">
                  سفارشات: 85
                </q-chip>
              </div>
              <div class="col">
                <q-chip color="secondary" text-color="white" icon="people">
                  مشتریان: 320
                </q-chip>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
      
      <div class="col-md-6 col-12">
        <q-card class="my-card">
          <q-card-section>
            <div class="text-h6">عملیات سریع</div>
          </q-card-section>
          
          <q-card-section class="q-pt-none">
            <div class="q-gutter-sm">
              <q-btn color="primary" label="سفارش جدید" icon="add_shopping_cart" />
              <q-btn color="secondary" label="مشتریان" icon="people" />
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useDomain } from '@/composables';

const { domainConfig, isCrmDomain } = useDomain();

onMounted(() => {
  console.log('CRM Dashboard loaded');
  console.log('Domain config:', domainConfig.value);
});
</script>

<style scoped>
.dashboard-crm {
  padding: 16px;
}

.my-card {
  min-height: 120px;
}
</style>
