# راهنمای استفاده از فیلترهای جدول

## طراحی جدید فیلترها (مشابه Excel)

فیلترها حالا در header جدول قرار دارند و با کلیک روی آیکون فیلتر کنار نام هر ستون، منوی فیلتر باز می‌شود.

## انواع فیلترهای پشتیبانی شده

### 1. فیلتر متنی (text)
```javascript
{
    name: 'name',
    label: 'نام',
    field: 'name',
    filterType: 'text'
}
```

**شرایط فیلتر متنی:**
- شامل شود (includes)
- شامل نشود (excludes)
- شروع شود با (startsWith)
- پایان یابد با (endsWith)

### 2. فیلتر عددی (number)
```javascript
{
    name: 'price',
    label: 'قیمت',
    field: 'price',
    filterType: 'number'
}
```

**شرایط فیلتر عددی:**
- کوچکتر باشد از (lessThan)
- کوچکتر یا مساوی باشد با (lessThanOrEqual)
- بزرگتر باشد از (greaterThan)
- بزرگتر یا مساوی باشد با (greaterThanOrEqual)

### 3. فیلتر انتخابی (select)
```javascript
{
    name: 'status',
    label: 'وضعیت',
    field: 'status',
    filterType: 'select',
    filterOptions: [
        { label: 'فعال', value: 'active' },
        { label: 'غیرفعال', value: 'inactive' },
        { label: 'در انتظار', value: 'pending' }
    ]
}
```

**ویژگی‌های فیلتر انتخابی:**
- انتخاب چندگانه از لیست گزینه‌ها
- نمایش به صورت chips
- بدون نیاز به شرایط فیلتر اضافی
- فیلتر بر اساس مقادیر انتخاب شده

### 4. فیلتر تاریخ شمسی (date)
```javascript
{
    name: 'created_at',
    label: 'تاریخ ایجاد',
    field: 'created_at',
    filterType: 'date'
}
```

**شرایط فیلتر تاریخ:**
- مساوی باشد (dateEquals) - انتخاب تاریخ واحد
- بین دو تاریخ (dateBetween) - انتخاب بازه تاریخ
- قبل از (dateBefore) - انتخاب تاریخ واحد
- بعد از (dateAfter) - انتخاب تاریخ واحد

## مثال کامل استفاده

```vue
<template>
    <j-table-data-crud
        :columns="columns"
        url="/api/users"
        :permissions="permissions"
    />
</template>

<script setup>
const columns = ref([
    {
        name: 'id',
        label: 'شناسه',
        field: 'id',
        filterType: 'number'
    },
    {
        name: 'name',
        label: 'نام',
        field: 'name',
        filterType: 'text'
    },
    {
        name: 'status',
        label: 'وضعیت',
        field: 'status',
        filterType: 'select',
        filterOptions: [
            { label: 'فعال', value: 'active' },
            { label: 'غیرفعال', value: 'inactive' }
        ]
    },
    {
        name: 'created_at',
        label: 'تاریخ ایجاد',
        field: 'created_at',
        filterType: 'date'
    }
]);
</script>
```

## ویژگی‌های جدید

### 🎯 **طراحی Responsive:**
- **💻 دسکتاپ:** آیکون فیلتر در header هر ستون (مشابه Excel) + دکمه "حذف فیلترها"
- **📱 موبایل:** دکمه "فیلترها" + Drawer کشویی از سمت راست
- منوی کشویی برای تنظیمات فیلتر
- نمایش وضعیت فعال/غیرفعال فیلتر
- Badge تعداد فیلترهای فعال در موبایل
- **آیکون Sort:** استفاده از آیکون پیش‌فرض Quasar
- **حذف همه فیلترها:** دکمه قرمز در toolbar (فقط وقتی فیلتر فعال باشد)

### 🔧 **قابلیت‌های پیشرفته:**
1. **فیلتر متنی/عددی**: دکمه "اعمال" و Enter برای اعمال فیلتر
2. **فیلتر انتخابی هوشمند**: پشتیبانی از آرایه و نام فیلد
3. **فیلتر تاریخ شمسی**: پشتیبانی کامل از تقویم شمسی با readonly input
4. **بازه تاریخ**: امکان انتخاب بازه زمانی (از تاریخ - تا تاریخ)
5. **پاک کردن فیلتر**: دکمه پاک کردن برای همه انواع فیلتر
6. **ذخیره در URL**: تمام فیلترها در URL ذخیره می‌شوند
7. **نمایش بصری**: تغییر رنگ آیکون برای فیلترهای فعال
8. **UX بهتر**: منوهای کارت‌مانند با دکمه‌های واضح

## نکات مهم

### 🔗 **ساختار Query Parameters:**
- فیلترها: `filters[field]=value` (مثل: `filters[code]=6565`)
- شرایط: `conditions[field]=condition` (مثل: `conditions[code]=excludes`)
- فیلترهای چندگانه: `filters[status][0]=active&filters[status][1]=pending`

**نکات مهم:**
- شرایط حالا مستقیماً value ذخیره می‌شوند، نه object با label و value
- فیلترهای انتخابی به صورت آرایه ارسال می‌شوند: `filters[field][index]=value`

### 📅 **فیلترهای تاریخ:**
- تمام تاریخ‌ها شمسی هستند
- **دو روش ورودی تاریخ:**
  - **تایپ مستقیم:** فرمت `1402/05/15` با mask خودکار
  - **انتخاب از تقویم:** کلیک روی آیکون تقویم
- دکمه "اعمال" برای اعمال فیلتر تاریخ
- برای بازه تاریخ، هر دو تاریخ باید انتخاب شوند
- اعتبارسنجی خودکار فرمت تاریخ شمسی

### 🎯 **فیلترهای متنی/عددی:**
- دکمه "اعمال" یا کلید Enter برای اعمال
- انتخاب نوع فیلتر (شامل، شروع با، و...)

### 📋 **فیلترهای انتخابی:**
- انتخاب چندگانه مستقیم
- بدون نیاز به شرایط اضافی
- نمایش به صورت chips
- ارسال به صورت آرایه: `filters[field][0]`, `filters[field][1]`, ...

## مثال‌های عملی Query Parameters:

### فیلتر متنی:
```
filters[name]=احمد&conditions[name]=includes
```

### فیلتر انتخابی (چندگانه):
```
filters[status][0]=active&filters[status][1]=pending
```

### فیلتر تاریخ واحد:
```
filters[created_at]=1402/05/15&conditions[created_at]=dateEquals
```

### فیلتر بازه تاریخ:
```
filters[created_at_from]=1402/05/01&filters[created_at_to]=1402/05/30&conditions[created_at]=dateBetween
```

## نحوه ورودی تاریخ:

### 🖊️ **تایپ مستقیم:**
- فرمت: `1402/05/15`
- Mask خودکار: `####/##/##`
- Placeholder: نمونه تاریخ
- اعتبارسنجی خودکار

### 📅 **انتخاب از تقویم:**
- کلیک روی آیکون تقویم
- تقویم شمسی کامل
- انتخاب آسان تاریخ

### ✅ **اعتبارسنجی پیشرفته:**
- **فرمت:** بررسی `YYYY/MM/DD`
- **محدوده سال:** 1300-1500
- **ماه:** 1-12
- **روز:** 1-31 (با در نظر گیری تقویم شمسی)
- **تقویم شمسی دقیق:**
  - ماه‌های 1-6: 31 روز
  - ماه‌های 7-11: 30 روز
  - ماه 12: 29 یا 30 روز (سال کبیسه)
- **جلوگیری از ورود کاراکتر نامعتبر:** فقط عدد و `/`
- **نمایش خطا:** تاریخ نامعتبر با پیام خطا نمایش داده می‌شود
- **حفظ مقدار:** فیلد پاک نمی‌شود، کاربر می‌تواند تصحیح کند
- **اعتبارسنجی real-time:** هنگام تایپ و خروج از فیلد

### 🚫 **مثال‌های تاریخ نامعتبر:**
- `1401/01/35` → ❌ نمایش خطا: "ماه اول حداکثر 31 روز دارد"
- `1401/13/15` → ❌ نمایش خطا: "ماه 13 وجود ندارد"
- `1401/12/31` → ❌ نمایش خطا: "ماه 12 حداکثر 30 روز دارد"
- `1250/05/15` → ❌ نمایش خطا: "سال خارج از محدوده مجاز"

### 🔄 **فرآیند تصحیح:**
1. کاربر تاریخ نامعتبر وارد می‌کند
2. سیستم خطا نمایش می‌دهد
3. فیلد حفظ می‌شود
4. کاربر می‌تواند تصحیح کند
5. دکمه "اعمال" فقط با تاریخ معتبر کار می‌کند

## 📱 ویژگی‌های موبایل

### 🎨 **طراحی موبایل:**
- **دکمه فیلتر:** دکمه "فیلترها" در بالای جدول
- **Badge:** نمایش تعداد فیلترهای فعال
- **Drawer:** پنل کشویی از سمت راست
- **Scroll یکتا:** فقط یک اسکرول برای کل محتوا (بدون تداخل)
- **Layout column:** استفاده از Flexbox برای جلوگیری از اسکرول دوگانه

### 📐 **Breakpoints:**
- **موبایل:** `$q.screen.lt.md` (کمتر از 1024px)
- **دسکتاپ:** `$q.screen.gt.sm` (بیشتر از 600px)

### 🎯 **مزایای موبایل:**
1. **فضای بیشتر:** تمام صفحه برای فیلترها
2. **دسترسی آسان:** دکمه بزرگ و قابل لمس
3. **سازماندهی بهتر:** هر فیلتر در بخش جداگانه
4. **عملکرد بهتر:** بدون تداخل با جدول

### 🔄 **نحوه کارکرد موبایل:**
1. کاربر روی دکمه "فیلترها" کلیک می‌کند
2. Drawer از سمت راست باز می‌شود
3. تمام فیلترها به صورت عمودی نمایش داده می‌شوند
4. کاربر فیلترها را تنظیم می‌کند
5. دکمه "اعمال" یا تغییر خودکار فیلتر را اجرا می‌کند
6. Badge تعداد فیلترهای فعال را نمایش می‌دهد

## 🔧 جزئیات تکنیکی

### 📐 **ساختار Drawer:**
```vue
<q-drawer class="column">
    <q-toolbar>
        <!-- Header ثابت -->
    </q-toolbar>

    <div class="col" style="overflow-y: auto;">
        <!-- محتوای قابل اسکرول -->
    </div>
</q-drawer>
```

### ✅ **مزایای ساختار جدید:**
1. **یک اسکرول:** فقط محتوای اصلی اسکرول می‌شود
2. **Header ثابت:** toolbar همیشه در بالا باقی می‌ماند
3. **Performance بهتر:** کمتر reflow و repaint
4. **UX بهتر:** رفتار طبیعی‌تر اسکرول

### ❌ **مشکل قبلی:**
```vue
<!-- مشکل: دو اسکرول -->
<q-drawer>
    <q-toolbar></q-toolbar>
    <q-scroll-area class="fit">
        <!-- اسکرول اول: q-scroll-area -->
        <div>
            <!-- اسکرول دوم: محتوای طولانی -->
        </div>
    </q-scroll-area>
</q-drawer>
```

## 🔄 آیکون Sort

### 🎯 **رویکرد ساده:**
- **استفاده از Quasar:** آیکون sort پیش‌فرض Quasar
- **بدون تغییر:** عدم دخالت در CSS یا موقعیت
- **کارکرد استاندارد:** رفتار مشابه سایر جداول Quasar

### 📐 **ساختار Header:**
```vue
<q-th @click="col.sortable && props.sort(col.name)">
    <div class="flex items-center justify-between">
        <span>{{ col.label }}</span>
        <!-- آیکون sort توسط Quasar خودکار اضافه می‌شود -->
        <q-btn icon="filter_list" />
    </div>
</q-th>
```

### ✅ **مزایای رویکرد ساده:**
1. **سازگاری:** با استانداردهای Quasar سازگار
2. **پایداری:** کمتر احتمال شکستن در آپدیت‌ها
3. **سادگی:** کد کمتر و قابل نگهداری‌تر
4. **کارکرد مطمئن:** بدون مشکلات CSS یا JavaScript

---

## 🗑️ حذف فیلترها

### 🖥️ **دسکتاپ - دکمه حذف همه فیلترها:**

#### **موقعیت:**
- در toolbar جدول، کنار سایر دکمه‌ها
- فقط زمانی نمایش داده می‌شود که فیلتر فعال باشد

#### **ظاهر:**
```vue
<q-btn v-if="$q.screen.gt.xs && activeFiltersCount > 0"
    dense flat icon="clear_all"
    label="حذف فیلترها"
    color="negative"
    @click="clearAllFilters">
</q-btn>
```

#### **عملکرد:**
- حذف تمام فیلترها (متنی، انتخابی، تاریخ)
- حذف تمام شرایط فیلتر
- حذف فیلترهای بازه تاریخ
- بروزرسانی خودکار نتایج

### 📱 **موبایل:**
- دکمه حذف فیلتر در drawer موبایل موجود است
- برای هر فیلتر جداگانه

---

## 🎯 فیلتر Select هوشمند

### 🔧 **دو روش تعریف گزینه‌ها:**

#### 1. **آرایه مستقیم:**
```javascript
{
    field: 'status',
    label: 'وضعیت',
    filterType: 'select',
    filterOptions: [
        { label: 'فعال', value: 'active' },
        { label: 'غیرفعال', value: 'inactive' },
        { label: 'در انتظار', value: 'pending' }
    ]
}
```

#### 2. **نام فیلد از response سرور:**
```javascript
{
    field: 'status',
    label: 'وضعیت',
    filterType: 'select',
    filterOptions: 'statuses' // نام فیلد در response سرور (کنار data)
}
```

#### 3. **نام فیلد از داده‌های جدول:**
```javascript
{
    field: 'department',
    label: 'بخش',
    filterType: 'select',
    filterOptions: 'department_name' // نام فیلد در داده‌های جدول
}
```

### ⚙️ **نحوه کارکرد:**

#### **تابع `getSelectOptions`:**
```javascript
const getSelectOptions = (col) => {
    // اگر آرایه باشد، مستقیم استفاده کن
    if (Array.isArray(col.filterOptions)) {
        return col.filterOptions;
    }

    // اگر رشته باشد
    if (typeof col.filterOptions === 'string') {
        const fieldName = col.filterOptions;

        // ابتدا در serverData جستجو کن (مثل 'statuses')
        if (serverData.value && serverData.value[fieldName]) {
            return serverData.value[fieldName];
        }

        // اگر پیدا نشد، از داده‌های جدول استخراج کن
        const uniqueValues = [...new Set(
            rows.value
                .map(row => row[fieldName])
                .filter(value => value !== null && value !== undefined && value !== '')
        )];

        return uniqueValues.map(value => ({
            label: value,
            value: value
        }));
    }

    return [];
};
```

### 🎯 **مزایای رویکرد هوشمند:**

1. **انعطاف‌پذیری:**
   - آرایه ثابت برای گزینه‌های از پیش تعریف شده
   - فیلدهای response سرور (مثل `statuses`)
   - استخراج خودکار از داده‌های جدول

2. **اولویت‌بندی هوشمند:**
   - ابتدا از response سرور استفاده می‌کند
   - سپس از داده‌های جدول استخراج می‌کند

3. **بهینه‌سازی:**
   - حذف مقادیر تکراری با `Set`
   - فیلتر کردن مقادیر خالی

4. **سادگی:**
   - تنها یک تابع برای همه حالات
   - عدم نیاز به کد اضافی

5. **ارسال صحیح به سرور:**
   - فیلترها و شرایط براساس `name` ستون ارسال می‌شوند
   - نه براساس `field` (که ممکن است متفاوت باشد)

6. **URL State Management:**
   - فیلترها و شرایط در URL براساس `name` ذخیره می‌شوند
   - هنگام reload، `name` به `field` تبدیل می‌شود

### 📊 **مثال کاربردی:**

```javascript
// ستون‌های جدول
const columns = [
    {
        name: 'status',           // نام ستون که به سرور ارسال می‌شود
        field: 'label_status',    // فیلد نمایش در جدول
        label: 'وضعیت',
        filterType: 'select',
        filterOptions: 'statuses' // از response سرور
    },
    {
        name: 'department',       // نام ستون که به سرور ارسال می‌شود
        field: 'department_name', // فیلد نمایش در جدول
        label: 'بخش',
        filterType: 'select',
        filterOptions: 'department_name' // از داده‌های جدول
    },
    {
        name: 'priority',         // نام ستون که به سرور ارسال می‌شود
        field: 'priority_label',  // فیلد نمایش در جدول
        label: 'اولویت',
        filterType: 'select',
        filterOptions: [ // آرایه ثابت
            { label: 'بالا', value: 'high' },
            { label: 'متوسط', value: 'medium' },
            { label: 'پایین', value: 'low' }
        ]
    }
];
```

### 🔄 **نحوه ارسال به سرور:**

وقتی فیلتر `status` با مقدار `DRAFT` و شرط `includes` انتخاب شود:
```
GET /api/data?filters[status][0]=DRAFT&conditions[status]=includes
```

**نه این:**
```
GET /api/data?filters[label_status][0]=DRAFT&conditions[label_status]=includes
```

### 🔗 **URL State Management:**

#### **ذخیره در URL:**
```
http://panel.erp.test:3000/order?filters[status][0]=FINANCIAL_APPROVAL&conditions[status]=includes
```

#### **بازیابی از URL:**
1. خواندن `filters[status][0]=FINANCIAL_APPROVAL` و `conditions[status]=includes`
2. تبدیل `status` (name) به `label_status` (field)
3. ذخیره در:
   - `filters.value['label_status'] = ['FINANCIAL_APPROVAL']`
   - `filterConditions.value['label_status'] = 'includes'`

#### **توابع کمکی:**
```javascript
// تبدیل field به name (برای ارسال)
const getColumnNameByField = (field) => {
    const column = columns.value.find(col => col.field === field);
    return column ? column.name : field;
};

// تبدیل name به field (برای بازیابی)
const getColumnFieldByName = (name) => {
    const column = columns.value.find(col => col.name === name);
    return column ? column.field : name;
};
```

### 🌐 **ساختار Response سرور:**

```json
{
    "data": [
        {
            "id": 1,
            "status": "DRAFT",
            "department_name": "فروش"
        }
    ],
    "meta": {
        "total": 100,
        "current_page": 1
    },
    "statuses": [
        {
            "value": "DRAFT",
            "label": "پیش نویس",
            "label_customer": "پیش نویس"
        },
        {
            "value": "CONFIRMED",
            "label": "تایید شده"
        }
    ]
}
```