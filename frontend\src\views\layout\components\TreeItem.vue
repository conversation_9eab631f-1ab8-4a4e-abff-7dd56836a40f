<template>
  <template v-if="children.filter(f => !f.hidden).length > 0">


    <q-expansion-item ref="expan" v-if="!hidden" :icon="meta.icon" :label="meta.title" :header-inset-level="level"
      :default-opened="opened" class="overflow-hidden m-1 rounded-md" :header-class="(route.matched.map(m => m.name).includes(name) ? ' bg-gray-500 bg-opacity-10' : '')
        ">
      <TreeItem v-for="child in children.filter(f => !f.hidden)" :current-route="currentRoute" :key="child" :level="level + 0.1"
        v-bind="child" />


    </q-expansion-item>
  </template>
  <template v-else>
    <q-item v-if="!hidden" clickable v-ripple :inset-level="level" :to="{ name: name }" active-class="tab-active"
      class="m-1 rounded-md">
      <q-item-section v-if="meta.icon" avatar>
        <q-icon :name="meta.icon" />
      </q-item-section>
      <q-item-section>{{ meta.title ?? "" }}</q-item-section>
    </q-item>
  </template>
</template>

<script>
import { ref, watch, computed } from "vue";
import { useRoute } from "vue-router";
import { routes } from "@/helpers";
export default {
  props: {
    meta: {
      type: Object,
      default: {},
    },
    hidden: Boolean,
    level: {
      type: Number,
      default: 0,
    },
    name: String,
    currentRoute: String,
    children: {
      type: Array,
      default: [],
    },
  },
  setup(props) {
    const expan = ref(null);
    const opened = ref(false);

    const route = useRoute();

    watch(
      () => route.name && expan.value,
      () => {

        const route_this = routes.findNested("name", props.name);

        if (
          expan.value &&
          (route_this ? [route_this] : []).findNested(
            "name",
            props.currentRoute
          )
        ) {
          console.log(expan.value.label)

          expan.value.show();
        }
      }
    );

    return {
      opened,
      expan,
      route,
    };
  },
};
</script>