<?php

namespace Modules\Production\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\API\BaseController;
use App\Repositories\ModelRepositoryInterface;
use Illuminate\Support\Arr;
use Modules\Production\Entities\Machine;
use Modules\Production\Entities\Station;

class StationController extends BaseController
{
    public function __construct(ModelRepositoryInterface $repository)
    {
        $repository->model = Station::class;
        $this->repository = $repository;
    }
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return $this->repository->getAll();
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        return $this->repository->create();
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(Station $station)
    {
        // $station['asd'] = $station->instructionItemChecklists()->with('instructionItem.parents')->get();
        $station->machines = $station->machines()->pluck('id');
        return $this->handleResponse($station, null, [
            'additional' => [
                'machines' => Machine::query()->get()
            ],
        ]);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, Station $station)
    {
        $station->machines()->sync($request->input('machines' ?? []));
        $station->update($request->all());
        $station->machines = $station->machines()->pluck('id');
        return $this->handleResponse($station);
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy(Station $station)
    {
        return $this->repository->delete($station);
    }

    public function search(Request $request)
    {
        $query = Station::query();
        if ($request->has('relations') && is_array($request->relations)) {
            foreach ($request->relations as $relation)
                switch ($relation) {
                    case 'works':
                        $query->with('works');
                }
        }
        return $this->handleResponse($query->get());
    }
}
