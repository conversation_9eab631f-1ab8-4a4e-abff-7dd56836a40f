<?php

use App\Models\City;
use App\Models\Province;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create($this->table(), function (Blueprint $table) {
            $table->id();
            $table->string('full_name');
            $table->string('display_name')->nullable();
            $table->string('phone_number');
            $table->text('address')->nullable();

            $table->string('status')->default('pending');
            $table->string('national_code')->unique()->nullable();
            $table->string('mobile_number')->unique()->nullable();
            $table->foreignIdFor(Province::class);//->constrained(Province::getTableName())->cascadeOnUpdate();
            $table->foreignIdFor(City::class);//->constrained(City::getTableName())->cascadeOnUpdate();
            $table->string('image');
            $table->json('location')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists($this->table());
    }

    public function table(){
        $prefix = config('buyandsell.prefix');
        return ($prefix ? $prefix .'__' : '').'parties';
    }
};
