<template>
  <div class="q-gutter-sm">
    <j-table-data url="/production/instruction" :columns="columns">
      <template #dialog="props">
        <table-form v-bind="props" v-model:form="props.form" />
      </template>
       <template #select_bar="{selected}">
        <j-btn icon="view_list" dense color="primary" :to="{name: 'instruction_id', params: {id: selected.id}}" />
      </template>
    </j-table-data>
    
  </div>
</template>

<script>
import TableForm from "./form.vue";

export default {
  setup() {

    const columns = [
      {
        name: 'name',
        required: true,
        label: 'نام',
        field: 'name',
        sortable: true,
        filter: 'FilterInput',
      },

    ]

    return {
      columns,
    };
  },
  components: { TableForm },
};
</script>
