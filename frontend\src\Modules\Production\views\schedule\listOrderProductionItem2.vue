<template>
    <j-table class="my-sticky-dynamic" :rows="data" :columns="columns" :loading="loading" row-key="id" virtual-scroll
        dense :virtual-scroll-item-size="48" :virtual-scroll-sticky-size-start="48" :pagination="pagination"
        :rows-per-page-options="[0]" @virtual-scroll="onScroll" v-model:selected="selected" selection="single"
        @onFilter="onFilter" v-model:filters="filters">
        <template #bottom v-if="selected.length > 0">
            <div class="mx-auto flex space-x-2">
                <j-input v-model.number="count" type="number" :max="selected[0].count" :min="0" dense
                    class="h-8 important bg-white" hide-bottom-space style="width:100px" input-class="h-8" />

                <j-btn :disabled="count == 0" label="افزودن" color="primary" dense />
            </div>
        </template>
    </j-table>
</template>
 
<script>
import { ref, computed, nextTick } from 'vue'
import { api } from '@/boot/axios'

export default {
    setup() {
        const nextPage = ref(1)
        const lastPage = ref(0)
        const loading = ref(false)
        const attribute_columns = ref([])
        const count = ref(0)


        const data = ref([])
        api.get('production/production_order_items', { params: { per_page: 10 } }).then(res => {
            data.value = res.data
            nextPage.value = res.meta.current_page + 1
            lastPage.value = res.meta.last_page
        })
        const attributes = ref([])
        api.get('good/attribute').then(res => {
            attributes.value = res.data
        })


        const columns = computed(() => {
            if (data.value.length > 0) {
                attribute_columns.value = data.value.map(m => Object.keys(m.attributes)).flat().unique();
            }
            return [...[
                {
                    name: "full_name",
                    label: "نمایندگی",
                    field: row => row.production_order ? row.production_order.party_name : '',
                    filter: {
                        type: 'FilterInput',
                        relation: 'productionOrder.party',
                    },
                },
                {
                    name: "customer_name",
                    label: "مشتری",
                    field: row => row.production_order ? row.production_order.customer_name : '',
                    filter: {
                        type: 'FilterInput',
                        relation: 'productionOrder',
                    },
                },
                {
                    name: "name",
                    label: "کالا/خدمات",
                    field: row => row.good.name ?? '',
                    filter: {
                        type: 'FilterInput',
                        relation: 'good',
                    },
                },
                {
                    name: "count",
                    label: "تعداد",
                    field: "count",
                    filter: 'FilterInput'

                }
            ],
            ...attributes.value.filter(f => attribute_columns.value.includes(f.id + '')).map(m => {
                let filter = '';
                switch (m.type) {
                    case 'SELECT':
                        filter = 'FilterSelect'
                        break;
                    case 'SWITCH':
                        filter = 'FilterSwitch'
                        break;

                    case 'NUMBER':
                    case 'INPUT':
                        filter = 'FilterInput'

                }
                return {
                    name: 'attributes.' + m.id,
                    label: m.name,
                    field: row => {
                        switch (m.type) {
                            case 'SELECT':
                                const find = m.items.findIndex(ff => ff.id + '' == row.attributes[m.id])
                                return find >= 0 ? m.items[find].name : '';
                            case 'SWITCH':
                                return row.attributes[m.id] ? 'دارد' : ''
                            case 'NUMBER':
                            case 'INPUT':
                                return row.attributes[m.id] ?? ''
                        }
                    },
                    filter,
                    filterOption: m.items.map(m => ({ label: m.name, value: m.id })),

                }
            })
            ];

        })
        const filters = ref({})
        const param_filter = ref({})
        const selected = ref([])
        return {
            columns,
            data,
            nextPage,
            loading,
            filters,
            selected,
            count,
            onFilter() {


                const old_param_filter = Object.assign({}, param_filter.value);
                param_filter.value = Object.assign({}, filters.value);
                Object.keys(param_filter.value).filter((f) => {
                    if (param_filter.value[f].value !== false && !param_filter.value[f].value) delete param_filter.value[f];
                });
                if (
                    !(
                        Object.keys(param_filter.value).length == 0 &&
                        Object.keys(old_param_filter) == 0
                    )
                )
                    api.get('production/production_order_items', { params: { per_page: 10, filters: param_filter.value } }).then(res => {
                        data.value = res.data
                        nextPage.value = res.meta.current_page + 1
                        lastPage.value = res.meta.last_page
                    })


            },
            pagination: { rowsPerPage: 0 },

            onScroll({ to, ref }) {
                const lastIndex = data.value.length - 1

                if (loading.value !== true && nextPage.value <= lastPage.value && to === lastIndex) {
                    loading.value = true

                    api.get('production/production_order_items', { params: { per_page: 10, page: nextPage.value, filters: param_filter.value } }).then(res => {
                        data.value.push(...res.data)
                        nextPage.value = res.meta.current_page + 1
                        lastPage.value = res.meta.last_page
                        nextTick(() => {
                            ref.refresh()
                            loading.value = false
                        })
                    })


                }
            }
        }
    }
}
</script>
