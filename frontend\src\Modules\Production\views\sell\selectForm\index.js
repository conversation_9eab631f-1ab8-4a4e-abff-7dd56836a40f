import { ref } from "vue";

export const conditionCompute = ({ form, good }) => {
    const hidden_attributes = ref([]);
    const disable_attributes = ref([]);
    const good_attributes = ref([]);

    const conditionProccess = (conditions) => {
        // console.log('-----------------------------')
        conditions.forEach((condition) => {

            if (
                condition.if.filter((cond) => {
                    if (form.value.attributes[cond.attribute_id] == undefined)
                        return true;
                    if (
                        cond.value ==
                        form.value.attributes[cond.attribute_id] ||
                        (typeof cond.value == "object" &&
                            cond.value.includes(
                                form.value.attributes[cond.attribute_id] ?? null
                            ))
                    ) {
                        return false;
                    }
                    if (
                        cond.between &&
                        cond.between[0] <=
                        form.value.attributes[cond.attribute_id] &&
                        cond.between[1] >=
                        form.value.attributes[cond.attribute_id]
                    ) {
                        return false;
                    }
                    return true;
                }).length == 0
            ) {
                condition.then.forEach((cond) => {
                    if (cond.attribute_id.constructor.name != "Array")
                        cond.attribute_id = [cond.attribute_id];

                    cond.attribute_id.forEach((cond_attribute_id) => {
                        // const first_value =
                        //     form.value.attributes[cond_attribute_id];
                        if (cond.items) {
                            const find = good_attributes.value.findIndex(
                                (f) => f.id == cond_attribute_id
                            );
                            if (
                                find >= 0 &&
                                good_attributes.value[find].type == "SELECT"
                            ) {
                                good_attributes.value[find].items =
                                    good.value.attributes[find].items.filter(
                                        (f) => cond.items.includes(f.id)
                                    );
                                //console.log(good_attributes[find].name, condition)

                                if (cond.items.length == 1)
                                    form.value.attributes[cond_attribute_id] =
                                        cond.items[0];
                                if (
                                    form.value.attributes[cond_attribute_id] &&
                                    cond.items.length > 1 &&
                                    !cond.items.includes(
                                        form.value.attributes[cond_attribute_id]
                                    )
                                ) {
                                    form.value.attributes[cond_attribute_id] =
                                        null;
                                }
                            }
                        }

                        if (typeof cond.value !== "undefined") {
                            if (cond.value === null)
                                delete form.value.attributes[cond_attribute_id];
                            else
                                form.value.attributes[cond_attribute_id] =
                                    cond.value;
                        }
                        if (cond.disable) {
                            disable_attributes.value.attach(cond_attribute_id);
                        }
                        if (cond.hidden) {
                            hidden_attributes.value.attach(cond_attribute_id);
                            //console.log("hidden", hidden_attributes.value);
                        } else if (cond.hidden === false) {
                            if (
                                (form.value.attributes[cond_attribute_id] ==
                                    undefined ||
                                    form.value.attributes[cond_attribute_id] ==
                                    null) &&
                                good.value.attributes.findIndex(
                                    (ff) => ff.id == cond_attribute_id
                                ) >= 0 &&
                                good.value.attributes[
                                    good.value.attributes.findIndex(
                                        (ff) => ff.id == cond_attribute_id
                                    )
                                ].type == "SWITCH"
                            )
                                form.value.attributes[
                                    cond_attribute_id
                                ] = false;

                            hidden_attributes.value.dettach(cond_attribute_id);
                            //console.log("show", hidden_attributes.value);
                        }
                        if (cond.formula) {
                            form.value.attributes[cond_attribute_id] =
                                formulaProcess(cond.formula);
                        }

                        // if (
                        //     [4,26].includes(cond_attribute_id)
                        // )
                        //     console.log(cond_attribute_id,
                        //         {
                        //             zekhamat: form.value.attributes.centerLayerThickness,
                        //             gotr: form.value.attributes[26],
                        //         },
                        //         condition,
                        //         cond,
                        //         conditions.filter(
                        //             (f) =>
                        //                 f.if.filter(
                        //                     (ff) =>
                        //                         ff.attribute_id == cond_attribute_id
                        //                 ).length > 0
                        //         )
                        //     );

                        // conditionProccess(
                        //     conditions.filter(
                        //         (f) =>
                        //             f.if.filter(
                        //                 (ff) =>
                        //                     ff.attribute_id == cond_attribute_id
                        //             ).length > 0
                        //     )
                        // );
                        // if (
                        //     form.value.attributes[cond_attribute_id] !==
                        //     first_value
                        // )
                        //     conditionProccess(conditions);
                    });
                });
                //console.log("then", condition.then);
            }
        });
    };

    const formulaProcess = (formula) => {
        // console.log(formula)
        if (typeof formula == "number") return formula;
        if (formula.attribute_id) return getTextAttribute(formula.attribute_id);
        switch (formula.type) {
            case "sum":
                return formula.items
                    .map((m) => formulaProcess(m))
                    .reduce((a, b) => a + b);
            case "minus":
                return formula.items
                    .map((m) => formulaProcess(m))
                    .reduce((a, b) => a - b);
            case "zarb":
                return formula.items
                    .map((m) => formulaProcess(m))
                    .reduce((a, b) => a * b);
            case "divide":
                return formula.items
                    .map((m) => formulaProcess(m))
                    .reduce((a, b) => a / b);
        }
        return 0;
    };

    const getTextAttribute = (attribute_id) => {
        const find = good.value.attributes.findIndex(
            (f) => f.id == attribute_id
        );
        if (!form.value.attributes[attribute_id] || find == -1) return 0;

        switch (good.value.attributes[find].type) {
            case "NUMBER":
                return 1 * form.value.attributes[attribute_id];
            case "SELECT":
                const find2 = good.value.attributes[find].items.findIndex(
                    (f) => f.id == form.value.attributes[attribute_id]
                );
                if (find2 >= 0)
                    return 1 * good.value.attributes[find].items[find2].name;
        }
        return 0;
    };

    return {
        conditionProccess,
        hidden_attributes,
        disable_attributes,
        good_attributes,
    };
};




export const attributeColumns = ({ items, attributes, extra = {} }) => {
    if (!attributes)
        return [];
    const attribute_columns = items.map(m => Object.keys(m.attributes)).flat().unique();

    return attributes.filter(f => attribute_columns.includes(f.key + '') && f.showing).sort(function (a, b) {
        if (a.sort < b.sort) return -1;
        if (a.sort > b.sort) return 1;
        return 0;
    }).map(m => {


        return {
            name: m.key,
            label: m.name,
            vertical: true,
            hasImage: m.type == 'SELECT_IMAGE',
            image: row => {
                if (m.type == 'SELECT_IMAGE') {
                    const find = attributes.findIndex(f => m.id)
                    if (find >= 0) return attributes[find].items[attributes[find].items.findIndex(f => f.id == row.attributes[m.key])]?.data?.image;
                }
                return ''

            },
            align: 'center',
            field: row => {
                switch (m.type) {
                    case 'SELECT':
                    case 'SELECT_IMAGE':
                        const find = m.items.findIndex(ff => ff.key + '' == row.attributes[m.key])
                        return find >= 0 ? m.items[find].name : '';
                    case 'SWITCH':
                        return row.attributes[m.key] !== undefined ? (row.attributes[m.key] ? 'دارد' : '') : ''
                    case 'FILE':
                        return row.attributes[m.key]
                    case 'NUMBER':
                    case 'INPUT':
                        return row.attributes[m.key] ?? ''
                }
            },
            ...extra
        }
    })
}