<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\API\BaseController as BaseController;
use App\Models\User;
use Illuminate\Http\Resources\Json\JsonResource;
use Laravel\Sanctum\PersonalAccessToken;
use Spatie\Permission\Models\Role;
use Validator;


class AuthController extends BaseController
{

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'username' => ['required'],
            'password' => ['required'],
        ]);
        if (Auth::attempt($credentials)) {
            $auth = Auth::user();
            $auth->tokens()->delete();
            $success['token'] =  $auth->createToken('LaravelSanctumAuth', ['*'], now()->addMinutes(60 * 24))->plainTextToken;
            $success['name'] =  $auth->name;

            return $this->handleResponse($success, trans('auth.success'));
        } else {
            return $this->handleError(trans('auth.failed'), ['error' => trans('auth.failed')], 400);
        }
    }

    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'username' => 'required',
            'password' => 'required',
            'confirm_password' => 'required|same:password',
        ]);

        if ($validator->fails()) {
            return $this->handleError($validator->errors());
        }

        $input = $request->all();
        $input['password'] = bcrypt($input['password']);
        $user = User::create($input);
        $success['token'] =  $user->createToken('LaravelSanctumAuth', ['*'], now()->addMinutes(60 * 24))->plainTextToken;
        $success['name'] =  $user->name;

        return $this->handleResponse($success, 'User successfully registered!');
    }
}
