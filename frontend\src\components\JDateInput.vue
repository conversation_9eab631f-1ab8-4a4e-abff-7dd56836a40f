<template>
    <div class="flex flex-col">
        <j-input v-model="date" mask="date" :label="label" :rules="['date']" :dense="dense"
            :hide-bottom-space="hideBottomSpace">
            <template v-if="popup" v-slot:append>
                <j-icon name="event" class="cursor-pointer">
                    <q-popup-proxy ref="ref_popup" cover transition-show="scale" transition-hide="scale" @show="show">
                        <j-date v-model="date" :options="options" square minimal flat :events="events"
                            :event-color="eventColor" @navigation="navigation" />
                    </q-popup-proxy>
                </j-icon>
            </template>
        </j-input>
        <j-date v-if="!popup" v-model="date" :options="options" minimal flat class="mx-auto my-0" :events="events"
            :event-color="eventColor" @navigation="navigation" style="width: 100%;max-width: 500px" />
    </div>
</template>
<script>
import { ref, watch } from 'vue'
export default {
    props: {
        value: String,
        label: String,
        dense: Boolean,
        hideBottomSpace: Boolean,
        options: Array | Function,
        events: Array | Function,
        eventColor: String | Function,
        popup: {
            type: Boolean,
            default: true,
        },
    },
    setup(props, context) {
        const date = ref(props.value ?? null)
        const ref_popup = ref(null)

        watch(() => props.value, (newValue) => {
            date.value = newValue
        })
        watch(() => date.value, (newValue) => {
            if (ref_popup.value && typeof ref_popup.value.hide === 'function') {
                ref_popup.value.hide()
            }
            context.emit('input', newValue)
            context.emit('update:input', newValue)
            context.emit('update:value', newValue)
        })
        return {
            date,
            ref_popup,
            navigation(a) {
                context.emit('navigation', a)
            },
            show(a) {
                context.emit('show', a);
            }
        }
    },
}
</script>