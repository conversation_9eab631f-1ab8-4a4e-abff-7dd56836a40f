<template>
  <j-table-data-crud url="/good/attribute" :columns="columns" routeName="attributes" :permissions="permissions">

    <template v-slot:body-cell-image="props">
      <q-td :props="props">
        <j-image-viewer :src="props.value" class="w-24" fullHeight />
      </q-td>
    </template>
  </j-table-data-crud>
</template>

<script setup>

const permissions = {
  create: 'attributes.create',
  edit: 'attributes.edit',
  delete: 'attributes.delete',
}

const columns = [
  {
    name: 'name',
    required: true,
    label: 'نام',
    field: 'name',
    sortable: true,
  },
  {
    name: 'type',
    required: true,
    label: 'نوع',
    field: 'label_type',
    sortable: true,
  },
]


</script>
