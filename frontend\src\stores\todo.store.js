// stores/todoStore.js
import { defineStore } from 'pinia';
import axios from 'axios';

export const useTodoStore = defineStore('todoStore', {
  state: () => ({
    todos: [],
    isLoading: false,
    error: null,
    lastFetched: 0,
  }),
  actions: {
    async fetchTodos() {
      const now = new Date().getTime();
      
      if (now - this.lastFetched < 60000 && this.todos.length > 0) {
        console.log('Using cached data');
        return;
      }

      this.isLoading = true;
      this.error = null;

      try {
        const response = await axios.get('https://jsonplaceholder.typicode.com/todos');
        this.todos = response.data;
        this.lastFetched = now;
      } catch (error) {
        this.error = 'Failed to fetch todos';
      } finally {
        this.isLoading = false;
      }
    },
    async addTodo(newTodo) {
      this.isLoading = true;
      try {
        const response = await axios.post('https://jsonplaceholder.typicode.com/todos', newTodo);
        this.todos.push(response.data);
      } catch (error) {
        this.error = 'Failed to add todo';
      } finally {
        this.isLoading = false;
      }
    },
    async deleteTodo(id) {
      this.isLoading = true;
      try {
        await axios.delete(`https://jsonplaceholder.typicode.com/todos/${id}`);
        this.todos = this.todos.filter(todo => todo.id !== id);
      } catch (error) {
        this.error = 'Failed to delete todo';
      } finally {
        this.isLoading = false;
      }
    },
  },
});
