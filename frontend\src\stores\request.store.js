import { defineStore } from 'pinia';

export const useRequestStore = defineStore({
    id: 'request',
    state: () => ({
        errors: {},
        loading: false,
    }),
    actions: {
        // async getAll() {
        //     this.users = { loading: true };
        //     fetchWrapper.get(baseUrl)
        //         .then(users => this.users = users)
        //         .catch(error => this.users = { error })
        // }
    }
});
