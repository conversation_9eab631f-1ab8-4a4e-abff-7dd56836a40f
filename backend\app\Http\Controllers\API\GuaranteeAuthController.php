<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\API\BaseController as BaseController;
use App\Models\GuaranteeUser;
use App\Models\User;
use Modules\BuyAndSell\Entities\Party;
use Validator;


class GuaranteeAuthController extends BaseController
{



    public function loginByPhone(Request $request)
    {
        $credentials = $request->validate([
            'mobile' => ['required', 'regex:/^(\+98|0)?9\d{9}$/', 'min:10'],
            'password' => ['required', 'numeric'],
        ]);



        $user = GuaranteeUser::where('phone_number', $request->input('mobile'))->first();
        $token = $user->smsTokens()->latest()->pluck('code')->first();



        if ($token == $request->input('password')) {
            auth()->guard('guarantee')->login($user);
            $auth = Auth::guard('guarantee')->user();
            $auth->tokens()->delete();
            $auth->smsTokens()->delete();
            $success['token'] =  $auth->createToken('LaravelSanctumAuth', ['*'], now()->addMinutes(60 * 24))->plainTextToken;

            $success['username'] =  $auth->full_name;

            return $this->handleResponse($success, trans('auth.success'));
        } else {
            return $this->handleError(trans('auth.failed_code'));
        }
    }


    public function sendVerifyCode(Request $request)
    {
        $credentials = $request->validate([
            'mobile' => ['required', 'regex:/^(\+98|0)?9\d{9}$/', 'min:10'],
        ]);

        $user = GuaranteeUser::firstOrCreate(
            [
                'phone_number' => $request->input('mobile'),
            ]
        );

        $token = $user->smsTokens()->create();
        $sendCode = $token->sendCode($user->phone_number);
        if ($sendCode['ok']) {
            return response()->json($sendCode);
        }
        $token->delete();
        return response()->json($sendCode, 400);
    }
}
