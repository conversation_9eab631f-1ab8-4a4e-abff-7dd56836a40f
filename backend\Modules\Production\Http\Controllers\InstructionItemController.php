<?php

namespace Modules\Production\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\API\BaseController;
use App\Repositories\ModelRepositoryInterface;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Production\Entities\Instruction;
use Modules\Production\Entities\InstructionItem;

class InstructionItemController extends BaseController
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index(Instruction $instruction)
    {
        return JsonResource::collection($instruction->items()->with(['parents.stationWork', 'stationWork', 'station','conditions'])->get()->map(function ($m) {
            $m->station_label = $m->station_label;
            return $m;
        }))->additional([
            'model' => $instruction,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request, Instruction $instruction)
    {
        $item = InstructionItem::create(array_merge($request->all(), [
            'name' => '',
            'instruction_id' => $instruction->id,
        ]));
        $item->parents()->sync($request->parents);
        if ($request->has('conditions')) $item->conditions()->sync(collect($request->input('conditions'))->map(function ($m) {
            return [
                'attribute_id' => $m['attribute_id'],
                'items' => $m['items'],
                'condition' => $m['condition'] ?? false,
            ];
        })->toArray());
        $item->conditions = $item->conditions()->get();
        $item['parents'] = $item->parents()->get()->pluck('id');

        return $this->handleResponse($item, trans('request.done'));
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(Instruction $instruction, InstructionItem $item)
    {
        $item['parents'] = $item->parents()->get()->pluck('id');
        $item->conditions = $item->conditions()->get();

        return $this->handleResponse($item);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, Instruction $instruction, InstructionItem $item)
    {
        $item->update(array_merge($request->all(), [
            'instruction_id' => $instruction->id,
        ]));
        $item->parents()->sync($request->parents);
        $item['parents'] = $item->parents()->get()->pluck('id');
        if ($request->has('conditions')) $item->conditions()->sync(collect($request->input('conditions'))->map(function ($m) {
            return [
                'attribute_id' => $m['attribute_id'],
                'items' => $m['items'],
                'condition' => $m['condition'] ?? false,
                'id' => $m['id'] ?? null,
            ];
        })->toArray());
        $item->conditions = $item->conditions()->get();
        return $this->handleResponse($item, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy(Instruction $instruction, InstructionItem $item)
    {
        return $this->handleResponse($item->delete());
    }

    public function search(Instruction $instruction)
    {
        return $this->handleResponse($instruction->items()->with('stationWork')->get());
    }
}
