<template>

    <j-form-data url="good/good" :form="form" hasCreateRoute>
        <template v-slot="{ form, formOptions }">
            <q-card flat bordered>
                <q-tabs v-model="tab" inline-label align="left" class="text-primary">
                    <q-tab name="form" label="مشخصات" />
                    <q-tab name="attribute" label="ویژگی ها" />
                    <q-tab name="meta" label="متا" />
                </q-tabs>
                <q-separator />
                <q-tab-panels v-model="tab" animated>
                    <q-tab-panel name="form">
                        <div class="flex gap-5">
                            <div class="grow">
                                <j-input v-model="form.name" error-field="name" label="نام" />
                                <j-toggle v-model="form.is_active" label="فعال"></j-toggle>
                                <j-toggle v-model="form.is_active_customer" label="فعال مشتری"></j-toggle>
                            </div>
                            <div class="w-fit">
                                <j-upload v-model:value="form.image_src" label="تصویر کالا" auto-upload
                                    accept="image/*" url="/api/upload-file" field-name="file" flat bordered
                                    thumbnail-fit="contain" />
                                
                            </div>
                        </div>

                        <!-- <j-select v-model="form.type" :options="formOptions.types" error-field="type" label="نوع"
                            emit-value map-options options- /> -->
                        <!-- <j-select-remote v-model="form.group_id" url="good/group/search" error-field="group_id"
                            label="گروه">
                        </j-select-remote> -->



                    </q-tab-panel>

                    <q-tab-panel name="attribute">
                        <attribute-good v-model:value="form.attributes" :attributes="formOptions.attributes"
                            v-model:default_attribute="form.default_attribute" />
                    </q-tab-panel>

                    <q-tab-panel name="meta">
                        <good-meta v-model:value="form.metas" />
                    </q-tab-panel>
                </q-tab-panels>
            </q-card>
        </template>
    </j-form-data>
</template>
<script setup>
import Instruction from './instruction.vue';
import AttributeGood from './AttributeGood.vue';
import GoodMeta from './GoodMeta.vue';
import { ref } from 'vue'

const tab = ref('form')
const form = ref({
    is_active: true,
    is_active_customer: true,
    type: 'PRODUCT',
    attributes: {},
    default_attribute: {},
})

</script>