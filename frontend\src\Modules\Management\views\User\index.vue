<template>
  <j-table-data-crud :columns="columns" url="users" :permissions="permissions" />
</template>
<script setup>
const permissions = {
  create: 'users.create',
  edit: 'users.edit',
  delete: 'users.delete',
}

const columns = [
  //{ name: 'id', label: 'شناسه', field: 'id', sortable: true, filterType: 'number' },
  { name: 'full_name', label: 'نام و نام خانوادگی', field: 'full_name', sortable: true, filterType: 'text' },
  { name: 'username', label: 'نام کاربری', field: 'username', sortable: true, filterType: 'text' },
  { name: 'email', label: 'ایمیل', field: 'email', sortable: true, filterType: 'text' },
]
</script>