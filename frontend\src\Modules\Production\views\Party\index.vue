<template>
  <j-table-data-crud :columns="columns" url="production/party" :tools="Tools" />
</template>
<script setup>
import Tools from './tools.vue'

const columns = [
      {
        name: 'full_name',
        required: true,
        label: 'نام طرف حساب',
        field: 'full_name',
        sortable: true,
        filterType: 'text',

      },
      {
        name: 'display_name',
        required: true,
        label: 'نام نمایشی',
        field: 'display_name',
        sortable: true,
        filterType: 'text',

      },
      {
        name: 'mobile_number',
        required: true,
        label: 'شماره تماس',
        field: 'mobile_number',
        sortable: true,
        filterType: 'text',

      },
    ]


</script>
