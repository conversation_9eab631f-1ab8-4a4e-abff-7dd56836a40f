{"__meta": {"id": "01JX0EPBFQ8RHNWHCF0RZ4VMM2", "datetime": "2025-06-05 19:57:24", "utime": **********.024642, "method": "POST", "uri": "/api/login", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.467971, "end": **********.024656, "duration": 0.5566849708557129, "duration_str": "557ms", "measures": [{"label": "Booting", "start": **********.467971, "relative_start": 0, "end": **********.672908, "relative_end": **********.672908, "duration": 0.*****************, "duration_str": "205ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.672923, "relative_start": 0.*****************, "end": **********.024657, "relative_end": 9.5367431640625e-07, "duration": 0.***************, "duration_str": "352ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.701846, "relative_start": 0.*****************, "end": **********.704679, "relative_end": **********.704679, "duration": 0.002833127975463867, "duration_str": "2.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.021978, "relative_start": 0.***************, "end": **********.022425, "relative_end": **********.022425, "duration": 0.0004470348358154297, "duration_str": "447μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.022458, "relative_start": 0.****************, "end": **********.022477, "relative_end": **********.022477, "duration": 1.8835067749023438e-05, "duration_str": "19μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.0.1", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "api.erp.test:8000", "Timezone": "Asia/Tehran", "Locale": "fa"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00843, "accumulated_duration_str": "8.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `username` = 'admin' limit 1", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 396}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 339}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/API/AuthController.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Http\\Controllers\\API\\AuthController.php", "line": 24}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.743514, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "raasa", "explain": null, "start_percent": 0, "width_percent": 30.724}, {"sql": "delete from `sessions` where `id` = '6S0dpbhKx72IOJm6wYaWBdbOZTTcFRVqyxe8sWtT'", "type": "query", "params": [], "bindings": ["6S0dpbhKx72IOJm6wYaWBdbOZTTcFRVqyxe8sWtT"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 269}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 609}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 557}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 528}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 404}], "start": **********.002738, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:269", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 269}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=269", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "269"}, "connection": "raasa", "explain": null, "start_percent": 30.724, "width_percent": 4.27}, {"sql": "delete from `personal_access_tokens` where `personal_access_tokens`.`tokenable_type` = 'App\\\\Models\\\\User' and `personal_access_tokens`.`tokenable_id` = 1 and `personal_access_tokens`.`tokenable_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/API/AuthController.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Http\\Controllers\\API\\AuthController.php", "line": 26}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.006707, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "AuthController.php:26", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/API/AuthController.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Http\\Controllers\\API\\AuthController.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FHttp%2FControllers%2FAPI%2FAuthController.php&line=26", "ajax": false, "filename": "AuthController.php", "line": "26"}, "connection": "raasa", "explain": null, "start_percent": 34.994, "width_percent": 19.336}, {"sql": "insert into `personal_access_tokens` (`name`, `token`, `abilities`, `expires_at`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values ('LaravelSanctumAuth', '45ae6f4109aa06aa4edb9a8cc25754416386af608ba17a94b3c9f318407ca002', '[\\\"*\\\"]', '2025-06-06 19:57:24', 1, 'App\\\\Models\\\\User', '2025-06-05 19:57:24', '2025-06-05 19:57:24')", "type": "query", "params": [], "bindings": ["LaravelSanctumAuth", "45ae6f4109aa06aa4edb9a8cc25754416386af608ba17a94b3c9f318407ca002", "[\"*\"]", "2025-06-06 19:57:24", 1, "App\\Models\\User", "2025-06-05 19:57:24", "2025-06-05 19:57:24"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/laravel/sanctum/src/HasApiTokens.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php", "line": 64}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/API/AuthController.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Http\\Controllers\\API\\AuthController.php", "line": 27}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.010415, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "HasApiTokens.php:64", "source": {"index": 18, "namespace": null, "name": "vendor/laravel/sanctum/src/HasApiTokens.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FHasApiTokens.php&line=64", "ajax": false, "filename": "HasApiTokens.php", "line": "64"}, "connection": "raasa", "explain": null, "start_percent": 54.33, "width_percent": 45.67}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://api.erp.test:8000/api/login", "action_name": null, "controller_action": "App\\Http\\Controllers\\API\\AuthController@login", "uri": "POST api/login", "controller": "App\\Http\\Controllers\\API\\AuthController@login<a href=\"phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FHttp%2FControllers%2FAPI%2FAuthController.php&line=18\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api", "file": "<a href=\"phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FHttp%2FControllers%2FAPI%2FAuthController.php&line=18\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/API/AuthController.php:18-34</a>", "middleware": "api", "duration": "558ms", "peak_memory": "26MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-577287535 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-577287535\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-770085388 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-770085388\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1857266876 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">api.erp.test:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">39</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://crm.erp.test:3001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://crm.erp.test:3001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">fa-IR,fa;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1857266876\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1199680863 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1199680863\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1924112509 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 05 Jun 2025 16:27:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1924112509\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-132739059 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132739059\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://api.erp.test:8000/api/login", "controller_action": "App\\Http\\Controllers\\API\\AuthController@login"}, "badge": null}}