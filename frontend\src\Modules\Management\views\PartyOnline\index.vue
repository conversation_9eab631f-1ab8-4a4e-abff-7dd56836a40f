<template>
  <j-table-data-crud :columns="[
    //{ name: 'id', label: 'شناسه', field: 'id', sortable: true, filterType: 'number' },
    { name: 'full_name', label: 'نام و نام خانوادگی', field: 'full_name', sortable: true, filterType: 'text' },
    { name: 'username', label: 'شماره موبایل', field: 'mobile_number', sortable: true, filterType: 'text' },
    { name: 'enable', label: 'وضعیت', field: row => row.enable ? 'فعال' : 'غیرفعال', sortable: true, filterType: 'text' },
  ]" url="production/party" :tools="Tools">
    <template #body-cell-enable="props">
      <q-td v-bind="props">
        <div>
          <q-badge :color="props.row.enable ? 'green' : 'red'" rounded class="q-mr-sm" />{{ props.value }}
        </div>
      </q-td>
    </template>
  </j-table-data-crud>
</template>
<script setup>
import Tools from './tools.vue'
</script>