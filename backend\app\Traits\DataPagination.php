<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;

trait DataPagination {

    public function scopeJPaginate(Builder $query, $perPage = 10, $sortBy = 'id', $descending = 'true')
    {
        $request = request();
        $perPage = $request->input('rowsPerPage', $perPage);
        $sortBy = $sortBy ?? $request->input('sortBy');
        $descending = $descending ?? $request->input('descending');
        if ($descending == 'true' && $sortBy) {
           // $query->orderByDesc($request->input('sortBy'));
        }

        if (!$descending !== 'true' && $sortBy) {
           // $query->orderBy($request->input('sortBy') ?? 'id');
        }

        // دریافت پارامتر صفحه از request
        $page = request('page', 1);

        // اضافه کردن هر منطق سفارشی دیگر برای کوئری
        return $query->paginate($perPage, ['*'], 'page', $page);
    }
}
