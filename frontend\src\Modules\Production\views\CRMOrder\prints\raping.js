export default function (data, attributes, template) {
    let goods = data.items
        //.filter((f) => [8,18].includes(f.good.group_id))
        .map((m) => ({
            ...m,
            good_name: m.good.name,
            width: m.attributes.doorWidth * 1,
            height: m.attributes.doorHeight * 1,
            sort: [
                m.good_id,
                m.attributes.typeMaterialDoor,
                m.attributes.doorWidth,
                m.attributes.doorHeight,
            ].join("_"),
        }))
        .sortByDesc("sort");

    let attribute_columns = [];
    if (template.id == 4) {
        //بغل بازو
        goods = Object.values(
            goods
                .filter((f) => [8, 18, 24].includes(f.good.group_id))
                .map((m) => ({
                    ...m,
                    key: [m.attributes.hasEdgeOfDoor, m.attributes.hasAbzar].join("_"),
                }))
                .group("key")
        );
        attribute_columns = [
            {
                label: "کالا",
                // style: `width: 11px`,
                style: `min-width: 40px;width:40px;font-size:10px`,

                field: row => {
                    return row.good.name;
                },
            },
            {
                label: "تعداد",
                // style: `width: 11px`,
                style: `width: 20px`,
                verticalLabel: true,

                field: row => row.count,
            },
            {
                label: "رنگ",
                // style: "width:120px",
                style: `min-width: 60px;width:60px;font-size:10px`,
                field: row => row.attributes_label.pvcColor ?? "",
            },
            {
                label: "ارتفاع",
                style: "width:30px",
                verticalLabel: true,

                field: row => row.attributes_label.doorHeight ?? "",
            },
            {
                label: "عرض",
                style: "width:30px",
                verticalLabel: true,

                field: row => row.attributes_label.doorWidth ?? "",
            },
            {
                label: "جنس کالا",
                style: "width:35px;font-size:12px",
                verticalLabel: true,
                field: row => row.attributes_label.typeMaterialDoor ?? "",
            },
            {
                label: "نوع لنگه",
                style: "width:30px",
                verticalLabel: true,

                field: row => row.attributes_label.doorLengeh ?? "",
            },
            {
                label: "قطر درب",
                style: "width:25px",
                verticalLabel: true,

                field: row => row.attributes_label.doorThickness ?? "",
            },
            {
                label: "ابزار",
                field: row => row.attributes_label.hasAbzar ?? '',
                verticalLabel: true,
                style: "width: 20px",
            },
            {
                label: "پاسار بالا 13.5",
                field: row => row.attributes_label.isTopPasar13 ?? '',
                verticalLabel: true,
                style: "width: 25px",
            },
            {
                label: "ضخامت بغل بازو",
                field: row => row.attributes_label.baghalBazooThickness ?? '',
                verticalLabel: true,
                style: "width: 25px",
            },
            {
                label: "ارتفاع بغل بازو",
                field: row => row.attributes_label.baghalBazooHeight ?? '',
                verticalLabel: true,
                style: "width: 30px",
            },
            // {
            //     label: "عرض بغل بازو",
            //     field: row =>
            //         row.attributes_label.baghalBazooWidth,
            //     verticalLabel: true,
            //     style: "width: 30px",
            // },
            {
                label: "تعداد بغل بازو 13.5",
                field: row =>
                    row.attributes_label.countBaghalBazoo13 ?? '',
                verticalLabel: true,
                style: "width: 30px",
            },
            {
                label: "تعداد بغل بازو 17",
                field: row =>
                    row.attributes_label.countBaghalBazoo17 ?? '',

                verticalLabel: true,
                style: "width: 25px",
            },
            {
                label: "تعداد پاسار 13.5",
                field: row => row.attributes_label.countPasar13 ?? '',
                verticalLabel: true,
                style: "width: 25px",
            },
            {
                label: "تعداد پاسار 17",
                field: row => row.attributes_label.countPasar17 ?? '',
                verticalLabel: true,
                style: "width: 25px",
            },
            {
                label: "طول پاسار",
                field: row => row.attributes_label.pasarHeight ?? '',

                verticalLabel: true,
                style: "width: 25px",
            },
            {
                label: "تعداد قید 13.5",
                field: row => row.attributes_label.countGheyd13 ?? '',
                verticalLabel: true,
                style: "width: 25px",
            },
            {
                label: "تعداد قید 17",
                field: row => row.attributes_label.countGheyd17 ?? '',
                verticalLabel: true,
                style: "width: 25px",
            },
            {
                label: "قابلبه",
                field: row =>
                    row.attributes.hasEdgeOfDoor
                        ? row.attributes.alignEdgeOfDoor == 'threeSide'
                            ? "دارد"
                            : row.attributes_label.alignEdgeOfDoor
                        : "",

                verticalLabel: true,
                style: "width: 30px;font-size:10px",
            },
        ];
    }
    if (template.id == 5) {
        //کاور
        goods = Object.values(
            goods
                .filter(
                    (f) =>
                        ['frame', 'rokoob'].includes(f.good?.group?.key)
                        || (['door'].includes(f.good?.group?.key) && (f.attributes.hasRokoob || f.attributes.hasFrame))
                )
                .group("")
        );
        attribute_columns = [
            // {
            //     label: "کالا",
            //     // style: `width: 11px`,
            //     style: `min-width: 50px;width:50px`,

            //     field: f => {
            //         if (
            //             (['door'].includes(f.good?.group?.key) && f.attributes.hasFrame)
            //         )
            //             return "پرواز";
            //         return f.good.name;
            //     },
            // },
            {
                label: "تعداد",
                // style: `width: 11px`,
                style: `width: 25px`,
                verticalLabel: true,

                field: row => row.count,
            },
            {
                label: "رنگ",
                style: "width:120px",
                field: row => row.attributes_label.pvcColor ?? "",
            },
            {
                label: "جنس کالا",
                style: "width:50px",
                verticalLabel: true,
                field: row => row.attributes_label.typeMaterialRokoob ?? "",
            },
            {
                label: "نوع پرواز",
                style: "width:50px",
                verticalLabel: true,
                field: row => row.attributes_label.typeParvaz ?? "",
            },
            {
                label: "عرض پرواز",
                field: row => row.attributes_label.widthParvaz ?? "",
                verticalLabel: true,
                style: "width: 30px",
            },

            {
                label: "شاخه پرواز",
                field: row => row.attributes_label.perLengthParvaz ?? "",
                verticalLabel: true,
                style: "width: 30px",
            },
            {
                label: "تعداد پرواز",
                field: row => row.attributes_label.countParvaz ?? "",
                verticalLabel: true,
                style: "width: 30px",
            },
            {
                label: "نوع داخلی",
                style: "width:50px",
                verticalLabel: true,
                field: row => row.attributes_label.typeDakheli ?? "",
            },
            {
                label: "عرض داخلی",
                field: row => row.attributes_label.widthDakheli ?? "",
                verticalLabel: true,
                style: "width: 30px",
            },

            {
                label: "شاخه داخلی",
                field: row => row.attributes_label.perLengthDakheli ?? "",
                verticalLabel: true,
                style: "width: 30px",
            },

            {
                label: "تعداد داخلی",
                field: row => row.attributes_label.countDakheli ?? "",
                verticalLabel: true,
                style: "width: 30px",
            },
        ];
    }
    if (template.id == 6) {
        //چهارچوب

        goods = Object.values(
            goods
                .filter(
                    (f) =>
                        ['frame'].includes(f.good?.group?.key)
                        || (['door'].includes(f.good?.group?.key) && f.attributes.hasFrame)
                )
                .map((m) => ({
                    ...m,
                    key: [m.attributes.typeMaterialFrame, m.attributes.widthFloor].join("_"),
                }))
                .group("key")
        );

        attribute_columns = [
            {
                label: "تعداد",
                // style: `width: 11px`,
                style: `width: 25px`,
                verticalLabel: true,

                field: row => row.count,
            },
            {
                label: "رنگ",
                style: "width:120px",
                field: row => row.attributes_label.pvcColor ?? "",
            },
            {
                label: "ارتفاع چهارچوب",
                field: row => row.attributes_label.frameHeight,
                verticalLabel: true,
                style: "width: 35px",
            },
            {
                label: "عرض چهارچوب",
                field: row => row.attributes_label.frameWidth,
                verticalLabel: true,
                style: "width: 35px",
            },
            {
                label: "جنس کالا",
                style: "width:50px",
                field: row => row.attributes_label.typeMaterialFrame ?? "",
            },
            {
                label: "آستانه",
                field: row =>
                    !row.attributes.hasThreshold ? "" : row.attributes_label.hasThreshold,
                verticalLabel: true,
                style: "width: 25px",
            },
            {
                label: "کف",
                field: row => row.attributes_label.widthFloor,
                verticalLabel: true,

                style: "width: 25px",
            },
            {
                label: "شاخه پاسار چهارچوب",
                field: row => row.attributes_label.perLengthPasarFrame ?? "",
                verticalLabel: true,

                style: "width: 35px",
            },

            {
                label: "تعداد شاخه پاسار",
                field: row => row.attributes_label.countPasarFrame ?? "",
                verticalLabel: true,

                style: "width: 35px",
            },
            {
                label: "شاخه چهارچوب",
                field: row => row.attributes_label.perLengthBaoFrame ?? "",
                verticalLabel: true,

                style: "width: 35px",
            },
            {
                label: "تعداد شاخه چهارچوب",
                field: row => row.attributes_label.countBaoFrame ?? "",
                verticalLabel: true,

                style: "width: 35px",
            },
            {
                label: "قابلبه",
                field: row => row.attributes_label.thicknessEdgeOfFrame,
                verticalLabel: true,
                style: "width: 35px",
            },
        ];
    }
    const columns = [
        {
            label: "ردیف",
            verticalLabel: true,

            style: `width: 20px`,
            field: (row, index) => {
                return index + 1;
            },
        },
        {
            name: "code",
            label: "سریال",
            field: (row, index) => row.id,
            style: "width: 30px;font-size:10px",
            verticalLabel: true,
        },

        ...attribute_columns,
        // ...template.station.attributes.map((attribute) => {
        //     return {
        //         label: attribute.name,
        //         style: `width: ${attribute.id == 3 ? "120px" : "30px"}`,
        //         verticalLabel: attribute.id !== 3 ,
        //         field: row => row.attributes_label[attribute.id] ?? "",
        //     };
        // }),
        {
            label: "توضیحات",
            style: `width: 80px;min-width:80px;`,
            field: row => row.description ?? "",
        },
    ];

    const rows_data = goods
        .map((good) => {
            return `<table class="j-table w-full text-center odd-highlight verticalTable p-td-0" style="margin-top:5px">
        <tr>
        ${columns
                    .map((column) => {
                        return `<th ${!!column.verticalLabel ? 'class="text-vertical"' : ""
                            } ${column.style ? `style="${column.style}"` : ""
                            }><div style="line-height:15px">${column.label}</div></th>`;
                    })
                    .join("")}
        </tr>
        
        ${good
                    .map((row, index) => {
                        return (
                            `<tr>` +
                            columns
                                .map((column) => {
                                    return `<td ${column.style ? `style="${column.style}"` : ""
                                        }>${column.field(row, index)}</td>`;
                                })
                                .join("") +
                            `</tr>`
                        );
                    })
                    .join("")}
        
    </table>`;
        })
        .join("<br><br>");

    return `
    
    <table class="j-table w-full text-center mb-3">
        
       
        <thead>
                    <tr>
                        <td class="no-border p-0">
                            <table class="j-table w-full text-center odd-highlight">
                                <tr class="h-8">

                                    <td class="no-border w-1/3 bg-white">
                                        <div class="text-left text-sm">
                                            <b>نام نمایندگی: </b> ${data.party_name
        }
                                        </div>
                                        <div class="text-left text-sm">
                                            <b>نام مشتری: </b> ${data.customer_name
        }
                                        </div>
                                    </td>
                                    <td class="no-border w-1/3 bg-white">
                                        <img src="/images/logo-factor.png" class="h-10 m-auto" />
                                        <h6 style="text-align: center;font-weight: bolder;">برگه تولید ${template.station.name
        }</h6>
                                    </td>
                                    <td class="no-border w-1/3 bg-white text-right">
                                        <table class="ml-auto mr-0 text-sm">
                                            <tr>
                                                <th class="no-border bg-white">شناسه سفارش:</th>
                                                <td class="no-border bg-white">${data.code
        }</td>

                                            </tr>
                                            <tr>
                                                <th class="no-border bg-white">تاریخ سفارش:</th>

                                                <td class="no-border bg-white">${data.submit_date
        }</td>
                                            </tr>
                                            <tr>
                                                <th class="no-border bg-white">تاریخ سفارش:</th>

                                                <td class="no-border bg-white">${data.delivery_date
        }</td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>


                            </table>
                        </td>
                    </tr>
                   

                </thead>

        <tbody>
        <tr>
            <td colspan="10" class="no-border">${rows_data}</td>
        </tr>
        
        ${data.description
            ? `
        <tr style="margin-top:5px;">
            <th colspan="10">توضیحات</th>
            </tr>
        <tr>
            <td colspan="10">${data.description}</td>
        </tr>`
            : ""
        }
        </tbody>
    </table>
    
    
    `;
}
