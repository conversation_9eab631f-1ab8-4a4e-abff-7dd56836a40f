<?php

namespace Modules\Production\Entities;

use Modules\Good\Entities\Attribute;
use Modules\Good\Entities\AttributeGroup;
use Modules\Good\Entities\Good;
use Modules\Good\Entities\Group;
use Modules\Good\Entities\GroupPivot;

class Instruction extends BModel
{
    protected $fillable = [
        'name',
        'group_id',
    ];

    public function items()
    {
        return $this->hasMany(InstructionItem::class);
    }

    public function group()
    {
        return $this->belongsTo(Group::class);
    }
    public function groups()
    {
        return $this->belongsToMany(Group::class, GroupInstruction::class);
    }

    public function attributes()
    {
        return $this->belongsToMany(AttributeGroup::class, InstructionAttribute::class)->withPivot(['options'])->using(GroupPivot::class);
    }
    public function goods()
    {
        return $this->belongsToMany(Good::class, GoodInstruction::class);
    }
}
