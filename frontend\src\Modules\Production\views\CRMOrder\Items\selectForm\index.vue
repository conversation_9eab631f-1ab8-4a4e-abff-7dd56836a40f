<template>
    <div class="grid  gap-5">
        <select-good ref="ref_select_good" @confirm="addToTable" v-model="add" />


        <div class="flex p-2 gap-2">
            <j-btn dense icon="add" outline color="primary" :label="!$q.screen.xs ? 'افزودن' : ''" @click="onAdd()"
                @shortkey="onAdd()" v-shortkey="['insert']" />
            <template v-if="selected && selected.length > 0">
                <j-btn outline dense icon="edit" :label="!$q.screen.xs ? 'ویرایش' : ''" color="green" @click="onEdit()" />
                <j-btn outline dense icon="file_copy" :label="!$q.screen.xs ? 'کپی' : ''" color="primary"
                    @click="onCopy()" />
                <j-btn outline dense icon="delete" :label="!$q.screen.xs ? 'حذف' : ''" color="red" @click="onDelete()" />
            </template>
        </div>
        <template v-for="item, group_id in data.group(m => m.good.group_id)">
            <div v-if="groups && groups.length > 0" class="flex">
                <j-table v-model:selected="selected" :columns="columns(item, group_id)" :rows="item" separator="cell"
                    hide-bottom row-key="key" selection="single" :rows-per-page-options="[0]" dense class="flex-auto w-12">

                    <template #body-cell-price="props">
                        <q-td :props="props">
                            <detail-price v-model:price_details="props.row.price_details" v-model:price="props.row.price"
                                @update:price_details="updateData" />
                        </q-td>
                    </template>
                   
                    <template #item-price="props">
                        <detail-price v-model:price_details="props.row.price_details" v-model:price="props.row.price"
                            @update:price_details="updateData" />
                    </template>
                    <template #body-cell-count="props">
                        <q-td :props="props">
                            {{ props.row.count }}
                           
                        </q-td>
                    </template>
                    <template #body-cell-description="props">
                        <q-td :props="props">
                            {{ props.row.description }}
                            <q-popup-edit v-model="props.row.description" @update:model-value="updateData" buttons
                                v-slot="scope">
                                <q-input v-model="scope.value" dense autofocus @keyup.enter="scope.set" hide-bottom-space />
                            </q-popup-edit>
                        </q-td>
                    </template>

                    <template v-if="item && item.length > 0" #bottom-row>
                        <q-tr>
                            <q-td class="text-bold text-center" colspan="4">
                                جمع
                            </q-td>
                            
                            <q-td class="text-center">
                                {{ item.reduce((a, b) => a + b.count * 1, 0) }}
                            </q-td>
                            <template v-if="checkPermission('show price order')">

                                <q-td />
                                <q-td class="text-center">
                                    {{ String.currencyFormat(item.reduce((a, b) => a + b.count * b.price, 0)) }}
                                </q-td>
                            </template>

                            <q-td colspan="50" />
                        </q-tr>
                    </template>
                </j-table>
            </div>
        </template>


    </div>
</template>
<script>
import { computed, ref, watch } from 'vue';
import { api } from '@/boot/axios';
import SelectGood from './selectGood.vue';
import { useQuasar } from "quasar";
import DetailPrice from './detailPrice.vue';
import DetailAttribute from './detailAttribute.vue';
import { attributeColumns } from '.';
import { checkPermission } from '@/helpers';
import { useRequestStore } from '@/stores';

export default {
    props: {
        id: Number,
        data: {
            type: Array,
            default: () => []
        },
        onSubmit: {
            type: Function,
        }
    },
    setup(props, { emit }) {
        const selected = ref([])
        const ref_select_good = ref(null)
        const add = ref(false)
        const $q = useQuasar();
        const attributes = ref([])
        const groups = ref([])
        const requestStore = useRequestStore();

        api.get('good/attribute').then(res => {
            attributes.value = res.data
        })
        api.get('good/group/attributes').then(res => {
            groups.value = res
        })

        const columns = (item, group_id) => {
            if (groups.value.length == 0)
                return [];
            return [
                {
                    name: "index",
                    label: 'ردیف',
                    field: row => row.key + 1,
                    align: 'center',
                    summary: true,
                    checkbox_label: true
                },
                {
                    name: "id",
                    label: 'شناسه',
                    field: row => row.id ?? '-',
                    align: 'center',
                },
                {
                    name: "good",
                    label: "کالا/خدمات",
                    field: row => row.good.name ?? '',
                    align: 'center',
                    hasImage: true,
                    image: row => row.good?.image_src ?? '',
                    summary: true,
                    checkbox_label: true,
                },
                {
                    name: "count",
                    label: "تعداد",
                    field: "count",
                    align: 'center',
                    summary: true

                }, {
                    name: "price",
                    label: "قیمت",
                    field: row => Intl.NumberFormat().format(row.price) + ' ریال',
                    align: 'center',
                    summary: true,
                    permissions: 'show price order',

                }
                , {
                    name: "sum_price",
                    label: "جمع",
                    field: row => Intl.NumberFormat().format(row.price * row.count) + ' ریال',
                    align: 'center',
                    summary: true,
                    permissions: 'show price order',

                },

                ...attributeColumns({
                    attributes: groups.value[groups.value.findIndex(f => f.id == group_id)]?.attributes, items: item, extra: {
                        //hide_table: true
                    }
                }),
                {
                    name: "description",
                    label: "توضیحات",
                    field: "description",
                    align: 'center',

                }
            ];

        }
        const data = ref(props.data.map((m, i) => ({ ...m, key: i })) ?? [])
        watch(() => props.data, (newVal) => {
            data.value = newVal.map((m, i) => ({ ...m, key: i }))
        })



        // const conditions = ref([])
        // api.get('good/condition').then(res => {
        //     conditions.value = res.result
        // })
        const updateData = () => {
            // console.log('updateData')
            emit('update:data', data.value)
            //props.onSubmit()
        }

        const addToTable = (value, is_edit) => {
            // console.log('addToTable',value)
            // const find = data.value.findIndex(f => f.good_id == value.good_id && JSON.stringify(f.attributes) == JSON.stringify(value.attributes))
            // if (find >= 0)
            //     Object.assign(data.value[find], value)
            // else
            //     data.value.push(value)
            if (is_edit) {
                const find = data.value.findIndex(f => f.key == selected.value[0].key);
                if (find >= 0) {
                    Object.assign(data.value[find], value)
                }
            } else
                data.value.push({ ...value, key: data.value.length + 1 })

            emit('update:data', data.value)
            // props.onSubmit()
            selected.value = []
            add.value = false;
        }

        const onDelete = () => {
            $q.dialog({
                title: "مطمئن هستید؟",
                cancel: true,
                persistent: true,
            }).onOk(() => {
                const value = selected.value[0];
                //const find = data.value.findIndex(f => f.good_id == value.good_id && JSON.stringify(f.attributes) == JSON.stringify(value.attributes))
                const find = data.value.findIndex(f => f.key == value.key)

                if (find >= 0)
                    data.value.splice(find, 1)
                selected.value = []

                emit('update:data', data.value)
                // props.onSubmit()
            });
        }

        const onEdit = () => {
            add.value = true;
            ref_select_good.value.onEdit(selected.value[0])
        }
        const onCopy = () => {
            add.value = true;
            ref_select_good.value.onCopy(selected.value[0])
        }
        const onAdd = () => {
            add.value = true
            ref_select_good.value.onAdd()

        }
        // const attribute_columns = computed(() => {
        //     return data.value.map(m => Object.keys(m.attribute)).flat().unique()
        // })

        return {
            columns,
            data,
            add,
            attributes,
            groups,
            // conditions,
            // attribute_columns,
            addToTable,
            onEdit,
            onAdd,
            onDelete,
            onCopy,
            updateData,
            selected,
            ref_select_good,
            checkPermission,
            requestStore,

        };
    },
    components: { SelectGood, DetailPrice, DetailAttribute }
}
</script>