<template>
    <j-form-data url="roles" :form="form" hasCreateRoute>
        <template v-slot="{ form, formOptions }">

            <j-input v-model="form.label" required error-field="label" label="نام" />
            <j-input v-model="form.name" required error-field="name" label="name" />
            <j-input v-model="form.guard_name" required error-field="guard_name" label="گارد" />

            <!-- <q-list bordered dense> -->
                <q-item-label header>دسترسی</q-item-label>
                <tree :nodes="formOptions.permissions" v-model:ticked="form.permissions"
                     />
            <!-- </q-list> -->
        </template>
    </j-form-data>
</template>
<script setup>
import { ref, watch } from 'vue';

const form = ref({ permissions: [] })
import tree from './tree.vue';
</script>