<?php

namespace App\Http\Controllers\API;

use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Hash;
use Modules\Production\Entities\Station;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class CustomerController extends BaseController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return JsonResource::collection(Customer::where('is_super_admin', false)->get())->additional([
            'formOption' => [
                'permissions' => Permission::get(['id', 'label']),
                'roles' => Role::with('permissions')->get(['id', 'label']),
            ]
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required',
            'Customername' => 'required',
            'password' => 'required',
            'confirm_password' => 'required|same:password',
        ]);
        $input = $request->all();
        $input['password'] = bcrypt($input['password']);
        $model = Customer::create($input);
        if ($request->has('permissions')) $model->permissions()->sync($request->permissions);
        if ($request->has('roles')) $model->roles()->sync($request->roles);

        $model->permissions = $model->permissions()->get(['id'])->pluck('id');
        $model->roles = $model->roles()->get(['id'])->pluck('id');

        return $this->handleResponse($model, trans('request.done'));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Customer $Customer)
    {
        $Customer->permissions = $Customer->permissions()->get(['id'])->pluck('id');
        $Customer->roles = $Customer->roles()->get(['id'])->pluck('id');
        return $this->handleResponse($Customer);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Customer $Customer)
    {
        $request->validate([
            'name' => 'required',
            'Customername' => 'required',
            'confirm_password' => 'required_with:password|same:password',
        ]);

        $input = $request->all();
        if ($request->has('password')) $input['password'] = bcrypt($input['password']);

        $Customer->update($input);
        $Customer->permissions()->sync($request->permissions);
        $Customer->roles()->sync($request->roles);
        $Customer->permissions = $Customer->permissions()->get(['id'])->pluck('id');
        $Customer->roles = $Customer->roles()->get(['id'])->pluck('id');

        return $this->handleResponse($Customer, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Customer $Customer)
    {
        $Customer->delete();
    }

    /**
     * Search the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function search()
    {
        //
    }


    public function show_current(Request $request)
    {

        $Customer = $request->Customer();
        $roles = $Customer->roles()->with('permissions')->get();
        $Customer->roles = $roles->pluck('name');
        $Customer->is_membership = $Customer->membershipRequests()->where('status', 'confirm')->count() > 0;

        $Customer->permissions = array_merge($roles->pluck('permissions')->flatten()->pluck('name')->toArray(), $Customer->permissions()->get()->pluck('name')->toArray());
        return $Customer;
    }

    public function update_current(Request $request)
    {

        $request->validate([
            'confirm_password' => 'required_with:password|same:password',
        ]);
        $Customer = $request->Customer();
        if ($request->has('password')) {
            $Customer->password = Hash::make($request->input('password'));
        }
        $Customer->code_melli = $request->input('code_melli');
        $Customer->name = $request->input('name');
        $Customer->save();
        return response()->json(['message' => 'با موفقیت انجام شد!']);
    }
}
