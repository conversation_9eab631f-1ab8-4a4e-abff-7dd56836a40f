<template>
  <q-input
    ref="inputRef"
    :rules="computedRules"
    :error-message="errorMessage"
    :error="hasError"
    v-bind="inputAttrs"
    outlined 
  >
    <!-- Prepend slot -->
    <template v-if="$slots.prepend" #prepend="slotProps">
      <slot name="prepend" v-bind="slotProps || {}" />
    </template>

    <!-- Append slot -->
    <template v-if="$slots.append" #append="slotProps">
      <slot name="append" v-bind="slotProps || {}" />
    </template>

    <!-- Before slot -->
    <template v-if="$slots.before" #before="slotProps">
      <slot name="before" v-bind="slotProps || {}" />
    </template>

    <!-- After slot -->
    <template v-if="$slots.after" #after="slotProps">
      <slot name="after" v-bind="slotProps || {}" />
    </template>

    <!-- Loading slot -->
    <template v-if="$slots.loading" #loading="slotProps">
      <slot name="loading" v-bind="slotProps || {}" />
    </template>

    <!-- Default slot -->
    <template v-if="$slots.default" #default="slotProps">
      <slot name="default" v-bind="slotProps || {}" />
    </template>
  </q-input>
</template>
<script setup>
import { ref, computed, useAttrs } from 'vue';
import { useRequestStore } from '@/stores';
import { validationRules } from '@/helpers';

// Props
const props = defineProps({
  required: {
    type: Boolean,
    default: false
  },
  requiredIf: {
    type: [Boolean, Function],
    default: false
  },
  compareField: {
    type: String,
    default: ''
  },
  errorField: {
    type: String,
    default: ''
  },
  minLength: {
    type: Number,
    default: null
  },
  maxLength: {
    type: Number,
    default: null
  },
  type: {
    type: String,
    default: 'text',
    validator: (value) => ['text', 'email', 'password', 'number', 'tel'].includes(value)
  },
  customRules: {
    type: Array,
    default: () => []
  }
});

// Refs
const inputRef = ref(null);
const requestStore = useRequestStore();
const attrs = useAttrs();

// Computed
const inputAttrs = computed(() => {
  // Props that should be passed to q-input
  const qInputProps = {
    type: props.type
  };

  // Merge with attrs, but attrs takes precedence
  return {
    ...qInputProps,
    ...attrs
  };
});

// Validation computed
const computedRules = computed(() => {
  const rules = [];

  // Required validation
  if (props.required) {
    rules.push(validationRules.required());
  }

  // Required if validation
  if (props.requiredIf) {
    const condition = typeof props.requiredIf === 'function'
      ? props.requiredIf()
      : props.requiredIf;

    if (condition) {
      rules.push(validationRules.required());
    }
  }

  // Type-specific validations
  if (props.type === 'email') {
    rules.push(validationRules.email());
  }

  if (props.type === 'number') {
    rules.push(validationRules.numeric());
  }

  if (props.type === 'tel') {
    rules.push(validationRules.phone());
  }

  // Length validations
  if (props.minLength) {
    rules.push(validationRules.minLength(props.minLength));
  }

  if (props.maxLength) {
    rules.push(validationRules.maxLength(props.maxLength));
  }

  // Compare field validation
  if (props.compareField) {
    rules.push(validationRules.confirmed(props.compareField));
  }

  // Custom rules
  rules.push(...props.customRules);

  return rules;
});

const errorMessage = computed(() => {
  const fieldErrors = requestStore.errors[props.errorField];
  if (fieldErrors && fieldErrors.length > 0) {
    return Array.isArray(fieldErrors) ? fieldErrors.join(', ') : fieldErrors;
  }
  return undefined;
});

const hasError = computed(() => {
  return !!requestStore.errors[props.errorField];
});

// Methods
const focus = () => {
  if (inputRef.value) {
    inputRef.value.focus();
  }
};

const blur = () => {
  if (inputRef.value) {
    inputRef.value.blur();
  }
};

const select = () => {
  if (inputRef.value) {
    inputRef.value.select();
  }
};

// Expose methods
defineExpose({
  focus,
  blur,
  select,
  inputRef
});
</script>
