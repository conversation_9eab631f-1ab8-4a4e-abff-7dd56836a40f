<?php

namespace Modules\Production\Entities;

use App\Models\SmsToken;
use App\Traits\DataFilter;
use App\Traits\DataPagination;
use App\Traits\ModuleTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Spatie\Permission\Traits\HasRoles;



class Party extends Authenticatable
{
   use HasApiTokens, HasFactory, Notifiable, LogsActivity, HasRoles, SoftDeletes, DataFilter, DataPagination, ModuleTrait;
   protected $guard = 'crm';
   protected static function newFactory()
    {
        return \Modules\Production\Database\factories\PartyFactory::new();
    }
   // public function getTable()
   // {
   //    return $this->table ?? (config('production.prefix') ? config('buyandsell.prefix') . '__' : '') . Str::snake(Str::pluralStudly(class_basename($this)));
   // }
   protected $fillable = [
      'full_name',
      'display_name',
      //'phone_number',
      'address',

      'landline_number',
      'city_id',
      'national_code',
      'province_id',
      'second_name',
      'location',
      'image',
      'status',
      'enable',
      'password',
      'mobile_number',
   ];

   protected $casts = [
      'location' => 'array',
      'enable' => 'boolean',
   ];

   protected $hidden = [
      'password',
      'remember_token',
  ];
   const NULL = null;
   const PENDING = 'PENDING';
   const CONFIRM = 'CONFIRM';

   public static $statuses = [
      [
         'value' => self::NULL,
         'label' => 'عدم تایید',
      ],
      [
         'value' => self::PENDING,
         'label' => 'در انتظار تایید',
      ],
      [
         'value' => self::CONFIRM,
         'label' => 'تایید شده',
      ],

   ];
   protected $appends = [
      'label_status',
   ];
   public function getLabelStatusAttribute()
   {
      return collect(self::$statuses)->where('value', $this->status)->pluck('label')->first();
   }

   public function getActivitylogOptions(): LogOptions
   {
      return LogOptions::defaults()->logAll();
   }

   public function smsTokens()
   {
      return $this->morphMany(SmsToken::class, 'tokenable');
   }
   public function getDisplayNameAttribute()
   {
      return $this->attributes['display_name'] ?? $this->attributes['full_name'];
   }

   // public static function getTableName()
   //  {
   //      return (new static)->getTable();
   //  }
}
