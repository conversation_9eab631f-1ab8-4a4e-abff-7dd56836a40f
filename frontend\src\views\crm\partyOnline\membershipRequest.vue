<template>
    <q-tabs v-model="tab" dense align="left" :breakpoint="0">
        <q-tab name="form" label="مشخصات" />
        <q-tab name="permission" label="دسترسی ها" />
    </q-tabs>
    <q-separator />
    <q-tab-panels v-model="tab" animated>
        <q-tab-panel name="form">
            <membership-request-form :form="form" :formOption="formOption">
                <j-select v-model="form.status" :options="formOption.statuses" required class="sm:col-span-2" />
            </membership-request-form>
        </q-tab-panel>
        <q-tab-panel name="permission">
            <q-item-label header>سمت</q-item-label>

            <div class="grid gap-2 sm:grid-cols-5">
                <q-checkbox v-for="option, index in formOption.roles" v-model="form.roles" :val="option.id"
                    :label="option.label" :key="index" />
            </div>
            <hr>
            <q-item-label header>دسترسی</q-item-label>
            <div class="grid gap-2 sm:grid-cols-5">
                <q-checkbox
                    v-for="option, index in formOption.permissions.filter(per => !formOption.roles.filter(f => form.roles && form.roles.includes(f.id)).map(m => m.permissions.map(mm => mm.id)).flat().unique().includes(per.id))"
                    v-model="form.permissions" :val="option.id" :label="option.label" :key="index" />
            </div>
        </q-tab-panel>
    </q-tab-panels>
</template>
<script setup>
import { ref } from 'vue'
import membershipRequestForm from '@/views/crm/partyOnline/membershipRequestForm.vue'
const { form } = defineProps({
    form: {
        type: Object,
        default: () => { }
    },
    formOption: {
        type: Object,
        default: () => { }
    },
})
const tab = ref('form')
</script>
