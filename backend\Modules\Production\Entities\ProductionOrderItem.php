<?php

namespace Modules\Production\Entities;

use App\Casts\ObjectCast;
use Modules\Good\Entities\Good;
use Modules\Good\Entities\Group;

class ProductionOrderItem extends BModel
{
   protected $fillable = [
      'code',
      'production_order_id',
      'good_id',
      'attributes',
      'count',
      'price',
      'total_price',
      'price_details',
      'description',
   ];

   protected $casts = [
      'attributes' => ObjectCast::class,
      'price_details' => 'array',

   ];

   public function good()
   {
      return $this->belongsTo(Good::class);
   }

  

   public function productionOrder()
   {
      return $this->belongsTo(ProductionOrder::class);
   }

   public function getLabelAttributes($attributes)
   {
      $res = [];
      foreach ($this->getAttribute('attributes') as $atttribute_id => $order_attribute) {
         foreach ($attributes as  $attribute) {
            if ($atttribute_id == $attribute['key']) {
               $item_id = null;
               $item_key = null;
               $value = null;
               switch ($attribute['type']) {
                  case 'SELECT':
                  case 'SELECT_IMAGE':
                     $item_id = $order_attribute;
                     $item_key = $order_attribute;
                     $value = $order_attribute;
                     foreach ($attribute['items'] as $item) {
                        if ($item['key'] == $order_attribute) {
                           $value = $item['name'];
                           $item_key =$item['key'];

                           break;
                        }
                     }
                     break;
                  case 'SWITCH':
                     $value = $order_attribute ? 'دارد' : '';
                     break;
                  case 'NUMBER':
                     $value = $order_attribute * 1;
                     break;
                  case 'FILE':
                  case 'INPUT':
                     $value = $order_attribute;
               }

               $res[$attribute['key']] = [
                  'attribute_id' => $attribute['id'],
                  'attribute_name' => $attribute['name'],
                  'attribute_key' => $attribute['key'],
                  'attribute_type' => $attribute['type'],
                  'item_id' => $item_id,
                  'item_key' => $item_key,
                  'value' => $order_attribute,
                  'label' => $value,
                  // 'name' => $attribute['name'],
                  // 'key' => $attribute['key'],
                  // 'label' => $value,
               ];
               break;
            }
         }
      }
      return $res;


      // $order_attributes = collect($this->getAttribute('attributes'));
      // return $order_attributes->map(function ($order_attribute, $atttribute_id) use ($attributes) {
      //    $attribute = $attributes->where('id', $atttribute_id)->first();
      //    if ($attribute) {
      //       $value = null;
      //       switch ($attribute->type) {
      //          case 'SELECT':
      //          case 'SELECT_IMAGE':
      //             $value = $attribute->items->where('id', $order_attribute)->pluck('name')->first();
      //             break;
      //          case 'SWITCH':
      //             $value = $order_attribute ? 'دارد' : '';
      //             break;
      //          case 'NUMBER':
      //             $value = $order_attribute * 1;
      //             break;
      //          case 'FILE':
      //          case 'INPUT':
      //             $value = $order_attribute;
      //       }

      //       return [
      //          'id' => $atttribute_id,
      //          'value' => $order_attribute,
      //          'name' => $attribute->name,
      //          'label' => $value,
      //       ];
      //    }
      // });
   }
   // public function instructionItems()
   // {
   //    return $this->hasMany(Instruction::class)->where('group_id', $this->good?->group_id);
   // }
   public function productionOrderItemChecklist()
   {
      return $this->hasMany(ProductionOrderItemChecklist::class);
   }
   public function productionOrderItemWorkInstruction()
   {
      return $this->hasMany(ProductionOrderItemWorkInstruction::class);
   }
}
