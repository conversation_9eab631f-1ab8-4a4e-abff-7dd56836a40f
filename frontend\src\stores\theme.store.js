import { defineStore } from 'pinia';
import { Dark } from 'quasar';
import { THEME_MODES, STORAGE_KEYS } from '@/constants';
import { getCurrentTheme, applyTheme } from '@/config/themes';

export const useThemeStore = defineStore({
    id: 'theme',
    state: () => ({
        mode: THEME_MODES.AUTO, // 'light', 'dark', 'auto'
        isDark: false,
        settings: {
            density: 'comfortable', // 'compact', 'comfortable', 'spacious'
            borderRadius: 'medium', // 'small', 'medium', 'large'
            fontSize: 'medium', // 'small', 'medium', 'large'
            animations: true,
            highContrast: false,
            reducedMotion: false
        },
        colors: getCurrentTheme().colors,
        initialized: false
    }),

    getters: {
        currentTheme: (state) => state.isDark ? 'dark' : 'light',

        isAutoMode: (state) => state.mode === THEME_MODES.AUTO,

        themeClasses: (state) => ({
            [`theme-density-${state.settings.density}`]: true,
            [`theme-radius-${state.settings.borderRadius}`]: true,
            [`theme-font-${state.settings.fontSize}`]: true,
            'theme-animations-disabled': !state.settings.animations,
            'theme-high-contrast': state.settings.highContrast,
            'theme-reduced-motion': state.settings.reducedMotion
        })
    },

    actions: {
        // Initialize theme system
        init() {
            if (this.initialized) return;

            // Load saved settings first
            this.loadSettings();

            // Determine initial theme state
            this.determineInitialTheme();

            // Apply theme immediately
            this.applyTheme();

            // Set up system theme detection
            this.setupSystemThemeDetection();

            this.initialized = true;
        },

        // Determine initial theme state
        determineInitialTheme() {
            switch (this.mode) {
                case THEME_MODES.LIGHT:
                    this.isDark = false;
                    break;
                case THEME_MODES.DARK:
                    this.isDark = true;
                    break;
                case THEME_MODES.AUTO:
                default:
                    // Use system preference
                    if (typeof window !== 'undefined' && window.matchMedia) {
                        this.isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                    } else {
                        this.isDark = false; // fallback
                    }
                    break;
            }
        },

        // Load settings from localStorage
        loadSettings() {
            try {
                const savedTheme = localStorage.getItem(STORAGE_KEYS.THEME);
                if (savedTheme) {
                    const parsed = JSON.parse(savedTheme);
                    this.mode = parsed.mode || THEME_MODES.AUTO;
                    this.settings = { ...this.settings, ...parsed.settings };
                    this.colors = { ...this.colors, ...parsed.colors };
                }
            } catch (error) {
                console.warn('Failed to load theme settings:', error);
            }
        },

        // Save settings to localStorage
        saveSettings() {
            try {
                const themeData = {
                    mode: this.mode,
                    settings: this.settings,
                    colors: this.colors
                };
                localStorage.setItem(STORAGE_KEYS.THEME, JSON.stringify(themeData));
            } catch (error) {
                console.warn('Failed to save theme settings:', error);
            }
        },

        // Set up system theme detection
        setupSystemThemeDetection() {
            if (typeof window !== 'undefined' && window.matchMedia) {
                const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

                // Initial check
                this.updateSystemTheme(mediaQuery.matches);

                // Listen for changes
                mediaQuery.addEventListener('change', (e) => {
                    this.updateSystemTheme(e.matches);
                });
            }
        },

        // Update system theme preference
        updateSystemTheme(systemPrefersDark) {
            if (this.mode === THEME_MODES.AUTO) {
                this.isDark = systemPrefersDark;
                this.applyTheme();
            }
        },

        // Apply theme to Quasar and document
        applyTheme() {
            // Set Quasar dark mode
            Dark.set(this.isDark);

            // Apply domain-specific theme
            applyTheme();

            // Apply CSS classes to body (wait for DOM if needed)
            const applyBodyClasses = () => {
                const body = document.body;
                if (!body) return false;

                // Remove existing theme classes
                body.classList.remove('body--light', 'body--dark');

                // Add current theme class
                body.classList.add(this.isDark ? 'body--dark' : 'body--light');

                return true;
            };

            // Try to apply immediately, if DOM not ready, wait
            if (!applyBodyClasses()) {
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', applyBodyClasses);
                } else {
                    // DOM is ready but body not available yet
                    setTimeout(applyBodyClasses, 0);
                }
            }

            // Apply custom CSS variables
            this.applyCSSVariables();
        },

        // Apply CSS variables for theming
        applyCSSVariables() {
            const applyVariables = () => {
                const root = document.documentElement;
                if (!root) return false;

                // Apply color variables
                Object.entries(this.colors).forEach(([key, value]) => {
                    root.style.setProperty(`--q-${key}`, value);
                });

                // Apply density variables
                const densityValues = {
                    compact: '0.8',
                    comfortable: '1',
                    spacious: '1.2'
                };
                root.style.setProperty('--theme-density', densityValues[this.settings.density]);

                // Apply border radius variables
                const radiusValues = {
                    small: '4px',
                    medium: '8px',
                    large: '12px'
                };
                root.style.setProperty('--theme-border-radius', radiusValues[this.settings.borderRadius]);

                // Apply font size variables
                const fontSizeValues = {
                    small: '0.875',
                    medium: '1',
                    large: '1.125'
                };
                root.style.setProperty('--theme-font-scale', fontSizeValues[this.settings.fontSize]);

                return true;
            };

            // Try to apply immediately, if DOM not ready, wait
            if (!applyVariables()) {
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', applyVariables);
                } else {
                    setTimeout(applyVariables, 0);
                }
            }
        },

        // Toggle dark mode
        toggleDarkMode() {
            if (this.mode === THEME_MODES.AUTO) {
                // Switch to manual mode
                this.mode = this.isDark ? THEME_MODES.LIGHT : THEME_MODES.DARK;
            } else {
                // Toggle between light and dark
                this.mode = this.mode === THEME_MODES.DARK ? THEME_MODES.LIGHT : THEME_MODES.DARK;
            }

            this.setThemeMode(this.mode);
        },

        // Set specific theme mode
        setThemeMode(mode) {
            this.mode = mode;

            switch (mode) {
                case THEME_MODES.LIGHT:
                    this.isDark = false;
                    break;
                case THEME_MODES.DARK:
                    this.isDark = true;
                    break;
                case THEME_MODES.AUTO:
                    // Use system preference
                    if (typeof window !== 'undefined' && window.matchMedia) {
                        this.isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                    }
                    break;
            }

            this.applyTheme();
            this.saveSettings();
        },

        // Update theme settings
        updateSettings(newSettings) {
            this.settings = { ...this.settings, ...newSettings };
            this.applyTheme();
            this.saveSettings();
        },

        // Update theme colors
        updateColors(newColors) {
            this.colors = { ...this.colors, ...newColors };
            this.applyTheme();
            this.saveSettings();
        },

        // Reset to default settings
        resetToDefaults() {
            this.mode = THEME_MODES.AUTO;
            this.settings = {
                density: 'comfortable',
                borderRadius: 'medium',
                fontSize: 'medium',
                animations: true,
                highContrast: false,
                reducedMotion: false
            };
            this.colors = {
                primary: '#1976d2',
                secondary: '#26a69a',
                accent: '#9c27b0',
                positive: '#21ba45',
                negative: '#c10015',
                info: '#31ccec',
                warning: '#f2c037'
            };

            this.setThemeMode(THEME_MODES.AUTO);
        },

        // Export settings
        exportSettings() {
            return {
                mode: this.mode,
                settings: this.settings,
                colors: this.colors
            };
        },

        // Import settings
        importSettings(themeData) {
            if (themeData.mode) this.mode = themeData.mode;
            if (themeData.settings) this.settings = { ...this.settings, ...themeData.settings };
            if (themeData.colors) this.colors = { ...this.colors, ...themeData.colors };

            this.applyTheme();
            this.saveSettings();
        }
    }
});
