<!-- NumberInput.vue -->
<template>
  <q-input
    ref="inputRef"
    v-model.number="localValue"
    type="number"
    :rules="computedRules"
    :min="min"
    :max="max"
    v-bind="$attrs"
    @update:model-value="handleInput"
  >
    <template v-for="(_, slot) in $slots" #[slot]="scope">
      <slot :name="slot" v-bind="scope || {}" />
    </template>
  </q-input>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0
  },
  min: {
    type: Number,
    default: -Infinity
  },
  max: {
    type: Number,
    required: true
  },
  rules: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:model-value']);

const localValue = ref(props.modelValue);
const inputRef = ref(null);

// ترکیب قوانین پیشفرض با قوانین دلخواه کاربر
const computedRules = computed(() => [
  ...props.rules,
  val => val >= props.min || `عدد نباید کمتر از ${props.min} باشد`,
  val => val <= props.max || `عدد نباید بیشتر از ${props.max} باشد`
]);

// اعتبارسنجی لحظه‌ای
const handleInput = (value) => {
  if (value > props.max) {
    localValue.value = props.max;
  } else if (value < props.min) {
    localValue.value = props.min;
  }
  emit('update:model-value', localValue.value);
};

// رصد تغییرات در modelValue خارجی
watch(() => props.modelValue, (newVal) => {
  localValue.value = newVal;
});

// اعتبارسنجی عمومی
const validate = () => inputRef.value.validate();

defineExpose({ validate });
</script>