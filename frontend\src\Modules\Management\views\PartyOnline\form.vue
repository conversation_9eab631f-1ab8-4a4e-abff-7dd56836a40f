<template>
    <j-form-data url="production/party" :form="form" hasCreateRoute>
        <template v-slot="{ form, formOptions }">
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <j-input v-model="form.full_name" required label="نام و نام خانوادگی" error-field="full_name"
                    autofocus />
                <j-input v-model="form.mobile_number" required label="شماره موبایل" error-field="mobile_number" />
                <j-input v-model="form.password" :required="!form?.id" :type="isPwd ? 'password' : 'text'"
                    label="کلمه عبور جدید" error-field="password">
                    <template v-slot:append>
                        <q-icon :name="isPwd ? 'visibility_off' : 'visibility'" class="cursor-pointer"
                            @click="isPwd = !isPwd" />
                    </template>
                </j-input>
                <j-input v-model="form.confirm_password"
                    :type="isPwd ? 'password' : 'text'" label="تکرار کلمه
                عبور جدید" error-field="confirm_password">
                    <template v-slot:append>
                        <q-icon :name="isPwd ? 'visibility_off' : 'visibility'" class="cursor-pointer"
                            @click="isPwd = !isPwd" />
                    </template>
                </j-input>
                <j-toggle v-model="form.enable" label="فعال" />
            </div>
        </template>




    </j-form-data>

</template>

<script setup>
import { ref } from 'vue';
const form = ref({ roles: [], permissions: [] })
const isPwd= ref(false)
</script>