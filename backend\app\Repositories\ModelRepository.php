<?php

namespace App\Repositories;

use DB;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Modules\Production\Entities\Station;

class ModelRepository implements ModelRepositoryInterface
{

    public $model;
    public function query()
    {

        return $this->handleFilter(
            $this->model::query()->orderBy(request('sortBy') ?? 'id', request('descending') == 'true' ? 'desc' : 'asc')
        );
    }
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function all($additional = [], $relations = null)
    {
        $query = $this->query();
        if ($relations)
            $query->with($relations);
        
        return JsonResource::collection($query
            // ->when($sort, function($q){
            //     dd(request('sorts'));
            // })
            // ->when(!$sort, function($q){
            //     $q->orderByDesc('id');
            // })
            ->paginate(request('rowsPerPage') == 0 ? 1000 : request('rowsPerPage')))->additional($additional);
    }

    /**
     * Show the specified resource.
     * @param Model $model
     * @return Response
     */
    public function find(Model $model)
    {
        return $this->handleResponse($model);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function create($input = null)
    {
        $model = $this->model::create($input ?? request()->all());
        return $this->handleResponse($model, trans('request.done'));
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param Model $model
     * @return Response
     */
    public function update(Model $model, $input = null)
    {
        $model->update(Arr::only($input ?? request()->all(), $model->getFillable()));
        return $this->handleResponse($model, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     * @param Model $model
     * @return Response
     */
    public function delete(Model $model)
    {
        return $model->delete();
    }

    /**
     * search the specified resource from storage.
     * @return Response
     */
    public function search($filters, $orderBy = 'id')
    {
        $query = $this->model::query();
        return $this->handleResponse($this->handleFilter($query->orderBy($orderBy), $filters)->get());
    }


    public function handleResponse($result, $msg = null)
    {
        $res = [
            'ok' => true,
            'result'    => $result,
        ];
        if ($msg)
            $res['message'] = $msg;

        return response()->json($res, 200);
    }
    public function handleError($error, $errorMsg = [], $code = 404)
    {
        $res = [
            'ok' => false,
            'message' => $error,
        ];
        if (!empty($errorMsg)) {
            $res['result'] = $errorMsg;
        }
        return response()->json($res, $code);
    }
    public static function handleFilter($query, $filters = null)
    {
        $filters = $filters ?? request()->input();
        if (!$filters)
            return $query;

        foreach ($filters as $column => $uncode_filter) {
            try {

                $filter = !is_string($uncode_filter) ? $uncode_filter : json_decode($uncode_filter, true);
            } catch (\Throwable $th) {
                continue;
            }
            if (!$filter || !isset($filter['value']) || (!$filter['value'] && $filter['value'] !== false))
                continue;


            $column = $filter['column'] ?? $column;
            if (isset($filter['relation'])) {
                $relation = $filter['relation'];
                unset($filter['relation']);
                $value = [
                    $column => $filter
                ];

                $query->whereHas($relation, function ($query) use ($value) {
                    return self::handleFilter($query, $value);
                });
                continue;
            }

            $attribute_item = explode('.', $column)[1] ?? null;
            $column = explode('.', $column)[0];

            switch ($filter['type']) {
                case 'FilterInput':
                    if (isset($filter['value']))
                        $query->where($column, 'like', "%" . ($attribute_item ? '"' . $attribute_item . '":"' : '') . ($filter['value']) . "%");
                    break;

                case 'FilterSwitch':
                    if (isset($filter['value']))
                        $query->where($column, 'like', "%" . ($attribute_item ? '"' . $attribute_item . '":' . ($filter['value'] ? 'true' : 'false') : ($filter['value'] ? 1 : 0))  . "%");
                    break;

                case 'FilterDate':
                    if (isset($filter['value']['date_of']))
                        $query->where($column, '>=', Verta::parse($filter['value']['date_of'], 'GMT')->formatGregorian('Y-m-d H:i:s'));
                    if (isset($filter['value']['date_to']))
                        $query->where($column, '<=', Verta::parse($filter['value']['date_to'], 'GMT')->formatGregorian('Y-m-d 23:59:59'));
                    break;

                case 'FilterSelect':
                    if (isset($filter['value'])) {
                        if (!is_array($filter['value']))
                            $filter['value'] = [$filter['value']];

                        if ($attribute_item) {
                            $query->where(function ($query) use ($column, $attribute_item, $filter) {
                                foreach ($filter['value'] as $f) {
                                    $query->orwhere($column, 'like', "%" . ($attribute_item ? '"' . $attribute_item . '":' : '') . $f . "%");
                                }
                            });
                        } else
                            $query->whereIn($column, $filter['value']);
                    }


                    break;
            }
        }
        return $query;
    }
}
