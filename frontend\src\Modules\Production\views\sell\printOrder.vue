<template>
    <j-btn v-if="selected && selected.status == 'PREORDER' && authStore?.user?.is_customer" dense icon="verified"
        title="تایید سفارش" color="green" @click="showDialogPrint()" />

    <j-btn v-else-if="selected && selected.status == 'DRAFT' && authStore?.user?.is_customer" dense icon="ios_share"
        title="ارسال به کارخانه" color="secondary" @click="showDialogPrint()" />

    <j-btn v-else-if="selected" dense icon="print" title="پرینت" color="secondary" @click="showDialogPrint()"
        @shortkey="showDialogPrint()" v-shortkey="{ en: ['shift', 'p'], fa: ['shift', 'ح'] }" />


    <j-dialog-bar v-model="dialogPrint" persistent content-class="p-0">

        <template #title>پرینت</template>
        <template #bar>
            <j-btn v-if="!['DRAFT', 'SEND_DRAFT', 'PREORDER'].includes(data.status) && checkRole(['sell_person'])" flat
                dense icon="print" label="پرینت تولید" title="پرینت تولید">
                <q-menu anchor="bottom start">
                    <q-list dense>
                        <template v-for="template, index in templates" :key="index">
                            <!-- <j-btn flat dense icon="print" :label="template.station.name"
                                :title="'پرینت ' + template.station.name"
                                /> -->
                            <q-item clickable v-close-popup @click="printTemplate(data, attributes, template)">
                                <q-item-section>{{ template.station.name }}</q-item-section>
                            </q-item>
                        </template>

                    </q-list>
                </q-menu>
            </j-btn>
            <!-- <j-btn flat dense icon="print" label="برگه تولید" @click="printTemplate(data, attributes)" /> -->
            <j-btn v-if="!['DRAFT', 'SEND_DRAFT', 'PREORDER'].includes(data.status)" flat dense icon="print"
                label="پرینت سفارش" title="پرینت سفارش" @click="print('content')" />
            <j-btn
                v-if="!['DRAFT', 'SEND_DRAFT', 'PREORDER'].includes(data.status) && checkRole(['sell_person']) && (['FINANCIAL_APPROVAL', 'PRODUCTION'].includes(data.status) || checkPermission('print_label'))"
                flat dense icon="print" label="پرینت لیبل" title="پرینت لیبل" @click="printLabel(data, attributes)" />
            <slot name="bar" />
            <checklist :selected="selected" :callback="callback" />

        </template>




        <div class="p-3" v-if="!loading">
            <div id="content" style="font-size:10px">
                <table class="j-table w-full text-center">
                    <thead>
                        <tr>
                            <td class="no-border p-0 pb-4" :colspan="columns.length + 1">
                                <table class="j-table w-full text-center odd-highlight">
                                    <tr class="h-7">

                                        <td class="no-border w-1/3 bg-white">

                                            <table class="mr-auto ml-0 text-sm">
                                                <tr>
                                                    <th class="no-border bg-white text-left w-28">شناسه سفارش: </th>
                                                    <td class="no-border bg-white text-right">{{ data.code }}</td>
                                                </tr>
                                                <tr>
                                                    <th class="no-border bg-white text-left">نام نمایندگی: </th>
                                                    <td class="no-border bg-white text-right">{{ data.party_name }}</td>
                                                </tr>
                                                <tr>
                                                    <th class="no-border bg-white text-left">نام مشتری: </th>
                                                    <td class="no-border bg-white text-right">{{ data.customer_name }}
                                                    </td>
                                                </tr>

                                            </table>

                                            <!-- <div class="text-left text-sm">
                                            <b>شناسه سفارش: </b> {{ data.code }}
                                        </div>
                                        <div class="text-left text-sm">
                                            <b>نام نمایندگی: </b> {{ data.party_name }}
                                        </div>
                                        <div class="text-left text-sm">
                                            <b>نام مشتری: </b> {{ data.customer_name }}
                                        </div> -->

                                        </td>
                                        <td class="no-border w-1/3 bg-white">
                                            <img src="/images/logo-factor.png" class="h-16 m-auto" />
                                        </td>
                                        <td class="no-border w-1/3 bg-white text-right">
                                            <table class="ml-auto mr-0 text-sm">
                                                <!-- <tr>
                                                <th class="no-border bg-white text-left w-28">شناسه سفارش:</th>
                                                <td class="no-border bg-white">{{ data.code }}</td>
                                            </tr> -->
                                                <tr v-if="data.submit_date">
                                                    <th class="no-border bg-white text-left">تاریخ سفارش:</th>
                                                    <td class="no-border bg-white">{{ data.submit_date }}</td>
                                                </tr>
                                                <tr v-if="data.delivery_date">
                                                    <th class="no-border bg-white text-left">تاریخ سفارش:</th>
                                                    <td class="no-border bg-white">{{ data.delivery_date }} ({{
                                                        data.diff_delivery_date
                                                    }} روز)</td>
                                                </tr>
                                                <tr v-if="data.user_name">
                                                    <th class="no-border bg-white text-left">نام کاربر:</th>
                                                    <td class="no-border bg-white">{{ data.user_name }}</td>
                                                </tr>
                                            </table>

                                            <!-- <div class="text-right text-sm">
                                            <b>شناسه سفارش: </b> {{ data.code }}
                                        </div>
                                        <div class="text-right text-sm">
                                            <b>تاریخ سفارش: </b> {{ data.submit_date }}
                                        </div>
                                        <div class="text-right text-sm">
                                            <b>تاریخ سفارش: </b> {{ data.delivery_date }}
                                        </div>
                                        <div class="text-right text-sm">
                                            <b>نام کاربر: </b> {{ data.user_name }}
                                        </div> -->
                                        </td>
                                    </tr>


                                </table>
                            </td>
                        </tr>

                    </thead>
                    <tbody>
                        <template v-for="doors, door_key in group_doors" :key="door_key">
                            <tr>
                                <td class="no-border p-0 pb-4" :colspan="columns.length + 1">
                                    <div class="table-border-radius">
                                        <table class="j-table w-full">
                                            <thead>

                                                <tr class="highlight">
                                                    <!-- <th class="no-border print-hidden w-10 bg-white"></th> -->
                                                    <th v-for="column, index in columns.filter(f => checkPermission(f.permissions ?? []) && !f.row && (f.group_key == undefined || f.group_key == door_key))"
                                                        :key="index" :class="{ 'text-vertical': !!column.verticalLabel }"
                                                        class="text-center" :style="column.style ?? ''">
                                                        <div>{{ column.label }}</div>
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>

                                                <template v-for="item, index in doors" :key="index">
                                                    <tr class="h-7" :class="{ highlight: index % 2 > 0 }">
                                                        <!-- <td class="no-border print-hidden bg-white">
                                        </td> -->
                                                        <td v-for="column, index2 in columns.filter(f => checkPermission(f.permissions ?? []) && !f.row && (f.group_key == undefined || f.group_key == door_key))"
                                                            :key="index2" :style="column.style ?? ''" class="text-center"
                                                            :rowspan="!column.merge ? '' : columns.filter(f => f.row && (typeof f.field == 'function' ? f.field(item, index) : item[f.field]) && (f.group_key == undefined || f.group_key == door_key)).length + 1">
                                                            <image-by-text v-if="column.image" :src="column.image(item)"
                                                                :text="typeof column.field == 'function' ? column.field(item, index) : item[column.field]" />
                                                            <span v-else
                                                                v-html="typeof column.field == 'function' ? column.field(item, index) : item[column.field]"></span>
                                                        </td>
                                                    </tr>
                                                    <tr v-for="column, index2 in columns.filter(f => f.row && (typeof f.field == 'function' ? f.field(item, index) : item[f.field]) && (f.group_key == undefined || f.group_key == door_key))"
                                                        :key="index2" class="h-7" :class="{ highlight: index % 2 > 0 }">
                                                        <td :colspan="columns.filter(f => !f.row && (f.group_key == undefined || f.group_key == door_key)).length"
                                                            :style="column.style ?? ''" class="text-center">
                                                            <image-by-text v-if="column.image" :src="column.image(item)"
                                                                :text="typeof column.field == 'function' ? column.field(item, index) : item[column.field]" />
                                                            <span v-else
                                                                v-html="typeof column.field == 'function' ? column.field(item, index) : item[column.field]"></span>
                                                        </td>
                                                    </tr>
                                                </template>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                        </template>
                        <!-- <template v-if="doors.door.length > 0">
                        <tr>
                            <td class="no-border p-0" :colspan="columns.length + 1">
                                <table class="j-table w-full odd-highlight">
                                    <tr>

                                        <th class="no-border print-hidden w-10 bg-white"></th>
                                        <th v-for="column, index in columns" :key="index"
                                            :class="{ 'text-vertical': !!column.verticalLabel }" class="text-center"
                                            :style="column.style ?? ''">
                                            <div>{{ column.label }}</div>
                                        </th>
                                    </tr>
                                    <tr v-for="item, index in doors" :key="index" class="h-7">
                                        <td class="no-border print-hidden bg-white">
                                        </td>
                                        <td v-for="column, index2 in columns" :key="index2" :style="column.style ?? ''"
                                            class="text-center">
                                            <image-by-text v-if="column.image" :src="column.image(item)"
                                                :text="typeof column.field == 'function' ? column.field(item, index) : item[column.field]" />
                                            <span v-else
                                                v-html="typeof column.field == 'function' ? column.field(item, index) : item[column.field]"></span>
                                        </td>

                                    </tr>
                                    <template
                                        v-if="doors.length + (data.description ? 2 : 0) + (rokoob.length ? rokoob.length + 1 : 0) + (shakheh.length ? shakheh.length + 1 : 0) + (others.length ? others.length + 1 : 0) < 8">
                                        <tr v-for="index in 8 - (doors.length + (data.description ? 2 : 0) + (rokoob.length ? rokoob.length + 1 : 0) + (shakheh.length ? shakheh.length + 1 : 0) + (others.length ? others.length + 1 : 0))"
                                            :key="index" class="h-7">
                                            <td class="no-border print-hidden bg-white">

                                            </td>
                                            <td v-for="column, index2 in columns" :key="index2"
                                                :style="column.style ?? ''">

                                            </td>

                                        </tr>
                                    </template>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td class="no-border h-3"></td>
                        </tr>
                    </template> -->


                        <tr v-if="frames.length > 0">
                            <td class="w-full no-border p-0 pb-4 align-top" :colspan="columns.length / 2 + 1">
                                <div class="table-border-radius">

                                    <table class="j-table w-full">
                                        <thead>
                                            <tr class="highlight">
                                                <th v-for="column, index in frame_columns.filter(f => checkPermission(f.permissions ?? []))"
                                                    :key="index" :class="{ 'text-vertical': !!column.verticalLabel }"
                                                    class="text-center" :style="column.style ?? ''">
                                                    <div>{{ column.label }}</div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="item, index in frames" :key="index" class="h-7"
                                                :class="{ highlight: index % 2 > 0 }">

                                                <td v-for="column, index2 in frame_columns" :key="index2"
                                                    class="text-center" :style="column.style ?? ''">

                                                    <span
                                                        v-html="typeof column.field == 'function' ? column.field(item, index) : item[column.field]"></span>
                                                </td>

                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr v-if="rokoob.length > 0">
                            <td class="w-full no-border p-0 pb-4 align-top" :colspan="columns.length / 2 + 1">
                                <div class="table-border-radius">

                                    <table class="j-table w-full">
                                        <thead>

                                            <tr class="highlight">
                                                <th v-for="column, index in rokoob_columns" :key="index"
                                                    :class="{ 'text-vertical': !!column.verticalLabel }" class="text-center"
                                                    :style="column.style ?? ''">
                                                    <div>{{ column.label }}</div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="item, index in rokoob" :key="index" class="h-7"
                                                :class="{ highlight: index % 2 > 0 }">

                                                <td v-for="column, index2 in rokoob_columns" :key="index2"
                                                    class="text-center" :style="column.style ?? ''">

                                                    <span
                                                        v-html="typeof column.field == 'function' ? column.field(item, index) : item[column.field]"></span>
                                                </td>

                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr v-if="maghta.length > 0">
                            <td class="w-full no-border p-0 pb-4 align-top" :colspan="columns.length / 2 + 1">
                                <div class="table-border-radius">

                                    <table class="j-table w-full">
                                        <thead>

                                            <tr class="highlight">
                                                <th v-for="column, index in maghta_columns" :key="index"
                                                    :class="{ 'text-vertical': !!column.verticalLabel }" class="text-center"
                                                    :style="column.style ?? ''">
                                                    <div>{{ column.label }}</div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="item, index in maghta" :key="index" class="h-7"
                                                :class="{ highlight: index % 2 > 0 }">

                                                <td v-for="column, index2 in maghta_columns" :key="index2"
                                                    class="text-center" :style="column.style ?? ''">

                                                    <span
                                                        v-html="typeof column.field == 'function' ? column.field(item, index) : item[column.field]"></span>
                                                </td>

                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr v-if="shakheh.length > 0">
                            <td class="w-full no-border p-0 pb-4 align-top" :colspan="columns.length / 2 + 1">
                                <div class="table-border-radius">

                                    <table class="j-table w-full">
                                        <thead>

                                            <tr class="highlight">
                                                <th v-for="column, index in shakheh_columns" :key="index"
                                                    :class="{ 'text-vertical': !!column.verticalLabel }"
                                                    :style="column.style ?? ''">
                                                    <div>{{ column.label }}</div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="item, index in shakheh" :key="index" class="h-7"
                                                :class="{ highlight: index % 2 > 0 }">

                                                <td v-for="column, index2 in shakheh_columns" :key="index2"
                                                    class="text-center" :style="column.style ?? ''">
                                                    <span
                                                        v-html="typeof column.field == 'function' ? column.field(item, index) : item[column.field]"></span>
                                                </td>

                                            </tr>
                                        </tbody>

                                    </table>
                                </div>
                            </td>
                        </tr>

                        <tr v-if="zehvar.length > 0">
                            <td class="w-full no-border p-0 pb-4 align-top" :colspan="columns.length / 2 + 1">
                                <div class="table-border-radius">

                                    <table class="j-table w-full">
                                        <thead>

                                            <tr class="highlight">
                                                <th v-for="column, index in zehvar_columns" :key="index"
                                                    :class="{ 'text-vertical': !!column.verticalLabel }"
                                                    :style="column.style ?? ''">
                                                    <div>{{ column.label }}</div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="item, index in zehvar" :key="index" class="h-7"
                                                :class="{ highlight: index % 2 > 0 }">

                                                <td v-for="column, index2 in zehvar_columns" :key="index2"
                                                    class="text-center" :style="column.style ?? ''">
                                                    <span
                                                        v-html="typeof column.field == 'function' ? column.field(item, index) : item[column.field]"></span>
                                                </td>

                                            </tr>
                                        </tbody>

                                    </table>
                                </div>
                            </td>
                        </tr>

                        <tr v-if="garniz.length > 0">
                            <td class="w-full no-border p-0 pb-4 align-top" :colspan="columns.length / 2 + 1">
                                <div class="table-border-radius">

                                    <table class="j-table w-full">
                                        <thead>

                                            <tr class="highlight">
                                                <th v-for="column, index in garniz_columns" :key="index"
                                                    :class="{ 'text-vertical': !!column.verticalLabel }"
                                                    :style="column.style ?? ''">
                                                    <div>{{ column.label }}</div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="item, index in garniz" :key="index" class="h-7"
                                                :class="{ highlight: index % 2 > 0 }">

                                                <td v-for="column, index2 in garniz_columns" :key="index2"
                                                    class="text-center" :style="column.style ?? ''">
                                                    <span
                                                        v-html="typeof column.field == 'function' ? column.field(item, index) : item[column.field]"></span>
                                                </td>

                                            </tr>
                                        </tbody>

                                    </table>
                                </div>
                            </td>
                        </tr>

                        <tr>
                            <td v-if="others.length > 0" class="w-full no-border p-0 pb-4 align-top"
                                :colspan="columns.length / 2 + 1">
                                <div class="table-border-radius">

                                    <table class="j-table w-full">
                                        <thead>

                                            <tr class="highlight">
                                                <th v-for="column, index in other_columns" :key="index"
                                                    :class="{ 'text-vertical': !!column.verticalLabel }" class="text-center"
                                                    :style="column.style ?? ''">
                                                    <div>{{ column.label }}</div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="item, index in others" :key="index" class="h-7"
                                                :class="{ highlight: index % 2 > 0 }">

                                                <td v-for="column, index2 in other_columns" :key="index2"
                                                    class="text-center" :style="column.style ?? ''">
                                                    <span
                                                        v-html="typeof column.field == 'function' ? column.field(item, index) : item[column.field]"></span>
                                                </td>

                                            </tr>
                                        </tbody>

                                    </table>
                                </div>
                            </td>
                        </tr>
                        <template v-if="data.description">
                            <tr class="h-8 highlight">
                                <th colspan="20">توضیحات</th>
                            </tr>
                            <tr class="h-8">
                                <td colspan="20">{{ data.description }}</td>
                            </tr>
                            <tr>

                                <td class="no-border h-3"></td>
                            </tr>
                        </template>

                    </tbody>

                </table>

                <div v-if="result.is_created_by_customer && statuses.findIndex(f => f.value == 'CONFIRM_CUSTOMER' && f.updated_at) > 0"
                    class="m-auto text-center" style="font-weight: bold;font-size: 18px;">
                    در تاریخ {{ statuses.find(f => f.value == 'CONFIRM_CUSTOMER').updated_at }} توسط نماینده مورد تایید قرار
                    گرفته
                    است.
                    <br>
                    مسئولیت تمامی این فاکتور بر عهده نمایندگی می باشد و هیچ گونه تغییرات دیگری اعمال نمی گردد.
                </div>
                <template v-if="authStore?.user?.is_customer && selected.status == 'PREORDER'">
                    <div class="m-auto text-center">
                        <j-btn label="تایید سفارش" color="green" @click="confirmCustomer" />
                    </div>
                </template>

                <template v-if="authStore?.user?.is_customer && selected.status == 'DRAFT'">
                    <div class="m-auto text-center">
                        <j-btn label="ارسال سفارش به کارخانه" color="primary" @click="sendDraft" />
                    </div>
                </template>
            </div>

        </div>


    </j-dialog-bar>
</template>
<script>
import { computed, ref, watch } from 'vue';
import { api } from '@/boot/axios';
import { print, checkPermission, checkRole } from '@/helpers'
import { printLabel, printTemplate } from './prints'
import { useAuthStore } from '@/stores';
import ImageByText from '../productionOrder/components/ImageByText.vue';
import { computeProductionOrderItem } from '../../computeProductionFormula';
import Checklist from './checklist.vue';
import { useQuasar } from "quasar";

export default {
    components: { ImageByText, Checklist },
    props: {
        selected: Object,
        callback: Function,
    },
    setup(props) {

        const data = ref({})
        const attributes = ref([])
        const templates = ref([])
        const doors = ref([])
        const group_doors = ref({})
        const frames = ref([])
        const rokoob = ref([])
        const shakheh = ref([])
        const others = ref([])
        const maghta = ref([])
        const zehvar = ref([])
        const garniz = ref([])


        const imageColor = row => {
            //  console.log(66666)
            const find = attributes.value.findIndex(f => f.key == "pvcColor")
            if (find >= 0) return attributes.value[find].items[attributes.value[find].items.findIndex(f => f.key == row.attributes.pvcColor)]?.data?.image;
            return ''
        }
        const columns = computed(() => {
            if (!data.value.items) {
                return []
            }
            return [
                {
                    name: "id",
                    label: "ردیف",
                    field: (row, index) => index + 1,
                    style: 'width: 25px;min-width: 25px',
                    verticalLabel: true,
                    merge: true,
                },
                {
                    name: "code",
                    label: "سریال",
                    field: (row, index) => row.id,
                    style: 'width: 35px;min-width: 35px',
                    verticalLabel: true,
                    merge: true,

                },
                {
                    name: "good",
                    label: "کالا/خدمات",
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    image: row => row.good.image_src || row.attributes_label?.template,
                    field: row => row.good.name,
                    merge: true,

                },
                {
                    label: "جنس",
                    field: row => row.attributes_label?.typeMaterialDoor,
                    style: 'width: 40px;width: min-40px;',

                },
                {
                    label: "ارتفاع درب",
                    field: row => row.attributes_label?.doorHeight,
                    //verticalLabel: true,
                    style: 'width: 35px;min-width: 35px;font-weight:900;border-right:2px solid',

                },
                {
                    label: "عرض درب",
                    field: row => row.attributes_label?.doorWidth,
                    //verticalLabel: true,
                    style: 'width: 35px;min-width: 35px;font-weight:900;border-left:2px solid',

                },
                // {
                //     name: "count",
                //     label: "تعداد",
                //     field: "count",
                //     //verticalLabel: true,

                //     style: 'width: 30px;min-width: 30px',

                // },
                {
                    label: "جهت درب",
                    field: row => row.attributes_label?.doorAlign,
                    //verticalLabel: true,

                    style: 'width: 40px;min-width: 40px',

                },
                {
                    label: "جای قفل",
                    field: row => row.attributes_label?.hasLockPlace,

                    //verticalLabel: true,
                    style: 'width: 25px;min-width: 25px',

                },
                {
                    label: "برجستگی",
                    field: row => row.attributes_label?.hasBarjestegi,
                    style: 'width: 25px;min-width: 25px',
                    verticalLabel: true,
                },
                {
                    label: "ابزار",
                    field: row => row.attributes_label?.hasAbzar,

                    //verticalLabel: true,
                    style: 'width: 25px;min-width: 25px',

                },
                {
                    label: "ضخامت ورق",
                    field: row => row.attributes_label?.centerLayerThickness ?? row.attributes_label?.sheetCNCThickness,
                    //verticalLabel: true,
                    style: 'width: 40px;min-width: 40px',

                },
                {
                    label: "قطر درب",
                    field: row => row.attributes_label.doorThickness,
                    //verticalLabel: true,
                    style: 'width: 25px;min-width: 25px',

                },

                {
                    label: "نوع لبه",
                    field: row => row.attributes_label?.typeEdge,
                    style: 'width: 40px;width: min-40px;',

                },
                {
                    label: "نوع لنگه",
                    field: row => row.attributes_label?.doorLengeh,
                    style: 'width: 50px;min-width: 50px',

                },
                {
                    label: "زهوار دماغه",
                    field: row => row.attributes_label.countZevardamageh > 0 ? row.attributes_label.countZevardamageh : '',
                    verticalLabel: true,
                    style: 'width: 25px;min-width: 25px',

                },
                {
                    label: "قابلبه",
                    field: row => row.attributes.alignEdgeOfDoor !== "threeSide" && row.attributes.hasEdgeOfDoor ? row.attributes_label?.alignEdgeOfDoor : row.attributes_label?.hasEdgeOfDoor,
                    style: 'width: 50px',
                },
                {
                    label: "رنگ",
                    field: row => row.attributes_label?.pvcColor,// row.attributes?.pvcColor !== 'two_color' ? row.attributes_label?.pvcColor : ["<b>نما: </b>" + row.attributes_label?.coverFrontDoor, "<b>پشت: </b>" + row.attributes_label?.coverBackDoor].join('<br>'),
                    style: 'width: 90px;min-width: 70px;font-size:12px',
                    image: imageColor,
                },
                {
                    label: "جنس چهارچوب",
                    field: row => row.attributes_label?.typeMaterialFrame,
                    group_key: 'byFrame',
                    style: 'width: 50px;border-right-width:2px',
                },
                {
                    label: "قطر دیوار",
                    field: row => row.attributes_label?.wallThickness,
                    //verticalLabel: true,
                    group_key: 'byFrame',
                    style: 'width: 30px;',
                },
                {
                    label: "کف چهارچوب",
                    field: row => row.attributes_label?.widthFloor,
                    //verticalLabel: true,
                    group_key: 'byFrame',
                    style: 'width: 25px;min-width: 25px',

                },
                {
                    label: "مکمل",
                    field: row => !row.attributes_label.hasMokammel ? '' : row.attributes_label.mokammelCount + ' شاخه ' + row.attributes_label.mokammelWidth + ' سانتی',
                    //verticalLabel: true,
                    group_key: 'byFrame',
                    style: 'width: 55px;min-width: 25px',

                },
                {
                    label: "آستانه",
                    field: row => row.attributes_label?.hasThreshold,
                    //verticalLabel: true,
                    group_key: 'byFrame',
                    style: 'width: 25px;min-width: 25px',

                },
                {
                    label: "رنگ نوار درزگیر",
                    field: row => row.attributes_label?.colorNavardarzgir,
                    // verticalLabel: true,
                    group_key: 'byFrame',
                    style: 'width: 45px;min-width: 45px',

                },
                {
                    label: "ارتفاع چهارچوب",
                    field: row => row.attributes_label?.frameHeight,
                    //verticalLabel: true,
                    group_key: 'byFrame',
                    style: 'width: 35px;font-weight:900;min-width: 35px',

                },
                {
                    label: "عرض چهارچوب",
                    field: row => row.attributes_label?.frameWidth,
                    //verticalLabel: true,
                    group_key: 'byFrame',
                    style: 'width: 35px;font-weight:900;min-width: 35px',
                },
                {
                    label: "عرض پرواز",
                    field: row => row.attributes_label?.widthParvaz,
                    // verticalLabel: true,
                    group_key: 'byFrame',
                    style: 'width: 30px;min-width: 30px',
                },
                // {
                //     label: "قد پرواز",
                //     field: row => Object.keys(row.attributes.parvazCounts).join(' , '),
                //     verticalLabel: true,
                //     group_key: 'byFrame',
                //     style: 'width: 30px;min-width: 30px',
                // },
                {
                    name: "countParvaz",
                    label: "تعداد پرواز",
                    field: row => row.attributes_label.countParvaz + ' شاخه ' + row.attributes_label.perLengthParvaz,
                    group_key: 'byFrame',
                    style: 'width: 60px;min-width: 60px',

                },
                {
                    label: "جنس روکوب",
                    field: row => row.attributes_label?.typeMaterialRokoob,
                    group_key: 'byRokoob',
                    style: 'width: 40px;border-right-width:2px',
                },
                {
                    label: "نوع پرواز",
                    field: row => row.attributes_label?.typeParvaz,
                    group_key: 'byRokoob',
                    style: 'width: 40px;min-width: 40px',

                },
                {
                    label: "عرض پرواز",
                    field: row => row.attributes_label?.widthParvaz,
                    // verticalLabel: true,
                    group_key: 'byRokoob',
                    style: 'width: 30px;min-width: 30px',

                },
                // {
                //     label: "قد پرواز",
                //     field: row => Object.keys(row.attributes.parvazCounts).join(' , '),
                //     verticalLabel: true,
                //     group_key: 'byRokoob',
                //     style: 'width: 30px;min-width: 30px',

                // },
                {
                    label: "نوع داخلی",
                    field: row => row.attributes_label?.typeDakheli,
                    group_key: 'byRokoob',
                    style: 'width: 40px;min-width: 40px',

                },
                {
                    label: "عرض داخلی",
                    field: row => row.attributes_label?.widthDakheli,
                    //verticalLabel: true,
                    group_key: 'byRokoob',
                    style: 'width: 30px;min-width: 30px',

                },
                // {
                //     label: "قد داخلی",
                //     field: row => Object.keys(row.attributes.dakheliCounts).join(' , '),
                //     verticalLabel: true,
                //     group_key: 'byRokoob',
                //     style: 'width: 30px;min-width: 30px',

                // },
                {
                    label: "تعداد پرواز",
                    field: row => row.attributes_label.countParvaz + ' شاخه ' + row.attributes_label.perLengthParvaz,
                    group_key: 'byRokoob',
                    style: 'width: 60px;min-width: 60px',

                },
                {
                    label: "تعداد داخلی",
                    field: row => row.attributes_label.countDakheli + ' شاخه ' + row.attributes_label.perLengthDakheli,
                    group_key: 'byRokoob',
                    style: 'width: 60px;min-width: 60px',

                },
                {
                    label: "واحد",
                    field: row => row.attributes_label.vahed,
                    style: 'width: 60px;min-width: 60px',
                },
                /*{
                    label: "قیمت",
                    field: row => String.currencyFormat(row.total_price),
                    permissions: 'show price order',
                    style: 'width: 70px;min-width: 70px',
                },*/
                {
                    name: "description",
                    label: "توضیحات",
                    row: true,
                    field: row => {
                        //const countZevardamageh = row.attributes.countZevardamageh > 0 ? 'زهوار دماغه : ' + row.attributes.countZevardamageh + ' عدد' : '';
                        //const mokamel = row.attributes[47] > 0 ? row.attributes[47] + ' شاخه مکمل ' + row.attributes_label.mokammelWidth + ' سانتی ' + row.attributes_label?.typeMaterialDoor : '';
                        //const parvaz = row.attributes.countParvaz > 0 ? row.attributes.countParvaz + ' شاخه پرواز ' + row.attributes_label?.perLengthParvaz + ' ' + row.attributes_label?.typeMaterialDoor : '';
                        return [
                            //countZevardamageh,
                            //mokamel,
                            // ...Object.entries(row.attributes_label_column)
                            //     .filter(f => ['61'].includes(f[0]))
                            //     .map(m => `<b>${m[1].attribute_name}</b>` + ' : ' + m[1].label),
                            (row.description ?? '')].filter(f => f).join('<br>')
                    },
                }
            ];

        })

        const maghta_columns = computed(() => {
            if (!data.value.items) {
                return []
            }
            return [
                {
                    name: "id",
                    label: "ردیف",
                    field: (row, index) => index + 1,
                    style: 'width: 25px;min-width: 25px',
                    verticalLabel: true,
                    merge: true,
                },
                {
                    name: "code",
                    label: "سریال",
                    field: (row, index) => row.id,
                    style: 'width: 35px;min-width: 35px',
                    verticalLabel: true,
                    merge: true,

                },
                {
                    name: "good",
                    label: "کالا/خدمات",
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    image: row => row.good.image_src || row.attributes_label?.template,
                    field: row => row.good.name,
                    merge: true,

                },
                {
                    label: "جنس",
                    field: row => row.attributes_label?.typeMaterialDoor,
                    style: 'width: 40px;width: min-40px;',

                },
                // {
                //     label: "ارتفاع درب",
                //     field: row => row.attributes_label?.doorHeight,
                //     //verticalLabel: true,
                //     style: 'width: 35px;min-width: 35px;font-weight:900;border-right:2px solid',

                // },
                // {
                //     label: "عرض درب",
                //     field: row => row.attributes_label?.doorWidth,
                //     //verticalLabel: true,
                //     style: 'width: 35px;min-width: 35px;font-weight:900;border-left:2px solid',

                // },
                // {
                //     name: "count",
                //     label: "تعداد",
                //     field: "count",
                //     //verticalLabel: true,

                //     style: 'width: 30px;min-width: 30px',

                // },

                // {
                //     label: "ابزار",
                //     field: row => row.attributes_label?.hasAbzar,

                //     //verticalLabel: true,
                //     style: 'width: 25px;min-width: 25px',

                // },
                {
                    label: "ضخامت ورق",
                    field: row => row.attributes_label?.centerLayerThickness ?? row.attributes_label?.sheetCNCThickness,
                    //verticalLabel: true,
                    style: 'width: 60px;min-width: 60px',

                },
                {
                    label: "قطر مقطع",
                    field: row => row.attributes_label.doorThickness,
                    //verticalLabel: true,
                    style: 'width: 80px;min-width: 80px',

                },


                {
                    label: "رنگ",
                    field: row => row.attributes_label?.pvcColor,
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    image: imageColor,
                },
                // {
                //     label: "جنس چهارچوب",
                //     field: row => row.attributes_label?.typeMaterialFrame,
                //     group_key: 'byFrame',
                //     style: 'width: 50px;border-right-width:2px',
                // },

                // {
                //     label: "کف چهارچوب",
                //     field: row => row.attributes_label?.widthFloor,
                //     //verticalLabel: true,
                //     group_key: 'byFrame',
                //     style: 'width: 25px;min-width: 25px',

                // },


                {
                    label: "واحد",
                    field: row => row.attributes_label.vahed,
                    style: 'width: 60px;min-width: 60px',
                },
                /*{
                    label: "قیمت",
                    field: row => String.currencyFormat(row.total_price),
                    permissions: 'show price order',
                    style: 'width: 70px;min-width: 70px',
                },*/
                {
                    name: "description",
                    label: "توضیحات",
                    row: true,
                    field: row => {
                        //const countZevardamageh = row.attributes.countZevardamageh > 0 ? 'زهوار دماغه : ' + row.attributes.countZevardamageh + ' عدد' : '';
                        //const mokamel = row.attributes[47] > 0 ? row.attributes[47] + ' شاخه مکمل ' + row.attributes_label.mokammelWidth + ' سانتی ' + row.attributes_label?.typeMaterialDoor : '';
                        //const parvaz = row.attributes.countParvaz > 0 ? row.attributes.countParvaz + ' شاخه پرواز ' + row.attributes_label?.perLengthParvaz + ' ' + row.attributes_label?.typeMaterialDoor : '';
                        return [
                            //countZevardamageh,
                            //mokamel,
                            // ...Object.entries(row.attributes_label_column)
                            //     .filter(f => ['61'].includes(f[0]))
                            //     .map(m => `<b>${m[1].attribute_name}</b>` + ' : ' + m[1].label),
                            (row.description ?? '')].filter(f => f).join('<br>')
                    },
                }
            ];

        })

        const frame_columns = computed(() => {
            if (!data.value.items) {
                return []
            }
            return [
                {
                    name: "id",
                    label: "ردیف",
                    field: (row, index) => index + 1,
                    style: 'width: 25px;min-width: 25px',
                    verticalLabel: true,
                },
                {
                    name: "code",
                    label: "سریال",
                    field: (row, index) => row.id,
                    style: 'width: 35px;min-width: 35px',
                    verticalLabel: true,
                },
                {
                    name: "good",
                    label: "کالا/خدمات",
                    style: 'width: 90px;min-width: 70px;font-size:12px',
                    image: row => row.good.image_src || row.attributes_label?.template,
                    field: row => row.good.name,
                },
                // {
                //     name: "count",
                //     label: "تعداد",
                //     field: "count",
                //     style: 'width: 30px;min-width: 30px',

                // },
                {
                    label: "رنگ",
                    field: row => row.attributes_label?.pvcColor,
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    image: imageColor,
                },

                {
                    label: "جنس چهارچوب",
                    field: row => row.attributes_label?.typeMaterialFrame,
                    group_key: 'byFrame',
                    style: 'width: 50px;border-right-width:2px',

                },
                {
                    label: "قطر دیوار",
                    field: row => row.attributes_label?.wallThickness,
                    group_key: 'byFrame',
                    style: 'width: 40px;',

                },
                {
                    label: "کف چهارچوب",
                    field: row => row.attributes_label?.widthFloor,
                    group_key: 'byFrame',
                    style: 'width: 45px;min-width: 25px',

                },
                {
                    label: "مکمل",
                    field: row => !row.attributes_label.hasMokammel ? '' : row.attributes_label.mokammelCount + ' شاخه ' + row.attributes_label.mokammelWidth + ' سانتی',
                    group_key: 'byFrame',
                    style: 'width: 45px;min-width: 25px',

                },
                {
                    label: "آستانه",
                    field: row => row.attributes_label?.hasThreshold,
                    group_key: 'byFrame',
                    style: 'width: 35px;min-width: 25px',

                },
                {
                    label: "رنگ نوار درزگیر",
                    field: row => row.attributes_label?.colorNavardarzgir,
                    group_key: 'byFrame',
                    style: 'width: 60px;min-width: 45px',

                },
                {
                    label: "ارتفاع چهارچوب",
                    field: row => row.attributes_label?.frameHeight,
                    group_key: 'byFrame',
                    style: 'width: 55px;font-weight:900;min-width: 35px',

                },
                {
                    label: "عرض چهارچوب",
                    field: row => row.attributes_label?.frameWidth,
                    group_key: 'byFrame',
                    style: 'width: 55px;font-weight:900;min-width: 35px',
                },
                {
                    label: "عرض پرواز",
                    field: row => row.attributes_label?.widthParvaz,
                    group_key: 'byFrame',
                    style: 'width: 50px;min-width: 30px',
                },
                // {
                //     label: "قد پرواز",
                //     field: row => row.attributes_label?.perLengthParvaz,
                //     verticalLabel: true,
                //     group_key: 'byFrame',
                //     style: 'width: 30px;min-width: 30px',
                // },
                // {
                //     name: "count",
                //     label: "تعداد پرواز",
                //     field: row => (row.count * (row.attributes_label?.countParvaz ?? 2.5)) + ' شاخه',
                //     group_key: 'byFrame',
                //     style: 'width: 40px;min-width: 40px',

                // },
                {
                    name: "count",
                    label: "تعداد پرواز",
                    field: row => row.attributes_label.countParvaz + ' شاخه ' + row.attributes_label.perLengthParvaz,
                    group_key: 'byFrame',
                    style: 'width: 80px;min-width: 60px',

                },
                {
                    label: "واحد",
                    field: row => row.attributes_label.vahed,
                    style: 'width: 60px;min-width: 60px',
                },
                /*{
                    label: "قیمت",
                    field: row => String.currencyFormat(row.total_price),
                    permissions: 'show price order',
                    style: 'width: 70px;min-width: 70px',
                },*/
                {
                    name: "description",
                    label: "توضیحات",
                    field: row => {
                        const mokamel = row.attributes[47] > 0 ? row.attributes[47] + ' شاخه مکمل ' + row.attributes_label.mokammelWidth + ' سانتی ' + row.attributes_label?.typeMaterialDoor : '';
                        //const parvaz = row.attributes.countParvaz > 0 ? row.attributes.countParvaz + ' شاخه پرواز ' + row.attributes_label?.perLengthParvaz + ' ' + row.attributes_label?.typeMaterialDoor : '';
                        return [mokamel, ...Object.entries(row.attributes_label_column)
                            .filter(f => ['61'].includes(f[0]))
                            .map(m => `<b>${m[1].attribute_name}</b>` + ' : ' + m[1].label), (row.description ?? '')].filter(f => f).join('<br>')
                    },
                    style: 'min-width: 50px;border-right-width:2px'

                }
            ];

        })

        const rokoob_columns = computed(() => {
            if (!data.value.items) {
                return []
            }
            return [
                {
                    name: "id",
                    label: "ردیف",
                    field: (row, index) => index + 1,
                    style: 'width: 25px;min-width: 25px',
                    verticalLabel: true,
                },
                {
                    name: "code",
                    label: "سریال",
                    field: (row, index) => row.id,
                    style: 'width: 35px;min-width: 35px',
                    verticalLabel: true,
                },
                {
                    name: "good",
                    label: "کالا/خدمات",
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    field: row => row.good.name ?? '',
                },
                {
                    label: "رنگ",
                    field: row => row.attributes_label?.pvcColor,
                    image: imageColor,
                    style: 'width: 70px;min-width: 70px;font-size:12px',

                },
                {
                    label: "جنس",
                    field: row => row.attributes_label?.typeMaterialRokoob ?? '-',
                    style: 'width: 50px;min-width: 50px',

                },
                {
                    label: "نوع پرواز",
                    field: row => row.attributes_label?.typeParvaz ?? '-',
                    style: 'width: 50px;min-width: 50px',

                },
                {
                    label: "عرض پرواز",
                    field: row => row.attributes_label?.widthParvaz ?? '-',
                    style: 'width: 60px;min-width: 60px',

                },
                {
                    label: "قد پرواز",
                    field: row => row.attributes_label?.perLengthParvaz ?? '-',
                    style: 'width: 60px;min-width: 60px',

                },
                {
                    label: "نوع داخلی",
                    field: row => row.attributes_label?.typeDakheli ?? '-',
                    style: 'width: 50px;min-width: 50px',

                },
                {
                    label: "عرض داخلی",
                    field: row => row.attributes_label?.widthDakheli ?? '-',
                    style: 'width: 60px;min-width: 60px',

                },
                {
                    label: "قد داخلی",
                    field: row => row.attributes_label?.perLengthDakheli ?? '-',
                    style: 'width: 60px;min-width: 60px',

                },
                // {
                //     name: "count",
                //     label: "تعداد",
                //     field: "count",
                //     style: 'width: 40px;min-width: 40px',

                // },
                {
                    name: "count",
                    label: "تعداد پرواز",
                    field: row => row.attributes_label.countParvaz > 0 ? row.attributes_label.countParvaz + ' شاخه ' : '-',

                    //field: row => !row.attributes_label?.countParvaz ? '-' : (row.count * (row.attributes_label?.countParvaz)) + ' شاخه',
                    style: 'width: 70px;min-width: 70px',

                },
                {
                    name: "count",
                    label: "تعداد داخلی",
                    field: row => row.attributes_label.countDakheli > 0 ? row.attributes_label.countDakheli + ' شاخه ' : '-',

                    //field: row => !row.attributes_label?.countDakheli ? '-' : (row.count * (row.attributes_label?.countDakheli)) + ' شاخه',
                    style: 'width: 70px;min-width: 70px',

                },
                {
                    label: "واحد",
                    field: row => row.attributes_label.vahed,
                    style: 'width: 60px;min-width: 60px',
                },
                /*{
                    label: "قیمت",
                    field: row => String.currencyFormat(row.total_price),
                    permissions: 'show price order',
                    style: 'width: 70px;min-width: 70px',
                },*/
                {
                    name: "description",
                    label: "توضیحات",
                    field: row => {
                        return [(row.description ?? '')].filter(f => f).join(' - ')
                    }
                    ,
                    style: 'min-width: 50px',

                }
            ];

        })

        const shakheh_columns = computed(() => {
            if (!data.value.items) {
                return []
            }
            return [
                {
                    name: "id",
                    label: "ردیف",
                    field: (row, index) => index + 1,
                    style: 'width: 30px;min-width: 30px',
                    verticalLabel: true,
                },
                {
                    name: "code",
                    label: "سریال",
                    field: (row, index) => row.id,
                    style: 'width: 35px;min-width: 35px',
                    verticalLabel: true,
                },
                {
                    name: "good",
                    label: "کالا/خدمات",
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    field: row => row.good.name ?? '',
                },
                {
                    label: "رنگ",
                    field: row => row.attributes_label?.pvcColor,
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    image: imageColor,

                },
                {
                    label: "جنس",
                    field: row => row.attributes_label.typeMaterialDoor,
                    style: 'width: 50px;min-width: 50px',

                },
                {
                    label: "نوع",
                    field: row => row.attributes_label?.typeRokoob,
                    style: 'width: 50px;min-width: 50px',

                },
                {
                    name: "count",
                    label: "تعداد شاخه",
                    field: "count",
                    style: 'width: 70px;min-width: 70px',

                },
                {
                    label: "واحد",
                    field: row => row.attributes_label.vahed,
                    style: 'width: 60px;min-width: 60px',
                },
                /*{
                    label: "قیمت",
                    field: row => String.currencyFormat(row.total_price),
                    permissions: 'show price order',
                    style: 'width: 70px;min-width: 70px',
                },*/
                {
                    name: "description",
                    label: "توضیحات",
                    field: "description",
                    field: row =>
                        [...Object.entries(row.attributes_label_column)
                            .filter(f => !['3', '14', '15'].includes(f[0]))
                            .map(m => `<b>${m[1].attribute_name}</b>` + ' : ' + m[1].label), (row.description ?? '')].filter(f => f).join('<br>'),

                    style: 'min-width: 50px',

                }
            ];

        })


        const zehvar_columns = computed(() => {
            if (!data.value.items) {
                return []
            }
            return [
                {
                    name: "id",
                    label: "ردیف",
                    field: (row, index) => index + 1,
                    style: 'width: 30px;min-width: 30px',
                    verticalLabel: true,
                },
                {
                    name: "code",
                    label: "سریال",
                    field: (row, index) => row.id,
                    style: 'width: 35px;min-width: 35px',
                    verticalLabel: true,
                },
                {
                    name: "good",
                    label: "کالا/خدمات",
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    field: row => row.good.name ?? '',
                },
                {
                    name: "count",
                    label: "تعداد شاخه",
                    field: row => row.attributes_label?.countLength,
                    style: 'width: 70px;min-width: 70px',

                },
                {
                    label: "رنگ",
                    field: row => row.attributes_label?.pvcColor,
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    image: imageColor,

                },
                {
                    label: "جنس",
                    field: row => row.attributes_label.typeMaterial,
                    style: 'width: 50px;min-width: 50px',

                },

                {
                    label: "ضخامت",
                    field: row => row.attributes_label?.sheetThickness ?? row.attributes_label[84],
                    style: 'width: 50px;min-width: 50px',

                },

                {
                    label: "طول",
                    field: row => row.attributes_label?.length,
                    style: 'width: 50px;min-width: 50px',

                },

                {
                    label: "عرض",
                    field: row => row.attributes_label?.width,
                    style: 'width: 50px;min-width: 50px',

                },
                {
                    label: "ابزار زهوار",
                    field: row => row.attributes_label?.abzar_zehvar ?? '',
                    style: 'width: 50px;min-width: 50px',

                },
                {
                    label: "واحد",
                    field: row => row.attributes_label.vahed,
                    style: 'width: 60px;min-width: 60px',
                },
                /*{
                    label: "قیمت",
                    field: row => String.currencyFormat(row.total_price),
                    permissions: 'show price order',
                    style: 'width: 70px;min-width: 70px',
                },*/
                {
                    name: "description",
                    label: "توضیحات",
                    field: "description",
                    field: row =>
                        [(row.description ?? '')].filter(f => f).join('<br>'),

                    style: 'min-width: 50px',

                }
            ];

        })

        const garniz_columns = computed(() => {
            if (!data.value.items) {
                return []
            }
            return [
                {
                    name: "id",
                    label: "ردیف",
                    field: (row, index) => index + 1,
                    style: 'width: 30px;min-width: 30px',
                    verticalLabel: true,
                },
                {
                    name: "code",
                    label: "سریال",
                    field: (row, index) => row.id,
                    style: 'width: 35px;min-width: 35px',
                    verticalLabel: true,
                },
                {
                    name: "good",
                    label: "کالا/خدمات",
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    field: row => row.good.name ?? '',
                },
                {
                    name: "count",
                    label: "تعداد شاخه",
                    field: row => row.attributes_label?.countLength,
                    style: 'width: 70px;min-width: 70px',

                },
                {
                    label: "رنگ",
                    field: row => row.attributes_label?.pvcColor,
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    image: imageColor,

                },
                {
                    label: "جنس",
                    field: row => row.attributes_label.typeMaterial,
                    style: 'width: 50px;min-width: 50px',

                },

                {
                    label: "ضخامت",
                    field: row => row.attributes_label?.sheetThickness ?? row.attributes_label[84],
                    style: 'width: 50px;min-width: 50px',

                },

                {
                    label: "طول",
                    field: row => row.attributes_label?.perLength,
                    style: 'width: 50px;min-width: 50px',

                },

                {
                    label: "عرض",
                    field: row => row.attributes_label?.width,
                    style: 'width: 50px;min-width: 50px',

                },


                {
                    label: "واحد",
                    field: row => row.attributes_label.vahed,
                    style: 'width: 60px;min-width: 60px',
                },
                /*{
                    label: "قیمت",
                    field: row => String.currencyFormat(row.total_price),
                    permissions: 'show price order',
                    style: 'width: 70px;min-width: 70px',
                },*/
                {
                    name: "description",
                    label: "توضیحات",
                    field: "description",
                    field: row =>
                        [(row.description ?? '')].filter(f => f).join('<br>'),

                    style: 'min-width: 50px',

                }
            ];

        })


        const other_columns = computed(() => {
            if (!data.value.items) {
                return []
            }
            return [
                {
                    name: "id",
                    label: "ردیف",
                    field: (row, index) => index + 1,
                    style: 'width: 30px;min-width: 30px',
                    verticalLabel: true,
                },
                {
                    name: "code",
                    label: "سریال",
                    field: (row, index) => row.id,
                    style: 'width: 35px;min-width: 35px',
                    verticalLabel: true,
                },
                {
                    name: "good",
                    label: "کالا/خدمات",
                    style: 'width: 70px;min-width: 70px;font-size:12px',
                    field: row => row.good.name ?? '',
                },
                // {
                //     name: "count",
                //     label: "تعداد",
                //     field: "count",
                //     style: 'width: 50px;min-width: 50px',

                // },
                {
                    label: "واحد",
                    field: row => row.attributes_label.vahed,
                    style: 'width: 60px;min-width: 60px',
                },
                /*{
                    label: "قیمت",
                    field: row => row.total_price,
                    permissions: 'show price order',
                    style: 'width: 60px;min-width: 60px',
                },*/
                {
                    name: "description",
                    label: "توضیحات",
                    field: "description",
                    field: row =>
                        `<div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 grid-cols-1">${[...Object.entries(row.attributes_label_column)
                            .filter(f => !['3', '15'].includes(f[0]))
                            .map(m => `<div></b><b>${m[1].attribute_name}</b>` + ' : ' + m[1].label + '</div>')].filter(f => f).join('')}</div><div>${row.description ?? ''}</div>`,

                    style: 'min-width: 50px',

                }
            ];

        })

        const isShowImage = ref(false)
        const urlImage = ref('')
        const loading = ref(false)

        watch(() => props.selected, (selected) => {
            props.selected = selected
        })

        const dialogPrint = ref(false)
        const statuses = ref([])
        const result = ref({})
        const showDialogPrint = () => {
            dialogPrint.value = true;
            loading.value = true;
            api.get(`/production/production_order/${props.selected.id}/print`).then(res => {
                result.value = res.result
                res.result.items = computeProductionOrderItem(res.result.items.sortBy('code'))
                statuses.value = res.statuses
                data.value = res.result;
                doors.value = res.result.items.filter(f => ['door'].includes(f.good?.group?.key));
                group_doors.value = res.result.items.filter(f => ['door'].includes(f.good?.group?.key)).group(g => (g.attributes.hasFrame ? 'byFrame' : (g.attributes.hasRokoob ? 'byRokoob' : 'door')))
                frames.value = res.result.items.filter(f => ['frame'].includes(f.good?.group?.key))
                rokoob.value = res.result.items.filter(f => ['rokoob'].includes(f.good?.group?.key))
                maghta.value = res.result.items.filter(f => ['maghta'].includes(f.good?.group?.key))
                zehvar.value = res.result.items.filter(f => ['zehvar'].includes(f.good?.group?.key))
                garniz.value = res.result.items.filter(f => ['garniz'].includes(f.good?.group?.key))

                // shakheh.value = res.result.items.filter(f => [13, 15, 23].includes(f.good.group_id))
                others.value = res.result.items.filter(f => !['door', 'frame', 'rokoob', 'maghta', 'zehvar', 'garniz'].includes(f.good?.group?.key))




                templates.value = res.templates
                attributes.value = res.attributes

                loading.value = false;
            })
        }

        const authStore = useAuthStore();
        const $q = useQuasar();
        return {
            loading,
            result,
            statuses,
            checkRole,
            showDialogPrint,
            dialogPrint,
            authStore,
            doors,
            frame_columns,
            frames,
            group_doors,
            others,
            other_columns,
            rokoob,
            rokoob_columns,
            shakheh,
            shakheh_columns,
            maghta,
            maghta_columns,
            zehvar,
            zehvar_columns,
            garniz,
            garniz_columns,
            data,
            templates,
            columns,
            print,
            attributes,
            printLabel,
            printTemplate,
            isShowImage,
            urlImage,
            checkPermission,
            showImage(value) {
                urlImage.value = value
                isShowImage.value = true
            },
            confirmCustomer() {
                $q.dialog({
                    title: "آیا سفارش مورد تایید است؟",
                    cancel: true,
                    ok: 'بله',
                    cancel: 'خیر',
                })
                    .onOk(async () => {
                        const url = '/production/production_order';
                        await api.post(url + '/' + props.selected.id + '/changeStatus', { status: 'CONFIRM_CUSTOMER' })
                        dialogPrint.value = false;
                        if (props.callback) props.callback()
                    })

            },
            async sendDraft() {
                $q.dialog({
                    title: "مطمئن هستید؟",
                    cancel: true,
                    ok: 'بله',
                    cancel: 'خیر',
                })
                    .onOk(async () => {
                        const url = '/production/production_order';
                        await api.post(url + '/' + props.selected.id + '/changeStatus', { status: 'SEND_DRAFT' })
                        dialogPrint.value = false;

                        if (props.callback) props.callback()
                    })
            }
        }
    },
}
</script>
