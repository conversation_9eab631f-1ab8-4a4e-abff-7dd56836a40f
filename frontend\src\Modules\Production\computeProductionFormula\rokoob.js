export const parvazItems = [
    {
        typeParvaz: "none_standard",
        typeMaterialRokoob: ["mdf"],
        includes: [200, 205, 210, 215, 220, 225, 237, 244],
    },
    {
        typeParvaz: "none_standard",
        typeMaterialRokoob: [ "fomizeh"],
        includes: [200, 205, 210, 215, 220, 225, 237, 244, /*250*/],
    },
    { typeParvaz: "french", typeMaterialRokoob: ["mdf"], includes: [237,/* 250*/] },
    {
        typeParvaz: "french",
        typeMaterialRokoob: ["fomizeh"],
        includes: [200, 205, 210, 215, 220, 225, 237, 244],
    },
    {
        typeParvaz: "mexic",
        typeMaterialRokoob: ["fomizeh", "mdf"],
        includes: [237, 244],
    },
];
export const dakheliItems = [
    {
        typeDakheli: "french",
        typeMaterialRokoob: ["mdf"],
        includes: [220, 244],
    },
    { typeDakheli: "french", typeMaterialRokoob: ["fomizeh"], includes: [220, 230] },
    {
        typeDakheli: "mexic",
        typeMaterialRokoob: ["fomizeh", "mdf"],
        includes: [220, 235],
    },
    {
        typeDakheli: "none_standard",
        typeMaterialRokoob: ["mdf", "fomizeh"],
        includes: [220, 230, 235, 244],
    },
];

export default function (
    { doorHeight = null, doorWidth = null, typeMaterialRokoob, typeParvaz, typeDakheli, countParvaz = null, perLengthParvaz = null, countDakheli = null, perLengthDakheli = null },
    count = 1
) {

    if (doorWidth) doorWidth *= 1;
    if (doorHeight) doorHeight *= 1;
    if (!(doorWidth && doorHeight)) {

        let res = {}
        if (countParvaz > 0) {
            res.countParvaz = countParvaz * count;
            res.perLengthParvaz = perLengthParvaz;
        }
        if (countDakheli > 0) {
            res.countDakheli = countDakheli * count;
            res.perLengthDakheli = perLengthDakheli;
        }


        return res;
    }
    const parvazCounts = (() => {
        const parvazSizes = parvazItems
            .filter(
                (f) =>
                    f.typeMaterialRokoob.includes(typeMaterialRokoob) &&
                    f.typeParvaz == typeParvaz
            )
            .map((m) => m.includes)[0];
        let counts = {};
        (() => {
            const vertical = doorHeight + 3 + 7;
            if (parvazSizes) {
                for (const parvazSize of parvazSizes) {
                    if (vertical <= parvazSize) {
                        counts[parvazSize] = (counts[parvazSize] ?? 0) + 2 * count;
                        return;
                    }
                }
            }


        })();
        (() => {

            if (doorWidth > 136)
                return counts[Object.keys(counts)[0]] += 2 * count;
            else if (doorWidth > 106)
                return counts[Object.keys(counts)[0]] += 1 * count;
            else return counts[Object.keys(counts)[0]] += 0.5 * count;

            //const horizontal = doorWidth + 3 + 14;

            // if (
            //     Object.keys(counts)[0] &&
            //     Object.keys(counts)[0] / 2 >= horizontal
            // ) {
            //     counts[Object.keys(counts)[0]] += 0.5 * count;
            //     return;
            // }

            // for (const parvazSize of parvazSizes) {
            //     if (horizontal * 2 <= parvazSize) {
            //         counts[parvazSize] =
            //             (counts[parvazSize] ?? 0) + 0.5 * count;
            //         return;
            //     }
            // }

            // for (const parvazSize of parvazSizes) {
            //     if (horizontal <= parvazSize) {
            //         counts[parvazSize] = (counts[parvazSize] ?? 0) + 1 * count;
            //         return;
            //     }
            // }
        })();
        return counts;
    })();

    const dakheliCounts = (() => {
        const dakheliSizes = dakheliItems
            .filter(
                (f) =>
                    f.typeMaterialRokoob.includes(typeMaterialRokoob) &&
                    f.typeDakheli == typeDakheli
            )
            .map((m) => m.includes)[0];
        let counts = {};
        (() => {
            const vertical = doorHeight + 3;
            if (dakheliSizes) {

                for (const dakheliSize of dakheliSizes) {
                    if (vertical <= dakheliSize) {
                        counts[dakheliSize] =
                            (counts[dakheliSize] ?? 0) + 2 * count;
                        return;
                    }
                }
            }
        })();
        (() => {
            if (!Object.keys(counts)[0])
                return;

            if (doorWidth >= 136)
                counts[Object.keys(counts)[0]] += 2 * count;
            else if (doorWidth >= 106)
                counts[Object.keys(counts)[0]] += 1 * count;
            else counts[Object.keys(counts)[0]] += 0.5 * count;


            // const horizontal = doorWidth + 3 + 14;

            // for (const dakheliSize of dakheliSizes) {
            //     if (horizontal * 2 <= dakheliSize) {
            //         counts[dakheliSize] =
            //             (counts[dakheliSize] ?? 0) + 0.5 * count;
            //         return;
            //     }
            // }
            // for (const dakheliSize of dakheliSizes) {
            //     if (horizontal <= dakheliSize) {
            //         counts[dakheliSize] =
            //             (counts[dakheliSize] ?? 0) + 1 * count;
            //         return;
            //     }
            // }
        })();
        return counts;
    })();

    return {
        parvazCounts,
        countParvaz: Object.values(parvazCounts).length > 0 ? Object.values(parvazCounts).reduce((s, x) => s + x, 0) : '',
        perLengthParvaz: Object.keys(parvazCounts).length > 0 ? Object.keys(parvazCounts)[0] : '',

        dakheliCounts,
        countDakheli: Object.values(dakheliCounts).length > 0 ? Object.values(dakheliCounts).reduce((s, x) => s + x, 0) : '',
        perLengthDakheli: Object.keys(dakheliCounts).length > 0 ? Object.keys(dakheliCounts)[0] : '',
    };
}
