<template>
  <div class="theme-switcher">
    <!-- Simple Toggle Mode -->
    <q-btn
      v-if="simple"
      :icon="themeIcon"
      flat
      round
      dense
      :color="isDark ? 'white' : 'primary'"
      @click="toggleTheme"
      class="theme-toggle-btn"
    >
      <q-tooltip class="text-body2">
        {{ themeTooltip }}
      </q-tooltip>
    </q-btn>

    <!-- Advanced Settings Mode -->
    <q-btn-dropdown
      v-else
      :icon="themeIcon"
      flat
      :label="label"
      :color="isDark ? 'white' : 'primary'"
      dropdown-icon="settings"
      class="theme-settings-btn"
    >
      <q-card class="theme-settings-card" style="min-width: 300px">
        <q-card-section class="pb-2">
          <div class="text-h6 flex items-center">
            <q-icon name="palette" class="ml-2" />
            تنظیمات تم
          </div>
        </q-card-section>

        <q-separator />

        <q-card-section class="space-y-4">
          <!-- Theme Mode Selection -->
          <div>
            <div class="text-subtitle2 mb-2">حالت تم</div>
            <q-btn-toggle
              v-model="themeMode"
              :options="themeModeOptions"
              toggle-color="primary"
              @update:model-value="setThemeMode"
              class="w-full"
            />
          </div>

          <!-- Quick Theme Toggle -->
          <div class="flex items-center justify-between">
            <span class="text-subtitle2">تم تاریک</span>
            <q-toggle
              :model-value="isDark"
              @update:model-value="toggleTheme"
              color="primary"
              :disable="isAutoMode"
            />
          </div>

          <q-separator />

          <!-- Density Settings -->
          <div>
            <div class="text-subtitle2 mb-2">تراکم</div>
            <q-btn-toggle
              v-model="settings.density"
              :options="densityOptions"
              toggle-color="primary"
              @update:model-value="updateDensity"
              class="w-full"
            />
          </div>

          <!-- Border Radius -->
          <div>
            <div class="text-subtitle2 mb-2">گردی گوشه‌ها</div>
            <q-btn-toggle
              v-model="settings.borderRadius"
              :options="borderRadiusOptions"
              toggle-color="primary"
              @update:model-value="updateBorderRadius"
              class="w-full"
            />
          </div>

          <!-- Font Size -->
          <div>
            <div class="text-subtitle2 mb-2">اندازه فونت</div>
            <q-btn-toggle
              v-model="settings.fontSize"
              :options="fontSizeOptions"
              toggle-color="primary"
              @update:model-value="updateFontSize"
              class="w-full"
            />
          </div>

          <q-separator />

          <!-- Animation Settings -->
          <div class="flex items-center justify-between">
            <span class="text-subtitle2">انیمیشن‌ها</span>
            <q-toggle
              v-model="settings.animations"
              @update:model-value="updateAnimations"
              color="primary"
            />
          </div>

          <!-- High Contrast -->
          <div class="flex items-center justify-between">
            <span class="text-subtitle2">کنتراست بالا</span>
            <q-toggle
              v-model="settings.highContrast"
              @update:model-value="updateHighContrast"
              color="primary"
            />
          </div>
        </q-card-section>

        <q-separator />

        <q-card-actions align="around" class="q-pa-md">
          <q-btn
            flat
            icon="refresh"
            label="بازنشانی"
            @click="resetSettings"
            color="negative"
            size="sm"
          />
          <q-btn
            flat
            icon="download"
            label="دریافت"
            @click="exportSettings"
            color="primary"
            size="sm"
          />
          <q-btn
            flat
            icon="upload"
            label="بارگذاری"
            @click="importSettings"
            color="primary"
            size="sm"
          />
        </q-card-actions>
      </q-card>
    </q-btn-dropdown>

    <!-- Import Dialog -->
    <q-dialog v-model="showImportDialog">
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">بارگذاری تنظیمات تم</div>
        </q-card-section>

        <q-card-section>
          <q-input
            v-model="importData"
            type="textarea"
            label="JSON تنظیمات"
            rows="6"
            outlined
            placeholder='{"mode": "dark", "settings": {...}, "colors": {...}}'
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="انصراف" @click="showImportDialog = false" />
          <q-btn
            flat
            label="اعمال"
            color="primary"
            @click="applyImportedSettings"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import { useThemeStore } from '@/stores';
import { THEME_MODES } from '@/constants';
import { Notify, copyToClipboard } from 'quasar';

// Props
const props = defineProps({
  simple: {
    type: Boolean,
    default: false
  },
  label: {
    type: String,
    default: 'تم'
  }
});

// Store
const themeStore = useThemeStore();

// Reactive data
const showImportDialog = ref(false);
const importData = ref('');

// Computed properties
const isDark = computed(() => themeStore.isDark);
const themeMode = computed(() => themeStore.mode);
const isAutoMode = computed(() => themeStore.isAutoMode);
const settings = computed(() => themeStore.settings);

const themeIcon = computed(() => {
  if (themeStore.mode === THEME_MODES.AUTO) {
    return 'brightness_auto';
  }
  return themeStore.isDark ? 'dark_mode' : 'light_mode';
});

const themeTooltip = computed(() => {
  const modeText = {
    [THEME_MODES.LIGHT]: 'روشن',
    [THEME_MODES.DARK]: 'تاریک',
    [THEME_MODES.AUTO]: 'خودکار'
  };
  return `تم ${modeText[themeStore.mode]}`;
});

// Options for toggles
const themeModeOptions = [
  { label: 'روشن', value: THEME_MODES.LIGHT, icon: 'light_mode' },
  { label: 'تاریک', value: THEME_MODES.DARK, icon: 'dark_mode' },
  { label: 'خودکار', value: THEME_MODES.AUTO, icon: 'brightness_auto' }
];

const densityOptions = [
  { label: 'فشرده', value: 'compact' },
  { label: 'راحت', value: 'comfortable' },
  { label: 'پراکنده', value: 'spacious' }
];

const borderRadiusOptions = [
  { label: 'کم', value: 'small' },
  { label: 'متوسط', value: 'medium' },
  { label: 'زیاد', value: 'large' }
];

const fontSizeOptions = [
  { label: 'کوچک', value: 'small' },
  { label: 'متوسط', value: 'medium' },
  { label: 'بزرگ', value: 'large' }
];

// Methods
const toggleTheme = () => {
  themeStore.toggleDarkMode();
};

const setThemeMode = (mode) => {
  themeStore.setThemeMode(mode);
};

const updateDensity = (density) => {
  themeStore.updateSettings({ density });
};

const updateBorderRadius = (borderRadius) => {
  themeStore.updateSettings({ borderRadius });
};

const updateFontSize = (fontSize) => {
  themeStore.updateSettings({ fontSize });
};

const updateAnimations = (animations) => {
  themeStore.updateSettings({ animations });
};

const updateHighContrast = (highContrast) => {
  themeStore.updateSettings({ highContrast });
};

const resetSettings = () => {
  themeStore.resetToDefaults();
  Notify.create({
    type: 'positive',
    message: 'تنظیمات تم بازنشانی شد',
    position: 'top'
  });
};

const exportSettings = async () => {
  const settings = themeStore.exportSettings();
  const jsonString = JSON.stringify(settings, null, 2);
  
  try {
    await copyToClipboard(jsonString);
    Notify.create({
      type: 'positive',
      message: 'تنظیمات در کلیپ‌بورد کپی شد',
      position: 'top'
    });
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    Notify.create({
      type: 'negative',
      message: 'خطا در کپی کردن تنظیمات',
      position: 'top'
    });
  }
};

const importSettings = () => {
  showImportDialog.value = true;
  importData.value = '';
};

const applyImportedSettings = () => {
  try {
    const settings = JSON.parse(importData.value);
    themeStore.importSettings(settings);
    showImportDialog.value = false;
    
    Notify.create({
      type: 'positive',
      message: 'تنظیمات با موفقیت اعمال شد',
      position: 'top'
    });
  } catch (error) {
    Notify.create({
      type: 'negative',
      message: 'فرمت JSON نامعتبر است',
      position: 'top'
    });
  }
};
</script>

<style scoped>
.theme-switcher {
  display: inline-block;
}

.theme-toggle-btn {
  transition: all 0.3s ease;
}

.theme-toggle-btn:hover {
  transform: scale(1.1);
}

.theme-settings-card {
  max-height: 80vh;
  overflow-y: auto;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}
</style>
