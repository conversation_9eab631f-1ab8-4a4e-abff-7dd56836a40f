<template>
    <j-form @submit="submit" class="w-full p-2 border-2 rounded-md bg-gray-100">
        <div class="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-4 gap-3">
            <j-select v-model="form.attribute_id" dense hide-bottom-space label="ویژگی" :outlined="false" required
                :options="attribute_items" option-label="name" option-value="id"
                @update:model-value="form.items = []" />
            <!-- <j-select v-if="form.attribute_id" v-model="form.items" dense hide-bottom-space label="مقدار"
                :outlined="false" required multiple
                :options="attribute_items[attribute_items.findIndex(f => f.id == form.attribute_id)].items"
                option-label="name" option-value="id" /> -->
            <j-toggle v-model="form.condition" dense label="فعال" />

            <template v-if="form.attribute_id">
                <j-select
                    v-if="['SELECT','SELECT_IMAGE'].includes(attribute_items[attribute_items.findIndex(f => f.id == form.attribute_id)].type)"
                    v-model="form.items" dense
                    :options="attribute_items[attribute_items.findIndex(f => f.id == form.attribute_id)].items"
                    option-label="name" option-value="key" use-input hide-bottom-space label="مقدار" multiple
                    :outlined="false" required :disable="is_edit" />
                <j-input
                    v-else-if="attribute_items[attribute_items.findIndex(f => f.id == form.attribute_id)].type == 'INPUT'"
                    v-model="form.items[0]" dense hide-bottom-space label="مقدار" :outlined="false" required
                    :disable="is_edit" />
                <j-input
                    v-else-if="attribute_items[attribute_items.findIndex(f => f.id == form.attribute_id)].type == 'NUMBER'"
                    v-model="form.items[0]" dense hide-bottom-space label="مقدار" type="number" :outlined="false" step="0.1"
                    required :disable="is_edit" />
                <j-toggle
                    v-else-if="attribute_items[attribute_items.findIndex(f => f.id == form.attribute_id)].type == 'SWITCH'"
                    v-model="form.items[0]" dense hide-bottom-space label="مقدار" :outlined="false" required
                    :disable="is_edit" />

            </template>
        </div>
        <j-btn size="md" :color="'primary'" type="submit" class="full-width text-white mt-4" :label="'افزودن'" />
    </j-form>
    <div class="flex q-table__card mt-5">
        <div class="flex flex-col border-r-gray-200 border-r-2 w-10">

            <template v-if="selected && selected.length > 0">
                <j-btn flat dense icon="delete" color="red" @click="onDelete()" />
            </template>
        </div>
        <j-table v-model:selected="selected" flat :columns="columns" :rows="data" separator="cell"
            :row-key="row => row.attribute_id" selection="single" :rows-per-page-options="[0]" dense
            class="flex-auto w-12">
        </j-table>
    </div>
</template>

<script>
import { api } from '@/boot/axios';
import { useQuasar } from 'quasar';
import { ref, watch } from 'vue';

export default {
    props: {
        data: Array,
        attributes: Array,
    },
    setup(props, { emit }) {
        const selected = ref([])
        const $q = useQuasar();

        // const selected_attributes = ref({})
        const attribute_items = ref([])
        api.get('good/attribute').then(res => {
            attribute_items.value = res.data
        })

        const data = ref(props.data ?? [])
        watch(() => props.data, (newVal) => {
            data.value = newVal
        })


        const good = ref({})
        const form = ref({})

        const onDelete = () => {
            $q.dialog({
                title: "مطمئن هستید؟",
                cancel: true,
                persistent: true,
            }).onOk(() => {
                const value = selected.value[0];
                const find = data.value.findIndex(f => f.good_id == value.good_id && f.attribute_id == value.attribute_id && f.attribute_item_id == value.attribute_item_id)

                if (find >= 0)
                    data.value.splice(find, 1)
                selected.value = []
            });
        }


        const reset = () => {
            form.value = {}
            good.value = {}

        }

        const columns = [
            {
                name: "attribute",
                label: "ویژگی",
                field: row => attribute_items.value.findIndex(f => f.id == row.attribute_id) == -1 ? '' : attribute_items.value[attribute_items.value.findIndex(f => f.id == row.attribute_id)].name,
            },
            {
                name: "condition",
                label: "فعال",
                field: row => row.condition ? 'فعال' : 'غیرفعال',
            },
            {
                name: "items",
                label: "مقدار",
                //field: row => attribute_items.value.filter(f => row.items.includes(f.id)).map(m => m.name).join(','),// == -1 ? '' : attribute_items.value[attribute_items.value.findIndex(f => f.id == row.attribute_id)].items[attribute_items.value[attribute_items.value.findIndex(f => f.id == row.attribute_id)].items.findIndex(f => f.id == row.attribute_item_id)].name,
                field: row => {
                    const find = attribute_items.value.findIndex(f => f.id == row.attribute_id)
                    if (find >= 0) {
                        switch (attribute_items.value[find].type) {
                            case 'SELECT':
                            case 'SELECT_IMAGE':
                                return attribute_items.value[find].items.filter(f => row.items.includes(f.key)).map(m => m.name).join(',')
                            case 'INPUT':
                            case 'NUMBER':
                                return row.items[0]
                            case 'SWITCH':
                                return row.items[0] ? 'دارد' : ''
                        }
                    }
                    return ''
                },

            }

        ];


        return {
            submit: () => {
                const find = data.value.findIndex(f => f.attribute_id == form.value.attribute_id)
                if (find >= 0)
                    Object.assign(data.value[find], form.value)
                else
                    data.value.push(form.value)
                emit('update:data', data.value)
                reset()
            },
            columns,
            data,
            good,
            form,
            onDelete,
            attribute_items,
            selected,
        }
    },
}
</script>