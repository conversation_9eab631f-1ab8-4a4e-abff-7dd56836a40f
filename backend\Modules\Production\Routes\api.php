<?php

use Illuminate\Support\Facades\Route;
use Modules\Production\Http\Controllers\ComputePriceOrderController;
use Modules\Production\Http\Controllers\CRMProductionOrderController;
use Modules\Production\Http\Controllers\GuaranteeController;
use Modules\Production\Http\Controllers\InstructionController;
use Modules\Production\Http\Controllers\InstructionItemController;
use Modules\Production\Http\Controllers\MachineController;
use Modules\Production\Http\Controllers\MachineProblemReportController;
use Modules\Production\Http\Controllers\PartyController;
use Modules\Production\Http\Controllers\StationWorkController;
use Modules\Production\Http\Controllers\ProductionChecklistController;
use Modules\Production\Http\Controllers\ProductionOrderController;
use Modules\Production\Http\Controllers\ProductionOrderItemController;
use Modules\Production\Http\Controllers\ProductionScheduleController;
use Modules\Production\Http\Controllers\StationController;
use Modules\Production\Http\Controllers\StationMachineController;
use Modules\Production\Http\Controllers\StationStatisticController;
use Modules\Production\Http\Controllers\WorkInstructionController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->group(function () {
    Route::japiResource('party', PartyController::class);
    Route::apiResource('station', StationController::class);
    Route::get('station/{station}/production_checklist', [ProductionChecklistController::class, 'index']);
    Route::get('station/{station}/new_production_checklist', [ProductionChecklistController::class, 'new_index']);
    Route::put('station/{station}/production_checklist', [ProductionChecklistController::class, 'update']);
    Route::put('production_checklist', [ProductionChecklistController::class, 'new_update']);
    Route::apiResource('instruction', InstructionController::class);
    Route::apiResource('instruction.items', InstructionItemController::class);
    Route::apiResource('production_orders', ProductionOrderController::class); //->middleware(['permission:production_order_read']);
    Route::japiResource('production_order', ProductionOrderController::class); //->middleware(['permission:production_order_read']);
    Route::get('production_order/{production_order}/print', [ProductionOrderController::class, 'print']);
    Route::get('production_order/{production_order}/tracking', [ProductionOrderController::class, 'tracking']);
    Route::get('production_order_calendar', [ProductionOrderController::class, 'calendar']);
    Route::apiResource('production_order.items', ProductionOrderItemController::class);
    Route::get('production_order_items', [ProductionOrderItemController::class, 'list']);
    Route::apiResource('schedule', ProductionScheduleController::class);
    Route::get('stations_statistic', [StationStatisticController::class, 'index']);
    Route::apiResource('station.works', StationWorkController::class);
    Route::apiResource('station.machines', StationMachineController::class);
    Route::apiResource('machines', MachineController::class);
    Route::apiResource('good.workInstructions', WorkInstructionController::class);
    Route::apiResource('station.machines.reports', MachineProblemReportController::class);
    Route::get('machine_problem_reports', [MachineProblemReportController::class, 'index_all']);
    Route::put('machine_problem_reports/{machine_problem_report}', [MachineProblemReportController::class, 'put_all']);
    Route::delete('machine_problem_reports/{machine_problem_report}', [MachineProblemReportController::class, 'delete_all']);

    Route::get('production_order/{production_order}/checklists', [ProductionOrderController::class, 'checklists']);
    Route::post('production_order/{production_order}/changeStatus', [ProductionOrderController::class, 'changeStatus']);


    Route::post('computePriceOrder', [ComputePriceOrderController::class, 'computePriceOrder']);

});

Route::middleware('auth:crm')->group(function () {
    Route::japiResource('crm_production_order', CRMProductionOrderController::class); //->middleware(['permission:production_order_read']);
    Route::post('crm_production_order/{crm_production_order}/sendToProduction', [CRMProductionOrderController::class, 'sendToProduction']);
    Route::get('crm_production_order/{production_order}/print', [CRMProductionOrderController::class, 'print']);

});
Route::post('checkBarcode', [GuaranteeController::class, 'checkBarcode']);

Route::get('production_order_report', [ProductionOrderController::class,'report']);