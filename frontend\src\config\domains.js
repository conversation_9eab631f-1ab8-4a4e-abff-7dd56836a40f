/**
 * Domain configuration for multi-domain frontend
 */

export const DOMAINS = {
    PANEL: import.meta.env.VITE_PANEL_DOMAIN || 'panel.erp.test',
    CRM: import.meta.env.VITE_CRM_DOMAIN || 'crm.erp.test'
};

export const DOMAIN_TYPES = {
    PANEL: 'panel',
    CRM: 'crm',
    UNKNOWN: 'unknown'
};

/**
 * Get current domain type based on window.location.host
 * @returns {string} Domain type (panel, crm, or unknown)
 */
export function getCurrentDomainType() {
    if (typeof window === 'undefined') {
        return DOMAIN_TYPES.PANEL; // Default for SSR
    }
    
    const currentHost = window.location.host;
    
    // Remove port from host for comparison
    const hostWithoutPort = currentHost.split(':')[0];
    const panelDomainWithoutPort = DOMAINS.PANEL.split(':')[0];
    const crmDomainWithoutPort = DOMAINS.CRM.split(':')[0];
    
    if (hostWithoutPort === panelDomainWithoutPort || currentHost.includes('localhost')) {
        return DOMAIN_TYPES.PANEL;
    } else if (hostWithoutPort === crmDomainWithoutPort) {
        return DOMAIN_TYPES.CRM;
    }
    
    return DOMAIN_TYPES.PANEL; // Default fallback
}

/**
 * Check if current domain is panel
 * @returns {boolean}
 */
export function isPanelDomain() {
    return getCurrentDomainType() === DOMAIN_TYPES.PANEL;
}

/**
 * Check if current domain is CRM
 * @returns {boolean}
 */
export function isCrmDomain() {
    return getCurrentDomainType() === DOMAIN_TYPES.CRM;
}

/**
 * Get domain-specific configuration
 * @returns {object} Domain configuration
 */
export function getDomainConfig() {
    const domainType = getCurrentDomainType();
    
    const configs = {
        [DOMAIN_TYPES.PANEL]: {
            name: 'پنل ایران پارس',
            theme: {
                primary: '#1976d2',
                logo: '/images/panel-logo.svg'
            },
            features: {
                modules: true,
                reports: true,
                management: true
            }
        },
        [DOMAIN_TYPES.CRM]: {
            name: 'CRM ایران پارس',
            theme: {
                primary: '#26a69a',
                logo: '/images/crm-logo.svg'
            },
            features: {
                orders: true,
                customers: true,
                sales: true
            }
        }
    };
    
    return configs[domainType] || configs[DOMAIN_TYPES.PANEL];
}
