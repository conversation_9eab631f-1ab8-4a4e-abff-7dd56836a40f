<template>
  <div class="dashboard-panel">
    <div class="row q-gutter-md">
      <div class="col-12">
        <q-card class="my-card">
          <q-card-section>
            <div class="text-h6">داشبورد پنل مدیریت</div>
            <div class="text-subtitle2">پنل ایران پارس</div>
          </q-card-section>
          
          <q-card-section class="q-pt-none">
            <div class="text-body1">
              خوش آمدید به پنل مدیریت سیستم ایران پارس
            </div>
          </q-card-section>
        </q-card>
      </div>
      
      <div class="col-md-6 col-12">
        <q-card class="my-card">
          <q-card-section>
            <div class="text-h6">آمار کلی</div>
          </q-card-section>
          
          <q-card-section class="q-pt-none">
            <div class="row q-gutter-md">
              <div class="col">
                <q-chip color="primary" text-color="white" icon="people">
                  کاربران: 150
                </q-chip>
              </div>
              <div class="col">
                <q-chip color="secondary" text-color="white" icon="inventory">
                  محصولات: 1,250
                </q-chip>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
      
      <div class="col-md-6 col-12">
        <q-card class="my-card">
          <q-card-section>
            <div class="text-h6">عملیات سریع</div>
          </q-card-section>
          
          <q-card-section class="q-pt-none">
            <div class="q-gutter-sm">
              <q-btn color="primary" label="مدیریت کاربران" icon="people" />
              <q-btn color="secondary" label="گزارشات" icon="assessment" />
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useDomain } from '@/composables';

const { domainConfig, isPanelDomain } = useDomain();

onMounted(() => {
  console.log('Panel Dashboard loaded');
  console.log('Domain config:', domainConfig.value);
});
</script>

<style scoped>
.dashboard-panel {
  padding: 16px;
}

.my-card {
  min-height: 120px;
}
</style>
