
// Theme CSS Variables
:root {
  // Default theme values
  --theme-density: 1;
  // --theme-border-radius: 8px;
  --theme-font-scale: 1;
}

// Theme density classes
.theme-density-compact {
  --theme-density: 0.8;

  .q-btn {
    transform: scale(var(--theme-density));
  }

  .q-field {
    transform: scale(var(--theme-density));
  }

  .q-card {
    padding: calc(16px * var(--theme-density));
  }
}

.theme-density-comfortable {
  --theme-density: 1;
}

.theme-density-spacious {
  --theme-density: 1.2;

  .q-btn {
    padding: calc(8px * var(--theme-density)) calc(16px * var(--theme-density));
  }

  .q-field {
    margin-bottom: calc(16px * var(--theme-density));
  }

  .q-card {
    padding: calc(24px * var(--theme-density));
  }
}





// Theme font size classes
.theme-font-small {
  --theme-font-scale: 0.875;

  font-size: calc(1rem * var(--theme-font-scale));
}

.theme-font-medium {
  --theme-font-scale: 1;

  font-size: calc(1rem * var(--theme-font-scale));
}

.theme-font-large {
  --theme-font-scale: 1.125;

  font-size: calc(1rem * var(--theme-font-scale));
}

// Animation control
.theme-animations-disabled {
  * {
    animation-duration: 0s !important;
    transition-duration: 0s !important;
  }
}

// High contrast mode
.theme-high-contrast {
  .q-btn {
    border: 2px solid currentColor;
  }

  .q-field__control {
    border: 2px solid currentColor;
  }

  .q-card {
    border: 1px solid currentColor;
  }
}

// Reduced motion
.theme-reduced-motion {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// Dark mode specific styles
.body--dark {
  .q-header .bg-white {
    background-color: var(--q-dark) !important;
    color: white !important;
  }

  .text-gray-700 {
    color: rgba(255, 255, 255, 0.87) !important;
  }

  .q-drawer {
    background-color: var(--q-dark);
  }

  .q-page {
    background-color: var(--q-dark-page);
  }

  // Sticky header table dark mode
  .my-sticky-header-table thead tr th {
    background: var(--q-dark) !important;
    color: rgba(255, 255, 255, 0.87);
  }

  .my-sticky-header-table tbody tr:first-child td {
    background: var(--q-dark) !important;
    color: rgba(255, 255, 255, 0.87);
  }

  // Sticky header column table dark mode
  .my-sticky-header-column-table td:first-child {
    background-color: var(--q-dark) !important;
    color: rgba(255, 255, 255, 0.87);
  }

  .my-sticky-header-column-table tr th,
  .my-sticky-header-column-table tbody tr:first-child td {
    background: var(--q-dark) !important;
    color: rgba(255, 255, 255, 0.87);
  }

  // Table styling for dark mode
  .even-highlight-2 > div > table > tbody > tr:nth-of-type(4n + 3),
  .even-highlight tr:nth-of-type(2n),
  .odd-highlight tr:nth-of-type(2n + 1) {
    background: rgba(255, 255, 255, 0.05);
  }

  .j-table td:not(.no-border),
  .j-table th:not(.no-border) {
    border-color: rgba(255, 255, 255, 0.12);
  }

  table.asd td {
    border-color: rgba(255, 255, 255, 0.12);
  }

  td.highlight {
    background: rgba(255, 255, 255, 0.87);
    color: var(--q-dark);
  }

  tr.highlight {
    background: rgba(255, 255, 255, 0.05);
  }

  // Navigation and active states
  .dark_active,
  .tab-active {
    background: linear-gradient(
      72.47deg,
      rgba(136, 233, 255, 0.3) 22.16%,
      rgba(208, 246, 255, 0.3) 76.47%
    ) !important;
    color: #88e9ff !important;
  }

  // Sticky column dark mode
  .sticky-column {
    background: var(--q-dark) !important;
    color: rgba(255, 255, 255, 0.87);
    box-shadow: 2px 0 5px rgba(255, 255, 255, 0.1);
  }

  th.sticky-column {
    background: var(--q-dark-page) !important;
  }

  // Top row table dark mode
  .topRowTable .q-table__top {
    background: var(--q-primary);
    color: white;
  }
}

// Light mode specific styles
.body--light {
  .q-header .bg-white {
    background-color: white !important;
    color: #374151 !important;
  }

  .q-drawer {
    background-color: white;
  }

  .q-page {
    background-color: #f3f4f6;
  }

  // Sticky header table light mode
  .my-sticky-header-table thead tr th {
    background: #fff !important;
    color: #374151;
  }


  .my-sticky-header-column-table tr th {
    background: #fff !important;
    color: #374151;
  }

  // Sticky column light mode
  .sticky-column {
    background: white !important;
    color: #374151;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  }

  th.sticky-column {
    background: #f8f9fa !important;
  }

  // Table styling for light mode
  .even-highlight-2 > div > table > tbody > tr:nth-of-type(4n + 3),
  .even-highlight tr:nth-of-type(2n),
  .odd-highlight tr:nth-of-type(2n + 1) {
    background: #e0e0e0;
  }

  .j-table td:not(.no-border),
  .j-table th:not(.no-border) {
    border-color: black;
  }

  table.asd td {
    border-color: black;
  }

  td.highlight {
    background: black;
    color: white;
  }

  tr.highlight {
    background: #dddddd;
  }

  // Navigation and active states
  .dark_active,
  .tab-active {
    background: linear-gradient(
      72.47deg,
      #88e9ff 22.16%,
      #d0f6ff 76.47%
    ) !important;
    color: #009fc6 !important;
  }

  
  // Scrollbar light mode
  ::-webkit-scrollbar {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 6px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 6px;
  }
}

// Additional dark mode scrollbar and form styling
.body--dark {
  // Scrollbar dark mode
  ::-webkit-scrollbar {
    background: var(--q-dark-page);
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 6px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  ::-webkit-scrollbar-track {
    background: var(--q-dark-page);
    border-radius: 6px;
  }

  // Form elements dark mode
  .q-field__control {
    background-color: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.87);
  }

  .q-table__container {
    background: var(--q-dark);
    color: rgba(255, 255, 255, 0.87);
  }

  // Card styling
  .q-card {
    background-color: var(--q-dark);
    color: rgba(255, 255, 255, 0.87);
  }

  // Menu and dialog styling
  .q-menu {
    background-color: var(--q-dark);
    color: rgba(255, 255, 255, 0.87);
  }

  .q-dialog__inner > div {
    background-color: var(--q-dark);
    color: rgba(255, 255, 255, 0.87);
  }

  // Tailwind Background Colors - Dark Mode Overrides
  // Fix for sticky elements with bg-white
  .bg-white {
    background-color: var(--q-dark) !important;
  }
  
    .border-b {
    border-color: rgba(255, 255, 255, 0.12) !important;
  }

  .border,
  .border-t,
  .border-r,
  .border-l {
    border-color: rgba(255, 255, 255, 0.12) !important;
  }
}