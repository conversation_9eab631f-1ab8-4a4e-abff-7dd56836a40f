<?php

namespace App\Http\Controllers\API;

use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Routing\Controllers\HasMiddleware;
use Spatie\Permission\Models\Permission;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Validation\Rule;

//use Spatie\Permission\Models\Permission;

class RoleController extends BaseController implements HasMiddleware
{

    public static function middleware(): array
    {
        return [
            new Middleware('permission:roles', only: ['index']),
            new Middleware('permission:roles.create', only: ['create', 'store']),
            new Middleware('permission:roles.edit', only: ['show', 'update']),
            new Middleware('permission:roles.delete', only: ['delete', 'destroy']),
            //new Middleware('subscribed', except: ['store']),
        ];
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return JsonResource::collection(Role::query()->filter()->jpaginate())->additional([
            // 'formOption' => [
            //     'permissions' => Permission::get(['id', 'label']),

            // ]
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'label'      => 'required',
            'name'       => 'required',
            'guard_name' => 'required',
        ]);
        $model = Role::create($request->all());
        $model->permissions()->sync($request->permissions);

        $model->permissions = $model->permissions()->get(['id'])->pluck('id');
        return $this->handleResponse($model, trans('request.done'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Role  $role
     * @return \Illuminate\Http\Response
     */
    public function show(Role $role)
    {
        $role->permissions = $role->permissions()->get(['id'])->pluck('id');

        return $this->handleResponse([
            'form'        => $role,
            'formOptions' => [
                'permissions' => Permission::get(['id', 'label', 'name']),
            ],
        ]);
    }

    public function create()
    {
        return $this->handleResponse([
            'formOptions' => [
                'permissions' => Permission::get(['id', 'label','name']),
            ],
        ]);
    }

    public function edit(Role $id)
    {
        $model              = $id;
        $model->permissions = $model->permissions()->get(['id'])->pluck('id');
        return $this->handleResponse([
            'form'        => $model,
            'formOptions' => [
                'permissions' => Permission::get(['id', 'label']),
            ],
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $ids
     * @return \Illuminate\Http\Response
     */
    public function delete(Request $request)
    {
        // اعتبارسنجی ورودی برای اطمینان از اینکه ورودی آرایه‌ای از شناسه‌هاست
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => ['integer', Rule::exists(Role::class, 'id')],
        ], [
            'ids.*.exists' => 'سمتی با شناسه :input یافت نشد یا امکان حذف ندارد.',
        ]);

        // دریافت شناسه‌ها از ورودی
        $ids = $validated['ids'];

        // حذف مدل‌ها با شناسه‌های مشخص شده
        try {
            Role::destroy($ids);
        } catch (\Exception $e) {
            // در صورت بروز خطا، می‌توانید یک پاسخ مناسب ارسال کنید
            return $this->handleError($e, 'امکان حذف وجود ندارد', 500);
        }

        // ارسال پاسخ موفقیت‌آمیز
        return $this->handleResponse(null, trans('request.done')); // response()->json(['message' => 'Records deleted successfully']);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Role  $role
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Role $role)
    {
        $role->update($request->all());
        $role->permissions()->sync($request->permissions);
        $role->permissions = $role->permissions()->get(['id'])->pluck('id');

        return $this->handleResponse($role, trans('request.done'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Role  $role
     * @return \Illuminate\Http\Response
     */
    public function destroy(Role $role)
    {

        return $this->handleResponse($role->delete(), trans('request.done'));
    }

    /**
     * Search the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function search()
    {
        //
    }
}
