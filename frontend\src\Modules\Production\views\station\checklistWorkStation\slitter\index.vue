<template>
    <q-tabs v-model="tab" dense active-color="white" active-bg-color="primary" indicator-color="primary" align="justify"
        narrow-indicator>
        <q-tab v-for="good, index in rows_data" :name="good.key" :label="good.label" :key="index" class="rounded-xl" />
    </q-tabs>
    <q-tab-panels v-model="tab" animated>
        <q-tab-panel v-for="rows, index in rows_data" :name="rows.key" :key="index" style="padding: 0"
            class="shadow-md">
            <div class="grid grid-cols-1 gap-8 w-full mt-3">
                <!-- <item :key="index4" v-bind="{ good: rows }" @afterSave="$emit('afterSave')" /> -->
                <template v-if="rows.items && rows.items.length > 0">
                    <item v-for="good, index4 in rows.items" :key="index4" v-bind="{ good }" />
                </template>
                <template v-else>
                    <q-tabs v-model="tab_type" dense active-color="white" active-bg-color="primary"
                        indicator-color="primary" align="justify" narrow-indicator>
                        <q-tab v-for="key in Object.keys(rows.all)" :name="key" :label="rows.all[key].label" :key="key"
                            class="rounded-xl" />
                    </q-tabs>
                    <q-tab-panels v-model="tab_type" animated>
                        <q-tab-panel v-for="key in Object.keys(rows.all)" :name="key" :key="key" style="padding: 0">
                            <item v-for="good, index4 in rows.all[key]?.items" :key="index4" v-bind="{ good }" />
                        </q-tab-panel>

                    </q-tab-panels>
                </template>
            </div>
        </q-tab-panel>
    </q-tab-panels>
</template>

<script>
import { computed, ref } from 'vue';
import item from './item.vue';
import { varagVast } from './index'

export default {
    components: { item },
    props: {
        data: {
            type: Array,
            default: () => []
        },
        name: String,
    },
    setup(props) {

        const rows_data = computed(() => {
            return Object.values(
                props.data

                    .map((m) => ({ ...m, key: m.attributes.pvcColor }))
                    .filter((f) => f.key !== 32)

                    .group("key")).map(m => {
                        const varag_vast = varagVast(m);
                        // console.log(varag_vast)
                        return {
                            key: m[0].key,
                            label: m[0]?.attributes_label?.pvcColor ?? '-',
                            items: varag_vast[props.name]?.items,
                            all: varag_vast,
                        }
                    })

        })


        const tab = ref('')
        const tab_type = ref('')
        return {
            rows_data,
            tab,
            tab_type,
        }
    }
}
</script>