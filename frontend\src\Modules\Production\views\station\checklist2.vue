<template>
  <j-table :columns="columns" :rows="dataFiltered" v-model:filters="filters" dense hide-bottom separator="cell"
    :rows-per-page-options="[0]" :grid="false" @onFilter="onFilter" selection="multiple" v-model:selected="selected"
    :row-key="(row) => row.id" class="my-sticky-dynamic custom-scrollbar" style="height: calc(100vh - 190px)">
    <template v-if="station_work.key" #top>
      <slot name="top" />
      <j-btn v-if="station_work.key" class="bg-secondary text-white" label="برگه تولید" dense flat icon="manage_search"
        @click="print(station_work.key)" />
    </template>
    <template #body-cell-description="props">
      <q-td :props="props">
        <q-btn v-if="props.value" push color="red" icon="error" flat dense>
          <q-popup-proxy>
            <q-banner v-html="props.value" class="block"/>
          </q-popup-proxy>
        </q-btn>
      </q-td>
    </template>
    <template #body-cell-station_work_name="props">
      <q-td :props="props">
        <q-chip square color="primary" text-color="white" clickable dense>{{
          props.value
        }}</q-chip>
      </q-td>
    </template>
    <template #body-cell-action="props">
      <q-td :props="props">
        <q-toggle v-model="selectedStatus[props.row.id]" dense toggle-indeterminate checked-icon="check" color="green"
          unchecked-icon="clear" true-value="DONE" false-value="REPAIRING" indeterminate-value="PROCESSING" />
      </q-td>
    </template>
    <template #body-cell-good="props">
      <q-td :props="props">
        <image-by-text v-bind="{
          src: props.row.attributes['template'] ?? props.row.good.image_src,
          text: props.row.good.name,
        }" />
      </q-td>
    </template>
    <template #header-cell-action="props">
      <q-th :props="props">
        <div class="grid gap-3 text-center">
          <j-toggle v-model="status_all" dense toggle-indeterminate checked-icon="check" color="primary" keep-color
            unchecked-icon="clear" true-value="DONE" false-value="REPAIRING" indeterminate-value="PROCESSING"
            class="mx-auto" @update:model-value="onToggleSelectAll" />

          <j-btn icon="save" flat dense color="primary" @click="save" />
        </div>
      </q-th>
    </template>
    <template #item-action="props">
      <q-toggle v-model="props.row.status" dense toggle-indeterminate checked-icon="check" color="green"
        unchecked-icon="clear" true-value="DONE" false-value="REPAIRING" indeterminate-value="PROCESSING" />
    </template>
  </j-table>
  <j-dialog-bar v-model="dialog">
    <template #title>{{ station_work.name }}</template>
    <checklistWorkStation :name="station_work.key" v-bind="{
      data: selected && selected.length > 0 ? selected : dataFiltered,
      attributes,
    }" @afterSave="$emit('afterSave')" />
  </j-dialog-bar>
</template>
<script>
import { computed, ref, watch } from "vue";
import checklistWorkStation from "./checklistWorkStation/index.vue";
import { useQuasar } from "quasar";
import { useRoute } from "vue-router";
import { api } from "@/boot/axios";
import ImageByText from "../productionOrder/components/ImageByText.vue";
export default {
  components: { checklistWorkStation, ImageByText },
  props: {
    attributes: Array,
    attribute_columns: Array,
    items: Array,
    station_work: Object,
    url: String,
  },
  setup(props, { emit }) {
    const $q = useQuasar();
    const route = useRoute();
    const url =
      "/production/station/" + route.params.id + "/new_production_checklist";

    const data = ref(props.items ?? []);
    watch(
      () => props.items,
      (newVal) => {
        data.value = newVal;
        onFilter(filters.value);
        if (selected.value.length > 0) {
          selected.value = selected.value.filter((f) =>
            data.value.map((m) => m.id).includes(f.id)
          );
        }
      }
    );

    const dialog = ref(false);
    const columns = computed(() => {
      const attribute_columns = props.attribute_columns.filter(
        (f) =>
          data.value
            .map((m) => Object.keys(m.attributes_label))
            .flat()
            .unique()
            .indexOf(f + "") !== -1
      );
      return [
        ...[
          {
            name: "index",
            label: "#",
            field: "index",
            headerClasses: "w-10",
          },

          {
            name: "description",
            label: "توضیحات",
            field: "description",
            vertical: true,
            headerClasses: "w-10",
            align: "center",

          },

          (() => {
            if (!props.station_work.key)
              return {
                name: "action",
                label: "عملیات",
                align: "center",
              };
            return "";
          })(),
          {
            name: "label_code",
            label: "شناسه لیبل",
            field: (row) => row.production_order_item_id,
            filter: {
              type: "FilterSelect",
              search: (row) => row.production_order_item_id,
            },
            filterSize: "lg",

            filterOption: dataFiltered.value
              .map((row) => row.production_order_item_id)
              .unique()
              .map((m) => ({ label: m, value: m })),
          },
          // {
          //     name: "station_work_name",
          //     label: "کار",
          //     field: () => props.data.station_work_name,
          // },
          {
            name: "code",
            label: "شناسه فاکتور",
            field: (row) => row.production_order_code,
            filter: {
              type: "FilterSelect",
              search: (row) => row.production_order_code,
            },
            filterSize: "lg",

            filterOption: dataFiltered.value
              .map((row) => row.production_order_code)
              .unique()
              .map((m) => ({ label: m, value: m })),
          },
          {
            name: "delivery_date",
            label: "تاریخ سفارش",
            field: (row) => row.delivery_date,
            filter: {
              type: "FilterSelect",
              search: (row) => row.delivery_date,
            },
            filterSize: "lg",

            filterOption: dataFiltered.value
              .map((row) => row.delivery_date)
              .unique()
              .map((m) => ({ label: m, value: m })),
          },
          {
            name: "party_name",
            label: "نماینده",
            field: (row) => row.party_name,
            filter: {
              type: "FilterSelect",
              search: (row) => row.party_name,
            },
            filterSize: "lg",

            filterOption: dataFiltered.value
              .map((row) => row.party_name)
              .unique()
              .map((m) => ({ label: m, value: m })),
          },
          {
            name: "customer_name",
            label: "مشتری",
            field: (row) => row.customer_name,
            filter: {
              type: "FilterSelect",
              search: (row) => row.customer_name,
            },
            filterSize: "lg",

            filterOption: dataFiltered.value
              .map((row) => row.customer_name)
              .unique()
              .map((m) => ({ label: m, value: m })),
          },

          {
            name: "good",
            label: "کالا/خدمات",
            field: (row) => row.good_name,
            filter: {
              type: "FilterSelect",
              search: (row) => row.good_name,
            },
            filterSize: "lg",

            filterOption: dataFiltered.value
              .map((row) => row.good_name)
              .unique()
              .map((m) => ({ label: m, value: m })),
          },
        ],
        ...props.attributes
          .sort(function (a, b) {
            if (a.pivot) {
              if (a.pivot.sort < b.pivot.sort) return -1;
              if (a.pivot.sort > b.pivot.sort) return 1;
            } else {
              if (a.sort < b.sort) return -1;
              if (a.sort > b.sort) return 1;
            }

            return 0;
          })
          .filter((f) => attribute_columns.includes(f.key))
          .map((m) => {

            const field = (row) => {
              let res = row.attributes_label[m.key] ?? "";
              // if (m.key == "countParvaz" && !res)
              //   res = row.attributes[m.key] ?? "";
              // if (m.key == "perLengthParvaz" && !res)
              //   res = row.attributes[m.key] ?? "";
              // if (
              //   [12].includes(row.group_id) &&
              //   ["countParvaz", "countDakheli"].includes(m.key)
              // )
              //   return 2.5;
              // if (
              //   [21, 22].includes(row.group_id) &&
              //   ["countParvaz", "countDakheli"].includes(m.key)
              // )
              //   return 1;
              // if (["widthParvaz"].includes(m.key) && !res) return 7;
              return res;
            };
            return {
              name: m.key,
              label: m.name,
              vertical: true,
              align: "center",
              field,
              class: "text-vertical",
              filter: {
                type: "FilterSelect",
                search: field,
              },
              filterSize: "lg",
              filterOption: dataFiltered.value
                .map((row) => row.attributes_label[m.key] ?? "")
                .unique()
                .map((m) => ({ label: m, value: m })),
            };
          }),
      ].filter((f) => f);
    });
    const isShowImage = ref(false);
    const urlImage = ref("");
    const dataFiltered = ref([]);

    const filters = ref({});

    const resetSelectConfirm = () => {
      data.value.map((m) => {
        m.status = "PROCESSING";
      });
    };
    const onFilter = (filters) => {
      status_all.value = false
      selectedStatus.value = {}
      dataFiltered.value = data.value
        .filter((item) => {
          let condition = true;
          Object.keys(filters).map((filter_key) => {
            if (
              typeof filters[filter_key].value == "object" &&
              filters[filter_key].value.length > 0
            ) {
              if (
                !filters[filter_key].value.includes(
                  filters[filter_key].search(item)
                )
              )
                condition = false;
            } else if (
              typeof filters[filter_key].value !== "object" &&
              filters[filter_key].value &&
              !String(filters[filter_key].search(item)).includes(
                filters[filter_key].value
              )
            )
              condition = false;
          });

          return condition;
        })
        .map((m, i) => {
          m.index = i + 1;
          return m;
        });
      resetSelectConfirm();
    };

    const selected = ref([]);
    const status_all = ref("");
    const selectedStatus = ref({});
    const onToggleSelectAll = (value) => {
      dataFiltered.value.map((m) => {
        selectedStatus.value[m.id] = value;
      });
    };
    return {
      print(name) {
        dialog.value = true;
        // print(selected.value && selected.value.length > 0 ? selected.value : dataFiltered.value, name, props.attributes);
      },
      status_all,
      onToggleSelectAll,
      dialog,
      selected,
      selectedStatus,
      columns,
      dataFiltered,
      isShowImage,
      urlImage,
      showImage(value) {
        urlImage.value = value;
        isShowImage.value = true;
      },
      filters,
      onFilter,
      save() {
        $q.dialog({
          title: "ذخیره",
          message: "آیا مطمئن هستید ذخیره شود؟",
          cancel: true,
          persistent: true,
        }).onOk(() => {
          api
            .put("/production/production_checklist", {
              data: selectedStatus.value,
            })
            .then(() => {
              emit("afterSave");
            });
        });
      },
    };
  },
};
</script>
<style>
.q-toggle__inner--falsy {
  color: #ff4242;
}

.q-toggle__inner--falsy .q-toggle__thumb:after {
  background-color: currentColor;
}

.q-toggle__inner--falsy .q-toggle__thumb .q-icon {
  color: #fff;
  opacity: 1;
}

.my-sticky-dynamic .q-table__top,
.my-sticky-dynamic .q-table__bottom,
.my-sticky-dynamic thead tr:first-child th {
  background-color: #fff;
}

.my-sticky-dynamic thead tr th {
  position: sticky;
  z-index: 1;
}

.my-sticky-dynamic thead tr:last-child th {
  top: 48px;
}

.my-sticky-dynamic thead tr:first-child th {
  top: 0;
}
</style>