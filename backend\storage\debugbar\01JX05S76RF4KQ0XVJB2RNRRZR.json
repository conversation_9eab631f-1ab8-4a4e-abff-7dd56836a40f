{"__meta": {"id": "01JX05S76RF4KQ0XVJB2RNRRZR", "datetime": "2025-06-05 17:21:40", "utime": **********.760682, "method": "GET", "uri": "/api/production/party?page=1&sortBy=id&rowsPerPage=10", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.445108, "end": **********.760697, "duration": 0.31558895111083984, "duration_str": "316ms", "measures": [{"label": "Booting", "start": **********.445108, "relative_start": 0, "end": **********.663985, "relative_end": **********.663985, "duration": 0.*****************, "duration_str": "219ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.663998, "relative_start": 0.*****************, "end": **********.760699, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "96.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.6837, "relative_start": 0.*****************, "end": **********.68791, "relative_end": **********.68791, "duration": 0.004209995269775391, "duration_str": "4.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.750044, "relative_start": 0.*****************, "end": **********.758574, "relative_end": **********.758574, "duration": 0.008529901504516602, "duration_str": "8.53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.758599, "relative_start": 0.****************, "end": **********.758614, "relative_end": **********.758614, "duration": 1.5020370483398438e-05, "duration_str": "15μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.0.1", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "api.erp.test:8000", "Timezone": "Asia/Tehran", "Locale": "fa"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0061600000000000005, "accumulated_duration_str": "6.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '107' limit 1", "type": "query", "params": [], "bindings": ["107"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7041152, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "raasa", "explain": null, "start_percent": 0, "width_percent": 30.519}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.712232, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "raasa", "explain": null, "start_percent": 30.519, "width_percent": 5.357}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-06-05 17:21:40', `personal_access_tokens`.`updated_at` = '2025-06-05 17:21:40' where `id` = 107", "type": "query", "params": [], "bindings": ["2025-06-05 17:21:40", "2025-06-05 17:21:40", 107], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.7139459, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "raasa", "explain": null, "start_percent": 35.877, "width_percent": 34.578}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.720815, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "raasa", "explain": null, "start_percent": 70.455, "width_percent": 5.357}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.7256958, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "raasa", "explain": null, "start_percent": 75.812, "width_percent": 6.494}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.72733, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "raasa", "explain": null, "start_percent": 82.305, "width_percent": 6.006}, {"sql": "select count(*) as aggregate from `_pro__parties` where `_pro__parties`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/DataPagination.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Traits\\DataPagination.php", "line": 27}, {"index": 22, "namespace": null, "name": "Modules/Production/Http/Controllers/PartyController.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Http\\Controllers\\PartyController.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.743683, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DataPagination.php:27", "source": {"index": 16, "namespace": null, "name": "app/Traits/DataPagination.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Traits\\DataPagination.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FTraits%2FDataPagination.php&line=27", "ajax": false, "filename": "DataPagination.php", "line": "27"}, "connection": "raasa", "explain": null, "start_percent": 88.312, "width_percent": 5.357}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`deleted_at` is null limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/DataPagination.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Traits\\DataPagination.php", "line": 27}, {"index": 22, "namespace": null, "name": "Modules/Production/Http/Controllers/PartyController.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Http\\Controllers\\PartyController.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.744905, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "DataPagination.php:27", "source": {"index": 16, "namespace": null, "name": "app/Traits/DataPagination.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Traits\\DataPagination.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FTraits%2FDataPagination.php&line=27", "ajax": false, "filename": "DataPagination.php", "line": "27"}, "connection": "raasa", "explain": null, "start_percent": 93.669, "width_percent": 6.331}]}, "models": {"data": {"Modules\\Production\\Entities\\Party": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FParty.php&line=1", "ajax": false, "filename": "Party.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 12, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => parties,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-351975084 data-indent-pad=\"  \"><span class=sf-dump-note>parties </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">parties</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-351975084\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.730026, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://api.erp.test:8000/api/production/party?page=1&rowsPerPage=10&sortBy=id", "action_name": "party.index", "controller_action": "Modules\\Production\\Http\\Controllers\\PartyController@index", "uri": "GET api/production/party", "controller": "Modules\\Production\\Http\\Controllers\\PartyController@index<a href=\"phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FHttp%2FControllers%2FPartyController.php&line=35\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Modules\\Production\\Http\\Controllers", "prefix": "api/production", "file": "<a href=\"phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FHttp%2FControllers%2FPartyController.php&line=35\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">Modules/Production/Http/Controllers/PartyController.php:35-39</a>", "middleware": "api, auth:sanctum", "duration": "317ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-610206070 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>sortBy</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>rowsPerPage</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-610206070\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-638826704 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-638826704\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-93909907 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">api.erp.test:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 107|t******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://panel.erp.test:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://panel.erp.test:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">fa-IR,fa;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-93909907\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1308034108 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1308034108\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-394021922 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 05 Jun 2025 13:51:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-394021922\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1063751822 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1063751822\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://api.erp.test:8000/api/production/party?page=1&rowsPerPage=10&sortBy=id", "action_name": "party.index", "controller_action": "Modules\\Production\\Http\\Controllers\\PartyController@index"}, "badge": null}}