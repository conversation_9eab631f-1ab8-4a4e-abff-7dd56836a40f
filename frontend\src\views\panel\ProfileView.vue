<script>
import { api } from '@/boot/axios';
import { ref } from 'vue-demi';

export default {
    setup() {
        api.get('user').then(res => {
            form.value = res;
        })
        const form = ref({})
        return {
            form,
            submit() {
                api.put('user', form.value);
            },
            isPwd: ref(true)
        }
    },
}
</script>

<template>
    <div class="profile-panel">
        <div class="text-h5 q-mb-md">پروفایل کاربری - پنل مدیریت</div>
        <j-form @submit="submit" class="bg-white p-5 shadow-md rounded">
            <j-input v-model="form.full_name" label="نام و نام خانوادگی" error-field="name" disable autofocus />
            <j-input v-model="form.username" label="نام کاربری" disable error-field="username" />
            <j-input v-model="form.password" :type="isPwd ? 'password' : 'text'" label="کلمه عبور جدید"
                error-field="password">
                <template v-slot:append>
                    <q-icon :name="isPwd ? 'visibility_off' : 'visibility'" class="cursor-pointer"
                        @click="isPwd = !isPwd" />
                </template>
            </j-input>
            <j-input v-model="form.confirm_password" :type="isPwd ? 'password' : 'text'" label="تکرار کلمه عبور جدید"
                error-field="confirm_password">
                <template v-slot:append>
                    <q-icon :name="isPwd ? 'visibility_off' : 'visibility'" class="cursor-pointer"
                        @click="isPwd = !isPwd" />
                </template>
            </j-input>
            <j-btn type="submit" label="ذخیره" icon="save" color="primary" />
        </j-form>
    </div>
</template>

<style scoped>
.profile-panel {
    padding: 16px;
}
</style>
