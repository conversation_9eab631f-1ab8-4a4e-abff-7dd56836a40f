import { defineStore } from "pinia";
import { api } from "@/boot/axios";
import { router } from "@/helpers";
import { Notify } from "quasar";
import { routes } from "@/helpers";
import { filterAsyncRoutes } from "@/helpers/router/middleware";
import { API_ENDPOINTS, STORAGE_KEYS, ROUTES } from "@/helpers";

export const useAuthStore = defineStore({
    id: "auth",
    state: () => ({
        // initialize state from local storage to enable user to stay logged in
        user: JSON.parseForce(localStorage.getItem(STORAGE_KEYS.USER)),
        token: localStorage.getItem(STORAGE_KEYS.TOKEN),
        returnUrl: null,
        roles: [],
        verifyMode: false,
        permissions: [],
        is_super_admin: false,
        loading: false,
        initialized: false
    }),
    actions: {
        hasPermission(permission) {
            return this.is_super_admin ?? this.permissions.includes(permission);
        },
        async login(form) {
            this.loading = true;

            try {
                const response = await api.post(API_ENDPOINTS.LOGIN, form);

                // Set token
                const token = response.result?.token || response.token;
                if (token) {
                    api.defaults.headers.common["Authorization"] = `Bearer ${token}`;
                    localStorage.setItem(STORAGE_KEYS.TOKEN, token);
                    this.token = token;
                }

                // Get user data
                await this.getUser();

                // Navigate to return URL or dashboard
                //const redirectTo = this.returnUrl || ROUTES.DASHBOARD;
                if (this.returnUrl)
                    router.push(this.returnUrl);
                else if (ROUTES.DASHBOARD)
                    router.push({ name: ROUTES.DASHBOARD })

                // Notify.create({
                //     type: "positive",
                //     message: "با موفقیت وارد شدید"
                // });

                return response;
            } catch (error) {
                const message = error.response?.data?.message || "خطا در ورود";
                Notify.create({
                    type: "negative",
                    message
                });
                throw error;
            } finally {
                this.loading = false;
            }
        },

        // async sendVerifyCode(form) {
        //     return api
        //         .post("/sendVerifyCode", form)
        //         .then(async (response) => {
        //             if (response.ok) {
        //                 this.verifyMode = true;
        //             }
        //         })
        //     // .catch((e) => {
        //     //     if (e.response.data.message)
        //     //         Notify.create({
        //     //             type: "negative",
        //     //             message: e.response.data.message,
        //     //         });
        //     // });
        // },
        // async loginByPhone(form) {
        //     return api
        //         .post("/loginByPhone", form)
        //         .then(async (response) => {
        //             api.defaults.headers.common["Authorization"] =
        //                 "Bearer " + response.result.token;
        //             localStorage.setItem("token", response.result.token);
        //             this.token = response.result.token;
        //             await this.getUser();
        //             router.push(this.returnUrl || "/");
        //         })
        //     // .catch((e) => {
        //     //     if (e.response.data.message)
        //     //         Notify.create({
        //     //             type: "negative",
        //     //             message: e.response.data.message,
        //     //         });
        //     // });
        // },
        async getUser() {
            if (!this.token) {
                this.initialized = true;
                return null;
            }

            try {
                // Set authorization header
                api.defaults.headers.common["Authorization"] = `Bearer ${this.token}`;

                const response = await api.get(API_ENDPOINTS.USER, {
                    xsrfCookieName: '',
                    xsrfHeaderName: '',
                });

                this.user = response.data || response;
                localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(this.user));

                // Set permissions and roles
                this.permissions = this.user.permissions || [];
                this.roles = this.user.roles || [];
                this.is_super_admin = this.user.is_super_admin || false;
                this.initialized = true;

                console.log('User data loaded:', this.user);
                return this.user;
            } catch (error) {
                console.error('Error fetching user:', error);
                // If token is invalid, logout
                if (error.response?.status === 401) {
                    console.log('Token invalid, logging out');
                    this.logout();
                }
                this.initialized = true;
                throw error;
            }
        },

        async initializeAuth() {
            console.log('initializeAuth called');

            // Prevent multiple initializations
            if (this.initialized) {
                console.log('Auth already initialized, skipping');
                return;
            }

            // Set authorization header if token exists
            if (this.token) {
                api.defaults.headers.common["Authorization"] = `Bearer ${this.token}`;

                // Get user data on app startup
                try {
                    await this.getUser();
                    console.log('Auth initialized successfully');
                } catch (error) {
                    console.error('Failed to initialize auth:', error);
                    // Mark as initialized even on error to prevent retries
                    this.initialized = true;
                }
            } else {
                this.initialized = true;
                console.log('No token found, auth initialization skipped');
            }
        },

        init() {
            this.user = null;
            this.token = null;
            this.returnUrl = null;
            this.roles = [];
            this.permissions = [];
            this.verifyMode = false;
            this.is_super_admin = false;
            this.loading = false;
            this.initialized = false;

            // Clear localStorage
            localStorage.removeItem(STORAGE_KEYS.USER);
            localStorage.removeItem(STORAGE_KEYS.TOKEN);

            // Clear API authorization header
            delete api.defaults.headers.common["Authorization"];
        },

        async logout() {
            try {
                // Call logout API if needed
                // await api.post(API_ENDPOINTS.LOGOUT);
            } catch (error) {
                console.error('Error during logout:', error);
            } finally {
                this.init();
                router.push(ROUTES.LOGIN);

                Notify.create({
                    type: "info",
                    message: "با موفقیت خارج شدید"
                });
            }
        },
        async routes() {
            //console.log(routes)
            //return routes;
            if (this.permissions == null) await this.getUser();
            return filterAsyncRoutes(routes, {
                is_super_admin: this.is_super_admin,
                roles: this.roles,
                permissions: this.permissions,
            });
        },
    },
});
