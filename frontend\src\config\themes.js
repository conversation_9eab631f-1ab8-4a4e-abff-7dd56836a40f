/**
 * Theme configuration for different domains
 */

import { getCurrentDomainType, DOMAIN_TYPES } from './domains';

export const PANEL_THEME = {
    name: 'پنل ایران پارس',
    colors: {
        primary: '#1976d2',
        secondary: '#26a69a',
        accent: '#9c27b0',
        positive: '#21ba45',
        negative: '#c10015',
        info: '#31ccec',
        warning: '#f2c037'
    },
    logo: '/images/panel-logo.svg',
    favicon: '/images/panel-favicon.ico',
    manifest: {
        name: "پنل ایران پارس",
        short_name: "ایران پارس",
        description: "پنل مدیریت تولید",
        theme_color: "#1976d2"
    }
};

export const CRM_THEME = {
    name: 'CRM ایران پارس',
    colors: {
        primary: '#26a69a',
        secondary: '#1976d2',
        accent: '#9c27b0',
        positive: '#21ba45',
        negative: '#c10015',
        info: '#31ccec',
        warning: '#f2c037'
    },
    logo: '/images/crm-logo.svg',
    favicon: '/images/crm-favicon.ico',
    manifest: {
        name: "CRM ایران پارس",
        short_name: "CRM",
        description: "سیستم مدیریت ارتباط با مشتری",
        theme_color: "#26a69a"
    }
};

/**
 * Get current theme based on domain
 * @returns {object} Theme configuration
 */
export function getCurrentTheme() {
    const domainType = getCurrentDomainType();
    
    switch (domainType) {
        case DOMAIN_TYPES.CRM:
            return CRM_THEME;
        case DOMAIN_TYPES.PANEL:
        default:
            return PANEL_THEME;
    }
}

/**
 * Apply theme to Quasar
 * @param {object} $q - Quasar instance
 */
export function applyTheme($q) {
    const theme = getCurrentTheme();
    
    // Apply colors to CSS variables
    const root = document.documentElement;
    Object.entries(theme.colors).forEach(([key, value]) => {
        root.style.setProperty(`--q-${key}`, value);
    });
    
    // Update document title
    document.title = theme.name;
    
    // Update favicon
    const favicon = document.querySelector('link[rel="icon"]');
    if (favicon) {
        favicon.href = theme.favicon;
    }
}

/**
 * Get theme-specific CSS variables
 * @returns {object} CSS variables
 */
export function getThemeVariables() {
    const theme = getCurrentTheme();
    const variables = {};
    
    Object.entries(theme.colors).forEach(([key, value]) => {
        variables[`--q-${key}`] = value;
    });
    
    return variables;
}
