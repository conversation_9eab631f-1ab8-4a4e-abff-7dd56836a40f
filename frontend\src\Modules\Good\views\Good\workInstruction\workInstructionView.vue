<template>
  <div class="q-gutter-sm">
    <j-table-data :url="`/production/good/${$route.params.id}/workInstructions`" dense :columns="columns"
      @loaded="loadedData">
      <template #dialog="props">
        <table-form v-bind="props" :good-id="$route.params.id" />
      </template>
      <template #body-cell-parents="props">
        <q-td :props="props">
          <q-chip v-for="parent, index in props.row.parents" :key="index" square text-color="white" color="primary" dense>{{
              parent.station_work ? parent.station_work.name : ''
          }}</q-chip>
        </q-td>
      </template>
    </j-table-data>
  </div>
</template>

<script>
import { usePublicStore } from "@/stores";
import TableForm from "./form.vue";

export default {
  setup() {

    const columns = [
      {
        name: 'name',
        required: true,
        label: 'نام',
        field: row => row.station_work ? row.station_work.name : '',
        sortable: true,
        filter: 'FilterInput',
      },
      {
        name: 'station',
        required: true,
        label: 'ایستگاه',
        field: row => row.station_work.station.name,
        sortable: true,
        filter: 'FilterInput',
      },
      {
        name: 'parents',
        required: true,
        label: 'پیش نیازها',
        field: row => row.parents.map(m => m.station_work.name).join(' , '),
      },

    ]
    const publicStore = usePublicStore();
    const loadedData = (value) => {
      publicStore.titleWindow = 'دستورالعمل ' + value.model.name
    }
    return {
      columns,
      loadedData,
    };
  },
  components: { TableForm },
};
</script>
