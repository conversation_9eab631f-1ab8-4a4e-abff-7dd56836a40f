{"__meta": {"id": "01JX05QJK4TAJCNT01CK10RD1P", "datetime": "2025-06-05 17:20:46", "utime": **********.885774, "method": "GET", "uri": "/api/production/production_order?page=1&sortBy=id&rowsPerPage=10", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.445873, "end": **********.885794, "duration": 0.43992090225219727, "duration_str": "440ms", "measures": [{"label": "Booting", "start": **********.445873, "relative_start": 0, "end": **********.668207, "relative_end": **********.668207, "duration": 0.****************, "duration_str": "222ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.668223, "relative_start": 0.****************, "end": **********.885796, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.684472, "relative_start": 0.*****************, "end": **********.687035, "relative_end": **********.687035, "duration": 0.002562999725341797, "duration_str": "2.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.758639, "relative_start": 0.*****************, "end": **********.876488, "relative_end": **********.876488, "duration": 0.*****************, "duration_str": "118ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.876528, "relative_start": 0.*****************, "end": **********.876564, "relative_end": **********.876564, "duration": 3.600120544433594e-05, "duration_str": "36μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.0.1", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "api.erp.test:8000", "Timezone": "Asia/Tehran", "Locale": "fa"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 25, "nb_statements": 25, "nb_visible_statements": 25, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02565, "accumulated_duration_str": "25.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '106' limit 1", "type": "query", "params": [], "bindings": ["106"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.706733, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "raasa", "explain": null, "start_percent": 0, "width_percent": 6.628}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.716785, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "raasa", "explain": null, "start_percent": 6.628, "width_percent": 1.559}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.7211108, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "raasa", "explain": null, "start_percent": 8.187, "width_percent": 1.559}, {"sql": "select count(*) as aggregate from `_pro__production_orders` where `_pro__production_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/DataPagination.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Traits\\DataPagination.php", "line": 27}, {"index": 22, "namespace": null, "name": "Modules/Production/Http/Controllers/ProductionOrderController.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Http\\Controllers\\ProductionOrderController.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.753663, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DataPagination.php:27", "source": {"index": 16, "namespace": null, "name": "app/Traits/DataPagination.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Traits\\DataPagination.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FTraits%2FDataPagination.php&line=27", "ajax": false, "filename": "DataPagination.php", "line": "27"}, "connection": "raasa", "explain": null, "start_percent": 9.747, "width_percent": 1.404}, {"sql": "select * from `_pro__production_orders` where `_pro__production_orders`.`deleted_at` is null limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/DataPagination.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Traits\\DataPagination.php", "line": 27}, {"index": 22, "namespace": null, "name": "Modules/Production/Http/Controllers/ProductionOrderController.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Http\\Controllers\\ProductionOrderController.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.755206, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DataPagination.php:27", "source": {"index": 16, "namespace": null, "name": "app/Traits/DataPagination.php", "file": "A:\\dev\\workspace\\new erp\\backend\\app\\Traits\\DataPagination.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FTraits%2FDataPagination.php&line=27", "ajax": false, "filename": "DataPagination.php", "line": "27"}, "connection": "raasa", "explain": null, "start_percent": 11.15, "width_percent": 1.754}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.7714958, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 12.904, "width_percent": 1.559}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 1 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.773974, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 14.464, "width_percent": 1.949}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.7785718, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 16.413, "width_percent": 1.559}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 3 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.780626, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 17.973, "width_percent": 3.821}, {"sql": "select * from `users` where `users`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.783794, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 21.793, "width_percent": 1.52}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 31 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.7856998, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 23.314, "width_percent": 1.598}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.7889428, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 24.912, "width_percent": 1.676}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 31 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.79072, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 26.589, "width_percent": 1.559}, {"sql": "select * from `users` where `users`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.7938728, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 28.148, "width_percent": 1.598}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 2 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.795551, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 29.747, "width_percent": 1.754}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.8016481, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 31.501, "width_percent": 1.949}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 5 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.8041089, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 33.45, "width_percent": 2.183}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.8079932, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 35.634, "width_percent": 1.715}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 1 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.809932, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 37.349, "width_percent": 3.353}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.815119, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 40.702, "width_percent": 8.928}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 1 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.8194659, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 49.63, "width_percent": 2.183}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.824323, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 51.813, "width_percent": 5.497}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 1 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.8276908, "duration": 0.0055, "duration_str": "5.5ms", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 57.31, "width_percent": 21.442}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.8388488, "duration": 0.00481, "duration_str": "4.81ms", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:129", "source": {"index": 21, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=129", "ajax": false, "filename": "ProductionOrder.php", "line": "129"}, "connection": "raasa", "explain": null, "start_percent": 78.752, "width_percent": 18.752}, {"sql": "select * from `_pro__parties` where `_pro__parties`.`id` = 2 and `_pro__parties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 135}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "A:\\dev\\workspace\\new erp\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.845577, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ProductionOrder.php:125", "source": {"index": 22, "namespace": null, "name": "Modules/Production/Entities/ProductionOrder.php", "file": "A:\\dev\\workspace\\new erp\\backend\\Modules\\Production\\Entities\\ProductionOrder.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=125", "ajax": false, "filename": "ProductionOrder.php", "line": "125"}, "connection": "raasa", "explain": null, "start_percent": 97.505, "width_percent": 2.495}]}, "models": {"data": {"App\\Models\\User": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Modules\\Production\\Entities\\ProductionOrder": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FProductionOrder.php&line=1", "ajax": false, "filename": "ProductionOrder.php", "line": "?"}}, "Modules\\Production\\Entities\\Party": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FEntities%2FParty.php&line=1", "ajax": false, "filename": "Party.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}}, "count": 30, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage_users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1063377822 data-indent-pad=\"  \"><span class=sf-dump-note>manage_users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage_users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1063377822\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.727566, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://api.erp.test:8000/api/production/production_order?page=1&rowsPerPage=10&sortBy=id", "action_name": "production_order.index", "controller_action": "Modules\\Production\\Http\\Controllers\\ProductionOrderController@index", "uri": "GET api/production/production_order", "controller": "Modules\\Production\\Http\\Controllers\\ProductionOrderController@index<a href=\"phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FHttp%2FControllers%2FProductionOrderController.php&line=38\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Modules\\Production\\Http\\Controllers", "prefix": "api/production", "file": "<a href=\"phpstorm://open?file=A%3A%2Fdev%2Fworkspace%2Fnew%20erp%2Fbackend%2FModules%2FProduction%2FHttp%2FControllers%2FProductionOrderController.php&line=38\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">Modules/Production/Http/Controllers/ProductionOrderController.php:38-43</a>", "middleware": "api, auth:sanctum", "duration": "444ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1364252001 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>sortBy</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>rowsPerPage</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1364252001\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1128150459 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1128150459\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1832796487 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">api.erp.test:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 106|A******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://panel.erp.test:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://panel.erp.test:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">fa-IR,fa;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1832796487\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1932468080 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1932468080\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1026318530 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 05 Jun 2025 13:50:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1026318530\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1641231416 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1641231416\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://api.erp.test:8000/api/production/production_order?page=1&rowsPerPage=10&sortBy=id", "action_name": "production_order.index", "controller_action": "Modules\\Production\\Http\\Controllers\\ProductionOrderController@index"}, "badge": null}}