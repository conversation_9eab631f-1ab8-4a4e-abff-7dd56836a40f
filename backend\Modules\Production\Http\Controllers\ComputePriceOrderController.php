<?php

namespace Modules\Production\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Good\Entities\Attribute;
use Modules\Good\Entities\AttributeItem;
use Modules\Good\Entities\Good;

class ComputePriceOrderController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function computePriceOrder(Request $request)
    {
        $good = $request->input('good');
        $attributes = $request->input('attributes');
        $good = Good::find($good['id']);

        $good_price = 0;
        $attributes_price = 0;

        if ($good) {
            $good_price = $good->metas()->where('key', 'price')->pluck('value')->first();
            $good_price = $good_price ? $good_price * 1 : 0;
        }

        if ($attributes) {
            // return AttributeItem::query()->whereHas('attribute', function ($q) {
            //     $q->whereIn('type', [Attribute::TYPE_SELECT, Attribute::TYPE_SELECT_IMAGE]);
            // })->whereIn('key', array_values($attributes))->with('metas')->get()->map(function ($m) {
            //     dd($this->priceAttributeCover($m));
            // });
            $attributes_price = AttributeItem::query()->whereHas('attribute', function ($q) {
                $q->whereIn('type', [Attribute::TYPE_SELECT, Attribute::TYPE_SELECT_IMAGE]);
            })->whereIn('key', array_values($attributes))->with('metas', function ($query) {
                $query->where('key', 'price');
            })->get()->map(function ($m) {

                $m->price = $m->metas->pluck('value')->first();
                $m->price = $m->price ? $m->price * 1 : 0;
                return [
                    "id" => $m['id'],
                    "key" => $m['key'],
                    "name" => $m['name'],
                    "price" => $m['price'],
                ];
            })->sum('price');
            // $good->attributes = Attribute::query()->whereIn('key', array_keys($attributes))->with('items.metas', function($query){
            //     return $query->where('key', 'price');
            // })->get();
        }
        return [
            'good_price' => $good_price,
            'attributes_price' => $attributes_price,
            'sum' => $good_price + $attributes_price,
        ];
    }

    private function priceDoor($good, $attributes)
    {
        switch ($good->id) {
            case 11: // door cnc
                $ex = explode("_", $attributes['modelDoorCNC']);
                switch ($ex[0]) {
                    case 'RC':
                        return 12e6;
                    case 'RCL':
                        return 13e6;
                    case 'RCM':
                        return 14e6;
                    case 'RCN':
                        return 15e6;
                    case 'RCZ':
                        return 16e6;
                    default:
                        return 0;
                }
                break;
            case 12: // door rapingi
                return 17e6;
            case 13: // frame
                return 18e6;
            case 14: // rokoob
                return 19e6;
        }
    }


    private function priceAttributeCover($attributes)
    {
        switch ($attributes->metas->where('key', 'type')->pluck('value')->first()) {
            case '0.2':
                return 5e6;
            case '0.4':
                return 6e6;
            case 'supermat':
                return 7e6;
            default:
                return 0;
        }
    }


    private function priceAttributeTypeMaterialDoor($attributes)
    {
        switch ($attributes['typeMaterialDoor']) {
            case 'mdf':
                switch ($attributes['sheetCNCThickness']) {
                    case '5':
                        return 20e6;
                    case '8':
                        return 30e6;
                    case '12':
                        return 40e6;
                    default:
                        return 0;
                }
            case 'fomizeh':
                switch ($attributes['sheetCNCThickness']) {
                    case '5':
                        return 25e6;
                    case '8':
                        return 35e6;
                    case '12':
                        return 45e6;
                    default:
                        return 0;
                }

            default:
                return 0;
        }
    }
}
