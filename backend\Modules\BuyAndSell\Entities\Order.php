<?php

namespace Modules\BuyAndSell\Entities;

interface OrderStatus
{
    const DRAFT = "DRAFT";
    const PENDING_CONFIRM = "PENDING_CONFIRM";
    const CONFIRM = "CONFIRM";

    const FINANCIAL_APPROVAL = "FINANCIAL_APPROVAL";
    const PRODUCTION = "PRODUCTION";
    const READY_DELIVERY = "READY_DELIVERY";
    const FINANCIAL_SETTLEMENT = "FINANCIAL_SETTLEMENT";

    const DELIVERED = "DELIVERED";
}

class Order extends BModel implements OrderStatus
{
    protected $fillable = [
        'type',
        'status',
        'party_id',
        'customer_name',
        'description',
    ];

    public static $statuses = [
        [
            'value' => self::DRAFT,
            'label' => 'پیش فاکتور',
        ],
        [
            'value' => self::PENDING_CONFIRM,
            'label' => 'در انتظار تایید',
        ],
        [
            'value' => self::CONFIRM,
            'label' => 'فاکتور',
        ],
        [
            'value' => self::FINANCIAL_APPROVAL,
            'label' => 'تسویه پیش پرداخت',
        ],
        [
            'value' => self::PRODUCTION,
            'label' => 'درحال تولید',
        ],
        [
            'value' => self::READY_DELIVERY,
            'label' => 'آماده تحویل',
        ],
        [
            'value' => self::FINANCIAL_SETTLEMENT,
            'label' => 'تسویه فاکتور',
        ],
        [
            'value' => self::DELIVERED,
            'label' => 'حمل شده',
        ],
    ];

    protected $appends = [
        // 'user_name',
        'party_name',
        'label_status',
        'previous_status',
        'next_status',
        //'code',
    ];

    public static function boot()
    {
        parent::boot();
        self::created(function ($model) {
            $model->code = $model->getCode();
            $model->save();
        });
    }

    public function getPartyNameAttribute()
    {
        return $this->party->full_name ?? '';
    }
    // public function getUserNameAttribute()
    // {
    //     return $this->user ? $this->user->name : '';
    // }
    public function getLabelStatusAttribute()
    {
        return collect(self::$statuses)->where('value', $this->status)->pluck('label')->first();
    }
    public function getPreviousStatusAttribute()
    {
        $status = $this->status;
        $find = collect(self::$statuses)->search(function ($item, $key) use ($status) {
            return $item['value'] == $status;
        });
        if ($find == 0) {
            return null;
        }
        return self::$statuses[$find - 1];

        // if (isset(self::$statuses[$find]['previous'])) {
        //     $find =  collect(self::$statuses)->search(fn ($item, $key) => $item['value'] == self::$statuses[$find]['previous']);
        //     return self::$statuses[$find];
        // }
        // return self::$statuses[$find - 1];
    }
    public function getNextStatusAttribute()
    {
        $status = $this->status;
        $find = collect(self::$statuses)->search(function ($item, $key) use ($status) {
            return $item['value'] == $status;
        });
        return count(self::$statuses) == $find + 1 ? null : self::$statuses[$find + 1];
    }
    
    public function getCode()
    {
        return substr($this['created_at'], 0, 4) . substr((10e3  + $this->id), 1);
    }

    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }
    
    public function party()
    {
        return $this->belongsTo(Party::class);
    }

    public function checklists()
    {
        return $this->hasMany(OrderCheckList::class);
    }
}
