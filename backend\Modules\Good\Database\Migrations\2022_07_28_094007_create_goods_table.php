<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Good\Entities\Category;
use Modules\Good\Entities\Good;
use Modules\Good\Entities\Group;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create(Good::getTableName(), function (Blueprint $table) {
            $table->id();
            $table->string('name');
            // $table->foreignIdFor(Category::class)->constrained(Category::getTableName());
            $table->foreignIdFor(Group::class)->nullable()->constrained(Group::getTableName());
            $table->enum('type', collect(Good::types)->pluck('value')->toArray());
            $table->string('image_src')->nullable();
            $table->json('default_attribute')->default('{}')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_active_customer')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists(Good::getTableName());
    }
};
