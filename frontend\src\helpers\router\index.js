import { createRouter, create<PERSON>eb<PERSON><PERSON><PERSON> } from "vue-router";
import { iconImage } from '@/helpers'
import { getCurrentDomainType, DOMAIN_TYPES } from '@/config/domains';
import {
    DashboardView,
    PanelLoginView,
    page404View,
    page403View,
    profileView,
    CRMprofileView,
    CrmLoginView,
    CrmDashboardView,
    membershipRequestView,
    partyOnlineView
} from "@/views/index";
// import MainLayout from "@/views/layout/index.vue";

import ModulesRouter from "@/Modules/routers.js";
import { useAuthStore } from "@/stores/auth.store";
import { CRMOrderForm, CRMOrderView, sellView } from "@/Modules/Production/views/index";
import middlewarePermissions from "./middlewares/permission";
import middlewareRoles from "./middlewares/role";
import auth from "./middlewares/auth";
import middlewareDisable from "./middlewares/disable";
import { usePublicStore } from "@/stores";


export const crm_home_page = () => {
    const authStore = useAuthStore();
    if (authStore.user?.enable)
        return 'crm_production_order.index'; //
}
//export const route403 = () => '403'

const panel_routes = [
    {
        path: "/login",
        name: "login",
        component: PanelLoginView,
        hidden: true,
        meta: {
            layout: "div",
            title: "ورود",
        },
    },
    {
        path: "/",
        name: "dashboard",
        component: DashboardView,
        hidden: true,

        meta: {
            title: "آمار",
            // icon: "dashboard",
            icon: iconImage('pieChart'),
            permissions: 'fsdsd'
        },
        // beforeEnter: (to, from) => {
        //     return { name: 'production_order.index' }
        // },
    },
    {
        path: "/profile",
        name: "profile",
        component: profileView,
        hidden: true,

        meta: {
            title: "پروفایل",
            // disable: true,
        },
    },

    ...ModulesRouter.flatMap(module => (Array.isArray(module) ? module : [module])),

];



const crm_routes = [
    {
        path: "/login",
        name: "login",
        component: CrmLoginView,
        hidden: true,

        meta: {
            layout: "div",
            title: "ورود",
        },
    },
    {
        path: "/profile",
        name: "profile",
        component: CRMprofileView,
        hidden: true,

        meta: {
            title: "پروفایل",
            // disable: true,
        },
    },
    // {
    //     path: "/",
    //     name: "dashboard",
    //     component: CrmDashboardView,
    //     meta: {
    //         title: "داشبورد",
    //         icon: "dashboard",
    //         // toolbar: false,
    //     },
    //     beforeEnter: (to, from) => {
    //         const authStore = useAuthStore();
    //             return { name: 'production_order' }

    //     },
    // },
    // {
    //     path: "/membership-request",
    //     name: "membership_request",
    //     component: membershipRequestView,
    //     hidden: true,
    //     meta: {
    //         title: "درخواست عضویت",
    //     },
    //     beforeEnter: (to, from) => {
    //         const authStore = useAuthStore();
    //         console.log(!authStore.user.is_membership)
    //         if (authStore.user.is_membership)
    //             return { name: 'production_order' }

    //     },
    // },
    {
        path: "/",
        name: 'home',
        hidden: true,
        beforeEnter: (to, from) => {
            return { name: 'crm_production_order' }
        },
    },
    {
        path: "/order",
        name: "crm_production_order",
        component: CRMOrderView,
        meta: {
            title: "سفارشات",
            icon: iconImage("invoice"),
            //permissions: "crm_production_order.index",
        },
        // children: [
        //     {
        //         path: 'index',
        //         name: 'crm_production_order.index',
        //         component: CRMOrderView,
        //         meta: {
        //             title: "لیست سفارشات",
        //             icon: 'list',

        //         },
        //     },

        // ],
    },
    {
        path: '/order/create',
        name: 'crm_production_order.create',
        component: CRMOrderForm,
        hidden: true,
        meta: {
            title: "ایجاد سفارش",
            icon: 'add',
            //permissions: "crm_production_order.create",

        },
    },

    {
        path: '/order/:id/edit',
        name: 'crm_production_order.edit',
        component: CRMOrderForm,
        props: true,
        hidden: true,
        meta: {
            title: "ویرایش سفارش",
            icon: 'edit',
            //permissions: "crm_production_order.edit",

        },
    },

];
export const routes = (() => {
    let res = [];

    // Use domain configuration
    const domainType = getCurrentDomainType();

    console.log('Current domain type:', domainType);

    if (domainType === DOMAIN_TYPES.PANEL) {
        res.push(...panel_routes);
        console.log('Loading panel routes');
    } else if (domainType === DOMAIN_TYPES.CRM) {
        res.push(...crm_routes);
        console.log('Loading CRM routes');
    } else {
        // Default to panel routes
        res.push(...panel_routes);
        console.log('Loading default panel routes');
    }

    res.push(...[
        // { path: "/403", name: "403", component: page403View, hidden: true },
        { path: "/:pathMatch(.*)*", name: "404", component: page404View, hidden: true },
    ]);

    console.log('Final routes:', res);
    return res;
})();

export const router = createRouter({
    history: createWebHistory(import.meta.env.VITE_BASE_URL),
    routes,
});

// registerMiddleware(router);






// Creates a `nextMiddleware()` function which not only
// runs the default `next()` callback but also triggers
// the subsequent Middleware function.
function nextFactory(context, middleware, index) {
    const subsequentMiddleware = middleware[index];
    // If no subsequent Middleware exists,
    // the default `next()` callback is returned.
    if (!subsequentMiddleware) return context.next;

    return (...parameters) => {
        // Run the default Vue Router `next()` callback first.
        context.next(...parameters);
        // Then run the subsequent Middleware with a new
        // `nextMiddleware()` callback.
        const nextMiddleware = nextFactory(context, middleware, index + 1);
        subsequentMiddleware({ ...context, next: nextMiddleware });
    };
}

let checkAuth = false;

const middleware_groups = [
    auth,
    middlewarePermissions,
    // middlewareRoles,
    // middlewareDisable,
];

router.beforeEach(async (to, from, next) => {
    console.log('beforeEach')
    const publicStore = usePublicStore()

    if (publicStore.isNotAccess) {
        publicStore.setNotAccess(false);
    }


    document.title = ["اتوماسیون", (to.meta.title ?? '')].filter(f => f).join(' - ');

    const authStore = useAuthStore();

    // Skip duplicate auth checks if already initialized
    // The auth store is initialized in app.js on startup
    if (!(
        (to.meta.middleware && to.meta.middleware.length > 0) ||
        (typeof middleware_groups != 'undefined' && Array.isArray(middleware_groups) && middleware_groups.length > 0)
    ))
        return next();

    const context = {
        from,
        next,
        router,
        to,
        auth: authStore,
    };
    const middleware = [...(middleware_groups ? middleware_groups : []), ...(Array.isArray(to.meta.middleware)
        ? to.meta.middleware
        : [to.meta.middleware])
    ].filter(f => f);

    const nextMiddleware = nextFactory(context, middleware, 1);



    return middleware[0]({ ...context, next: nextMiddleware });




});


router.afterEach((to) => {
    console.log('afterEach')
    const publicStore = usePublicStore()
    if (publicStore.isNotFound) {
        publicStore.setNotFound(false);
    }

});