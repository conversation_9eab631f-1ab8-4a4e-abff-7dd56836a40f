<template>
    <q-field ref="_ref" :rules="rules" outlined
        :error-message="requestStore.errors[errorField] && requestStore.errors[errorField][0] ? requestStore.errors[errorField].join('') : undefined"
        :error="!!requestStore.errors[errorField]">
        <template v-for="_, slot in $slots" v-slot:[slot]="props" :key="slot">
            <slot :name="slot" v-bind="props" :key="slot" />
        </template>
    </q-field>
</template>
<script>
import { validationApi } from '@/helpers';
import { ref } from 'vue';

export default {
    props: {
        required: {
            type: Boolean,
            default: false,
        },
        errorField: {
            type: String,
            default: "",
        },
    },
    setup(props) {
        const { rules, requestStore } = validationApi(props)

        const _ref = ref(null)

        return {
            rules, requestStore,
            _ref,
            focus: () => _ref.value.focus(),

        }
    }
}
</script>
  