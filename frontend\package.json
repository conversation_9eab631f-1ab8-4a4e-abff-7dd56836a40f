{"name": "frontend", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "build": "vite build", "build:prod": "vite build --mode production", "preview": "vite preview --port 3000", "preview:prod": "vite preview --port 80", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --ignore-path .gitignore", "format": "prettier --write src/", "format:check": "prettier --check src/", "type-check": "vue-tsc --noEmit", "test": "vitest", "test:coverage": "vitest --coverage", "analyze": "vite-bundle-analyzer"}, "devDependencies": {"@quasar/vite-plugin": "^1.7.0", "@vitejs/plugin-vue": "^5.1.2", "@vue/eslint-config-prettier": "^9.0.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.20.0", "postcss": "^8.4.41", "postcss-rtlcss": "^5.3.1", "prettier": "^3.1.1", "sass": "^1.77.8", "sass-loader": "^16.0.0", "tailwindcss": "^3.4.9", "vazirmatn": "^33.0.3", "vite": "^5.0", "vite-bundle-analyzer": "^0.7.0", "vitest": "^1.0.0", "vue-tsc": "^1.8.0"}, "dependencies": {"@quasar/extras": "^1.16.12", "axios": "^1.6.4", "maska": "^3.0.0", "moment-jalaali": "^0.10.1", "pinia": "^2.2.1", "qr-code-styling": "^1.6.0-rc.1", "qrcode": "^1.5.4", "quasar": "^2.16.8", "vite-plugin-pwa": "^0.20.1", "vue": "^3.4.37", "vue-imask": "^7.6.1", "vue-inline-svg": "^3.1.3", "vue-router": "^4.4.3", "vue3-openlayers": "^11.0.1", "vue3-shortkey": "^4.0.0"}}