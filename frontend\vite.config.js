import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { VitePWA } from "vite-plugin-pwa";
import path from 'path';
import { quasar, transformAssetUrls } from "@quasar/vite-plugin";

const prefix_assets_dir = '';

export default defineConfig(({ mode }) => {
    // Determine which domain to serve based on environment or command line args
    const isDev = mode === 'development';
    const isCRM = process.env.VITE_SERVE_CRM === 'true';

    const serverConfig = isDev ? {
        host: isCRM ? 'crm.erp.test' : 'panel.erp.test',
        port: isCRM ? 3001 : 3000,
        cors: true,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
            'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization'
        }
    } : {};

    return {
        server: serverConfig,
    resolve: {
        alias: {
            "@": path.resolve(__dirname, 'src'),
        },
    },
    build: {
        rollupOptions: {
            output: {
                assetFileNames: (assetInfo) => {
                    let extType = assetInfo.name.split('.').pop();
                    if (/js|css/i.test(extType)) {
                        return prefix_assets_dir + `${extType}/[name].[hash][extname]`;
                    }

                    if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(extType)) {
                        extType = 'images';
                    }
                    if (/ttf|woff2|woff/i.test(extType)) {
                        extType = 'fonts';
                    }
                    const prefix_path = !assetInfo.originalFileName.includes("src/assets/") ? extType : [extType, assetInfo.originalFileName.replace("src/assets/", "").split("/").reverse().splice(1).reverse().join('/')].filter(f => f).join('/');
                   // console.log(prefix_path)
                    return prefix_assets_dir + `${prefix_path}/[name][extname]`;
                },
                chunkFileNames: prefix_assets_dir + 'js/[name].[hash].js',
                entryFileNames: prefix_assets_dir + 'js/[name].[hash].js',
            },
        },
    },
    plugins: [
        VitePWA({
            base: "/",
            scope: '/',
            workbox: {
                navigateFallback: "",
                maximumFileSizeToCacheInBytes: 5 * 1024 * 1024,
            },
            registerType: 'autoUpdate',
            injectRegister: 'auto',
            manifest: {
                name: "پنل ایران پارس",
                short_name: "ایران پارس",
                description: "پنل تولید",
                theme_color: "#ffffff",
                icons: [
                    {
                        src: "/images/192.png",
                        sizes: "192x192",
                        type: "image/png",
                    },
                    {
                        src: "/images/512.png",
                        sizes: "512x512",
                        type: "image/png",
                    },
                ],
            },
            includeManifestIcons: true,
        }),
        vue({
            template: { transformAssetUrls },
        }),
        quasar({
            sassVariables: path.resolve(__dirname + "/quasar-variables.sass"),
        }),
    ],
    };
});
