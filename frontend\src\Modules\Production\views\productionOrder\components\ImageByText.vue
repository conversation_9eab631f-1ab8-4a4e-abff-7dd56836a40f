<template>
    <div class="flex gap-2 items-center place-content-center">
        <j-image-viewer v-if="localSrc" v-model:src="localSrc" class="print-hidden" />
        <span>{{ text }}</span>
    </div>
</template>

<script>
import { ref, watch } from 'vue';

export default {
    props: {
        src: String,
        text: String,
    },
    setup(props, { emit }) {
        // تعریف یک متغیر محلی برای نگهداری مقدار prop
        const localSrc = ref(props.src);

        // همگام‌سازی localSrc با prop اصلی
        watch(() => props.src, (newValue) => {
            localSrc.value = newValue;
        });

        // ارسال تغییرات localSrc به والد
        watch(localSrc, (newValue) => {
            emit('update:src', newValue);
        });

        return {
            localSrc,
        };
    }
}
</script>
